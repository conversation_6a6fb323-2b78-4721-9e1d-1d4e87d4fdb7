<template>
  <a-input-group compact class="easy-number-group">
    <a-input-number :min="-999999999" :max="999999999" v-model:value="defaultValue[0]" :placeholder="props.placeholder[0]" @blur="onBlur"></a-input-number>
    <!-- <a-input class="site-input-split" placeholder="~" disabled /> -->
    <SwapRightOutlined class="pl pr text-[#999] text-14px" />
    <a-input-number :min="-999999999" :max="999999999" v-model:value="defaultValue[1]" :placeholder="props.placeholder[1]" @blur="onBlur"></a-input-number>
  </a-input-group>
</template>

<script lang="ts" setup>
import { SwapRightOutlined } from '@ant-design/icons-vue'
import { clone } from 'lodash'

const props = withDefaults(
  defineProps<{
    value: number[]
    placeholder: string[]
  }>(),
  {
    value: () => [],
    placeholder: () => [],
  },
)

const emit = defineEmits<{
  (e: 'update:value', value: number[]): void
}>()

const defaultValue = ref<number[]>([])

watchEffect(() => {
  if (props.value) {
    defaultValue.value = clone(props.value)
  }
})

// 对比数值大小
const onBlur = () => {
  if (defaultValue.value[0] && defaultValue.value[1] && defaultValue.value[0] > defaultValue.value[1]) {
    defaultValue.value = [defaultValue.value[1], defaultValue.value[0]]
  }
  emit('update:value', defaultValue.value)
}
</script>

<style lang="scss" scoped>
.easy-number-group {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;

  &::before {
    position: absolute;
    top: 0;
    bottom: 0;
    display: block;
    width: 100%;
    content: '';
    border: 1px #dcdee0 solid;
    border-radius: 4px;
  }

  .site-input-split {
    width: 30px;
    pointer-events: none;
    background-color: transparent;
    border: none;
    border-left: 0;
  }

  .ant-input-number {
    flex: 1;
    background-color: transparent;
    border-color: transparent;

    &:hover {
      border-color: #69b9ff;
    }
  }
}
</style>
