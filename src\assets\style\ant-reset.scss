.ant-checkbox-group {
  .ant-checkbox-wrapper .ant-checkbox {
    align-self: flex-start;
    margin-top: 3px;
  }
}

.ant-radio-group {
  .ant-radio-wrapper .ant-radio {
    align-self: flex-start;
    margin-top: 3px;
  }
}

.ant-form-inline .ant-form-item {
  margin-inline-end: 6px;
}

.ant-modal-confirm .ant-modal-confirm-body > * + .ant-modal-confirm-title + .ant-modal-confirm-content {
  max-width: calc(100% - 32px) !important;
}

.ant-modal-confirm .ant-modal-confirm-body > .ant-modal-confirm-title + .ant-modal-confirm-content {
  max-width: 100%;
}

.ant-pagination-options-quick-jumper {
  input {
    vertical-align: top;
  }
}

.ant-pagination-options {
  .ant-select-in-form-item {
    width: auto;
  }
}

.ant-timeline {
  .ant-timeline-item {
    padding-bottom: 12px;
  }

  .ant-timeline-item-head {
    position: relative;
    background-color: #1890ff;
    border: 1px solid #fff;
    border-color: #fff;

    &::before {
      position: absolute;
      top: 50%;
      left: 50%;
      width: 14px;
      height: 14px;
      content: '';
      border: 1px solid #1890ff;
      border-radius: 50%;
      transform: translate(-50%, -50%);
    }
  }

  .ant-timeline-item-tail {
    inset-block-start: 11px;
    height: calc(100% - 12px);
    border-color: #1890ff;
    border-inline-start-width: 1px;
  }

  .ant-timeline-item-last {
    position: relative;

    &::before {
      position: absolute;
      top: 12px;
      left: 4px;
      width: 1px;
      height: calc(100% - 30px);
      content: '';
      background-color: #1890ff;
    }

    &::after {
      position: absolute;
      top: calc(100% - 22px);
      left: 1px;
      width: 7px;
      height: 7px;
      content: '';
      background-color: #1890ff;
      border-radius: 90%;
    }
  }

  .ant-timeline-item-content {
    inset-block-start: -16px;

    .timeline-title {
      margin-bottom: 8px;
      color: #666;
    }

    .timeline-content {
      padding: 16px;
      color: #333;
      background-color: #f6f8fa;
    }
  }
}

.ant-dropdown-menu-item {
  .ant-dropdown-menu-title-content:hover {
    color: $color;
  }
}

.ant-switch {
  height: 22px;
  line-height: 22px;

  &.ant-switch.ant-switch-checked .ant-switch-handle {
    inset-inline-start: calc(100% - 20px);
  }

  .ant-switch-inner .ant-switch-inner-unchecked {
    margin-top: -23px;
  }

  .ant-switch-handle {
    width: 18px;
    height: 18px;
    border-radius: 50%;
    box-shadow: 0 2px 4px 0 rgb(0 35 11 / 20%);
  }
}

.ant-menu-submenu-selected {
  .ant-menu-submenu-title .ant-menu-item-icon .iconfont {
    color: #fff !important;
  }
}

.ant-pagination.ant-pagination-mini {
  .ant-pagination-options .ant-pagination-options-size-changer {
    width: 95px !important;

    .ant-select-selector {
      height: 24px;
      line-height: 24px;

      .ant-select-selection-item {
        line-height: 22px;
      }
    }
  }
}
