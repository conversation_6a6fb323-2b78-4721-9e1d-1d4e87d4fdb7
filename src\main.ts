import VxeTable from 'vxe-table'
import Antd from 'ant-design-vue'
import print from 'vue3-print-nb'
import { createApp } from 'vue'
import { createPinia } from 'pinia'
import { registerSW } from 'virtual:pwa-register'

import VxeUI from '@/core/VxeUi'
import router from '@/router'

import 'ant-design-vue/dist/reset.css'
import 'vxe-table/lib/style.css'
import 'virtual:uno.css'

// 自定义样式 （additionalData）
// INFO 只有在main.js引入一个其他scss文件或者在.vue文件中使用<style>，并且里面有内容，则 scss.additionalData 配置的全局scss文件就可以正确引入了
import '@/assets/style/reset.scss'
import '@/assets/style/common.scss'
import '@/assets/style/iconfont.css'
import '@/assets/style/ant-reset.scss'

import '@/core/index'

import App from './App.vue'

import { GetUserInfo } from '@/servers/User'

const pinia = createPinia()

// PWA immediate自动更新 & 定时任务更新
const swConfig = {
  immediate: true,
  async onRegisteredSW(swUrl, r) {
    if (r) {
      const logColor1 = `color: white; padding: 2px 5px; border-radius: 2px;`
      const logColor2 = `font-weight: bold; color: #aaa;`
      const headerConfig: Record<string, unknown> = {
        cache: 'no-store',
        headers: {
          cache: 'no-store',
          'cache-control': 'no-cache',
        },
      }
      fetch('./version.json', headerConfig)
        .then((response) => {
          if (!response.ok) {
            throw new Error(`网络响应不是一个有效的 JSON：${response.statusText}`)
          }
          return response.json()
        })
        .then(async (data) => {
          const version = data.version
          const localVersion = localStorage.getItem('srm_version') || version
          const updateSW = async () => {
            const resp = await fetch(swUrl, headerConfig)
            if (resp?.status === 200) {
              r.update()
              return Promise.resolve()
            }
          }
          if (version != localVersion) {
            console.info(`%cUPDATE BEFORE%c 版本号不一致`, `${logColor1} background: #faad14`, logColor2)
            await updateSW()
            const wsTimer = setInterval(() => {
              if (!r.installing) {
                console.info(`%cUPDATED%c 已更新`, `${logColor1} background: #52c41a`, logColor2)
                localStorage.setItem('srm_version', version)
                clearInterval(wsTimer)
              }
            })
          } else {
            const now = Date.now()
            const intervalMS = 60 * 60 * 1000 * 6
            const startTime = localStorage.getItem('ws_start_time')
            if (!startTime) localStorage.setItem('ws_start_time', `${now}`)
            if (startTime && now - Number(startTime) > intervalMS) {
              console.info(`%cUPDATE BEFORE%c 超过规定时间准备更新`, `${logColor1} background: #faad14`, logColor2)
              if (r.installing || !navigator) return
              if ('connection' in navigator && !navigator.onLine) return

              await updateSW()
              console.info(`%cUPDATED%c 已更新`, `${logColor1} background: #52c41a`, logColor2)
              localStorage.setItem('ws_start_time', `${now}`)
            }
          }
        })
        .catch((error) => {
          console.error('获取数据时发生错误:', error)
        })
    }
  },
  onRegisterError: (error) => {
    console.error('registerSW onRegisterError: ', error)
  },
}
registerSW(swConfig)

const app = createApp(App).use(Antd).use(VxeUI).use(VxeTable).use(router).use(print).use(pinia)
const userInfo = ref<UserInfo | null>()
// cleanupPWA()
router.beforeEach(async (to, _from, next) => {
  const userData = localStorage.getItem('userData') || ''
  const code: any = to.query.code || ''
  if (to.fullPath === '/login') {
    userInfo.value = null
    app.provide('userInfo', userInfo.value)
  } else if (!userInfo.value?.id) {
    try {
      if (to.fullPath !== '/' || window.self === window.top) {
        const res = await GetUserInfo()
        if (res.code === 0) {
          userInfo.value = res.data
        }
      }
    } catch {
      userInfo.value = null
      console.warn('getUserInfo 401')
    }
    app.provide('userInfo', userInfo.value)
  }
  if (code || to.fullPath === '/loading') {
    if (code) {
      localStorage.setItem('code', code)
      next({ path: '/loading' })
    } else {
      next()
    }
  } else if (userData || to.fullPath === '/login') {
    if (!to.name) return next('/')
    // if (edition()) {
    //   next({ path: '/login' })
    // } else {
    // }
    localStorage.setItem('lastUseTime', String(Math.floor(Date.now() / 1000)))
    next()
  } else {
    next({ path: '/login' })
  }
})
app.mount('#app')

// 获取版本号
// const edition = () => {
//   const edition = localStorage.getItem('edition')
//   const VERSION = import.meta.env.VITE_APP_VERSION
//   if (edition) {
//     if (edition != VERSION) {
//       // localStorage.clear()
//     }
//   } else {
//     localStorage.setItem('edition', import.meta.env.VITE_APP_VERSION)
//   }
//   return false
// }
// 自动登录
if (window.performance.navigation.type == 0) {
  if (localStorage.getItem('autoLogin') !== 'true') {
    localStorage.removeItem('userData')
  }
}
// 超十五天未使用
if (Math.floor(Date.now() / 1000) - Number(localStorage.getItem('lastUseTime')) > 3600 * 24 * 15) {
  localStorage.removeItem('userData')
}

console.info(`%cVERSION%c ${import.meta.env.VITE_APP_VERSION}`, 'color: white; background: #42b983; padding: 2px 5px; border-radius: 2px', 'font-weight: bold; color: #aaa;')
console.info(
  `%cBUILD%c ${new Date(+(import.meta.env.VITE_APP_TIMESTAMP || 0)).toLocaleString('zh-CN')}`,
  'color: white; background: #1890FF; padding: 2px 5px; border-radius: 2px',
  'font-weight: bold; color: #aaa;',
)
