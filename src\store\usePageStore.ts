import { defineStore } from 'pinia'

export type NavItem = {
  path: string
  fullPath: string
  name?: string | symbol | undefined
  title?: string | symbol | undefined
  code?: unknown
  id: unknown
  sourcePath?: string
}

export const usePageStore = defineStore('page', () => {
  // keepalive页面不缓存列表
  const excludeList = ref<string[]>([])
  const exclude = computed(() => {
    return excludeList.value
  })
  const addByExclude = (path: string) => {
    removeByInclude(path)
    excludeList.value = [...new Set([...excludeList.value, path])]
  }
  const removeByExclude = (path: string) => {
    excludeList.value = excludeList.value.filter((item) => item !== path)
  }
  const isExclude = (path: string) => {
    return excludeList.value.includes(path)
  }

  // keepalive页面缓存列表
  const includeList = ref<string[]>([])
  const include = computed(() => includeList.value)
  const addByInclude = (path: string) => {
    removeByExclude(path)
    includeList.value = [...new Set([...includeList.value, path])]
  }
  const removeByInclude = (path: string) => {
    includeList.value = includeList.value.filter((item) => item !== path)
  }
  const hasInclude = (path: string) => {
    return includeList.value.includes(path)
  }

  // 加载页面需调用hook:usePage刷新方法列表
  const refreshList = ref<string[]>([])
  const refreshRows = computed(() => refreshList.value)
  const addByRefresh = (path: string) => {
    refreshList.value = [...new Set([...refreshList.value, path])]
  }
  const removeByRefresh = (path: string) => {
    refreshList.value = refreshList.value.filter((item) => item !== path)
  }
  const hasRefresh = (path: string) => {
    return refreshList.value.includes(path)
  }

  // 头部菜单
  const navPageList = ref<NavItem[]>([])
  const navPageArr = computed(() => navPageList.value)

  const initNavPage = (arr) => {
    navPageList.value = arr
  }
  const setNavPage = (nav: NavItem, params?: any) => {
    const found = navPageList.value.find((item) => decodeURIComponent(item.fullPath) === decodeURIComponent(nav.fullPath))
    const result = found || nav
    if (params) {
      for (const key of Object.keys(params)) {
        result[key] = params[key]
      }
    }
    if (!found) navPageList.value.push(nav)
    localStorage.setItem('navPageArr', JSON.stringify(navPageList.value))
    return result
  }
  const removeNavPage = (path) => {
    const index = navPageList.value.findIndex((item) => item.fullPath === path)
    let item
    if (index > -1) {
      item = navPageList.value.splice(index, 1)[0]
    }
    localStorage.setItem('navPageArr', JSON.stringify(navPageList.value))
    return {
      index,
      item,
    }
  }

  const reset = () => {
    excludeList.value = []
    includeList.value = []
    refreshList.value = []
    navPageList.value = []
  }

  return {
    exclude,
    addByExclude,
    removeByExclude,
    isExclude,

    include,
    addByInclude,
    removeByInclude,
    hasInclude,

    refreshRows,
    addByRefresh,
    removeByRefresh,
    hasRefresh,

    initNavPage,
    navPageArr,
    setNavPage,
    removeNavPage,

    reset,
  }
})
