<template>
  <a-drawer v-model:open="openFlag" width="700px" :bodyStyle="bodyStyle">
    <template #title>
      <span class="font-bold">下载中心</span>
      <span class="font-bold text-12 opacity-50">（说明：文件仅保存24小时）</span>
    </template>
    <a-input-search v-model:value="searchText" placeholder="请输入搜索内容" @search="handleSearch" />
    <div class="flex flex-col flex-1 overflow-hidden mt-15px">
      <BaseTable ref="baseTableRef" v-model:form="formArr" :tableColumns="tableColumns" :showLineHeightSetter="false" :get-list="GetDownloadTaskList" :form-format="formFormat" :row-style="rowStyle">
        <template #task_state="{ row }">
          {{ stateMap[row.task_state] }}
        </template>
        <template #operate="{ row }">
          <span class="text-12 text-#999" v-if="row.task_state === 4">已过期</span>
          <span class="text-12 text-red" v-else-if="row.task_state === 3">导出失败</span>
          <template v-else>
            <a-button type="link" class="px-0! mr-12" size="small" @click="handlePreview(row.id)">预览</a-button>
            <a-button type="link" :class="{ 'text-#999': row.file_download_num > 0 }" class="px-0!" size="small" @click="handleDownLoad(row)">下载</a-button>
          </template>
        </template>
      </BaseTable>
    </div>
  </a-drawer>
</template>

<script lang="ts" setup>
import { CheckCircleFilled, LoadingOutlined } from '@ant-design/icons-vue'
import { Button, notification } from 'ant-design-vue'

import { downloadUrl, Enum2Map } from '@/utils/index'
import eventBus from '@/utils/eventBus'

import { DownloadTaskStateEnum, GetDownloadTaskInfos, GetDownloadTaskList, Preview, DownloadFile } from '@/servers/DownloadCenter'

type NoticeType = 'loading' | 'success'

const TASK_DELAY_TIME = 5000

const stateMap = ref(Enum2Map(DownloadTaskStateEnum))
const bodyStyle = ref<Record<string, string>>({
  display: 'flex',
  flexDirection: 'column',
})

const baseTableRef = ref()
const formArr = ref([])
const tableColumns = ref<any[]>([
  { name: '文件名称', key: 'file_name', is_show: true },
  { name: '导出进度', key: 'task_state', is_show: true, width: 80 },
  { name: '导出时间', key: 'finish_at', is_show: true, width: 140 },
  { name: '操作', key: 'operate', is_show: true, width: 100 },
])
const rowStyle = ({ row }) => {
  if (row.task_state === 4) return { color: '#999' }
  if (row.task_state === 3) return { color: '#fff000' }
  return {}
}

const openFlag = ref(false)
const open = () => {
  openFlag.value = true
  baseTableRef.value?.search()
}

const searchText = ref('')
const handleSearch = () => {
  baseTableRef.value.search()
}
const handlePreview = (id) => {
  Preview({ id }).then((res) => {
    window.open(res.data, '_blank')
  })
}
const handleDownLoad = (data) => {
  DownloadFile({ taskId: data.id }).then((res) => {
    data.file_download_num += 1
    eventBus.emit('changeDownLoadCount')
    downloadUrl(res.data, data.file_name)
  })
}

const formFormat = (data) => ({
  ...data,
  keyword: searchText.value,
})

// 根据类型显示通知
const showNotice = (type: NoticeType, item?: Record<string, any>) => {
  const { message, description, icon, btn, duration, key } = {
    loading: {
      message: '文件导出准备中',
      description: '文件已加入准备队列，您可以先进行其他操作，完成时将自动弹出下载框并在消息中收到含有下载链接的提醒。',
      icon: h(LoadingOutlined, { class: 'text-#1890FF f-14' }),
      duration: 5,
      key: 'loading',
    },
    success: {
      message: '导出文件准备完成',
      description: `【${item?.file_name}】导出文件已经准备完成。文件下载链接将于24小时后失效，请尽快下载。`,
      icon: h(CheckCircleFilled, { class: 'text-#52c41a f-14' }),
      btn: () => h(Button, { type: 'primary', onClick: () => handleDownLoad(item) }, { default: () => '立即下载' }),
      duration: 5,
      key: `${item?.id}`,
    },
  }[type]
  if (!message) return
  notification.open({
    message: h('div', { class: 'flex align-center' }, [icon, h('div', { class: 'font-14 ml-10 font-bold text-#333' }, message)]),
    description: h('div', { class: 'text-#999' }, description),
    icon: null,
    duration,
    btn,
    key,
  })
}

const downLoadIds = ref<number[]>([])
const timer = ref()
// 获取下载中的任务
const getTaskInfo = (params?: Record<string, any>) => {
  return new Promise((resolve) => {
    if (!downLoadIds.value.length) {
      clearTimeout(timer.value)
      timer.value = null
      return resolve(true)
    }
    let closeLoadingFlag = false
    GetDownloadTaskInfos(downLoadIds.value)
      .then((res) => {
        for (const item of res.data) {
          if ([DownloadTaskStateEnum.完成, DownloadTaskStateEnum.失败].includes(item.task_state)) {
            const index = downLoadIds.value.indexOf(item.id)
            if (~index) {
              downLoadIds.value.splice(index, 1)
              if (item.task_state === DownloadTaskStateEnum.完成) {
                // 有入参直接下载
                if (params && params.download === item.id) {
                  handleDownLoad(item)
                } else {
                  showNotice('success', item)
                }
              }
            }
          }
          if (item.task_state === DownloadTaskStateEnum.进行中) {
            showNotice('loading')
            closeLoadingFlag = true
          }
        }
        if (!closeLoadingFlag) {
          notification.close('loading')
        }
        timer.value = setTimeout(getTaskInfo, TASK_DELAY_TIME)
      })
      .finally(() => {
        resolve(true)
      })
  })
}

// 获取未下载数量
// const getCount = () => {
//   GetDownloadingTaskNum().then((res) => {
//     eventBus.emit('download')
//   })
// }

onMounted(async () => {
  getTaskInfo()
  eventBus.on('downLoadId', async (params: any = {}) => {
    downLoadIds.value.push(params?.id || params)
    clearTimeout(timer.value)
    await getTaskInfo(params?.download ? { download: params.id } : undefined)
    params?.resolve && params.resolve()
  })
})

defineExpose({ open, showNotice })
</script>

<style lang="scss" scoped></style>
