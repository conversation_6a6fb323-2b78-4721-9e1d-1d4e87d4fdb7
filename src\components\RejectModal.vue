<template>
  <a-modal v-model:open="visible" title="驳回" @ok="rejectSubmit" :confirm-loading="loading">
    <a-form ref="rejectFormRef" :model="rejectForm" :rules="rejectFormRules">
      <slot name="form"></slot>
      <a-form-item
        label="驳回原因"
        name="remark"
        :rules="[
          { required: true, message: '驳回原因必填' },
          { max: 200, message: '驳回原因不能超过200个字符' },
        ]"
      >
        <a-textarea v-model:value="rejectForm.remark" placeholder="请输入驳回原因" :maxlength="200" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script lang="ts" setup>
const emit = defineEmits(['confirm'])

const rejectForm = ref({
  remark: '',
})
const rejectFormRules = ref({
  remark: [
    { required: true, message: '驳回原因必填' },
    { max: 200, message: '驳回原因不能超过200个字符' },
  ],
})
const visible = ref(false)
const rejectFormRef = ref()
const rejectSubmit = () => {
  rejectFormRef.value.validate().then(() => {
    loading.value = true
    emit('confirm', {
      ...params.value,
      remark: rejectForm.value.remark,
    })
  })
}

const loading = ref(false)
const params = ref({})
const open = (val) => {
  params.value = val
  rejectForm.value = {
    remark: '',
  }

  rejectFormRules.value = {
    remark: [
      { required: true, message: '驳回原因必填' },
      { max: 200, message: '驳回原因不能超过200个字符' },
    ],
  }
  visible.value = true
  loading.value = false
  nextTick(() => {
    rejectFormRef.value.clearValidate()
  })
}

const close = () => {
  visible.value = false
}

defineExpose({
  open,
  close,
})
</script>

<style lang="scss" scoped>
//
</style>
