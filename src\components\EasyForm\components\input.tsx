import { InputProps, Input, InputNumber } from 'ant-design-vue'

export default defineComponent({
  name: 'EasyFormInput',
  props: {
    innerProps: {
      require: true,
      type: Object as PropType<InputProps>,
    },
    item: {
      require: true,
      type: Object,
      default: () => ({}),
    },
    form: {
      type: Object as PropType<Record<string, any>>,
      default: () => ({}),
    },
    type: {
      type: String,
    },
  },
  setup(props) {
    const renderVN = () => {
      if (!props.innerProps) return null
      const defaultParams = {
        placeholder: props.item.placeholder || `请输入${props.item.label}`,
        allowClear: true,
        class: ['w-full'],
      }

      if (props?.type === 'textarea') {
        return h(Input.TextArea, {
          ...defaultParams,
          ...props.innerProps,
        })
      }

      if (props?.type === 'number') {
        return h(InputNumber as any, {
          ...defaultParams,
          ...props.innerProps,
        })
      }

      return [
        h(Input, {
          ...defaultParams,
          ...props.innerProps,
        }),
      ]
    }
    return {
      renderVN,
    }
  },
  render() {
    return this.renderVN()
  },
})
