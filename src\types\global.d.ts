declare global {
  type EasyFormItemProps = import('../components/EasyForm/index.d').FormItemProps
  type UserInfo = {
    readonly id: number
    readonly user_name?: string | null
    readonly real_name?: string | null
    readonly department_id: number | null
    readonly role_ids?: string | null
    readonly oa_user_id?: number | null
    readonly customer_id?: number | null
    readonly company?: string | null
    readonly department?: string | null
    readonly job_name?: string | null
    readonly email?: string | null
  }
  interface Number {
    bankRound(digits: number): string
    roundNext(digits: number): string
  }
}

export {}
