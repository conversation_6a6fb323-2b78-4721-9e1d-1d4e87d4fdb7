<!-- 商品库列表 -->
<template>
  <div class="flex flex-col h-full main">
    <Form ref="formRef" v-model:form="formArr" :page-type="PageTypeEnum.ProductInventory" @search="handleSearch" @setting="tableRef?.showTableSetting()"></Form>
    <BaseTable ref="tableRef" :page-type="PageTypeEnum.ProductInventory" v-model:form="formArr" :get-list="GetProductInventoryList" :form-format="formFormat" :isIndex="true">
      <template #right-btn>
        <a-button v-if="btnPermission[120201]" type="primary" @click="handleSync" :loading="syncLoading" :disabled="syncLoading">立即同步</a-button>
      </template>
      <template #category="{ row }">
        <span>{{ formSelectMap?.productType?.[`${row.category}`] || row.category }}</span>
      </template>
    </BaseTable>
  </div>
</template>
<script lang="ts" setup>
import { message } from 'ant-design-vue'

import { GetProductInventoryList, SyncProductSkuInventoryInfo } from '@/servers/BusinessCommon'

import { PageTypeEnum } from '@/enums/tableEnum'

const { btnPermission } = usePermission()

const formRef = ref()
const tableRef = ref()
const formArr = ref([
  { label: '商品编码', value: '', type: 'input', key: 'sku_id' },
  { label: '款式编码', value: '', type: 'input', key: 'style_code' },
  { label: '仓库ID', value: '', type: 'input', key: 'warehouse_id' },
  { label: '仓库名称', value: '', type: 'input', key: 'warehouse_name' },
])

const { formSelectMap } = useSearchForm(formArr)

const formFormat = (data) => ({
  ...data,
})

const handleSearch = () => {
  if (tableRef.value) tableRef.value.search()
}

const syncLoading = ref(false)
// 立即同步
const handleSync = async () => {
  syncLoading.value = true
  SyncProductSkuInventoryInfo()
    .then((res) => {
      if (res.success) {
        message.success('同步完成')
        tableRef.value.search()
      }
    })
    .finally(() => {
      syncLoading.value = false
    })
}
</script>
