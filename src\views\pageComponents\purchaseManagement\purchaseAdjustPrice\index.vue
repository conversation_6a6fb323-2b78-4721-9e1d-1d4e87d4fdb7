<template>
  <div class="flex flex-col h-full main">
    <Form ref="formRef" v-model:form="formArr" :page-type="PageTypeEnum.PurchaseAdjustPrice" @search="tableRef?.search()" @setting="tableRef?.showTableSetting()">
      <template #header>
        <StatusTabs v-model:status="status" :options="purchaseAdjustPriceStatusOption" :count-map="labelStatusCountMap" @change="tableRef?.search()" />
      </template>
    </Form>

    <BaseTable
      ref="tableRef"
      :page-type="PageTypeEnum.PurchaseAdjustPrice"
      v-model:form="formArr"
      :auto-search="false"
      :get-list="GetPurchaseAdjustPriceList"
      :form-format="formatData"
      :isCheckbox="true"
    >
      <template #left-btn>
        <a-button v-if="btnPermission[82004] || btnPermission[82005] || btnPermission[82006] || btnPermission[82007]" @click="handleShowAuditModal(true)">批量审核通过</a-button>
        <a-button v-if="btnPermission[82004] || btnPermission[82005] || btnPermission[82006] || btnPermission[82007]" @click="handleShowAuditModal(false)">批量审核拒绝</a-button>
      </template>
      <template #right-btn>
        <a-button v-if="btnPermission[82002]" type="primary" @click="handleShowAdd">添加调价表</a-button>
      </template>
      <template #fix_option="{ row, column }">
        <RightOperate :list="rightOperateList" :row="row" :column="column" />
      </template>
    </BaseTable>
    <adjust-price-drawer ref="adjustPriceDrawerRef" @query="tableRef?.search()" />
    <audit-modal ref="auditModalRef" @audit="handleSubmitAudit" />
  </div>
</template>

<script setup lang="ts">
import { message } from 'ant-design-vue'

import { purchaseAdjustPriceStatusOption } from '@/common/options'
import { DetailTypeEnum } from '@/common/enum'

import AdjustPriceDrawer from '@/views/pageComponents/purchaseManagement/purchaseAdjustPrice/components/AdjustPriceDrawer.vue'

import AuditModal from '../components/AuditModal.vue'

import { GetPurchaseAdjustPriceList, BatchAuditPurchaseAdjustPrice } from '@/servers/PurchaseAdjustPrice'

import { PageTypeEnum } from '@/enums/tableEnum'

const auditModalRef = useTemplateRef('auditModalRef')

const adjustPriceDrawerRef = useTemplateRef('adjustPriceDrawerRef')

const { btnPermission } = usePermission()
const { labelStatusCountMap, labelStatusRefresh } = useLabelStatus(PageTypeEnum.PurchaseAdjustPrice)

const tableRef = ref()
const formRef = ref()

const status = ref('')

const formArr = ref([
  { label: '单据编号', value: '', type: 'inputDlg', key: 'number' },
  { label: '商品编码', value: '', type: 'inputDlg', key: 'sku_ids' },
  { label: '调价表名称', value: '', type: 'input', key: 'name' },
  { label: '对应价目表编号', value: '', type: 'input', key: 'price_list_number' },
  { label: '时间', value: null, type: 'range-picker', key: 'create_at', formKeys: ['create_time', 'end_time'], placeholder: ['创建开始时间', '创建结束时间'] },
])

const rightOperateList = ref([
  {
    label: '查看',
    show: 82001, // 直接使用权限标识
    onClick: ({ row }) => {
      handleShowDetail(row.id, DetailTypeEnum.VIEW, row.audit_status)
    },
  },
  {
    label: '审核',
    show: ({ row }) => checkHasAudit(row.audit_status), // 调用方法检查审核状态
    onClick: ({ row }) => {
      handleShowDetail(row.id, DetailTypeEnum.AUDIT, row.audit_status)
    },
  },
  {
    label: '编辑',
    show: ({ row }) => [10, 95].includes(row.audit_status) && row.is_update && btnPermission.value[82003],
    onClick: ({ row }) => {
      handleShowDetail(row.id, DetailTypeEnum.EDIT, row.audit_status)
    },
  },
])

// 显示审核模态框
const handleShowAuditModal = (type: boolean) => {
  if (tableRef.value?.checkItemsArr?.length === 0) {
    message.warning('请选择要审核的调价表')
    return
  }
  if (tableRef.value?.checkItemsArr.some((i) => ![20, 30, 40, 50].includes(i.audit_status))) {
    message.warning('请选择正确的需审核的调价表')
    return
  }
  let haveAlike = false
  let noPreAudit = false
  tableRef.value?.checkItemsArr.forEach((i) => {
    if (i.audit_status === 20 && !btnPermission.value[82004]) {
      noPreAudit = true
    }
    if (i.audit_status === 30 && !btnPermission.value[82005]) {
      noPreAudit = true
    }
    if (i.audit_status === 40 && !btnPermission.value[82006]) {
      noPreAudit = true
    }
    if (i.audit_status === 50 && !btnPermission.value[82007]) {
      noPreAudit = true
    }
    tableRef.value?.checkItemsArr.forEach((j) => {
      if (i.audit_status !== j.audit_status) {
        haveAlike = true
      }
    })
  })
  if (haveAlike) {
    message.warning('请选择相同状态的单据进行批量审核操作')
    return
  }
  if (noPreAudit) {
    message.warning('您没有该状态的审核权限')
    return
  }
  auditModalRef.value?.open(type)
}

// 提交审核
const handleSubmitAudit = async (auditForm: any) => {
  try {
    await BatchAuditPurchaseAdjustPrice({
      ...auditForm,
      ids: tableRef.value?.checkItemsArr.map((i) => i.id),
    })
    message.success('审核成功')
    auditModalRef.value?.close()
    tableRef.value?.search()
  } catch (_) {
    auditModalRef.value?.close()
  }
}

const formatData = (data: any) => {
  labelStatusRefresh()
  return {
    ...data,
    audit_status: status.value,
  }
}

// 查看
const handleShowDetail = (id: number, type: DetailTypeEnum, status: number) => {
  adjustPriceDrawerRef.value?.open(type, id, PurchaseOrderAuditStatusEnum[status], status)
}
// 显示新增调价表
const handleShowAdd = () => {
  adjustPriceDrawerRef.value?.open(DetailTypeEnum.ADD)
}

const checkHasAudit = (status: number) => {
  if (![20, 30, 40, 50].includes(status)) {
    return false
  }
  if (status === 20 && !btnPermission.value[82004]) {
    return false
  }
  if (status === 30 && !btnPermission.value[82005]) {
    return false
  }
  if (status === 40 && !btnPermission.value[82006]) {
    return false
  }
  if (status === 50 && !btnPermission.value[82007]) {
    return false
  }
  return true
}

onMounted(() => {
  tableRef.value?.search()
})
</script>

<style scoped lang="scss"></style>
