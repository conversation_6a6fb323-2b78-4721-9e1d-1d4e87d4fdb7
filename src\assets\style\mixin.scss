$color: #1890ff;
$font-default: #666;
$bg-hover-color: #e8f4ff;

@mixin clearfix {
  &::after {
    display: table;
    clear: both;
    content: '';
  }
}

@mixin flex($justify: center, $align: center, $direction: row, $wrap: no-wrap) {
  display: flex;
  flex-flow: $direction $wrap;
  align-items: $align;
  justify-content: $justify;
}

@mixin scrollBar {
  /* Firefox 滚动条样式 */
  // scrollbar-width: 14px; /* 滚动条宽度 */
  // scrollbar-color: #c0c0c0 transparent; /* 滑块颜色 轨道颜色 */
  /* Edge 和 Webkit 浏览器滚动条样式 */
  &::-webkit-scrollbar {
    width: 14px; /* 垂直滚动条的宽度 */
    height: 14px; /* 水平滚动条的高度 - 热区范围 */
  }
  /* 滚动条轨道 */
  &::-webkit-scrollbar-track {
    background: transparent; /* 轨道的背景色 */
    border-radius: 7px; /* 轨道的圆角 */
  }
  /* 滚动条滑块 */
  &::-webkit-scrollbar-thumb {
    min-width: 6px; /* 滑块最小宽度 */
    min-height: 6px; /* 滑块最小高度 */
    background-color: #c0c0c0; /* 滑块的背景色 - 默认样式 */
    background-clip: content-box; /* 背景只应用到内容区域 */
    border: 4px solid transparent; /* 透明边框，创建填充空间 */
    border-radius: 10px; /* 滑块的圆角 */
  }
  /* 滚动条滑块在悬停时的样式 */
  &::-webkit-scrollbar-thumb:hover {
    background-color: #666; /* 悬停时的背景色 - 鼠标移入/点击样式 */
  }
  /* 滚动条滑块在激活时的样式 */
  &::-webkit-scrollbar-thumb:active {
    background-color: #666; /* 点击时的背景色 - 鼠标移入/点击样式 */
  }
  /* 滚动条角落 */
  &::-webkit-scrollbar-corner {
    background: #f8f8f8;
  }
}

@mixin relative {
  position: relative;
  width: 100%;
  height: 100%;
}

@mixin pct($pct) {
  position: relative;
  width: #{$pct};
  margin: 0 auto;
}

@mixin bgSize($url, $size: 100% 100%, $pos: center top) {
  background: url($url) no-repeat $pos;
  background-size: $size;
}

// 模糊蒙层
@mixin blurMask($rgba: 0.7, $blur: 5px) {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000;
  width: 100vw;
  height: 100vh;

  &::after {
    position: absolute;
    top: 0;
    left: 0;
    z-index: -1;
    width: 100%;
    height: 100%;
    content: '';
    background: rgba(0, 0, 0, $rgba);
    backdrop-filter: blur($blur);
    opacity: 0;
    animation: opacityFrame 0.3s ease-in-out;
    animation-fill-mode: forwards;

    @keyframes opacityFrame {
      from {
        opacity: 0;
      }

      to {
        opacity: 1;
      }
    }
  }
}

@mixin module($width, $height) {
  position: absolute;
  top: 50%;
  left: 50%;
  width: $width;
  height: $height;
  background: #fff;
  transform: translate(-50%, -50%);
  animation: opacityFrame 0.3s ease-in-out;
}

@mixin link($c: $color) {
  color: $c;
  transition: color 0.2s ease-in-out;

  &:hover {
    color: $color;
    cursor: pointer;
    opacity: 0.8;
  }
}
