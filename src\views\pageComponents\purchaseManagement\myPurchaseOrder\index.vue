<template>
  <div class="flex flex-col h-full main">
    <Form ref="formRef" v-model:form="formArr" :page-type="PageTypeEnum.PurchaseMyOrder" @search="tableRef?.search()" @setting="tableRef?.showTableSetting()">
      <template #header>
        <StatusTabs v-model:status="status" :options="purchaseMyOrderOption" @change="tableRef?.search()" />
      </template>
    </Form>
    <BaseTable ref="tableRef" :page-type="PageTypeEnum.PurchaseMyOrder" v-model:form="formArr" :isCheckbox="true" :get-list="GetMyOrderList" :form-format="formatData">
      <template #left-btn>
        <a-button type="primary" @click="getBatchConfirmOrder">批量确认订单</a-button>
        <!-- <a-button @click="LookClick({ id: 39 })">查看</a-button> -->
      </template>
      <!-- 结算方式 -->
      <template #settlement_type="{ row }">
        <span>{{ settlementTypeMap[row.settlement_type] }}</span>
      </template>
      <!-- 商品类型 -->
      <template #product_type="{ row }">
        <span>{{ productTypeMap[row.product_type] }}</span>
      </template>
      <!-- 订单状态 -->
      <template #order_status="{ row }">
        <span>{{ OrederStatusMap[row.order_status] }}</span>
      </template>
      <!-- 供应商状态 -->
      <template #supplier_type="{ row }">
        <span>{{ supplierTypeMap2[row.supplier_type] }}</span>
      </template>
      <template #fix_option="{ row, column }">
        <RightOperate :list="rightOperateList" :row="row" :column="column" />
      </template>
    </BaseTable>
  </div>
</template>
<script setup lang="ts">
import { Modal, message } from 'ant-design-vue'

import { purchaseMyOrderOption } from '@/common/options'
import { supplierTypeMap2, OrederStatusMap, settlementTypeMap, productTypeMap } from '@/common/map'

import { ConfirmOrder, GetMyOrderList, BatchConfirmOrder } from '@/servers/PurchaseManage'

import { PageTypeEnum } from '@/enums/tableEnum'

const { btnPermission } = usePermission()
const router = useRouter()
const status = ref(10)
const tableRef = ref()
const formArr = ref([
  { label: '申请单编号', value: null, type: 'input', key: 'number' },
  { label: '计划单号', value: null, type: 'input', key: 'plan_number' },
  // { label: '商品编码', value: null, type: 'input', key: 'k3_sku_id' },
  // { label: '商品名称', value: null, type: 'input', key: 'sku_name' },
  // { label: '供应商名称', value: null, type: 'input', key: 'supplier' },
  { label: '申请时间', value: null, type: 'range-picker', formKeys: ['apply_start_time', 'apply_end_time'], placeholder: ['申请开始时间', '申请结束时间'] },
  { label: '审核时间', value: null, type: 'range-picker', formKeys: ['start_time', 'end_time'], placeholder: ['审核开始时间', '审核结束时间'] },
])
const formatData = (data: any) => {
  return {
    ...data,
    audit_status: status.value,
  }
}
const LookClick = (row) => {
  let type = 1
  if (OrederStatusMap[row.order_status] == '进行中' && supplierTypeMap2[row.supplier_type] == '待确认') {
    type = 2
  }
  const sessionPageStr = sessionStorage.getItem('sessionPage')
  let sessionPage = {}
  if (sessionPageStr) {
    sessionPage = JSON.parse(sessionPageStr)
    sessionPage[`verifyMyOrder${row.id}-${type}`] = 'true'
  } else {
    sessionPage[`verifyMyOrder${row.id}-${type}`] = 'true'
  }
  sessionStorage.setItem('sessionPage', JSON.stringify(sessionPage))
  router.push(`/verifyMyOrder/${row.id}/${type}`)
}
const ConfirmOrderdata = async (item) => {
  const res = await ConfirmOrder({ id: item.id })
  console.log('res', res)
  try {
    if (res.success) {
      tableRef.value.search()
      message.success('订单已确认')
    } else {
      message.error(res.message)
    }
  } catch (e) {
    console.log(e)
  }
}
const VerifyOrder = (item) => {
  Modal.confirm({
    title: '一次确认：是否确认订单?',
    content: '',
    onOk() {
      Modal.confirm({
        title: '二次确认：是否确认订单?',
        content: '',
        onOk() {
          ConfirmOrderdata(item)
        },
        // eslint-disable-next-line @typescript-eslint/no-empty-function
        onCancel() {
          message.info('取消确认')
        },
      })
    },
    // eslint-disable-next-line @typescript-eslint/no-empty-function
    onCancel() {
      message.info('取消确认')
    },
  })
}
const getBatchConfirmOrder = () => {
  const arr: any = []
  tableRef.value.checkItemsArr.forEach((item) => {
    arr.push(item.id)
  })
  console.log('arr', arr)

  if (arr.length > 0) {
    Modal.confirm({
      title: '一次确认：是否确认订单?',
      content: '',
      onOk() {
        Modal.confirm({
          title: '二次确认：是否确认订单?',
          content: '',
          async onOk() {
            const res = await BatchConfirmOrder(arr)
            console.log('res', res)
            if (res.success) {
              tableRef.value.search()
              message.success('订单已确认')
            } else {
              message.error(res.message)
            }
          },
          // eslint-disable-next-line @typescript-eslint/no-empty-function
          onCancel() {
            message.info('取消确认')
          },
        })
      },
      // eslint-disable-next-line @typescript-eslint/no-empty-function
      onCancel() {
        message.info('取消确认')
      },
    })
  } else {
    message.info('请勾选批量确认数据')
  }
}

const rightOperateList = ref([
  {
    label: '查看',
    show: 91001,
    onClick: ({ row }) => {
      LookClick(row)
    },
  },
  {
    label: '确认订单',
    show: ({ row }) => {
      return OrederStatusMap[row.order_status] == '进行中' && btnPermission.value[91002] && supplierTypeMap2[row.supplier_type] == '待确认'
    },
    onClick: ({ row }) => {
      VerifyOrder(row)
    },
  },
])
</script>
<style scoped lang="scss">
.btnBox2 {
  display: flex;
  align-items: center;
  justify-content: flex-start;

  .ant-btn {
    margin-right: 10px;
  }
}
</style>
