{"globals": {"AliProductMapMatchStatusEnum": true, "AuditStatusEnum": true, "BillPayableOrderAuditEnum": true, "BillPayableOrderSettlementEnum": true, "BillSettlementStatusEnum": true, "BookingOrderAuditStatusEnum": true, "Component": true, "ComponentPublicInstance": true, "ComputedRef": true, "DirectiveBinding": true, "EffectScope": true, "ExtractDefaultPropTypes": true, "ExtractPropTypes": true, "ExtractPublicPropTypes": true, "FinishedApplyOrderSourceTypeEnum": true, "FormInstance": true, "InjectionKey": true, "InvoiceStatusEnum": true, "InvoiceTypeEnum": true, "MaybeRef": true, "MaybeRefOrGetter": true, "ModalProps": true, "OpLogPageTypeEnum": true, "OutboundWarehouseOrderStatusEnum": true, "PaymentOrderAuditStatusEnum": true, "PaymentOrderSourceTypeEnum": true, "PaymentOrderStatusEnum": true, "PaymentStatusEnum": true, "PaymentTypeEnum": true, "ProductTypeEnum": true, "PropType": true, "PurchaseApplyOrderEnum": true, "PurchaseApplyOrderSourceTypeEnum": true, "PurchaseApplyOrderStatusEnum": true, "PurchaseOrderAbnormalStatusEnum": true, "PurchaseOrderAuditStatusEnum": true, "PurchaseOrderReceivedStatusEnum": true, "PurchaseOrderStatusEnum": true, "PurchaseOrderWarehousedStatusEnum": true, "PurchasePaymentOrderStatusEnum": true, "PurchasePaymentStatusEnum": true, "PurchaseProductTypeEnum": true, "PurchaseReconciliationStatusEnum": true, "PurchaseReturnApplicationTypeEnum": true, "PurchaseReturnPriceTrendEnum": true, "PurchaseReturnReasonTypeEnum": true, "PurchaseReturnSupplierCategoryEnum": true, "PurchaseSettlementTypeEnum": true, "PurchaseTypeEnum": true, "RecommendOrderOperationTypeEnum": true, "ReconciliationStatusEnum": true, "Ref": true, "ReturnReasonTypeEnum": true, "SelectProps": true, "SettlementTypeEnum": true, "ShipmentStatusEnum": true, "SizeType": true, "SupplierLiquidateOrderTypeEnum": true, "SupplierSourceTypeEnum": true, "SupplierStatusEnum": true, "SupplierTypeEnum": true, "TakeOrderMethodEnum": true, "TakeOrderTypeEnum": true, "UploadFileModuleEnum": true, "VNode": true, "VxeButtonEvents": true, "VxeColumnPropTypes": true, "VxeFormEvents": true, "VxeFormPropTypes": true, "VxeGridListeners": true, "VxeGridPropTypes": true, "VxeGridProps": true, "VxePulldownInstance": true, "VxeTableDefines": true, "VxeTablePropTypes": true, "WritableComputedRef": true, "computed": true, "createApp": true, "customRef": true, "defineAsyncComponent": true, "defineComponent": true, "effectScope": true, "getCurrentInstance": true, "getCurrentScope": true, "h": true, "inject": true, "isProxy": true, "isReactive": true, "isReadonly": true, "isRef": true, "markRaw": true, "nextTick": true, "onActivated": true, "onBeforeMount": true, "onBeforeRouteLeave": true, "onBeforeRouteUpdate": true, "onBeforeUnmount": true, "onBeforeUpdate": true, "onDeactivated": true, "onErrorCaptured": true, "onMounted": true, "onRenderTracked": true, "onRenderTriggered": true, "onScopeDispose": true, "onServerPrefetch": true, "onUnmounted": true, "onUpdated": true, "onWatcherCleanup": true, "productTypeEnum": true, "provide": true, "reactive": true, "readonly": true, "ref": true, "resolveComponent": true, "shallowReactive": true, "shallowReadonly": true, "shallowRef": true, "toRaw": true, "toRef": true, "toRefs": true, "toValue": true, "triggerRef": true, "unref": true, "useAttrs": true, "useCssModule": true, "useCssVars": true, "useEasyTable": true, "useId": true, "useLabelStatus": true, "useLink": true, "useModel": true, "usePage": true, "usePermission": true, "useRoute": true, "useRouter": true, "useSearchForm": true, "useSlots": true, "useTemplateRef": true, "watch": true, "watchEffect": true, "watchPostEffect": true, "watchSyncEffect": true, "BillPayableOrderStatusEnum": true, "PurchaseOrderAuditStatusStyleEnum": true, "PurchaseOrderStatusStyleEnum": true, "Rule": true}}