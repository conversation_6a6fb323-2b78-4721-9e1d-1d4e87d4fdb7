<template>
  <a-drawer title="选择账单" width="85vw" :visible="visible" @close="handleClose" :maskClosable="false" destroyOnClose>
    <div class="flex h-full overflow-auto">
      <div class="p-16px h-full overflow-auto w65vw">
        <a-space class="mb-12">
          <a-input placeholder="账单单号" v-model:value="queryParams.number" :maxlength="200" allowClear />
          <a-input placeholder="供应商子公司" v-model:value="queryParams.company_supplier_name" :maxlength="200" allowClear />
          <a-button type="primary" @click="handleSearch">查询</a-button>
          <a-button @click="handleReset">重置</a-button>
        </a-space>
        <vxe-table
          v-if="leftVisible"
          :border="true"
          ref="productTableRef"
          size="mini"
          :row-config="{ isHover: true, keyField: 'id', height: 40 }"
          :custom-config="{ mode: 'popup' }"
          :data="data"
          :show-overflow="true"
          :show-header-overflow="true"
          :show-footer-overflow="true"
          :column-config="{ resizable: true }"
          class="tableBoxwidth"
          :checkbox-config="{
            trigger: 'row',
          }"
          min-height="0"
          stripe
          v-bind="$attrs"
          @checkbox-all="handleSelectAll"
          @checkbox-change="handleSelectChange"
        >
          <vxe-column type="checkbox" width="50" fixed="left"></vxe-column>
          <slot name="column">
            <template v-for="i in tableKey" :key="i.field">
              <vxe-column v-bind="i">
                <template #default="{ row }">
                  <span>{{ row[i.field] }}</span>
                </template>
              </vxe-column>
            </template>
          </slot>
        </vxe-table>
        <div class="paginationBox">
          <div class="pagination">
            <a-pagination
              show-quick-jumper
              :total="total"
              show-size-changer
              v-model:current="queryParams.page"
              v-model:page-size="queryParams.pageSize"
              :page-size-options="['10', '20', '30', '40', '50']"
              @change="handlePageChange"
              size="small"
            >
              <template #buildOptionText="props">
                <span>{{ props.value }}条/页</span>
              </template>
            </a-pagination>
          </div>
          <div class="totalBox">
            <div class="text">总数:</div>
            <div class="total">{{ total }}</div>
          </div>
        </div>
      </div>
      <div class="flex-1 overflow-y-auto p-16 text-black">
        <div class="flex mb-12 h30px line-height-30px h-30px">
          <div class="text-16px">已选{{ selectProductList.length }}个账单</div>
          <a-button class="ml-auto" type="primary" @click="cleanCheck">清空</a-button>
        </div>
        <vxe-table
          v-if="leftVisible"
          :border="true"
          ref="selectTableRef"
          size="mini"
          :row-config="{ isHover: true, keyField: 'id', height: 40 }"
          :custom-config="{ mode: 'popup' }"
          :data="selectProductList"
          :show-overflow="true"
          :show-header-overflow="true"
          :show-footer-overflow="true"
          :column-config="{ resizable: true }"
          class="tableBoxwidth"
          min-height="0"
          stripe
          v-bind="$attrs"
        >
          <vxe-column type="seq" title="序号" width="60" fixed="left"></vxe-column>
          <template v-for="i in selectTableKey" :key="i.field">
            <vxe-column v-bind="i">
              <template #default="{ row }">
                <div class="flex items-center justify-between">
                  <span>{{ row[i.field] }}</span>
                  <a-button type="link" class="ml-2" @click="handleUnselectRow(row)">取消选中</a-button>
                </div>
              </template>
            </vxe-column>
          </template>
        </vxe-table>
      </div>
    </div>
    <template #footer>
      <div class="flex justify-start">
        <a-space>
          <a-button type="primary" @click="handleSelectProduct">确认</a-button>
          <a-button @click="handleClose">取消</a-button>
        </a-space>
      </div>
    </template>
  </a-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, nextTick, watch } from 'vue'
import { message } from 'ant-design-vue'

import { GetBillListResultByPayOrder } from '@/servers/paymentOrder'

// 定义 props
interface Props {
  visible?: boolean
  selectedBills?: any[]
  supplierId?: string
  supplierName?: string
  paymentOrderId?: number | string | null // 新增：付款单ID，用于编辑时过滤数据
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  selectedBills: () => [],
  supplierId: '',
  supplierName: '',
})

// 定义 emits
const emits = defineEmits<{
  selectProduct: [selectedBills: any[], supplierId: string, supplierName: string]
  updateProduct: [updatedBills: any[]]
}>()

// 响应式数据
const visible = ref(props.visible)
const leftVisible = ref(false)
const data = ref<any[]>([])
const total = ref(0)
const selectProductList = ref<any[]>([])
const tempSelectedList = ref<any[]>([])
const initialSelection = ref<any[]>([])
const isUpdatingSelection = ref(false)

// 查询参数
const queryParams = reactive({
  page: 1,
  pageSize: 10,
  number: '',
  company_supplier_name: '',
  id: null as string | number | null, // 新增：付款单ID字段
})

// 表格列配置
const tableKey = ref([
  { field: 'number', title: '账单单号', width: 140 },
  { field: 'bill_month', title: '账单月份', width: 120, formatter: ({ row }) => dayjs(row.bill_month).format('YYYY-MM') },
  { field: 'supplier_name', title: '供应商', width: 150 },
  { field: 'company_supplier_name', title: '供应商子公司', width: 150 },
  { field: 'actual_payable_amount', title: '实际应付金额', width: 120 },
  { field: 'paid_amount', title: '已付金额', width: 120 },
])

const selectTableKey = ref([
  { field: 'number', title: '账单单号', width: 140 },
  { field: 'bill_month', title: '账单月份', width: 120 },
  { field: 'supplier_name', title: '供应商', width: 150 },
  { field: 'company_supplier_name', title: '供应商子公司', width: 150 },
  { field: 'actual_payable_amount', title: '实际应付金额', width: 120 },
  { field: 'paid_amount', title: '已付金额', width: 120 },
])

// 表格引用
const productTableRef = ref()
const selectTableRef = ref()

// 校验供应商一致性
const validateSupplierConsistency = (records: any[]) => {
  if (records.length === 0) {
    return { valid: true, message: '' }
  }

  // 获取第一个记录的供应商信息
  const firstRecord = records[0]
  const firstSupplierId = firstRecord.supplier_id
  const firstCompanySupplierId = firstRecord.company_supplier_id

  // 检查所有记录是否都是同一个供应商
  const hasDifferentSupplier = records.some((record) => record.supplier_id !== firstSupplierId)

  if (hasDifferentSupplier) {
    return {
      valid: false,
      message: '只能选择同一个供应商的账单',
    }
  }

  // 检查所有记录是否都是同一个供应商子公司
  const hasDifferentCompanySupplier = records.some((record) => record.company_supplier_id !== firstCompanySupplierId)

  if (hasDifferentCompanySupplier) {
    return {
      valid: false,
      message: '只能选择同一个供应商子公司的账单',
    }
  }

  return { valid: true, message: '' }
}

// 监听 visible 变化
watch(
  () => props.visible,
  (newVal) => {
    visible.value = newVal
    if (newVal) {
      leftVisible.value = true
      // 初始化选中数据
      tempSelectedList.value = JSON.parse(JSON.stringify(props.selectedBills))
      initialSelection.value = JSON.parse(JSON.stringify(props.selectedBills))
      selectProductList.value = JSON.parse(JSON.stringify(props.selectedBills))

      // 更新查询参数
      queryParams.company_supplier_name = props.supplierName || ''
      queryParams.id = props.paymentOrderId || null // 新增：设置付款单ID

      // 获取列表并设置选中状态
      getProductList().then(() => {
        // 确保表格选中状态与数据一致，只选中已存在的账单
        const $table = productTableRef.value
        if ($table) {
          // 设置标志，防止触发选中事件
          isUpdatingSelection.value = true

          // 清空所有选中状态
          $table.clearCheckboxRow()

          // 使用 nextTick 确保数据更新后再设置选中状态
          nextTick(() => {
            // 只选中已存在的账单
            data.value.forEach((row) => {
              const isSelected = tempSelectedList.value.some((selected) => selected.id === row.id || selected.number === row.number)
              if (isSelected) {
                $table.setCheckboxRow(row, true)
              }
            })

            // 重置标志
            isUpdatingSelection.value = false
          })
        }
      })
    }
  },
  { immediate: true },
)

// 修改 handleSelectProduct 函数
const handleSelectProduct = () => {
  // 在确定时，先触发更新事件同步数据，然后关闭抽屉
  emits('updateProduct', tempSelectedList.value)
  emits('selectProduct', tempSelectedList.value, props.supplierId, props.supplierName)
  visible.value = false
  leftVisible.value = false
}

// 修改 handleClose 方法
const handleClose = () => {
  // 恢复初始选中状态，不影响父组件数据
  tempSelectedList.value = JSON.parse(JSON.stringify(initialSelection.value))
  selectProductList.value = JSON.parse(JSON.stringify(initialSelection.value))

  // 只关闭抽屉，不触发任何事件
  visible.value = false
  leftVisible.value = false
}

// 获取账单列表
const getProductList = async () => {
  const res = await GetBillListResultByPayOrder(queryParams)

  // 直接使用原始数据，不添加checked字段
  data.value = res.data.list
  total.value = res.data.total

  // 设置选中状态
  const $table = productTableRef.value
  if ($table) {
    // 设置标志，防止触发选中事件
    isUpdatingSelection.value = true

    // 先清空所有选中状态
    $table.clearCheckboxRow()

    // 使用 nextTick 确保数据更新后再设置选中状态
    await nextTick()

    // 只选中已存在的账单
    data.value.forEach((row) => {
      const isSelected = tempSelectedList.value.some((selected) => selected.id === row.id || selected.number === row.number)
      if (isSelected) {
        $table.setCheckboxRow(row, true)
      }
    })

    // 重置标志
    isUpdatingSelection.value = false
  }
}

// 分页
const handlePageChange = (page: number, pageSize: number) => {
  queryParams.page = page
  queryParams.pageSize = pageSize
  // 翻页时不清空选中状态，只更新数据
  getProductList()
}

// 查询
const handleSearch = () => {
  // 构建查询参数
  const searchParams = {
    ...queryParams,
    page: 1,
  }

  // 更新查询参数
  Object.assign(queryParams, searchParams)

  // 获取列表
  getProductList()
}

// 重置
const handleReset = () => {
  queryParams.number = ''
  queryParams.company_supplier_name = ''
  queryParams.id = props.paymentOrderId || null // 新增：保持付款单ID
  queryParams.page = 1
  getProductList()
}

// 全选
const handleSelectAll = ({ records }: any) => {
  if (isUpdatingSelection.value) return

  // 校验供应商一致性
  const isValidSelection = validateSupplierConsistency(records)
  if (!isValidSelection.valid) {
    // 显示错误提示
    message.warning(isValidSelection.message)

    // 恢复之前的状态
    const $table = productTableRef.value
    if ($table) {
      isUpdatingSelection.value = true
      $table.clearCheckboxRow()

      // 重新设置之前选中的状态
      tempSelectedList.value.forEach((item) => {
        const row = data.value.find((row) => row.id === item.id || row.number === item.number)
        if (row) {
          $table.setCheckboxRow(row, true)
        }
      })
      isUpdatingSelection.value = false
    }
    return
  }

  // 更新临时选中列表（只更新本地状态，不触发事件）
  tempSelectedList.value = records
  selectProductList.value = records

  // 移除立即触发更新事件，等用户点击确定按钮时再同步
  // emits('updateProduct', records)
}

// 单选
const handleSelectChange = ({ records }: any) => {
  if (isUpdatingSelection.value) return

  // 校验供应商一致性
  const isValidSelection = validateSupplierConsistency(records)
  if (!isValidSelection.valid) {
    // 显示错误提示
    message.warning(isValidSelection.message)

    // 恢复之前的状态
    const $table = productTableRef.value
    if ($table) {
      isUpdatingSelection.value = true
      $table.clearCheckboxRow()

      // 重新设置之前选中的状态
      tempSelectedList.value.forEach((item) => {
        const row = data.value.find((row) => row.id === item.id || row.number === item.number)
        if (row) {
          $table.setCheckboxRow(row, true)
        }
      })
      isUpdatingSelection.value = false
    }
    return
  }

  // 更新临时选中列表（只更新本地状态，不触发事件）
  tempSelectedList.value = records
  selectProductList.value = records

  // 移除立即触发更新事件，等用户点击确定按钮时再同步
  // emits('updateProduct', records)
}

// 取消选中
const handleUnselectRow = (row: any) => {
  const index = tempSelectedList.value.findIndex((item) => item.id === row.id || item.number === row.number)
  if (index > -1) {
    tempSelectedList.value.splice(index, 1)
    selectProductList.value.splice(index, 1)

    // 移除立即触发更新事件，等用户点击确定按钮时再同步
    // emits('updateProduct', tempSelectedList.value)
  }
}

// 清空选中
const cleanCheck = () => {
  tempSelectedList.value = []
  selectProductList.value = []

  // 清空表格选中状态
  const $table = productTableRef.value
  if ($table) {
    $table.clearCheckboxRow()
  }

  // 移除立即触发更新事件，等用户点击确定按钮时再同步
  // emits('updateProduct', [])
}

// 暴露方法
defineExpose({
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  open: (selectedBills: any[], supplierSubsidiaryId: string, supplierSubsidiaryName: string) => {
    visible.value = true
    leftVisible.value = true
    tempSelectedList.value = JSON.parse(JSON.stringify(selectedBills))
    initialSelection.value = JSON.parse(JSON.stringify(selectedBills))
    selectProductList.value = JSON.parse(JSON.stringify(selectedBills))

    // 设置供应商子公司名称和付款单ID
    queryParams.company_supplier_name = supplierSubsidiaryName || ''
    queryParams.id = props.paymentOrderId || null // 新增：设置付款单ID

    getProductList()
  },
  close: () => {
    visible.value = false
    leftVisible.value = false
  },
})
</script>

<style scoped>
.tableBoxwidth {
  width: 100%;
}

.paginationBox {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 16px;
}

.pagination {
  display: flex;
  align-items: center;
}

.totalBox {
  display: flex;
  align-items: center;
}

.totalBox .text {
  margin-right: 8px;
}

.totalBox .total {
  font-weight: bold;
}
</style>
