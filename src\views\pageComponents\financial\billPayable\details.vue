<template>
  <div class="pb-70!">
    <EasyForm v-if="type" ref="formRef" :mode="mode" :formItems="formItems" v-model:form="formData">
      <template #details>
        <a-tabs v-model:activeKey="billTabKey" @change="onChangeBillTab">
          <a-tab-pane v-for="item in billTabs" :key="item.value" :tab="item.label" />
        </a-tabs>
        <div class="flex flex-col">
          <div class="mb-10 ml-auto">
            <a-button v-if="billTabKey === 1 && ['new'].includes(mode)" type="primary" @click="handleAddInventoryOrder">添加入库单</a-button>
          </div>
          <div v-show="billTabKey === 1">
            <vxe-grid ref="gridRef" v-bind="gridOptions">
              <template #number="{ row, column }">
                <a-input-number
                  v-if="inputNumberShow({ row, column })"
                  class="w-full"
                  v-model:value="row[column.field]"
                  :precision="column.params.precision"
                  :min="column.params.min ?? undefined"
                  :disabled="inputNumberDisabled({ row, column })"
                  @change="(value) => inputNumberChange({ row, column, value })"
                />
                <span v-else>{{ (row[column.field] ?? 0).roundNext(column?.params?.precision || 0) }}</span>
              </template>

              <template #readonly="{ row, column }">
                <span>{{ (row[column.field] ?? 0).roundNext(column?.params?.precision || 0) }}</span>
              </template>

              <template #link="{ row }">
                <span class="link" @click="toPurchaseDetail(row)">{{ row.purchase_order_number }}</span>
              </template>

              <!-- 税率列自定义显示（应付明细列表） -->
              <template #tax_rate="{ row }">
                <span v-if="row.order_type === 1">{{ row.tax_rate }}%</span>
                <span v-else-if="row.order_type === 2">-</span>
                <span v-else>{{ row.tax_rate }}%</span>
              </template>

              <template #action="{ row }" v-if="type === 'new'">
                <a-button type="link" @click="deleteItem(row)">删除</a-button>
              </template>
            </vxe-grid>
          </div>
          <div v-if="billTabKey !== 1">
            <DivGrid ref="divGridRef" :rowHeight="30" border></DivGrid>
          </div>
        </div>
      </template>
    </EasyForm>

    <div class="submit-row" v-if="initFlag">
      <template v-if="['edit', 'new'].includes(type)">
        <a-button type="primary" :loading="submitLoading" class="ml mr" @click="handleSubmit(true)">提交审核</a-button>
        <a-button type="default" :loading="submitLoading" class="ml mr" @click="handleSubmit(false)">保存暂不提交</a-button>
      </template>
      <a-button v-if="['detail'].includes(type)" class="ml mr" @click="handleSubmit(false, true)">保存文字修改</a-button>
      <!-- <template v-else-if="type === 'review'">
        <a-button class="ml mr" @click="reviewType(1)">审核通过</a-button>
        <a-button class="ml mr" @click="reviewType(2)">审核拒绝</a-button>
      </template> -->
      <a-button class="ml mr" @click="() => closePage()">关闭</a-button>
      <a-button v-if="shouldShowReviewButton" type="primary" class="ml mr" @click="reviewTimerRef.setModel(true, formData.id, 10)">审核记录</a-button>
    </div>
    <SelectBookingOrder ref="selectBookingOrderRef" :syncConfirm="onSelectBookingOrderConfirm"></SelectBookingOrder>

    <reviewTimer ref="reviewTimerRef"></reviewTimer>
  </div>
</template>

<script lang="ts" setup>
import { message } from 'ant-design-vue'
import dayjs from 'dayjs'

import { usePermission } from '@/hook/usePermission'
import { isEmpty, addFloat } from '@/utils'

import { setFormItems, setPayColumns, prepaidColumns, refundColumns, mouldColumns } from './details.data'

import reviewTimer from '../../purchaseManagement/components/purchaseorderManagementComponents/reviewTimer.vue'

import {
  AddBillPayableOrder,
  // BillPayablePrepaidDeductibleDetails,
  // BillPayableRefundOnlyWithDeductibleDetails,
  // BillPayableMouldDetail,
  EditBillPayableOrder,
  GetBillPayableOrderDetail,
} from '@/servers/BillPayable'
import { GetSupplierLiquidateList } from '@/servers/SupplierLiquidate'

const formRef = ref()
const reviewTimerRef = ref()
const route = useRoute()
const { btnPermission } = usePermission()
const { closePage, setRefreshByPath, pushPage, setPageOptions } = usePage({
  initMethod: () => initMethod(),
  formRef,
})

// 添加安全检查，确保 btnPermission 已初始化
const isPermissionReady = computed(() => {
  return btnPermission?.value && typeof btnPermission.value === 'object'
})

// 判断是否应该显示审核记录按钮
const shouldShowReviewButton = computed(() => {
  // 从供应商清算生成应付单时不显示审核记录按钮
  const {
    query: { ids },
  } = route
  return !ids && btnPermission?.value?.[135006]
})

const formData = ref<Record<string, any>>({
  pay_order_date: dayjs().format('YYYY-MM-DD'),
  audit_status: BillPayableOrderAuditEnum.待提审,
  invoice_type: 2,
})
const formItems = ref<EasyFormItemProps[]>(setFormItems())

// 新增入库单
const selectBookingOrderRef = ref()
const formatUUID = (f) => ({
  ...f,
  // $uuid: `${f.io_warehouse_id}-${f.k3_sku_id}`,
  $uuid: `${f.unique_value}`,
  remark: f.io_remark, // 将 io_remark 赋值给 remark 字段
  purchase_date: f.purchase_time,
  purchase_order_detail_id: f.purchase_order_detail_id,
})
const handleAddInventoryOrder = () => {
  // 获取当前表单中的供应商信息
  const currentSupplierId = formData.value.supplier_id
  const currentCompanySupplierId = formData.value.company_supplier_id

  // 构建选择组件的配置
  const selectConfig = {
    left: ['进出仓单号', '单据类型', '供应商', '供应商子公司', '进出仓日期', '采购单号', '商品编号', '商品名称', '采购入库数量', '应付金额'],
    right: ['序号', '进出仓单号'],
    search: ['进出仓单号', '供应商', '供应商子公司', '采购单号', '商品编码'], // 恢复供应商搜索字段
    api: GetSupplierLiquidateList,
    width: 1500,
    checkMethod: ({ row }) => {
      return row.remaining_quantity !== 0
    },
    keyField: '$uuid',
    dataFormat: (data) => {
      const formattedData = data.map(formatUUID)
      return formattedData
    },
    // 添加供应商筛选参数
    params: {
      supplier_id: currentSupplierId || currentCompanySupplierId,
    },
    // 添加供应商信息回显
    supplierInfo:
      currentSupplierId || currentCompanySupplierId
        ? {
            supplier_id: currentSupplierId,
            supplier_name: formData.value.supplier_name,
            // 供应商子公司不回显
          }
        : null,
  }

  selectBookingOrderRef.value.open(selectConfig, gridRef.value.getTableData().fullData)
}
// 新增入库单回调(异步)
const onSelectBookingOrderConfirm = async (data) => {
  const [first, ...other] = data || []
  if (!first) return
  const propData = {
    supplier_id: first.supplier_id,
    supplier_name: first.supplier_name,
    company_supplier_id: first.company_supplier_id,
    company_supplier_name: first.company_supplier_name,
  }
  // 若包含了多个入库单对应的供应商子公司不一样，只显示主公司名；若一致，则主公司子公司均带出
  if (other.length > 0) {
    const supplierEveryValue = other.every((item) => item.supplier_id === first.supplier_id)
    if (!supplierEveryValue) {
      propData.supplier_id = undefined
      propData.supplier_name = undefined
    }
    const companySupplierEveryValue = other.every((item) => item.company_supplier_id === first.company_supplier_id)
    if (!companySupplierEveryValue) {
      propData.company_supplier_id = undefined
      propData.company_supplier_name = undefined
    }
  }
  if (!propData.company_supplier_id && !propData.supplier_id) {
    message.warn('供应商子公司不一致')
    return Promise.reject('添加失败')
  }
  await formRef.value.changeValue(propData)

  const _data = data.map((row) => {
    const found = gridRef.value.getTableData().fullData.find((f) => f.$uuid === row.$uuid)
    return {
      ...row,
      id: undefined,
      remark: row.io_remark, // 将 io_remark 赋值给 remark 字段
      purchase_date: row.purchase_time,
      purchase_order_detail_id: row.purchase_order_detail_id,
      ...(found || {}),
    }
  })

  gridRef.value.reloadData(_data)

  // await getOtherPriceDetail(_data)

  await calcSumPrice()

  return Promise.resolve(true)
}

// 获取其他详情(预付可抵明细...)
// const getOtherPriceDetail = async (data) => {
//   const purchase_order_ids = [...new Set(data.map((f) => f.purchase_order_id))]
//   const purchase_order_detail_ids = [...new Set(data.map((f) => f.purchase_order_detail_id))]
//   const company_supplier_ids = [...new Set(data.map((f) => f.company_supplier_id))]
//   try {
//     // 获取预付可抵明细
//     const res1 = await BillPayablePrepaidDeductibleDetails({ purchase_order_ids })
//     billTabs.value[1].data = res1.data.bill_payable_prepaid_deductible_details

//     // 获取仅退款可抵明细
//     const res2 = await BillPayableRefundOnlyWithDeductibleDetails({ company_supplier_ids })
//     billTabs.value[2].data = res2.data.bill_payable_refund_only_fees_details

//     // 开模明细
//     const res3 = await BillPayableMouldDetail({ purchase_order_detail_ids, order_status: [...new Set(data.map((f) => f.order_type))] })
//     billTabs.value[3].data = res3.data.bill_payable_mould_details
//   } catch (e) {
//     console.error('error: ', e)
//   }
// }

// 去采购单详情
const toPurchaseDetail = (row) => {
  pushPage(`/purchaseOrderLook/${row.purchase_order_id}`, { source: true })
}

const deleteItem = async (row) => {
  gridRef.value.remove(row)
  await nextTick()
  // getOtherPriceDetail(gridRef.value.getTableData().fullData)
}

// tab明细
const billTabKey = ref(1)
const divGridRef = ref()
const billTabs = shallowRef(
  [
    { label: '应付明细', value: 1, columns: setPayColumns() },
    { label: '预付可抵明细', value: 2, columns: prepaidColumns, data: [], alias: 'bill_payable_pre_detail' },
    { label: '仅退款可抵明细', value: 3, columns: refundColumns, data: [], alias: 'bill_payable_refund_detail' },
    { label: '开模明细', value: 4, columns: mouldColumns, data: [], alias: 'bill_payable_mould_detail' },
  ].slice(0, 1),
)
const onChangeBillTab = async (key) => {
  await nextTick()
  const target = billTabs.value.find((f) => f.value === key)
  if (key !== 1) {
    divGridRef.value.updateGrid({
      columns: target?.columns.map((f) => ({ ...f, width: f.width || 140 })),
      data: target?.data || [],
    })
  }
}

// 应付明细
const gridRef = ref()
const sumData = ref<Record<string, number>>({})
const footerMethod = ({ columns, data }) => {
  const footerData = [
    columns.map((column, _columnIndex) => {
      if (_columnIndex === 0) return '合计'
      if (['应付金额', '赠品应付金额'].includes(column.title)) {
        // 使用工具函数解决浮点数精度问题
        // 先对每个值进行精度处理，再进行合计
        const sum = data.reduce((acc, cur) => {
          const value = Number(cur[column.field] ?? 0)
          // 先对单个值进行精度处理，再进行累加
          const roundedValue = Number(value.roundNext(column?.params?.precision ?? 2))
          return addFloat(acc, roundedValue)
        }, 0)

        sumData.value[column.field] = sum
        return sum.roundNext(column?.params?.precision ?? 0)
      }
      return null
    }),
  ]
  calcSumPrice()
  return footerData
}
const gridOptions = ref<VxeGridProps>()

// 总额计算
const calcSumPrice = async () => {
  await nextTick()
  // 应付总额
  if (formData.value.source_type > 1) return

  // 使用工具函数解决浮点数精度问题
  const invAmount = Number(sumData.value.inv_amount ?? 0)
  const giftAmountDue = Number(sumData.value.gift_amount_due ?? 0)
  const totalAmount = addFloat(invAmount, giftAmountDue)

  await formRef.value.changeValue({
    inv_amount: totalAmount,
  })
}

// 数字输入选择器相关
const inputNumberShow = ({ column, row }) => {
  const isBack = row.order_type === SupplierLiquidateOrderTypeEnum.采购退库

  // 特殊处理实际单价：根据权限控制
  if (column.title === '实际单价') {
    // 检查是否有编辑实际单价的权限 (135010)

    if (!isPermissionReady.value || !btnPermission?.value?.[135010]) {
      return false // 无权限时不显示输入框
    }
    // 有权限时，允许在新增和编辑模式下编辑
    return ['new', 'edit'].includes(type.value)
  }

  // 赠品应付金额字段不可编辑，只读显示
  if (column.title === '赠品应付金额') {
    return false
  }

  // 其他字段的处理逻辑
  if ((['赠品数量', '赠品单价'].includes(column.title) && isBack) || type.value !== 'new') return false
  return true
}
const inputNumberDisabled = ({ column, row }) => {
  const isBack = row.order_type === SupplierLiquidateOrderTypeEnum.采购退库
  if (isBack && ['赠品数量', '赠品单价', '赠品应付金额'].includes(column.title)) {
    return true
  }
  return false
}

// 修改价格
const inputNumberChange = async (params) => {
  console.log('inputNumberChange')
  const { row, column } = params
  if (['赠品数量', '赠品单价'].includes(column.title)) {
    // 使用工具函数解决浮点数精度问题
    const giftQuantity = Number(row.gift_purchase_quantity ?? 0)
    const giftUnitPrice = Number(row.gift_purchase_unit_price ?? 0)
    row.gift_amount_due = Number((giftUnitPrice * giftQuantity).roundNext(2))
  }
  if (['实际单价'].includes(column.title)) {
    // 使用工具函数解决浮点数精度问题
    const actualUnitPrice = Number(row[column.field] ?? 0)
    const purchaseInboundQuantity = Number(row.purchase_inbound_quantity ?? 0)
    row.inv_amount = Number((actualUnitPrice * purchaseInboundQuantity).roundNext(2))
  }
  gridRef.value.updateFooter()
}

const submitLoading = ref(false)
// const modal: any = inject('modal') // 不再使用 modal，注释掉
const handleSubmit = async (is_pass, isText?: boolean) => {
  const formState = await formRef.value.validate()
  const params = {
    id: formData.value.id,
    is_pass,
    ...(isText ? formData.value : {}),
    ...formState,
    attachments: (formState.attachments || []).map((f) => f.id),
  }

  params.bill_payable_order_detail = gridRef.value.getTableData().fullData

  params.bill_payable_order_detail.forEach((f, index) => {
    const gift = [f.gift_purchase_quantity, f.gift_purchase_unit_price, f.gift_amount_due]
    const pass = gift.every((f) => isEmpty(f)) || gift.every((f) => !isEmpty(f))
    if (!pass) {
      message.warn(`应付明细 序号 ${index + 1} 赠品信息不完整`)
      throw new Error('请填写赠品信息')
    }
  })

  // 检查税率混合情况
  // const tax_rates = params.bill_payable_order_detail.map((f) => f.tax_rate)

  // 判断是否混合了含税和不含税的单据
  // 0税率或无税率视为不含税，其他值视为含税
  // const hasTaxRates = tax_rates.some((rate) => rate && rate > 0) // 有含税的单据
  // const hasNoTaxRates = tax_rates.some((rate) => !rate || rate === 0) // 有不含税的单据

  const fn = async () => {
    const api = formData.value.id ? EditBillPayableOrder : AddBillPayableOrder
    const res = await api(params, { loading: submitLoading })
    if (res.success) {
      message.success('提交成功')
      setRefreshByPath('/billPayable')
      closePage()
    }
  }

  // 如果同时包含含税和不含税的单据，显示确认对话框
  // if (hasTaxRates && hasNoTaxRates) {
  //   modal.open({
  //     title: '本应付单中混合 含税与不含税的单，确定继续提交吗？',
  //     async onOk() {
  //       await fn() {
  //         await fn()
  //       },
  //     },
  //   })
  // } else {
  //     await fn()
  //   }
  // }

  // 直接执行，不再显示确认弹窗
  await fn()
}

const type = ref('new')
const mode = computed(() => {
  return ['edit', 'new'].includes(type.value) ? 'new' : 'detail'
})

const initFlag = ref(false)
const initMethod = async () => {
  const {
    params: { id },
    query: { ids },
    meta: { code },
  } = route

  type.value = code === 'billPayableView' ? 'detail' : code === 'billPayableEdit' ? 'edit' : 'new'

  // 设置表格
  gridOptions.value = {
    columns: (billTabs.value[0]?.columns as any[]).filter((f) => !(f.field === 'action' && mode.value !== 'new')),
    size: 'mini',
    data: [],
    resizable: true,
    minHeight: 40,
    maxHeight: 600,
    border: true,
    showFooter: true,
    cellStyle: ({ column }) => {
      if (['应付金额', '赠品应付金额'].includes(column.title)) {
        return {
          color: '#009F2B',
        }
      }
      return null
    },
    footerMethod,
    footerRowStyle: () => {
      return {
        background: 'rgba(232, 234, 236, 0.3)',
      }
    },
    footerCellStyle: ({ column }) => {
      if (['应付金额', '赠品应付金额'].includes(column.title)) {
        return {
          color: '#009F2B',
        }
      }
      return null
    },
  }

  await nextTick()

  if (id) {
    const res = await GetBillPayableOrderDetail({ id })
    setPageOptions({ name: `${type.value == 'edit' ? '编辑' : '查看'}应付单-${res.data.number}` })
    console.log('res', res.data)
    formData.value.id = id
    formData.value.source_type = res.data.source_type
    await formRef.value.changeValue({
      ...res.data,
      attachments: res.data.files,
    })

    if (type.value === 'detail') {
      for (const key of ['invoice_type', 'invoice_date', 'invoice_number', 'invoice_subject', 'amount_invoiced', 'attachments']) {
        formRef.value.changeItem(key, { isDetail: false })
      }
      formData.value.is_pass = res.data.is_pass
    }
    const tableData = res.data.bill_payable_order_detail.map((f) => ({
      ...formatUUID(f),
      remark: f.io_remark || f.remark, // 优先使用 io_remark，兼容 remark
      purchase_date: f.purchase_time || f.purchase_date,
      // 用于计算可分配剩余数量
      // $current_write_offs: f.current_write_offs,
      // $remaining_quantity: f.remaining_quantity,
    }))
    gridRef.value.reloadData(tableData)
    for (const tab of billTabs.value) {
      if (tab.alias) {
        tab.data = res.data[tab.alias]
      }
    }
  }

  if (ids) {
    setPageOptions({ name: '生成应付单' })
    try {
      const { data } = await GetSupplierLiquidateList({ ids: (ids as string).split(',') })
      // 修复数据结构：从 data.datas.list.list 获取数据，而不是 data.list.list
      const listData = data?.datas?.list || data?.list || []

      if (listData && listData.length > 0) {
        // 等待数据加载完成
        await onSelectBookingOrderConfirm(listData.map(formatUUID))
      } else {
        message.warning('没有获取到数据，请检查参数')
      }
    } catch (_error) {
      message.error('加载数据失败，请重试')
    }
  } else {
    // 如果没有ids参数，根据type设置对应的页面标题
    if (type.value === 'new') {
      setPageOptions({ name: '新增应付单' })
    }
  }

  initFlag.value = true
}
</script>

<style lang="scss" scoped>
//
</style>
