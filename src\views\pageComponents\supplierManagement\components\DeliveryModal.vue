<template>
  <a-modal v-model:open="visible" :title="title" :width="px2(600)" @ok="handleOk">
    <a-textarea v-model:value="autoAddress" placeholder="粘贴文本到此处，将自动识别地址" :rows="4" />
    <a-flex justify="end" class="my-8">
      <a-button type="primary" @click="handleAutoAddress">智能识别</a-button>
    </a-flex>
    <a-space direction="vertical" :size="16" class="w-full mt-2">
      <a-row :gutter="16">
        <a-col :span="8">
          <!-- 省/直辖市/自治区 -->
          <a-select show-search class="w-full" v-model:value="form.province" placeholder="省/直辖市/自治区" @change="getCityList" :options="provinceList" :filter-option="filterOption"></a-select>
        </a-col>
        <a-col :span="8">
          <!-- 市 -->
          <a-select show-search class="w-full" v-model:value="form.city" placeholder="市" @change="getCountyList" :options="cityList"></a-select>
        </a-col>
        <a-col :span="8">
          <!-- 区/县 -->
          <a-select show-search class="w-full" v-model:value="form.area" placeholder="区/县" :options="countyList"></a-select>
        </a-col>
      </a-row>
      <a-textarea v-model:value="form.address" placeholder="请输入详细地址" :rows="4" />
    </a-space>
  </a-modal>
</template>

<script lang="ts" setup>
import { px2 } from '@/utils'
import { areaCity } from '@/utils/address'
import { message } from 'ant-design-vue'
import AddressParse from 'address-parse'

const autoAddress = ref('')

const visible = ref(false)
const form = ref({
  province: null,
  city: null,
  area: null,
  address: null,
})
const title = ref('')
const open = (data, newTitle) => {
  autoAddress.value = ''
  visible.value = true
  form.value = {
    province: data.province,
    city: data.city,
    area: data.area,
    address: data.shipments_address || data.address,
  }
  title.value = newTitle

  provinceList.value = areaCity.map((item) => ({ label: item.name, value: item.name }))

  if (data.province) {
    cityList.value = areaCity.find((item) => item.name === form.value.province)?.children.map((item) => ({ label: item.name, value: item.name })) || []
  }
  if (data.city) {
    countyList.value =
      areaCity
        .find((item) => item.name === form.value.province)
        ?.children.find((item) => item.name === form.value.city)
        ?.children.map((item) => ({ label: item.name, value: item.name })) || []
  }
}

const emit = defineEmits(['confirm'])
const handleOk = () => {
  if (!form.value.province || !form.value.city || !form.value.area || !form.value.address) {
    message.warning('请填写完整信息')
    return
  }

  visible.value = false
  emit('confirm', form.value)
}

const provinceList = ref()
const cityList = ref([] as any[])
const countyList = ref([] as any[])
const getCityList = () => {
  form.value.city = null
  form.value.area = null
  cityList.value = areaCity.find((item) => item.name === form.value.province)?.children.map((item) => ({ label: item.name, value: item.name })) || []
  countyList.value = []
}
const getCountyList = () => {
  countyList.value =
    areaCity
      .find((item) => item.name === form.value.province)
      ?.children.find((item) => item.name === form.value.city)
      ?.children.map((item) => ({ label: item.name, value: item.name })) || []
  form.value.area = null
}

const filterOption = (input, option) => {
  return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
}

const handleAutoAddress = () => {
  if (!autoAddress.value.trim()?.length) {
    message.error('请输入地址')
    return
  }
  const list = AddressParse.parse(autoAddress.value, true)
  if (!list.length) {
    message.error('地址有误，识别失败')
    return
  }

  const { province, city, area, details } = list[0]
  console.log(list[0])
  if (province) form.value.province = province
  if (city) form.value.city = city
  if (area) form.value.area = area
  if (details) form.value.address = details

  if (province) {
    cityList.value = areaCity.find((item) => item.name === form.value.province)?.children.map((item) => ({ label: item.name, value: item.name })) || []
  }
  if (city) {
    countyList.value =
      areaCity
        .find((item) => item.name === form.value.province)
        ?.children.find((item) => item.name === form.value.city)
        ?.children.map((item) => ({ label: item.name, value: item.name })) || []
  }
}

defineExpose({
  open,
})
</script>

<style lang="scss" scoped>
//
</style>
