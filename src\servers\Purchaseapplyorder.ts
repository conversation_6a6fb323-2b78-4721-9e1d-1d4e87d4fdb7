import { request } from './request'

// 获取采购申请列表
export const GetPurchaseApplyOrderList = (data: any) => {
  return request({ url: '/api/PurchaseApplyOrder/GetPurchaseApplyOrderList', data })
}

// 添加采购申请表
export const AddPurchaseApplyOrder = (data: any, config?) => {
  return request({ url: '/api/PurchaseApplyOrder/AddPurchaseApplyOrder', data, config })
}

// 修改采购申请表
export const UpdatePurchaseApplyOrder = (data: any, config?) => {
  return request({ url: '/api/PurchaseApplyOrder/UpdatePurchaseApplyOrder', data, config })
}

// 审核采购申请表
export const Audit = (data: any) => {
  return request({ url: '/api/PurchaseApplyOrder/Audit', data })
}

// 批量审核通过 / 拒绝
export const BatchAuditPurchaseApplyOrder = (data: any) => {
  return request({ url: '/api/PurchaseApplyOrder/BatchAuditPurchaseApplyOrder', data })
}

// 获取采购申请表
export const GetPurchaseApplyOrder = (data: any) => {
  return request({ url: '/api/PurchaseApplyOrder/GetPurchaseApplyOrder', data }, 'GET')
}

// 获取收料仓库
export const GetWarehouses = () => {
  return request({ url: '/api/PurchaseApplyOrder/GetWarehouses' }, 'GET')
}

// 获取查询申请人下拉列表
export const GetApplyers = () => {
  return request({ url: '/api/PurchaseApplyOrder/GetApplyers' }, 'GET')
}

// 获取部门
export const GetDeptByUser = (id: any) => {
  return request({ url: `/api/UserModule/GetSubDepartmentsByUserId?id=${id}` }, 'GET')
}
// 返回当前用户及下属团队成员
export const GetUsersAndChilrd = (id) => {
  return request({ url: `/api/UserModule/GetUsersAndChilrd?id=${id}` }, 'GET')
}

// 获取当前用户信息
export const GetUserInfo = () => {
  return request({ url: '/api/User/GetUserInfo' }, 'GET')
}

export const OpenOrClosePurchaseApplyOrder = (data: any) => {
  return request({ url: '/api/PurchaseApplyOrder/OpenOrClosePurchaseApplyOrder', data })
}

// 获取采购方式计算数
export const GetPurchaseMethodCount = (data: any) => {
  return request({ url: '/api/PurchaseApplyOrder/GetPurchaseMethodCount', data })
}

// 获取采购预处理信息
export const GetPurchaseOrderPreProcessingList = (data: any) => {
  return request({ url: '/api/PurchaseApplyOrder/GetPurchaseOrderPreProcessingList', data })
}

// 批量修改备注
export const BatchUpdateRemark = (data: any) => {
  return request({ url: '/api/PurchaseApplyOrder/BatchUpdateRemark', data })
}

// 批量添加采购单
export const BatchAddPurchaseOrder = (data: any) => {
  return request({ url: '/api/PurchaseApplyOrder/BatchAddPurchaseOrder', data })
}
