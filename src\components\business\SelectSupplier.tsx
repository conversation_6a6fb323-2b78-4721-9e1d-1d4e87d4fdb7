import { Form, Checkbox, Input, Modal, Pagination, TabPane, Tabs, Radio, Select, Tag, FormItemRest, Button } from 'ant-design-vue'
import type { CheckboxValueType } from 'ant-design-vue/es/checkbox/interface'
import type { SelectValue } from 'ant-design-vue/es/select'
import { UnorderedListOutlined } from '@ant-design/icons-vue'

import { GetSupplierOptions } from '@/servers/PaymentApply'

export default defineComponent({
  name: 'SelectSupplier',
  emits: ['update:value', 'update:label', 'change'],
  props: {
    pageSize: {
      type: Number,
      default: 57,
    },
    mode: {
      type: String as PropType<'single' | 'multiple'>,
      default: 'multiple',
    },
    title: {
      type: String,
      default: '供应商',
    },
    value: {
      type: [Array, String, Number] as PropType<SelectValue>,
    },
    api: {
      type: Function,
      default: GetSupplierOptions,
    },
    apiParams: {
      type: Object as PropType<Record<string, any>>,
      default: () => ({}),
    },
    label: {
      type: String,
    },
    labelInValue: {
      type: Boolean,
      default: true,
    },
    // 隐藏select
    hide: {
      type: Boolean,
    },
    type: {
      type: Number,
      default: undefined,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  setup(props, { emit }) {
    const confirmLoading = ref(false)
    const searchText = ref('')
    const activeKey = ref(1)
    const isCheckAll = ref(false)
    const page = ref(1)
    const total = ref(0)

    const formItemContext = Form.useInjectFormItemContext()

    const openFlag = ref(false)
    const openKey = ref('default')

    const mergeConfig = ref<Record<string, any>>({})
    const selectedState = ref<Record<string, Record<string, string>>>({})

    // 防抖函数
    let searchTimer: number | null = null
    const debouncedSearch = (callback: () => void) => {
      if (searchTimer) {
        clearTimeout(searchTimer)
      }
      searchTimer = setTimeout(callback, 300)
    }

    // 监听搜索文本变化，当清空时自动搜索
    watch(searchText, (newVal) => {
      if (newVal === '' && openFlag.value) {
        debouncedSearch(() => getList())
      }
    })

    const open = (config?: Record<string, any>, selectRecords?: Record<string, any>[]) => {
      const { key, ...defaultConfig } = config || {}
      searchText.value = ''
      mergeConfig.value = {
        callback: () => {},
        ...props,
        ...defaultConfig,
      }
      openKey.value = key || 'default'
      selectedState.value[openKey.value] = {}
      if (selectRecords?.length) {
        for (const { label, value } of selectRecords) {
          selectedState.value[openKey.value][value] = label
        }
      } else {
        if (mergeConfig.value.mode === 'single' && !mergeConfig.value.labelInValue) {
          selectedState.value[openKey.value] = {
            [mergeConfig.value.value as string]: mergeConfig.value.label || '',
          }
        } else {
          const _value = (mergeConfig.value?.value || []) as string[]
          selectedState.value[openKey.value] = _value.reduce((acc, cur) => {
            const { label, value } = JSON.parse(cur || '{}')
            return {
              ...acc,
              [value]: label,
            }
          }, {})
        }
      }

      getList()
      openFlag.value = true
    }
    const getList = async (pageNum = 1) => {
      const { api, pageSize, apiParams, type } = mergeConfig.value
      const res = await api({
        keyword: searchText.value,
        name: searchText.value,
        pageSize,
        page: pageNum,
        sortField: '',
        sortType: '',
        type,
        ...(apiParams || {}),
      })
      const _selectedState: any[] = []
      list.value = (res.data?.list || res.data || []).map((f) => {
        const label = f.label || f.value
        const value = `${f.key || f.value || ''}`
        if (selectedState.value[openKey.value][value]) {
          _selectedState.push(value)
        }
        return {
          label,
          value,
        }
      })
      selectedList.value = mergeConfig.value.mode === 'single' ? _selectedState?.[0] : _selectedState
      page.value = pageNum
      total.value = res?.data?.total
    }

    const tabList = computed(() => [
      { label: '全部', value: 1 },
      { label: `已选项（${Object.keys(selectedState.value[openKey.value]).length || 0}）`, value: 2 },
    ])

    const list = ref<Record<string, any>[]>([])
    const selectedList = ref<CheckboxValueType[]>([])
    const onCheckAllChange = (e: any) => {
      const isChecked = e.target.checked
      for (const item of list.value) {
        if (isChecked) {
          selectedState.value[openKey.value][item.value] = item.label
        } else {
          delete selectedState.value[openKey.value][item.value]
        }
      }
      selectedList.value = e.target.checked ? list.value.map((item) => item.value) : []
    }
    const indeterminate = computed(() => list.value.some((item) => selectedList.value.find((v) => v === item.value) && !isCheckAll.value))

    const renderListVN = () => {
      const listProps = {
        class: ['mt-5', 'w-full', 'overflow-y-auto'],
        value: selectedList.value,
        'onUpdate:value': (value) => {
          selectedList.value = value
          for (const item of list.value) {
            const flag = [value].flat(1).includes(item.value)
            if (flag) {
              if (mergeConfig.value.mode === 'single') {
                selectedState.value[openKey.value] = {
                  [item.value]: item.label,
                }
                return
              }
              selectedState.value[openKey.value][item.value] = item.label
            } else {
              delete selectedState.value[openKey.value][item.value]
            }
          }
        },
      }
      const defaultRender = (type) => {
        return {
          default: () =>
            list.value.map((f) => {
              const comProps = {
                value: f.value,
                style: {
                  width: `calc(33.3% - 8px)`,
                  marginBottom: '8px',
                },
              }
              return h(type, comProps, {
                default: () => h('span', { class: ['overflow-hidden', 'whitespace-pre-line'] }, f.label),
              })
            }),
        }
      }
      return [
        mergeConfig.value.mode === 'multiple' &&
          h(
            Checkbox,
            {
              class: ['mb-10'],
              checked: isCheckAll.value,
              indeterminate: indeterminate.value,
              'onUpdate:checked': (checked) => {
                isCheckAll.value = checked
              },
              onChange: onCheckAllChange,
            },
            { default: () => '全选' },
          ),
        mergeConfig.value.mode === 'multiple' ? h(Checkbox.Group, listProps, defaultRender(Checkbox)) : h(Radio.Group, listProps, defaultRender(Radio)),
        h('div', { class: ['flex-1'] }),
        total.value &&
          h(Pagination, {
            class: 'mt-10',
            size: 'small',
            current: page.value,
            total: total.value,
            showQuickJumper: true,
            showSizeChanger: false,
            pageSize: mergeConfig.value.pageSize,
            onChange: getList,
            'onUpdate:current': (val) => {
              page.value = val
            },
            showTotal: (total: number) => `共 ${total} 条`,
          }),
      ]
    }
    const renderSelectedVN = () => {
      return h(
        Checkbox.Group,
        {
          class: ['mt-5', 'w-full', 'overflow-y-auto'],
          value: Object.keys(selectedState.value[openKey.value]),
          onChange: (value) => {
            for (const key of Object.keys(selectedState.value[openKey.value])) {
              const found = value.find((v) => v === key)
              if (!found) {
                const index = selectedList.value.findIndex((v) => v === key)
                if (~index) {
                  selectedList.value.splice(index, 1)
                }
                delete selectedState.value[openKey.value][key]
              }
            }
          },
          'onUpdate:value': (value) => {
            for (const key of Object.keys(selectedState.value[openKey.value])) {
              const found = value.find((v) => v === key)
              if (!found) {
                const index = selectedList.value.findIndex((v) => v === key)
                if (~index) {
                  selectedList.value.splice(index, 1)
                }
                delete selectedState.value[openKey.value][key]
              }
            }
          },
        },
        {
          default: () =>
            Object.keys(selectedState.value[openKey.value]).map((value) => {
              const setProps = {
                value,
                style: {
                  width: `calc(33.3% - 8px)`,
                  marginBottom: '8px',
                },
                class: ['overflow-hidden', 'whitespace-pre-line'],
              }
              return h(Checkbox, setProps, {
                default: () => selectedState.value[openKey.value][value],
              })
            }),
        },
      )
    }

    const renderVN = () => {
      const modalProps: ModalProps = {
        open: openFlag.value,
        confirmLoading: confirmLoading.value,
        title: `选择${(mergeConfig.value.title || '').replace(/^(请?)选择/, '')}`,
        width: 900,
        onCancel: () => {
          openFlag.value = false
        },
        onOk: () => {
          confirmLoading.value = true

          setTimeout(() => {
            const _data = selectedState.value[openKey.value]
            if (_data && formItemContext) {
              formItemContext.clearValidate()
            }
            if (mergeConfig.value.mode === 'single' && !mergeConfig.value.labelInValue) {
              emit('update:value', Object.keys(_data)?.[0])
              emit('update:label', Object.values(_data)?.[0])
            } else {
              emit(
                'update:value',
                Object.entries(_data).map(([value, label]) => JSON.stringify({ label, value })),
              )
            }
            emit('change')
            confirmLoading.value = false
            openFlag.value = false
            mergeConfig.value.callback && mergeConfig.value.callback(Object.entries(_data).map(([value, label]) => ({ label, value })))
          }, 100)
        },
        okText: '确定',
        cancelText: '取消',
        bodyStyle: {
          height: `${window.innerHeight - 200}px`,
          display: 'flex',
          flexDirection: 'column',
        },
      }
      const options: any = []
      if (props.mode === 'single') {
        let label = props.label
        if (props.labelInValue && props.value) {
          label = JSON.parse(`${props.value}`)?.label
        }
        options.push({
          label,
          value: `${props.value}`,
        })
      }
      return [
        !props.hide &&
          h(
            Input.Group,
            {
              compact: true,
              class: 'w-full flex!',
              onClick: () => {
                !props.disabled && open()
              },
            },
            {
              default: () => [
                h(
                  Select,
                  {
                    class: 'flex-1',
                    value: props.value ? (props.mode !== 'multiple' ? `${props.value}` : props.value) : undefined,
                    placeholder: (props.title || '').replace(/^选择/, ''),
                    mode: props.mode !== 'multiple' ? undefined : props.mode,
                    open: false,
                    maxTagCount: 'responsive',
                    maxTagPlaceholder: (value) => `+ ${value.length}`,
                    allowClear: true,
                    'onUpdate:value': (val) => {
                      emit('update:value', val)
                    },
                    options,
                    disabled: props.disabled,
                    showSearch: false,
                    showArrow: false,
                  },
                  {
                    tagRender: ({ value }) => {
                      const obj = JSON.parse(value || '{}')
                      return h(Tag, {}, { default: () => obj.label })
                    },
                  },
                ),
                h(Button, { icon: h(UnorderedListOutlined), class: 'w-30!' }),
              ],
            },
          ),
        h(
          Modal,
          { ...modalProps, style: { top: '50px' } },
          {
            default: () => [
              // 搜索
              h(
                FormItemRest,
                {},
                {
                  default: () => {
                    return h(Input.Search, {
                      placeholder: `请输入${(mergeConfig.value.title || '').replace(/^(请?)选择/, '')}`,
                      status: undefined,
                      class: ['mt-10'],
                      value: searchText.value,
                      'onUpdate:value': (val) => {
                        searchText.value = val
                      },
                      onSearch: () => debouncedSearch(() => getList()),
                      onPressEnter: () => debouncedSearch(() => getList()),
                    })
                  },
                },
              ),
              // 标签页
              h(
                Tabs,
                {
                  activeKey: activeKey.value,
                  'onUpdate:activeKey': (val) => {
                    activeKey.value = val as number
                  },
                },
                {
                  default: () => tabList.value.slice(0, mergeConfig.value.mode === 'single' ? 1 : 2).map((f) => h(TabPane, { key: f.value, tab: f.label })),
                },
              ),
              // 列表
              activeKey.value === 1 && renderListVN(),
              // 已选
              activeKey.value === 2 && renderSelectedVN(),
            ],
          },
        ),
      ]
    }

    return {
      renderVN,
      open,
    }
  },

  render() {
    return this.renderVN()
  },
})
