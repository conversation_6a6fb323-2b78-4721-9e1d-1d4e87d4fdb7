<template>
  <a-drawer :footer="false" v-model:open="drawerVisible" width="35vw" title="部门管理" placement="right" :maskClosable="false" :footer-style="{ textAlign: 'left' }">
    <div class="detailBox">
      <LoadingOutlined v-show="detailloading" class="loadingIcon" />
      <a-button class="detailBoxIncreaseBtn" v-if="btnPermission[22103]" @click="increase" type="primary" size="small">新建部门</a-button>
      <a-tree
        v-if="department.length != 0"
        default-expand-all
        class="departmentTree"
        :fieldNames="fieldNames"
        :draggable="btnPermission.isShowBtn22102"
        block-node
        :tree-data="department"
        @drop="onDrop"
      >
        <template #title="{ name, data }">
          <div class="departmentTitle">
            <span>{{ name }}</span>
            <div class="departmentTitleBtns">
              <div id="departmentDrawerEdit" v-if="btnPermission[22104]" @click.stop="edit(data)" class="departmentTitleBtn"><edit-outlined /></div>
              <div id="departmentDrawerDel" v-if="btnPermission[22105]" @click.stop="beforeDel(data)" class="departmentTitleBtn"><delete-outlined /></div>
            </div>
          </div>
        </template>
      </a-tree>
    </div>
  </a-drawer>
  <a-drawer
    v-model:open="formDrawerVisible"
    @afterOpenChange="formRef?.clearValidate()"
    width="30vw"
    :title="drawerStatus === 1 ? '新建部门' : '编辑部门'"
    placement="right"
    :maskClosable="false"
    :footer-style="{ textAlign: 'left' }"
  >
    <a-form ref="formRef" :model="formData">
      <a-form-item label="部门名称" name="name" :rules="[{ required: true }, { max: 50, message: '输入内容不可超过50字符' }]">
        <a-input id="name" v-model:value="formData.name" placeholder="请输入部门名称" class="w250" />
      </a-form-item>
      <a-form-item label="上级部门" name="parent_id">
        <a-tree-select
          id="parent_id"
          v-model:value="formData.parent_id"
          style="width: 100%"
          placeholder="请选择上级部门"
          allow-clear
          :tree-data="filterToThreeLevels(department)"
          :fieldNames="fieldNames"
          :maxTagCount="1"
          :listHeight="400"
          :dropdownMatchSelectWidth="250"
        />
      </a-form-item>
    </a-form>
    <template #footer>
      <a-button id="departmentDrawerComfirm" v-if="btnPermission[22104]" style="margin-right: 10px" type="primary" @click="drawerComfirm">确认</a-button>
      <a-button id="departmentDrawerCancel" @click="formDrawerVisible = false">取消</a-button>
    </template>
  </a-drawer>
  <a-modal v-model:open="delVisible" width="500px" title="删除部门" ok-text="删除" cancel-text="取消" @ok="del" ok-type="danger">
    <div>此操作不可恢复，确定要删除该部门及其下级部门吗？</div>
  </a-modal>
</template>

<script lang="ts" setup>
import { DeleteOutlined, EditOutlined, LoadingOutlined } from '@ant-design/icons-vue'
import { message, type FormInstance } from 'ant-design-vue'

import { GetSupplierDepartmentList, CreateOrUpdateSupplierDepartment, DeleteSupplierDepartment } from '@/servers/SupplierOrganizational'

const drawerVisible = ref(false)
const targetId = ref(null)
const targetDepartment = ref<any>(null)
const department = ref<any>(null)
const fieldNames = {
  title: 'name',
  key: 'id',
  children: 'children',
  label: 'name',
  value: 'id',
}
const delVisible = ref(false)
const formDrawerVisible = ref(false)
const formData = ref<any>(null)
const btnPermission = inject('btnPermission') as any

const detailloading = ref(false)
const formRef = ref<FormInstance | null>(null)
const drawerStatus = ref(1)

const filterToThreeLevels = (departments, level = 0) => {
  if (level >= 3) return []
  return departments.map((department) => {
    const { name, id, children } = department
    const filteredChildren = filterToThreeLevels(children || [], level + 1)
    return {
      name,
      id,
      children: filteredChildren.length > 0 ? filteredChildren : undefined,
    }
  })
}

const loop = (data, key, callback) => {
  data.forEach((item, index) => {
    if (item.id === key) {
      return callback(item, index, data)
    }
    if (item.children) {
      return loop(item.children, key, callback)
    }
  })
}

const findNodeById = (data, id) => {
  for (let index = 0; index < data.length; index++) {
    const item = data[index]
    if (item.id === id) {
      return { children: item, index }
    }
    if (item.children) {
      const found = findNodeById(item.children, id)
      if (found) return found
    }
  }
  return null
}

const checkDepth = (data, maxDepth = 4) => {
  const getMaxDepth = (nodes, currentDepth = 1) => {
    let max = currentDepth
    for (const item of nodes) {
      if (item.children && item.children.length > 0) {
        max = Math.max(max, getMaxDepth(item.children, currentDepth + 1))
      }
    }
    return max
  }
  return getMaxDepth(data) <= maxDepth
}

const updateParentId = (data, parentId = 0) => {
  data.forEach((item) => {
    item.parent_id = parentId
    if (item.children) {
      updateParentId(item.children, item.id)
    }
  })
}

const onDrop = (info) => {
  const dropKey = info.node.key
  const dragKey = info.dragNode.key
  const dropPos = info.node.pos.split('-')
  const dropPosition = info.dropPosition - Number(dropPos[dropPos.length - 1])
  const oldTree = JSON.parse(JSON.stringify(department.value))
  const data = JSON.parse(JSON.stringify(department.value))
  let dragObj: any
  loop(data, dragKey, (item, index, arr) => {
    arr.splice(index, 1)
    dragObj = item
  })
  const newTree = [...data]
  let targetNode
  loop(newTree, dropKey, (item) => {
    item.children = item.children || []
    targetNode = item
  })
  if (!info.dropToGap) {
    targetNode.children.unshift(dragObj)
  } else if ((info.node.children || []).length > 0 && info.node.expanded && dropPosition === 1) {
    targetNode.children.unshift(dragObj)
  } else {
    let ar: any[] = []
    let i = 0
    loop(newTree, dropKey, (_item, index, arr) => {
      ar = arr
      i = index
    })
    if (dropPosition === -1) {
      ar.splice(i, 0, dragObj)
    } else {
      ar.splice(i + 1, 0, dragObj)
    }
  }
  if (!checkDepth(newTree)) {
    message.error('部门层级不能超过四级')
    department.value = oldTree
    return
  }
  department.value = newTree
  updateParentId(department.value)

  const updatedResult = findNodeById(newTree, dragObj.id)
  const updatedNode = updatedResult ? updatedResult.children : null
  const indexInParent = updatedResult ? updatedResult.index : null
  const newParentId = updatedNode ? updatedNode.parent_id : null

  const payload = {
    id: dragObj.id,
    name: dragObj.name,
    parent_id: newParentId !== undefined ? newParentId : 0,
    sort: indexInParent + 1,
    supplier_id: targetId.value,
  }
  CreateOrUpdateSupplierDepartment(payload)
    .then(() => {
      message.success('修改成功')
      GetSupplierDepartmentList({ id: targetId.value }).then((res) => {
        department.value = res.data
      })
    })
    .catch(() => {
      department.value = oldTree
    })
}

const beforeDel = (item) => {
  targetDepartment.value = item
  delVisible.value = true
}
const drawerComfirm = async () => {
  try {
    await formRef.value?.validateFields()
    const obj = JSON.parse(JSON.stringify(formData.value))
    if (!obj.sort) obj.sort = 99
    if (!obj.parent_id) obj.parent_id = 0

    obj.supplier_id = targetId.value
    CreateOrUpdateSupplierDepartment(obj).then(() => {
      message.success(`${drawerStatus.value === 1 ? '新增' : '修改'}成功`)
      formDrawerVisible.value = false
      GetSupplierDepartmentList({ id: targetId.value }).then((res) => {
        department.value = res.data
      })
    })
  } catch (errorInfo) {
    console.log('Failed:', errorInfo)
  }
}
const del = () => {
  DeleteSupplierDepartment({ id: targetDepartment.value.id })
    .then(() => {
      delVisible.value = false
      message.success('删除成功')
      GetSupplierDepartmentList({ id: targetId.value }).then((res) => {
        department.value = res.data
      })
    })
    .catch(() => {
      delVisible.value = false
    })
}
const edit = (item) => {
  drawerStatus.value = 2
  formDrawerVisible.value = true
  formData.value = {
    id: item.id,
    name: item.name,
    parent_id: item.parent_id == 0 ? null : item.parent_id,
    sort: item.sort,
  }
}
const increase = () => {
  drawerStatus.value = 1
  formDrawerVisible.value = true
  formData.value = {
    supplier_id: targetId.value,
    name: null,
    parent_id: null,
  }
}
const open = (item) => {
  department.value = []
  targetId.value = item.supplier_id
  GetSupplierDepartmentList({ id: item.supplier_id }).then((res) => {
    department.value = res.data
  })
  drawerVisible.value = true
}
// 暴露方法
defineExpose({
  open,
})
</script>

<style lang="scss" scoped>
.departmentTitle {
  display: flex;
  gap: 16px;
  align-items: center;

  .departmentTitleBtns {
    display: none;
    gap: 8px;
    align-items: center;

    .departmentTitleBtn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 20px;
      height: 20px;
      cursor: pointer;
    }
  }

  &:hover {
    .departmentTitleBtns {
      display: flex;
    }
  }
}

.detailBoxIncreaseBtn {
  margin-bottom: 20px;
}

.detailBox {
  padding-left: 16px;
}

::v-deep(.ant-form-item) {
  align-items: flex-start;

  .ant-form-item-label {
    display: flex;
    justify-content: flex-end;
    width: 100px;
    min-width: 100px;
    margin-right: 30px;

    label {
      &::after {
        display: none !important;
      }
    }
  }
}

.w250 {
  width: 250px;
}
</style>
