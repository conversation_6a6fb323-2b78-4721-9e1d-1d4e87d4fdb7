<template>
  <div class="form-top-tabs">
    <div class="form-top-tab" :class="{ current: tab.value === status }" v-for="tab in options" :key="tab.value" @click="onStatusChange(tab)">
      <span>{{ tab.label }}</span>
      <span class="easy-button-dot" v-if="tab.count || countMap?.[tab.code as string]">
        {{ getCount(tab) }}
      </span>
    </div>
  </div>
</template>

<script setup lang="ts">
interface StatusTab {
  label: string
  value: string | number
  code?: string
  count?: number
}

interface Props {
  options: StatusTab[]
  countMap?: Record<string, number>
}

interface Emits {
  (e: 'change', tab: StatusTab): void
}

const props = defineProps<Props>()
const status = defineModel('status', { required: true })

const getCount = (tab) => {
  const _c = tab.count ?? props.countMap?.[tab.code as string]
  return _c > 999 ? `999+` : _c
}

const emit = defineEmits<Emits>()

const onStatusChange = (tab: StatusTab) => {
  status.value = tab.value
  emit('change', tab)
}
</script>

<style scoped lang="scss">
.form-top-tabs {
  position: relative;
  display: flex;
  padding: 0 10px;
  margin-top: -22px;

  &::before {
    position: absolute;
    right: -4200px;
    bottom: 0;
    left: -200px;
    display: block;
    height: 1px;
    content: '';
    background: #dcdee0;
  }

  .form-top-tab {
    position: relative;
    display: flex;
    align-items: center;
    height: 36px;
    margin-right: 40px;
    cursor: pointer;

    &:hover {
      color: $color;
    }

    .easy-button-dot {
      transform: translate(95%, 5px) scale(0.965);
    }

    &.current {
      @include link;

      &::before {
        position: absolute;
        right: 0;
        bottom: 0;
        left: 0;
        display: block;
        height: 2px;
        content: '';
        background: $color;
      }
    }
  }
}
</style>
