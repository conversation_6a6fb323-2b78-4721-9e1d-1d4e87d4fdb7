<template>
  <a-drawer title="选择采购单" width="75vw" :visible="visible" @close="handleClose" :maskClosable="false">
    <div class="flex h-full overflow-auto">
      <div class="p-16px h-full overflow-auto flex-1">
        <a-space class="mb-12">
          <a-input placeholder="采购单号" v-model:value="queryParams.number" :maxlength="200" />
          <a-input placeholder="供应商子公司名称" v-model:value="queryParams.company_supplier_name" :maxlength="200" />
          <a-select class="w-200" placeholder="请选择收料仓库" allowClear v-model:value="queryParams.warehourse_name" show-search :filter-option="filterOption" :options="warehouseOptions"></a-select>
          <a-button type="primary" @click="handleSearch">查询</a-button>
        </a-space>
        <vxe-table
          v-if="leftVisible"
          :border="true"
          ref="productTableRef"
          size="mini"
          :row-config="{ isHover: true, keyField: 'number' }"
          :custom-config="{ mode: 'popup' }"
          :data="data"
          :show-overflow="true"
          :show-header-overflow="true"
          :show-footer-overflow="true"
          :column-config="{ resizable: true }"
          class="tableBoxwidth"
          :checkbox-config="{ reserve: true }"
          min-height="0"
          stripe
          v-bind="$attrs"
          @checkbox-all="selectChangeEvent"
          @checkbox-change="selectChangeEvent"
        >
          <vxe-column type="checkbox" field="checkbox" width="50" fixed="left"></vxe-column>
          <slot name="column">
            <template v-for="i in tableKey" :key="i.field">
              <vxe-column v-bind="i">
                <template v-if="$slots[String(i.field)]" #default="attr">
                  <slot :name="i.field" v-bind="attr" :item="i" />
                </template>
              </vxe-column>
            </template>
          </slot>
        </vxe-table>
        <div class="paginationBox">
          <div class="pagination">
            <a-pagination
              show-quick-jumper
              :total="total"
              show-size-changer
              v-model:current="queryParams.page"
              v-model:page-size="queryParams.pageSize"
              :page-size-options="['10', '20', '30', '40', '50']"
              @change="handlePageChange"
              size="small"
            >
              <template #buildOptionText="props">
                <span>{{ props.value }}条/页</span>
              </template>
            </a-pagination>
          </div>
          <div class="totalBox">
            <div class="text">总数:</div>
            <div class="total">{{ total }}</div>
          </div>
        </div>
      </div>
      <div class="w-350px overflow-y-auto p-16 text-black">
        <div class="flex mb-12 h30px line-height-30px h-30px">
          <div class="text-16px">已选{{ selectProductList.length }}个采购单</div>
          <a-button class="ml-auto" type="primary" @click="cleanCheck">清空</a-button>
        </div>
        <vxe-table
          :border="true"
          ref="selectTableRef"
          size="mini"
          :row-config="{ isHover: true, height: 40, keyField: 'number' }"
          :custom-config="{ mode: 'popup' }"
          :data="selectProductList"
          :show-overflow="true"
          :show-header-overflow="true"
          :show-footer-overflow="true"
          :column-config="{ resizable: true }"
          class="tableBoxwidth"
          :checkbox-config="{ reserve: true }"
          min-height="0"
          stripe
          v-bind="$attrs"
        >
          <vxe-column type="seq" width="50" fixed="left"></vxe-column>
          <slot name="column">
            <template v-for="i in selectTableKey" :key="i.field">
              <vxe-column v-bind="i">
                <template v-if="$slots[String(i.field)]" #default="attr">
                  <slot :name="i.field" v-bind="attr" :item="i" />
                </template>
              </vxe-column>
            </template>
          </slot>
        </vxe-table>
      </div>
    </div>
    <template #footer>
      <a-space>
        <a-button type="primary" @click="handleSelectProduct">确定</a-button>
        <a-button @click="handleClose">取消</a-button>
      </a-space>
    </template>
  </a-drawer>
</template>

<script setup lang="ts">
import { message } from 'ant-design-vue'
import { VxeTableInstance } from 'vxe-table'

import { filterOption } from '@/utils'

import { GetPurchaseOrderList, GetWarehouses } from '@/servers/PurchaseReturnApplication'

const productTableRef = ref<VxeTableInstance>()

const emits = defineEmits(['setValue', 'close'])

const visible = ref(false)
const leftVisible = ref(false)
const warehouseOptions = ref<any[]>([])

const data = ref([])

const total = ref(0)

const selectProductList = ref<any[]>([])

const queryParams = ref<any>({
  number: '',
  company_supplier_name: '',
  warehourse_name: null,
  page: 1,
  pageSize: 20,
  orderby_numbers: [],
})

const tableKey = ref([
  {
    field: 'number',
    title: '采购单号',
    width: 120,
  },
  {
    field: 'company_supplier_name',
    title: '供应商名称',
  },
  {
    field: 'type_string',
    title: '采购类型',
  },

  {
    field: 'buyer_name',
    title: '采购员',
  },
  {
    field: 'purchase_time',
    title: '采购时间',
  },
  {
    field: 'warehouse_name',
    title: '采购收料仓',
  },
  {
    field: 'total_purchase_quantity',
    title: '采购总数量',
  },
  {
    field: 'total_actual_inbound',
    title: '已入库总数量',
  },
  {
    field: 'remark',
    title: '备注',
  },
])

const selectTableKey = ref([
  {
    field: 'number',
    title: '采购单编号',
  },
])

// 打开
const open = async (ids?: number[]) => {
  visible.value = true
  leftVisible.value = true
  const $table = productTableRef.value
  $table?.clearCheckboxRow()
  queryParams.value.orderby_numbers = ids || []
  await getWareHouseList()
  await getProductList()
  selectProductList.value = data.value.filter((i) => ids?.includes(i.number))
  setTimeout(() => {
    const $table = productTableRef.value
    $table?.setCheckboxRow(selectProductList.value, true)
  }, 50)
}
// 关闭
const handleClose = () => {
  emits('close')
}

// 获取仓库选项
const getWareHouseList = async () => {
  const res = await GetWarehouses()
  warehouseOptions.value = res.data.map((i) => ({ label: i.value, value: i.value })) || []
}

// 获取列表
const getProductList = async () => {
  const res = await GetPurchaseOrderList(queryParams.value)
  data.value = res.data.list
  total.value = res.data.total
}
// 分页
const handlePageChange = (page: number, pageSize: number) => {
  queryParams.value.page = page
  queryParams.value.pageSize = pageSize
  getProductList()
}

// 查询
const handleSearch = () => {
  queryParams.value.page = 1
  getProductList()
}
// 选中事件
const selectChangeEvent = () => {
  const $table = productTableRef.value
  // 当前页选中的数据
  const currentSelectedRows = $table?.getCheckboxRecords() || []
  // 其他页选中的数据
  const otherSelectedRows = $table?.getCheckboxReserveRecords() || []
  selectProductList.value = [...currentSelectedRows, ...otherSelectedRows]
}

const cleanCheck = async () => {
  selectProductList.value = []
  leftVisible.value = false
  await getProductList()
  setTimeout(() => {
    if (selectProductList.value.length == 0) {
      const $table = productTableRef.value
      $table?.clearCheckboxRow()
    }
  }, 20)
  leftVisible.value = true
}
// 确定
const handleSelectProduct = () => {
  if (selectProductList.value.length == 0) {
    message.error('请选择采购单')
  } else {
    const supplierLen = Array.from(
      new Set(
        selectProductList.value
          .filter((v) => {
            return v.company_supplier_id
          })
          .map((v) => {
            return v.company_supplier_id
          }),
      ),
    )

    const warehouseLen = Array.from(
      new Set(
        selectProductList.value
          .filter((v) => {
            return v.warehouse_id
          })
          .map((v) => {
            return v.warehouse_id
          }),
      ),
    )

    if (supplierLen.length > 1 || warehouseLen.length > 1) {
      if (supplierLen.length > 1) {
        message.error('供应商子公司不一致,请重新选择')
      }
      if (warehouseLen.length > 1) {
        message.error('收料仓库不一致,请重新选择')
      }
      return
    }

    emits('setValue', selectProductList.value)
    handleClose()
  }
}

defineExpose({
  open,
})
</script>

<style scoped lang="scss">
.paginationBox {
  display: flex;
  align-items: center;
  margin-top: 0.83rem;

  .totalBox {
    display: flex;
    align-items: flex-end;
    margin-left: 20px;
    color: #000;

    .text {
      margin-right: 8px;
      font-size: 14px;
    }

    .total {
      font-size: 16px;
      color: #1890ff;
    }
  }
}
</style>
