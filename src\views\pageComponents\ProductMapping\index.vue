<template>
  <div class="flex flex-col h-full main">
    <Form ref="formRef" v-model:form="formArr" :page-type="PageTypeEnum.ProductMapping" @search="tableRef?.search()" @setting="tableRef?.showTableSetting()"></Form>
    <BaseTable ref="tableRef" :page-type="PageTypeEnum.ProductMapping" v-model:form="formArr" :get-list="GetAliProductMapList" :form-format="formFormat" :dataFormat="dataFormat" keyField="uuid">
      <template #fix_option="{ row, column }">
        <RightOperate :list="rightOperateList" :row="row" :column="column" />
      </template>
      <template #right-btn>
        <a-button type="primary" v-if="btnPermission[101002]" @click="addMappingBtn">新增映射</a-button>
      </template>
    </BaseTable>
    <AddNewMappingDlg ref="AddNewMappingDlgRef" @search="tableRef?.search()" />
    <MatchDlg ref="MatchDlgRef" @search="tableRef?.search()" />
  </div>
</template>
<script lang="ts" setup>
import AddNewMappingDlg from './components/AddNewMappingDlg.vue'
import MatchDlg from './components/MatchDlg.vue'

import { Get1688MRPSupplierCompanySelect } from '@/servers/BusinessCommon'
import { GetAliProductMapList } from '@/servers/ProductMapping'

import { PageTypeEnum } from '@/enums/tableEnum'

const { btnPermission } = usePermission()
const tableRef = ref()
const formRef = ref()
const AddNewMappingDlgRef = ref()
const MatchDlgRef = ref()
const formArr = ref([
  { label: '商品编号', value: '', type: 'input', key: 'k3_sku_id' },
  { label: '商品聚水潭编号', value: '', type: 'input', key: 'jst_sku_id' },
  { label: '系统供应商', value: null, type: 'search', key: 'company_supplier_name', selectArr: [] },
])
const rightOperateList = ref([
  {
    label: '匹配',
    show: 101001,
    onClick: ({ row }) => {
      matchBtn(row)
    },
  },
])
onMounted(() => {
  initApi()
})

// 获取供应商
const getSupplierList = async () => {
  const res = await Get1688MRPSupplierCompanySelect({ type: 2 })
  const arr = res.data.map((i) => ({ label: i.label, value: i.label }))
  formArr.value.map((i) => {
    if (i.key == 'company_supplier_name') {
      i.selectArr = arr
    }
    return i
  })
}

const addMappingBtn = () => {
  AddNewMappingDlgRef.value.open()
}

const matchBtn = (row) => {
  MatchDlgRef.value.open(row)
}

const initApi = () => {
  getSupplierList()
}

const formFormat = (data) => ({
  ...data,
  sortField: 'create_at',
})

const dataFormat = (data) => data.map((i) => ({ ...i, uuid: `${i.id}-${i.product_map_detail_id}` }))
</script>
<style lang="scss" scoped></style>
