<template>
  <a-drawer :maskClosable="false" title="编辑仓库" width="50vw" :visible="visible" @close="handleClose" :bodyStyle="{ padding: '0' }">
    <div class="flex h-full">
      <div class="w-1000 overflow-y-auto p-16">
        <div class="drawer-title">基本信息</div>
        <a-form :rules="rules" :model="form" ref="formRef">
          <a-row>
            <a-col :span="10">
              <a-form-item label="仓库编号" name="warehouse_id" :label-col="{ span: 6 }">
                <a-input v-model:value="form.warehouse_id" :disabled="true" :maxlength="50" />
              </a-form-item>
            </a-col>
            <a-col :span="10">
              <a-form-item label="仓库名称" name="warehouse_name" :label-col="{ span: 6 }">
                <a-input v-model:value="form.warehouse_name" placeholder="请输入仓库名称" :maxlength="50" :disabled="true" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="10">
              <a-form-item label="供应商子公司" name="company_supplier_id" :label-col="{ span: 6 }">
                <SelectSupplier v-model:value="form.company_supplier_id" :title="item.label" :api="item.api" :apiParams="item.apiParams" :mode="item.mode"></SelectSupplier>
                <!-- <a-select show-search class="w-full" v-model:value="form.company_supplier_id" placeholder="请选择" :options="companySupplierList"></a-select> -->
              </a-form-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="10">
              <a-form-item label="收货人" name="consignee" :label-col="{ span: 6 }">
                <a-input v-model:value="form.consignee" placeholder="请输入收货人" :maxlength="20" />
              </a-form-item>
            </a-col>
            <a-col :span="10">
              <a-form-item label="联系电话" name="phone_number" :label-col="{ span: 6 }">
                <a-input v-model:value="form.phone_number" placeholder="请输入联系电话" :maxlength="11" @input="inputRule" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="7">
              <a-form-item label="收货地址" name="province" :label-col="{ span: 9 }">
                <a-select
                  show-search
                  class="w-full"
                  v-model:value="form.province"
                  placeholder="省/直辖市/自治区"
                  @change="getCityList"
                  :options="provinceList"
                  :filter-option="filterOption"
                ></a-select>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="" name="city" :label-col="{ span: 0 }">
                <a-select show-search class="w-full" v-model:value="form.city" placeholder="市" @change="getCountyList" :options="cityList"></a-select>
              </a-form-item>
            </a-col>
            <a-col :span="7">
              <a-form-item label="" name="area" :label-col="{ span: 0 }">
                <a-select show-search class="w-full" v-model:value="form.area" placeholder="区/县" :options="countyList"></a-select>
              </a-form-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="20">
              <a-form-item label="详细地址" name="shipments_address" :label-col="{ span: 3 }">
                <a-input v-model:value="form.shipments_address" placeholder="请输入详细地址" :maxlength="50" />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </div>
    </div>
    <template #footer>
      <a-space :size="20">
        <a-button type="primary" @click="editBtn()">保存</a-button>
        <a-button @click="handleClose">关闭</a-button>
      </a-space>
    </template>
  </a-drawer>
</template>

<script setup lang="ts">
import { message } from 'ant-design-vue'

import { cloneDeep } from '@/utils/index'
import { areaCity } from '@/utils/address'
import SelectSupplier from '@/components/business/SelectSupplier'

import { GetPageMRPSupplierCompanySelect } from '@/servers/BusinessCommon'
import { Edit } from '@/servers/WarehouseApi'

const emits = defineEmits(['query'])
const visible = ref(false)
const formRef = ref()

// 定义 SelectSupplier 的配置项
const item = ref({
  label: '供应商子公司',
  api: GetPageMRPSupplierCompanySelect,
  apiParams: {},
  mode: 'single' as const,
})

const rules = ref<any>({
  warehouse_name: [
    { required: true, message: '请输入仓库名称', trigger: 'blur' },
    { max: 50, message: '仓库名称最多50个字符', trigger: 'blur' },
  ],
  consignee: [
    { required: true, message: '请输入收货人', trigger: 'blur' },
    { max: 50, message: '收货人最多50个字符', trigger: 'blur' },
  ],
  phone_number: [
    { required: true, message: '请输入联系电话', trigger: 'blur' },
    { max: 50, message: '联系电话最多50个字符', trigger: 'blur' },
  ],
  province: [{ required: true, message: '请选择省/直辖市/自治区', trigger: 'change' }],
  city: [{ required: true, message: '请选择市', trigger: 'change' }],
  area: [{ required: true, message: '请选择区/县', trigger: 'change' }],
  shipments_address: [
    { required: true, message: '请输入详细地址', trigger: 'blur' },
    { max: 50, message: '详细地址最多50个字符', trigger: 'blur' },
  ],
})

// 表单
const form = ref<any>({
  warehouse_id: '',
  warehouse_name: '',
  company_supplier_id: '',
  consignee: '',
  phone_number: '',
  province: null,
  city: null,
  area: null,
  shipments_address: '',
})
const provinceList = ref()
const cityList = ref([] as any[])
const countyList = ref([] as any[])
const getCityList = () => {
  form.value.city = null
  form.value.area = null
  cityList.value = areaCity.find((item) => item.name === form.value.province)?.children.map((item) => ({ label: item.name, value: item.name })) || []
  countyList.value = []
}
const getCountyList = () => {
  form.value.area = null
  countyList.value =
    areaCity
      .find((item) => item.name === form.value.province)
      ?.children.find((item) => item.name === form.value.city)
      ?.children.map((item) => ({ label: item.name, value: item.name })) || []
}
const companySupplierList = ref([])
const getCompanySupplierList = async () => {
  const res = await GetMRPSupplierCompanySelect({})
  companySupplierList.value = res.data
}

const filterOption = (input, option) => {
  return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
}
// 关闭
const handleClose = () => {
  formRef.value?.resetFields()
  visible.value = false
}
// 打开
const open = async (row: Record<string, unknown> = {}) => {
  provinceList.value = areaCity.map((item) => ({ label: item.name, value: item.name }))
  visible.value = true
  formRef.value?.resetFields()
  form.value = cloneDeep(row)
  form.value.province = row.province
  await getCityList()
  form.value.city = row.city
  await getCountyList()
  form.value.area = row.area
  await getCompanySupplierList()
  form.value.company_supplier_id = `${row.company_supplier_id || ''}`
}

const editBtn = async () => {
  try {
    await formRef.value?.validate()
    const submitForm = cloneDeep(form.value)
    const res = await Edit(submitForm)
    if (res.success) {
      message.success('编辑成功')
      handleClose()
      emits('query')
    }
  } catch (error) {
    console.log(error)
  }
}

const inputRule = (e: any) => {
  const isValid = typeof e.target.value === 'string' && /^\d+$/.test(e.target.value)
  if (!isValid) {
    form.value.phone_number = ''
  }
}

defineExpose({
  open,
})
</script>

<style scoped lang="scss"></style>
