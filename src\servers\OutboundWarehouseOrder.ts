import { request } from './request'

// 获取委外领料采购单商品列表
export const GetOutboundWarehouseBySkuList = (data) => {
  return request({ url: '/api/OutboundWarehouseOrder/GetOutboundWarehouseBySkuList', data })
}

// 获取委外领料类型采购单列表
export const GetOutboundWarehouseByPurchaseOrderList = (data) => {
  return request({ url: '/api/OutboundWarehouseOrder/GetOutboundWarehouseByPurchaseOrderList', data })
}

// 根据bom生成原料列表
export const GetOutboundRawMaterialBomList = (data) => {
  return request({ url: '/api/OutboundWarehouseOrder/GetOutboundRawMaterialBomList', data })
}

// 获取委外领料采购单物料列表
export const GetOutboundWarehouseByMaterialList = (data) => {
  return request({ url: '/api/OutboundWarehouseOrder/GetOutboundWarehouseByMaterialList', data })
}

// 新增委外领料单
export const AddOutboundWarehouseOrder = (data, config) => {
  return request({ url: '/api/OutboundWarehouseOrder/AddOutboundWarehouseOrder', data, config })
}

// 修改委外领料单
export const UpdateOutboundWarehouseOrder = (data, config) => {
  return request({ url: '/api/OutboundWarehouseOrder/UpdateOutboundWarehouseOrder', data, config })
}

// 获取委外领料单列表
export const GetOutboundWarehouseOrderList = (data) => {
  return request({ url: '/api/OutboundWarehouseOrder/GetOutboundWarehouseOrderList', data })
}

// 获取委外领料单详情
export const GetOutboundWarehouseOrderDetail = (data) => {
  return request({ url: '/api/OutboundWarehouseOrder/GetOutboundWarehouseOrderDetail', data }, 'POST')
}

// 批量审核
export const BatchAudit = (data) => {
  return request({ url: '/api/OutboundWarehouseOrder/BatchAudit', data })
}
