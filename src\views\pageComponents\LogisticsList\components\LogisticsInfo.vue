<template>
  <a-drawer v-model:open="formVisible" @afterOpenChange="formRef?.clearValidate()" width="30vw" :title="`编辑`" placement="right" :maskClosable="false" :footer-style="{ textAlign: 'left' }">
    <LoadingOutlined v-show="formLoading" class="loadingIcon" />
    <a-form v-if="!formLoading" ref="formRef" :model="formData">
      <a-form-item label="物流公司名称" name="company_name" :rules="[{ required: true }, { max: 50, message: '输入内容不可超过50字符' }]">
        <a-input v-model:value="formData.company_name" placeholder="请输入物流公司名称" class="w240" disabled />
      </a-form-item>
      <a-form-item label=" 物流公司编号" name="company_no" :rules="[{ required: true }, { max: 50, message: '输入内容不可超过50字符' }]">
        <a-input v-model:value="formData.company_no" placeholder="请输入物流公司编号" class="w240" :maxlength="50" disabled />
      </a-form-item>
      <a-form-item label=" 聚水潭编号" name="jst_logistics_company_id" :rules="[{ required: false }, { max: 50, message: '输入内容不可超过50字符' }]">
        <a-input v-model:value="formData.jst_logistics_company_id" placeholder="请输入聚水潭编号" class="w240" :maxlength="50" />
      </a-form-item>
      <a-form-item label="物流公司简称" name="company_abbreviation" :rules="[{ required: false }, { max: 50, message: '输入内容不可超过50字符' }]">
        <a-input v-model:value="formData.company_abbreviation" placeholder="请输入物流公司简称" class="w240" :maxlength="50" />
      </a-form-item>
      <a-form-item label="物流公司服务电话" name="company_phone" :rules="[{ required: false }, { max: 50, message: '输入内容不可超过11字符' }]">
        <a-input v-model:value="formData.company_phone" placeholder="请输入物流公司服务电话" class="w240" :maxlength="11" @input="inputRule" />
      </a-form-item>
    </a-form>
    <template #footer>
      <a-button style="margin-right: 10px" type="primary" @click="beforeEdit()">提交</a-button>
      <a-button @click="formVisible = false">关闭</a-button>
    </template>
  </a-drawer>
</template>

<script lang="ts" setup>
import { LoadingOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'

import { Detail, Edit } from '@/servers/LogisticsApi'

const emits = defineEmits(['query'])
const formVisible = ref(false)
const formLoading = ref(false)
const formData = ref<any>({
  id: null,
  company_name: '',
  company_no: '',
  jst_logistics_company_id: '',
  company_abbreviation: '',
  company_phone: '',
})
const formRef = ref<FormInstance>()
const open = (row: Record<string, any> = {}) => {
  formVisible.value = true
  formLoading.value = true
  Detail({ id: row.id })
    .then((res) => {
      formData.value = res.data
      formLoading.value = false
    })
    .catch(() => {
      formLoading.value = false
    })
}

const beforeEdit = async () => {
  try {
    await formRef.value?.validateFields()
    const obj = JSON.parse(JSON.stringify(formData.value))
    Edit(obj).then((res) => {
      if (res.success) {
        message.success('编辑成功')
        formVisible.value = false
        emits('query')
      }
    })
  } catch (errorInfo) {
    console.log('Failed:', errorInfo)
  }
}

const inputRule = (e: any) => {
  const isValid = typeof e.target.value === 'string' && /^\d+$/.test(e.target.value)
  if (!isValid) {
    formData.value.company_phone = ''
  }
}

// 暴露方法
defineExpose({
  open,
})
</script>

<style lang="scss" scoped>
::v-deep(.ant-form-item) {
  align-items: flex-start;

  .ant-form-item-label {
    display: flex;
    justify-content: flex-end;
    width: 110px;
    min-width: 110px;
    margin-right: 30px;

    label {
      &::after {
        display: none !important;
      }
    }
  }
}

.detailTitle {
  padding-left: 12px;
  margin-bottom: 20px;
  font-weight: bold;
  color: #000;
}

.w350 {
  width: 350px;
}

.w300 {
  width: 300px;
}

.w240 {
  width: 240px;
}

.w150 {
  width: 150px;
}

.w200 {
  width: 200px;
}

.description {
  padding-left: 20px;
  font-size: 12px;
  color: rgb(0 0 0 / 50%);
  white-space: nowrap;
}

.detailValueDescription {
  font-size: 12px;
  color: rgb(0 0 0 / 50%);
}

.detailBox {
  padding-top: 12px;

  .loadingIcon {
    font-size: 30px;
    color: #1890ff;
  }
}

.descriptionBtn {
  color: rgb(0 0 0 / 70%);
  text-decoration: underline;
  cursor: pointer;

  &:hover {
    color: #1890ff;
  }
}

.modalContent {
  font-size: 14px;
  word-break: break-word;
  white-space: pre-wrap;
}
</style>
