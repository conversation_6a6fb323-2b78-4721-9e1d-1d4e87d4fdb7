// 生成账单页面数据配置
export const setFormItems = () =>
  [
    // 基本信息部分
    { type: 'title', span: 24, title: '基本信息' },
    {
      label: '账单编号',
      type: 'input',
      // name: 'bill_no',
      name: 'number',
      placeholder: '请输入账单编号',
      span: 12,
      disabled: true,
      required: false,
    },
    {
      label: '账单月份',
      type: 'date',
      name: 'bill_month',
      placeholder: '请选择账单月份',
      span: 12,
      // required: true,
      props: { picker: 'month', format: 'YYYY-MM' },
      rules: [{ required: true, message: '请选择账单月份' }],
    },
    {
      label: '供应商',
      type: 'input',
      name: 'supplier_name',
      placeholder: '请选择供应商',
      span: 12,
      required: true,
      disabled: true,
      rules: [{ required: true, message: '请选择供应商' }],
    },
    {
      label: '供应商子公司',
      type: 'input',
      name: 'company_supplier_name',
      placeholder: '请选择供应商子公司',
      span: 12,
      disabled: true,
      required: false,
      tooltip: '当前供应商可能存在多个子公司',
    },
    {
      label: '备注',
      type: 'textarea',
      name: 'remark',
      placeholder: '请输入备注信息',
      span: 24,
      required: false,
      props: { rows: 2 },
      maxlength: 2000,
    },

    // 采购明细部分
    { type: 'title', span: 24, title: '采购明细' },
    { slot: 'purchase_details', span: 24 },

    // 金额计算部分
    {
      label: '应付总额',
      type: 'number',
      name: 'inv_amount',
      placeholder: '应付总额',
      span: 12,
      required: false,
      props: { precision: 2, disabled: true },
      tooltip: '采购单应付金额合计+赠品应付金额合计',
    },
    {
      label: '优化金额',
      type: 'number',
      name: 'optimize_amount',
      placeholder: '优化金额',
      span: 12,
      required: false,
      props: { precision: 2, min: 0 },
    },
    {
      label: '其他费用',
      type: 'number',
      name: 'other_fees',
      placeholder: '其他费用',
      span: 12,
      required: false,
      props: { precision: 2 },
    },
    {
      label: '扣款金额',
      type: 'number',
      name: 'deduct_amount',
      placeholder: '扣款金额',
      span: 12,
      required: false,
      props: { precision: 2, min: 0 },
    },
    {
      label: '备注',
      type: 'textarea',
      name: 'deduction_remark',
      placeholder: '请输入金额备注',
      span: 24,
      required: false,
      props: { rows: 2 },
      maxlength: 2000,
    },

    // 费用计入成本
    {
      label: '费用计入成本',
      type: 'text',
      name: 'charge_into_cost',
      span: 24,
      required: false,
      props: { disabled: true },
      defaultValue: '按金额计入成本',
    },

    // 实际应付金额计算
    {
      label: '实际应付金额',
      type: 'number',
      name: 'actual_payable_amount',
      placeholder: '实际应付金额',
      span: 12,
      required: false,
      props: { precision: 2, disabled: true },
      tooltip: '应付总额-优化金额-扣款金额+其他费用',
    },
  ] as EasyFormItemProps[]

// 接口字段到表格字段的映射配置
export const fieldMapping = {
  // 采购单相关字段
  purchase_number: 'purchase_order_no',
  return_application_number: 'return_application_no',
  bill_payable_number: 'payable_bill_no',
  purchase_order_status_string: 'purchase_status',

  // 供应商相关字段
  supplier_name: 'supplier',
  company_supplier_name: 'supplier_subsidiary',

  // 商品相关字段
  image_url: 'product_image',
  sku_name: 'product_name',
  k3_sku_id: 'product_no',
  tax_unit_price: 'purchase_unit_price',

  // 数量相关字段
  purchase_quantity: 'actual_quantity',
  undelivered_quantity: 'unfulfilled_quantity',

  // 金额相关字段
  total_purchase_amount: 'purchase_total_amount',
  inv_amount: 'purchase_payable_amount',

  // 赠品相关字段
  gift_purchase_quantity: 'gift_quantity',
  gift_purchase_unit_price: 'gift_unit_price',
  gift_amount_due: 'gift_payable_amount',
}

// 数据转换函数
export const transformApiData = (apiData: any[]) => {
  return apiData.map((item: any, index: number) => {
    const transformed: any = {
      // 保留原有的ID，如果没有则生成新的
      id: item.id || item.purchase_order_id || Date.now() + index,
      purchase_order_id: item.purchase_order_id || item.id,
      serial_no: index + 1,
    }

    // 直接使用接口返回的字段名，不需要映射
    Object.keys(item).forEach((key) => {
      if (key !== 'id' && key !== 'purchase_order_id') {
        transformed[key] = item[key] || (typeof item[key] === 'number' ? 0 : '')
      }
    })

    // 确保 actual_unit_price 字段存在，如果没有则使用 tax_unit_price
    if (!transformed.actual_unit_price && transformed.tax_unit_price) {
      transformed.actual_unit_price = transformed.tax_unit_price
    }

    // 确保 actual_unit_price 有正确的精度
    if (transformed.actual_unit_price) {
      transformed.actual_unit_price = parseFloat(transformed.actual_unit_price)
    }

    return transformed
  })
}

// 构建账单详情数据
export const buildBillDetails = (purchaseDetailsData: any[]) => {
  return purchaseDetailsData.map((item) => ({
    id: item.id || null,
    bill_id: item.bill_id || 0,
    purchase_order_id: item.purchase_order_id || item.id,
    purchase_order_detail_id: item.detail_id,
    return_application_ids: item.return_application_ids && item.return_application_ids.length > 0 ? item.return_application_ids : [],
    bill_payable_ids: item.bill_payable_ids && item.bill_payable_ids.length > 0 ? item.bill_payable_ids : [],
    purchase_time: item.purchase_time,
    k3_sku_id: item.k3_sku_id,
    sku_name: item.sku_name,
    tax_unit_price: item.tax_unit_price || 0,
    actual_unit_price: item.actual_unit_price || 0,
    purchase_inbound_quantity: item.purchase_inbound_quantity || 0,
    undelivered_quantity: item.undelivered_quantity || 0,
    return_quantity: item.return_quantity || 0,
    total_purchase_amount: item.total_purchase_amount || 0,
    refund_amount: item.refund_amount || 0,
    prepayment_amount: item.prepayment_amount || 0,
    inv_amount: item.inv_amount || 0,
    gift_purchase_unit_price: item.gift_purchase_unit_price || 0,
    gift_purchase_quantity: item.gift_purchase_quantity || 0,
    gift_amount_due: item.gift_amount_due || 0,
    supplier_id: item.supplier_id,
    company_supplier_id: item.company_supplier_id,
    is_need_return: item.is_need_return || false,
    is_saled: item.is_saled || false,
    purchase_order_status_string: item.purchase_order_status_string || '',
  }))
}

// 采购明细表格配置
export const getPurchaseGridOptions = () => {
  return {
    columns: [
      { title: '序号', type: 'seq', width: 80, fixed: 'left', align: 'center' },
      {
        title: '采购单编号',
        field: 'purchase_number',
        width: 150,
        slots: { default: 'purchase_number' },
      },
      {
        title: '退库申请单号',
        field: 'return_application_number',
        width: 150,
        slots: { default: 'return_application_number' },
      },
      {
        title: '应付单号',
        field: 'bill_payable_number',
        width: 150,
        slots: { default: 'bill_payable_number' },
      },
      { title: '采购时间', field: 'purchase_time', width: 120 },
      { title: '采购单状态', field: 'purchase_order_status_string', width: 120 },
      { title: '供应商', field: 'supplier_name', width: 150 },
      { title: '供应商子公司', field: 'company_supplier_name', width: 150 },
      {
        title: '商品图片',
        field: 'image_url',
        width: 100,
        slots: { default: 'image' },
      },
      { title: '商品名称', field: 'sku_name', width: 150 },
      { title: '商品编号', field: 'k3_sku_id', width: 120 },
      {
        title: '采购含税单价',
        field: 'tax_unit_price',
        width: 120,
        formatter: ({ cellValue }) => (cellValue ? `¥${cellValue}` : ''),
        cellStyle: () => ({ color: '#ff4d4f' }),
      },
      {
        title: '实际单价',
        field: 'actual_unit_price',
        width: 120,
        formatter: ({ cellValue }) => {
          if (cellValue) {
            const value = parseFloat(cellValue)
            return `¥${value}`
          }
          // return '¥0.00'
        },
        cellStyle: () => ({ color: '#ff4d4f' }),
        slots: { default: 'number' },
        params: { precision: 8 },
        editConfig: {
          trigger: 'click' as const,
          mode: 'cell' as const,
        },
        // max: 8,
      },
      {
        title: '采购数量',
        field: 'purchase_quantity',
        width: 100,
        cellStyle: () => ({ fontWeight: 'bold' }),
      },
      {
        title: '采购入库数量',
        field: 'purchase_inbound_quantity',
        width: 100,
        cellStyle: () => ({ fontWeight: 'bold' }),
      },
      {
        title: '退库数量',
        field: 'return_quantity',
        width: 100,
        cellStyle: () => ({ fontWeight: 'bold' }),
      },
      {
        title: '未交付数量',
        field: 'undelivered_quantity',
        width: 100,
        cellStyle: () => ({ fontWeight: 'bold' }),
      },
      {
        title: '采购总金额',
        field: 'total_purchase_amount',
        width: 100,
        cellStyle: () => ({ fontWeight: 'bold' }),
      },
      {
        title: '退款金额',
        field: 'refund_amount',
        width: 100,
        cellStyle: () => ({ fontWeight: 'bold' }),
      },
      {
        title: '预付金额',
        field: 'prepayment_amount',
        width: 100,
        cellStyle: () => ({ fontWeight: 'bold' }),
      },
      {
        title: '采购单应付金额',
        field: 'inv_amount',
        width: 180,
        cellStyle: () => ({ fontWeight: 'bold' }),
      },
      {
        title: '赠品数量',
        field: 'gift_purchase_quantity',
        width: 100,
        cellStyle: () => ({ fontWeight: 'bold' }),
        titleHelp: {
          message: '关联应付单的赠品数量汇总',
          enterable: false,
        },
      },
      {
        title: '赠品单价',
        field: 'gift_purchase_unit_price',
        width: 100,
        cellStyle: () => ({ fontWeight: 'bold' }),
        titleHelp: {
          message: '关联应付单的赠品单价',
          enterable: false,
        },
      },
      {
        title: '赠品应付金额',
        field: 'gift_amount_due',
        width: 100,
        cellStyle: () => ({ fontWeight: 'bold' }),
        titleHelp: {
          message: '关联应付单的赠品应付金额汇总',
          enterable: false,
        },
      },
      {
        title: '操作',
        field: 'action',
        fixed: 'right',
        slots: { default: 'action' },
        align: 'center',
        width: 100,
      },
    ] as any[],
    size: 'mini' as const,
    data: [] as any[],
    resizable: true,
    height: 600, // 固定高度600px，超过时内部滚动
    border: true,
    stripe: true,
    showOverflow: true,
    showFooter: true,
    editConfig: {
      trigger: 'click' as const,
      mode: 'cell' as const,
      showStatus: true,
    },
    footerMethod: ({ columns, data }) => {
      const footerData = [
        columns.map((column, columnIndex) => {
          if (columnIndex === 0) return '合计'

          // 需要合计的字段
          const sumFields = ['total_purchase_amount', 'refund_amount', 'prepayment_amount', 'inv_amount', 'gift_amount_due']

          if (sumFields.includes(column.field)) {
            const sum = data.reduce((acc, cur) => {
              const value = cur[column.field]
              // 安全地处理数值，确保是有效的数字
              if (value === null || value === undefined || value === '' || Number.isNaN(value)) {
                return acc
              }
              const numValue = parseFloat(value)
              if (Number.isNaN(numValue)) {
                return acc
              }
              return acc + numValue
            }, 0)

            // 使用标准的toFixed和Number转换来处理精度，避免NaN
            const roundedSum = Number(parseFloat(sum.toFixed(2)))

            // 确保返回的是有效数字
            if (Number.isNaN(roundedSum)) {
              return 0
            }

            return roundedSum
          }

          return null
        }),
      ]
      return footerData
    },
  }
}
