import dayjs from 'dayjs'

import { Enum2Map, Enum2Options } from '@/utils'

import { List } from '@/servers/LogisticsApi'
import { GetPurchaseByDeptSelect, GetMRPSupplierCompanySelect } from '@/servers/BusinessCommon'
import { GetWarehouses } from '@/servers/Purchaseapplyorder'

export const setFormItems = (formData, purchaseOrderDlgRef, formRef) => {
  return [
    { type: 'title', title: '基本信息', span: 24 },
    { type: 'input', disabled: true, label: '预约单编号', name: 'booking_order_number' },
    { type: 'input', disabled: true, label: '聚水潭编号', name: 'jst_po_id' },
    {
      type: 'select',
      label: '采购单编号',
      required: true,
      name: 'purchase_order_numbers',
      textFormat: ({ value }) => {
        return (value || []).join(',')
      },
      props: {
        open: false,
        mode: 'multiple',
        onClick: () => {
          purchaseOrderDlgRef.value.open(formData.value.purchase_order_numbers)
        },
      },
    },
    {
      type: 'select',
      disabled: true,
      label: '供应商子公司',
      name: 'company_supplier_id',
      alias: 'company_supplier_name',
      dataType: 'string',
      api: GetMRPSupplierCompanySelect,
    },
    {
      type: 'select',
      disabled: true,
      label: '商品类型',
      name: 'product_type',
      textFormat: ({ value }) => {
        return Enum2Map(productTypeEnum)[value]
      },
      options: Enum2Options(productTypeEnum),
    },
    {
      type: 'select',
      label: '采购收料仓',
      name: 'warehouse_id',
      alias: 'warehouse_name',
      dataType: 'string',
      api: GetWarehouses,
      props: {
        showSearch: true,
        filterOption: (input, option) => {
          return option?.label?.toLowerCase().indexOf(input.toLowerCase()) >= 0
        },
      },
    },
    {
      type: 'select',
      disabled: true,
      label: '创建人',
      dataType: 'string',
      name: 'creator_id',
      alias: 'creator_name',
      api: () =>
        GetPurchaseByDeptSelect({
          dept_id: null,
          keyword: null,
          is_chilrd_query: true,
        }),
      apiCallBack: (res) => {
        if (!formData.value.creator_id) {
          const display_name = JSON.parse(localStorage.getItem('userData') as any).display_name
          const obj = res.data.find((item) => item.label.indexOf(display_name) >= 0)
          formData.value.creator_id = obj.value
        }
      },
    },
    {
      type: 'date',
      disabled: true,
      label: '创建时间',
      placeholder: ' ',
      name: 'create_at',
      showTime: true,
    },
    {
      type: 'date',
      label: '预计到货时间',
      required: true,
      name: 'scheduled_arrival_time',
      showTime: { defaultValue: dayjs().set('hour', 23).set('minute', 59).set('second', 59) },
      props: {
        disabledDate: (current) => {
          return current && current < dayjs().startOf('day')
        },
        valueFormat: 'YYYY-MM-DD HH:mm:ss',
        format: 'YYYY-MM-DD HH:mm:ss',
      },
    },
    { type: 'input', disabled: true, label: '入库单编号', name: 'jst_poi_id', hide: true },
    { type: 'input', label: '备注', name: 'remark', span: 21, props: { maxlength: 200 } },

    { type: 'title', title: '发货信息', span: 24 },
    {
      type: 'radio',
      label: '发货方式',
      name: 'shipping_method',
      alias: 'shipping_method_str',
      required: true,
      options: [
        { label: '物流配送', value: 10 },
        { label: '供应商自配送', value: 20 },
      ],
      span: 24,
      props: {
        onChange: (value) => {
          formRef?.value?.changeItem('logistics_company_id', { hide: value !== 10 })
          formRef?.value?.changeItem('tracking_number', { hide: value !== 10 })
          formRef?.value?.changeItem('contact_person', { hide: value == 10 })
          formRef?.value?.changeItem('contact_phone', { hide: value == 10 })
        },
      },
    },
    {
      type: 'select',
      label: '物流公司',
      name: 'logistics_company_id',
      alias: 'logistics_company',
      required: true,
      api: ({ keyword }) => List({ logistics_company_name: keyword, PageSize: 20, Page: 1 }),
      optionFormatter: (item) => ({
        value: item.id,
        label: item.company_name,
      }),
      props: {
        showSearch: true,
      },
    },
    {
      type: 'input',
      label: '物流单号',
      name: 'tracking_number',
      required: true,
      props: { maxlength: 50 },
    },
    {
      type: 'input',
      label: '联系人',
      name: 'contact_person',
      required: true,
      props: { maxlength: 50 },
      hide: true,
    },
    {
      type: 'input',
      label: '联系电话',
      name: 'contact_phone',
      required: true,
      props: { maxlength: 50 },
      hide: true,
    },
    {
      type: 'title',
      title: '预约入库明细',
      span: 24,
    },
    { type: 'slot', slot: 'details', span: 24 },
    { type: 'title', title: '到货信息', span: 24, hide: true },
    {
      type: 'date',
      label: '实际到货时间',
      name: 'actual_arrival_time',
      required: true,
      showTime: { defaultValue: dayjs().set('hour', 23).set('minute', 59).set('second', 59) },
      props: {
        disabledDate: (current) => {
          return current && current < dayjs().startOf('day')
        },
      },
      span: 7,
      hide: true,
    },
    {
      type: 'upload',
      label: '上传到货凭证',
      required: true,
      name: 'fileIds',
      alias: 'files',
      span: 24,
      hide: true,
      module: UploadFileModuleEnum.ReservationOrder,
    },
  ].map((item) => ({ ...item, span: item.span || 7, placeholder: item.placeholder || (item.disabled ? ' ' : '') })) as EasyFormItemProps[]
}

export const setColumns = [
  { title: '序号', dataIndex: 'key', key: 'key', align: 'center', width: 70, fixed: 'left' },
  { title: '商品主图', dataIndex: 'image_url', key: 'pic', align: 'center', width: 100 },
  { title: '商品名称', dataIndex: 'sku_name', align: 'center', width: 100 },
  { title: '商品编码', dataIndex: 'k3_sku_id', align: 'center', width: 100 },
  { title: '商品聚水潭编码', dataIndex: 'jst_sku_id', align: 'center', width: 120 },
  { title: 'SRS平台商品编码', dataIndex: 'srs_platform_prod_code', width: 130 },
  { title: '颜色规格', dataIndex: 'type_specification', align: 'center', width: 100 },
  { title: '标准装箱数', dataIndex: 'packing_qty', key: 'purchaseAllNum3', align: 'center', width: 120 },
  { title: '采购总数量', dataIndex: 'total_purchase_quantity', align: 'center', width: 150, tooltip: '采购数量+赠品数量' },
  { title: '已预约入库数量', dataIndex: 'total_purchase_scheduled_quantity', align: 'center', width: 100, tooltip: '预约单的本次预约入库数量合计' },
  { title: '本次预约入库数量', dataIndex: 'scheduled_quantity', key: 'purchaseAllNum', align: 'center', width: 120 },
  { title: '箱数', dataIndex: 'box_num', align: 'center', width: 100, tooltip: '预约入库数量/箱规 并向上取整' },
  { title: '尾箱数', dataIndex: 'remaining_quantity', align: 'center', width: 100, tooltip: '预约入库数量/箱规 的余数' },
  { title: '回货图片', dataIndex: 'attachments', align: 'center', width: 140 },
  { title: '备注', dataIndex: 'remark', key: 'purchaseAllNum2', align: 'center', width: 300 },
]

export const setInnerColumns = [
  { title: '采购单编号', dataIndex: 'purchase_order_number', key: 'purchase_order_number', align: 'center' },
  { title: '采购数量', dataIndex: 'purchase_quantity', key: 'purchase_quantity', align: 'center' },
  { title: '赠品数量', dataIndex: 'gift_purchase_quantity', key: 'gift_purchase_quantity', align: 'center' },
  { title: '采购总数', dataIndex: 'total_purchase_quantity', key: 'total_purchase_quantity', align: 'center', tooltip: '采购数量+赠品数量' },
  { title: '待预约入库数量', dataIndex: 'pending_booking_qty', key: 'pending_booking_qty', align: 'center', tooltip: '采购总数-已预约入库数量' },
  {
    title: '已预约入库数量',
    dataIndex: 'total_purchase_scheduled_quantity',
    key: 'total_purchase_scheduled_quantity',
    align: 'center',
  },
  {
    title: '采购已预约入库数量',
    dataIndex: 'purchase_scheduled_qty',
    key: 'purchase_scheduled_qty',
    align: 'center',
  },
  {
    title: '赠品已预约入库数量',
    dataIndex: 'gift_purchase_scheduled_quantity',
    key: 'gift_purchase_scheduled_quantity',
    align: 'center',
  },
  { title: '采购本次预约数量', dataIndex: 'purchase_scheduled_quantity', key: 'purchase_scheduled_quantity', align: 'center', tooltip: '预约单本次预约入库数量分摊所得，上限为采购单的采购数量' },
  { title: '赠品本次预约数量', dataIndex: 'gift_scheduled_quantity', key: 'gift_scheduled_quantity', align: 'center', tooltip: '本次预约入库数量-采购本次预约数量' },
  { title: '本次预约入库数量', dataIndex: 'scheduled_quantity', key: 'scheduled_quantity', align: 'center', tooltip: '本预约单的本次预约入库数量；若关联多张采购单，上限为采购单的采购总数' },
]
