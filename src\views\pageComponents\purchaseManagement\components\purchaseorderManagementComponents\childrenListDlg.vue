<template>
  <a-drawer :maskClosable="false" title="拍单子表" width="65vw" :visible="visible" @close="visible = false" :bodyStyle="{ padding: '0' }">
    <div class="flex h-full">
      <div class="w-full overflow-y-auto p-16">
        <a-space class="mb-12" :size="16">
          <a-button type="primary" @click="updateTakeOrderData">更新订单信息</a-button>
        </a-space>
        <div class="flex h400px">
          <div class="h-full overflow-auto w-full">
            <SimpleTable ref="SimpleTableRef" :table-key="tableColumns" :data="tableData" isIndex>
              <template #order_type="{ row }">
                <a-select v-if="isEdit && row.old_type == 2" v-model:value="row.order_type" placeholder="请选择" :options="orderTypeOptionds" />
                <span v-else>{{ orderTypeMap[row.order_type] }}</span>
              </template>
              <template #order_status="{ row }">
                <span>{{ AliOrderStatusMap[row.order_status] }}</span>
              </template>
            </SimpleTable>
          </div>
        </div>
      </div>
    </div>
    <template #footer>
      <a-space :size="16">
        <a-button @click="updateTakeOrderType" v-if="isEdit">提交</a-button>
        <a-button @click="visible = false">取消</a-button>
      </a-space>
    </template>
  </a-drawer>
</template>

<script setup lang="ts">
import { message } from 'ant-design-vue'

import SimpleTable from '@/components/SimpleTable.vue'
import { AliOrderStatusMap } from '@/common/map'
import { Enum2Map } from '@/utils'

import { GetTakeOrderList, UpdateTakeOrderType, UpdateTakeOrderData } from '@/servers/PurchaseManage'

const isEdit = ref(false)
const id = ref()
const tableColumns = ref([
  { title: '线上订单号', field: 'online_order_number', width: '' },
  { title: '推单方式', field: 'method', width: '', formatter: ({ row }) => Enum2Map(TakeOrderMethodEnum)[row.method] },
  { title: '物流公司', field: 'logistics_company', width: '' },
  { title: '物流单号', field: 'logistics_company_number', width: '' },
  { title: '采购数量', field: 'purchase_quantity', width: '' },
  { title: '1688下单数量', field: 'order_quantity', width: '' },
  { title: '单价', field: 'unit_price', width: '' },
  { title: '金额', field: 'amount', width: '' },
  { title: '订单类型', field: 'order_type', width: '' },
  { title: '退款金额', field: 'refund_amount', width: '' },
  { title: '1688订单状态', field: 'order_status', width: '' },
  { title: '运费', field: 'shipping_fee', width: '' },
  { title: '优惠金额', field: 'discount_amount', width: '' },
])

const visible = ref(false)
const tableData: any = ref([])
const orderTypeOptionds = ref([
  { label: '定金', value: 1 },
  { label: '默认', value: 2 },
])
const orderTypeMap = {
  1: '定金',
  2: '默认',
}
const updateTakeOrderData = () => {
  UpdateTakeOrderData({ id: id.value }).then((res) => {
    if (res.success) {
      message.success('订单信息已更新!')
      getTakeOrderList()
    } else {
      message.error(res.message)
    }
  })
}

const getTakeOrderList = () => {
  GetTakeOrderList({ id: id.value }).then((res) => {
    if (res.success) {
      tableData.value = res.data.map((item) => {
        const titem = { ...item, old_type: item.order_type }
        return titem
      })
    } else {
      message.error(res.message)
    }
  })
}

const updateTakeOrderType = () => {
  const ids = tableData.value.filter((v) => v.order_type == 1 && v.old_type == 2).map((v) => v.id)
  if (ids.length == 0) {
    message.info('本次未做修改！')
    return
  }
  UpdateTakeOrderType(ids).then((res) => {
    if (res.success) {
      message.success('修改成功!')
      visible.value = false
    } else {
      message.error(res.message)
    }
  })
}

// 打开
const open = async (row, bol = false) => {
  id.value = row.id
  isEdit.value = bol
  await getTakeOrderList()
  visible.value = true
}

defineExpose({
  open,
})
</script>

<style scoped lang="scss"></style>
