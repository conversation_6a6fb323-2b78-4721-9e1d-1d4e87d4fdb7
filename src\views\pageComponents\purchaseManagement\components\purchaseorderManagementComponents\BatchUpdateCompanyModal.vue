<template>
  <a-modal v-model:open="visible" title="批量修改采购单" @ok="handleOk" @cancel="handleCancel">
    <div style="padding: 24px 0">
      <SelectSupplier
        ref="selectSupplierRef"
        v-model:value="formState.company_supplier_id"
        v-model:label="formState.company_supplier_name"
        :api="api"
        :api-params="{ is_contains_srs: true }"
        :label-in-value="false"
        mode="single"
        title="供应商子公司"
      />
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { message } from 'ant-design-vue'
import type { SelectValue } from 'ant-design-vue/es/select'

import SelectSupplier from '@/components/business/SelectSupplier'

import { BatchUpdatePurchaseOrderCompany } from '@/servers/PurchaseManage'
import { GetPageMRPSupplierCompanySelect } from '@/servers/BusinessCommon'

const emit = defineEmits(['success'])

const visible = ref(false)
const selectSupplierRef = ref()
const formState = reactive<{ company_supplier_id: SelectValue | undefined; company_supplier_name: string }>({
  company_supplier_id: undefined,
  company_supplier_name: '',
})
const purchaseOrderIds = ref<number[]>([])

const api = GetPageMRPSupplierCompanySelect
// const api = () => Get1688MRPSupplierCompanySelect({ type: 1 })

const open = (ids: number[]) => {
  purchaseOrderIds.value = ids
  formState.company_supplier_id = undefined
  formState.company_supplier_name = ''
  visible.value = true
}

const handleOk = async () => {
  if (!formState.company_supplier_id) {
    message.error('请选择供应商子公司')
    return
  }
  try {
    const params = {
      ids: purchaseOrderIds.value,
      company_supplier_id: formState.company_supplier_id,
    }
    const res = await BatchUpdatePurchaseOrderCompany(params)
    if (res.success) {
      message.success('批量修改成功')
      emit('success')
      handleCancel()
    } else {
      message.error(res.message)
    }
  } catch (_error) {
    // validation failed
  }
}

const handleCancel = () => {
  visible.value = false
}

defineExpose({ open })
</script>
