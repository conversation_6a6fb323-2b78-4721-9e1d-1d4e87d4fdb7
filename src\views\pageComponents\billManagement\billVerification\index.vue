<template>
  <div class="flex flex-col h-full">
    <!-- 状态tabs -->
    <div class="bg-white border-b pt-10 pb-10">
      <StatusTabs v-model:status="currentStatus" :options="statusOptions" :count-map="statusCounts" @change="handleStatusChange" />
    </div>

    <!-- 未对账列表组件 -->
    <UndoneBillList v-if="currentStatus === 'unverified'" ref="UnBillListRef" @statusChange="handleStatusChange" />

    <!-- 账单列表组件 -->
    <DoneBillList v-else ref="billListRef" :current-status="currentStatus" @statusChange="handleStatusChange" />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from 'vue'

import eventBus from '@/utils/eventBus'
import StatusTabs from '@/components/StatusTabs.vue'

import UndoneBillList from './components/UndoneBillList.vue'
import DoneBillList from './components/DoneBillList.vue'

const currentStatus = ref('unverified') // 默认显示未对账

const UnBillListRef = ref()
const billListRef = ref()

// 动态状态数量
const dynamicStatusCounts = ref({
  verified_pending: 0, // 已对账待请款
  awaiting_payment: 0, // 等待打款
})

// 状态数量映射
const statusCounts = computed(() => {
  if (!dynamicStatusCounts.value) return {}

  return {
    verified_pending: dynamicStatusCounts.value.verified_pending || 0,
    awaiting_payment: dynamicStatusCounts.value.awaiting_payment || 0,
  } as Record<string, number>
})

// 获取状态数量
const fetchStatusCounts = async () => {
  try {
    const { GetLabelStatusCount } = await import('@/servers/BusinessCommon')
    const { PageTypeEnum } = await import('@/enums/tableEnum')
    const response = await GetLabelStatusCount({ labelStatusType: PageTypeEnum.paymentOrder })
    if (response?.success && response.data) {
      dynamicStatusCounts.value = {
        verified_pending: response.data.bill_unreconcile_count || 0,
        awaiting_payment: response.data.wait_payment_count || 0,
      }
    }
  } catch (error) {
    console.error('获取状态数量失败:', error)
  }
}

// 动态状态选项
const statusOptions = computed(() => [
  { label: '未对账', value: 'unverified', code: 'unverified' },
  { label: '已对账待请款', value: 'verified_pending', code: 'verified_pending' },
  { label: '等待打款', value: 'awaiting_payment', code: 'awaiting_payment' },
  { label: '已打款', value: 'paid', code: 'paid' },
  { label: '全部', value: 'all', code: 'all' },
])

// 处理状态切换
const handleStatusChange = (tab: any) => {
  currentStatus.value = tab.value
  console.log('切换到状态:', tab.value)
}

// 刷新列表数据
const refreshList = () => {
  console.log('刷新账单验证列表数据')
  if (currentStatus.value === 'unverified') {
    // 刷新未对账列表
    UnBillListRef.value?.onSearch()
  } else {
    // 刷新已对账列表
    billListRef.value?.onSearch()
  }
}

// 监听刷新事件
const handleRefreshBillVerificationList = () => {
  console.log('收到刷新账单验证列表事件')
  refreshList()
}

// 暴露方法给父组件
defineExpose({
  currentStatus,
  handleStatusChange,
  refreshList,
})

onMounted(() => {
  // 监听刷新事件
  eventBus.on('refreshBillVerificationList', handleRefreshBillVerificationList)
  // 获取状态数量
  fetchStatusCounts()
})

onUnmounted(() => {
  // 移除事件监听
  eventBus.off('refreshBillVerificationList', handleRefreshBillVerificationList)
})
</script>

<style scoped>
/* 主页面样式 */
</style>
