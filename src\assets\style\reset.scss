@charset "UTF-8";
/* css 初始化 */
html,
body,
ul,
li,
ol,
dl,
dd,
dt,
p,
h1,
h2,
h3,
h4,
h5,
h6,
form,
fieldset,
legend,
img {
  padding: 0;
  margin: 0;
}

fieldset,
img,
input,
button {
  padding: 0;
  margin: 0;
  /* fieldset组合表单中的相关元素 */
  border: none;
  outline-style: none;
}

ul,
ol {
  list-style: none;
  /* 清除列表风格 */
}

input {
  padding-top: 0;
  padding-bottom: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
}

select,
input {
  vertical-align: middle;
}

select,
input,
textarea {
  margin: 0;
  font-size: 12px;
}

textarea {
  resize: none;
}
/* 防止多行文本框拖动 */
img {
  vertical-align: middle;
  border: 0;
}
/*  去掉图片低测默认的3像素空白缝隙 */
table {
  border-collapse: collapse;
  /* 合并外边线 */
}

.clearfix::before,
.clearfix::after {
  display: table;
  content: '';
}

.clearfix::after {
  clear: both;
}

a {
  text-decoration: none;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: 100%;
  font-weight: normal;
  text-decoration: none;
}

s,
i,
em {
  font-style: normal;
  text-decoration: none;
}
