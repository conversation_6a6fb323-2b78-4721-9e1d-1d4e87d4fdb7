import { request } from './request'

// /api/Supplier/QuerySupplier
export const QuerySupplier = (data) => request({ url: '/api/Supplier/QuerySupplier', data })

export const QuerySupplierCompany = (data) => request({ url: '/api/Supplier/QuerySupplierCompany', data })

// 获取供应商子公司信息
export const GetSupplierCompanyMessage = (data) => request({ url: '/api/Supplier/GetSupplierCompanyMessage', data }, 'GET')

export const GetSupplierInfo = (data) => request({ url: '/api/Supplier/GetSupplierInfo', data }, 'GET')

export const GetMRPSupplierGroupSelect = () => request({ url: '/api/Supplier/GetMRPSupplierGroupSelect' }, 'GET')

export const GetMRPSupplierSelect = () => request({ url: '/api/Supplier/GetMRPSupplierSelect' }, 'GET')

export const GetDept = () => request({ url: '/api/Supplier/GetDept' }, 'GET')

export const GetPurchaseByDept = (data) => request({ url: '/api/Supplier/GetPurchaseByDept', data })

// export const GetPurchaseAndSubordinate = (data) => request({ url: '/api/Supplier/GetPurchaseAndSubordinate', data })

// export const GetMRPSupplierGroupSelectByParent = (data) => request({ url: '/api/Supplier/GetMRPSupplierGroupSelectByParent', data }, 'GET')

export const SubmitAudit = (data) => request({ url: '/api/SupplierAudit/SubmitAudit', data })

export const ManagerAudit = (data) => request({ url: '/api/SupplierAudit/ManagerAudit', data })

export const FinanceAudit = (data) => request({ url: '/api/SupplierAudit/FinanceAudit', data })

export const GetSupplierAuditList = (data) => request({ url: '/api/Supplier/GetSupplierAuditList', data })

export const IsCheckProcess = (data) => request({ url: '/api/Supplier/IsCheckProcess', data }, 'GET')

export const AddSupplierAuditProcess = (data) => request({ url: '/api/Supplier/AddSupplierAuditProcess', data: { ...data, source_type: 1 } })

export const UpdateSupplierAuditProcess = (data) => request({ url: '/api/Supplier/UpdateSupplierAuditProcess', data: { ...data, source_type: 1 } })

export const GetSupplierAuditProcess = (data) => request({ url: '/api/Supplier/GetSupplierAuditProcess', data }, 'GET')

export const GetSupplierMessage = (data) => request({ url: '/api/Supplier/GetSupplierMessage', data }, 'GET')

export const UpdateSupplierBuyer = (data) => request({ url: '/api/Supplier/UpdateSupplierBuyer', data })

// 批量更换采购员
export const BindTransfer = (data) => {
  return request({ url: '/api/Supplier/BindTransfer', data })
}

// 获取供应商信息（批量更换采购员使用）
export const GetChangeSupplierCompanyMessage = (data: number[]) => {
  return request({ url: '/api/Supplier/GetChangeSupplierCompanyMessage', data })
}

// 修改供应商价目表
export const ChangePurchasePrice = (data) => {
  return request({ url: '/api/Supplier/ChangePurchasePrice', data }, 'GET')
}
