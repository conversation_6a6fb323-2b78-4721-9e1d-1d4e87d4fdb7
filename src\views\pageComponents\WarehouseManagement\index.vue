<template>
  <div class="flex flex-col h-full main">
    <Form ref="formRef" v-model:form="formArr" :page-type="PageTypeEnum.WarehouseManagement" @search="tableRef?.search()" @setting="tableRef?.showTableSetting()"></Form>
    <BaseTable ref="tableRef" :page-type="PageTypeEnum.WarehouseManagement" v-model:form="formArr" :get-list="List" :form-format="formFormat" :isIndex="true">
      <template #province="{ row }">
        <span v-if="row.province">{{ `${row.province}-${row.city}-${row.area}` }}</span>
      </template>
      <template #fix_option="{ row, column }">
        <RightOperate :list="rightOperateList" :row="row" :column="column" />
      </template>
      <template #left-btn>
        <a-button type="primary" @click="syncWarehouse()" v-if="btnPermission[112003]">同步仓库</a-button>
      </template>
    </BaseTable>
    <WarehouseDlg ref="WarehouseDlgRef" @query="tableRef?.search()" />
  </div>
</template>
<script lang="ts" setup>
import { Modal, message } from 'ant-design-vue'

import WarehouseDlg from './components/WarehouseDlg.vue'

import { List, SyncWarehouse, Delete } from '@/servers/WarehouseApi'
import { GetPageMRPSupplierCompanySelect } from '@/servers/BusinessCommon'

import { PageTypeEnum } from '@/enums/tableEnum'

const { btnPermission } = usePermission()
const WarehouseDlgRef = ref()
const tableRef = ref()
const formRef = ref()
const formArr = ref([
  { label: '仓库编号', value: '', type: 'input', key: 'warehouse_id' },
  { label: '仓库名称', value: '', type: 'input', key: 'warehouse_name' },
  { label: '供应商主公司', value: '', type: 'input', key: 'supplier_name' },
  {
    label: '供应商子公司',
    value: null,
    type: 'select-supplier',
    key: 'company_supplier_id',
    mode: 'single',
    api: GetPageMRPSupplierCompanySelect,
  },
])
const saveId = ref()

const formFormat = (data) => ({
  ...data,
})

const syncWarehouse = () => {
  SyncWarehouse().then((res) => {
    if (res.success) {
      message.success('已同步仓库信息！')
      tableRef.value.search()
    } else {
      message.error(res.message)
    }
  })
}

const delBtn = (row) => {
  saveId.value = row.warehouse_id
  Modal.confirm({
    title: '确定要删除吗?',
    icon: () => {},
    content: '',
    async onOk() {
      delWarehouse()
    },
    onCancel() {},
  })
}

const delWarehouse = () => {
  const obj = { warehouse_ids: [saveId.value] }
  Delete(obj).then((res) => {
    if (res.success) {
      message.success('已删除')
      tableRef.value.search()
    }
  })
}

const openWarehouseDlg = (row) => {
  WarehouseDlgRef.value.open(row)
}

const rightOperateList = ref([
  {
    label: '编辑',
    show: 112001,
    onClick: ({ row }) => {
      openWarehouseDlg(row)
    },
  },
  {
    label: '删除',
    show: 112002,
    onClick: ({ row }) => {
      delBtn(row)
    },
  },
])
</script>
<style lang="scss" scoped></style>
