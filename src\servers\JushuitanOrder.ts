import { request } from './request'

// 查询聚水潭采购入库列表
export const OaErpapiPurchaseList = (data) => {
  return request({ url: '/api/JushuitanOrder/OaErpapiPurchaseList', data }, 'POST')
}

// 查询聚水潭采购入库详情（通过入库单号查询）
export const OaErpapiPurchaseDetail = (data) => {
  return request({ url: '/api/JushuitanOrder/OaErpapiPurchaseDetail', data }, 'POST')
}

// 立即同步采购入库单
export const PurchaseinSyncNow = () => {
  return request({ url: '/api/JushuitanOrder/PurchaseinSyncNow' }, 'POST')
}

// 查询聚水潭采购退库列表
export const GetOaErpapiPurchaseoutList = (data) => {
  return request({ url: '/api/JushuitanOrder/GetOaErpapiPurchaseoutList', data }, 'POST')
}

// 查询聚水潭采购退库详情（通过退库单号查询）
export const OaErpapiPurchaseReturnDetail = (data) => {
  return request({ url: '/api/JushuitanOrder/GetOaErpapiPurchaseoutDetail', data }, 'POST')
}

// 立即同步采购退库单
export const PurchaseReturnSyncNow = () => {
  return request({ url: '/api/JushuitanOrder/PurchaseoutSyncNow' }, 'POST')
}

// 获取采购入库的制单人列表
export const GetPurchaseinCreatorList = () => {
  return request({ url: '/api/JushuitanOrder/GetInCreaterNameList' }, 'GET')
}

// 获取采购退库的制单人列表
export const GetPurchaseoutCreatorList = () => {
  return request({ url: '/api/JushuitanOrder/GetOutCreaterNameList' }, 'GET')
}

// 导出采购入库单详情
export const ExportPurchaseStorageOrderDetails = (data) => {
  return request({ url: '/api/JushuitanOrder/ExportPurchaseStorageOrderDetails', data }, 'POST')
}

// 导出采购入库单明细
export const ExportStockInOrderDetails = (data) => {
  return request({ url: '/api/JushuitanOrder/ExportStockInOrderDetails', data }, 'POST')
}
