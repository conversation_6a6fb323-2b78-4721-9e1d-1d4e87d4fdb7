<template>
  <ContentPage
    ref="contentRef"
    :get-list="OaErpapiPurchaseList"
    :sync-code="115001"
    :btn-permission="btnPermission"
    v-model:form-arr="formArr"
    :page-type="PageTypeEnum.PurchaseInto"
    :sync-loading="syncLoading"
    :export-code="115002"
    @details="handleDetails"
    @sync="handleSyn"
    @export="handleExport"
  >
    <PurchaseDetails @close="showDetails = false" ref="detailsRef" v-if="showDetails"></PurchaseDetails>
  </ContentPage>
</template>

<script lang="ts" setup>
import { message, Modal } from 'ant-design-vue'

import { checkFormParams } from '@/utils'

import PurchaseDetails from './PurchaseDetails.vue'

import ContentPage from '../components/ContentPage.vue'

import { GetPurchaseinCreatorList, OaErpapiPurchaseList, PurchaseinSyncNow, ExportPurchaseStorageOrderDetails, ExportStockInOrderDetails } from '@/servers/JushuitanOrder'
import { GetWarehouses } from '@/servers/Purchaseapplyorder'
import { GetPageMRPSupplierCompanySelect, GetSupplierCategorySelect } from '@/servers/BusinessCommon'

import { PageTypeEnum } from '@/enums/tableEnum'

const syncLoading = ref(false)
const showDetails = ref(false)

const { btnPermission } = usePermission()
const detailsRef = useTemplateRef('detailsRef')
const contentRef = useTemplateRef('contentRef')
//

const formArr = ref<any>([
  { label: '采购单编号', value: '', type: 'input', key: 'purcharse_order_number' },
  { label: '预约入库单编号', value: '', type: 'input', key: 'booking_order_number' },
  { label: '入库单编号', value: '', type: 'input', key: 'io_id' },
  { label: '商品编码', value: '', type: 'input', key: 'sku_id' },
  { label: '商品名称', value: '', type: 'input', key: 'sku_name' },
  {
    label: '供应商分类',
    value: [],
    type: 'select',
    key: 'supplier_categories',
    multiple: true,
    showSearch: true,
    api: GetSupplierCategorySelect,
  },
  {
    label: '供应商子公司',
    value: null,
    type: 'select-supplier',
    key: 'company_supplier_id',
    mode: 'single',
    api: GetPageMRPSupplierCompanySelect,
    apiParams: { is_contains_srs: true },
  },
  {
    label: '选择仓库',
    value: null,
    type: 'select',
    selectArr: [],
    key: 'wh_id',
    api: () => [],
  },
  {
    label: '制单人',
    value: null,
    type: 'select',
    selectArr: [],
    key: 'creator_id',
    api: GetPurchaseinCreatorList,
  },
  { label: '物流单号', value: '', type: 'input', key: 'l_id' },
  {
    label: '创建',
    value: null,
    type: 'range-picker',
    key: 'create_at',
    formKeys: ['create_start_time', 'create_end_time'],
    placeholder: ['创建开始时间', '创建结束时间'],
  },
  {
    label: '入库',
    value: null,
    type: 'range-picker',
    key: 'io_at',
    formKeys: ['io_start_time', 'io_end_time'],
    placeholder: ['入库开始时间', '入库结束时间'],
  },
  {
    label: '修改',
    value: null,
    type: 'range-picker',
    key: 'update_at',
    formKeys: ['update_start_time', 'update_end_time'],
    placeholder: ['修改开始时间', '修改结束时间'],
  },
  {
    label: '采购',
    value: null,
    type: 'range-picker',
    key: 'po_at',
    formKeys: ['po_start_time', 'po_end_time'],
    placeholder: ['采购开始时间', '采购结束时间'],
  },
])
useSearchForm(formArr)
const getOptions = async () => {
  const warehouses = await GetWarehouses()
  const creater = await GetPurchaseinCreatorList()
  formArr.value.forEach((item) => {
    if (item.key === 'creator_id') {
      item.selectArr = creater.data.map((i) => ({ label: i.value, value: i.key })) || []
    }
    if (item.key === 'wh_id') {
      item.selectArr = warehouses.data.map((i) => ({ label: i.value, value: i.key })) || []
    }
  })
}
const handleSyn = async () => {
  syncLoading.value = true
  const res = await PurchaseinSyncNow().finally(() => {
    syncLoading.value = false
  })
  message.success(res.message)
  contentRef.value?.search()
}
const handleDetails = async (row: any) => {
  showDetails.value = true
  await nextTick()
  detailsRef?.value?.open(row)
}

const handleExport = async (type) => {
  const count = contentRef.value?.tableRef?.checkItemsArr?.length || 0

  let msg = ''
  if ([1, 2, 3].includes(type) && count > 50000) {
    msg = '单次操作不得大于5万条！'
  }
  if ([1].includes(type) && !count) {
    msg = '请勾选数据'
  }

  if (msg) {
    if (contentRef.value) {
      contentRef.value.exportType = undefined
    }
    return message.info(msg)
  }

  // 获取搜索参数，使用 checkFormParams 统一处理
  const searchParam: any = {}
  checkFormParams({ formArr: formArr.value, obj: searchParam })

  if (type === 4 && !searchParam?.company_supplier_id) {
    return message.warn('请选择供应商')
  }

  const params = {
    export_type: type === 4 ? undefined : type,
    ids: type === 4 ? undefined : contentRef.value?.tableRef?.checkItemsArr?.map((f) => f.id) || [],
    search_param: searchParam,
  }

  Modal.confirm({
    title: '确认导出',
    content: '确定要导出采购入库数据吗？',
    okText: '确定',
    cancelText: '取消',
    async onOk() {
      try {
        if (contentRef.value) {
          contentRef.value.exportLoading = true
        }
        const api = type === 4 ? ExportStockInOrderDetails : ExportPurchaseStorageOrderDetails
        const res = await api(type === 4 ? params.search_param : params)
        if (res.success) {
          message.success('导出成功')
        } else {
          message.error(res.message || '导出失败')
        }
      } catch (_error) {
        message.error('导出失败')
      } finally {
        if (contentRef.value) {
          contentRef.value.exportLoading = false
        }
      }
    },
  })
}
onMounted(() => {
  getOptions()
})
</script>

<style lang="scss" scoped>
//
</style>
