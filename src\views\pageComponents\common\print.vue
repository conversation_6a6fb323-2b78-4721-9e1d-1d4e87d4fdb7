<template>
  <div v-if="flag" class="t-body calc-box hidden-t" ref="calcItemsRef">
    <div class="t-body-row flex text-#333 min-h-28">
      <div class="t-body-column" v-for="(head, index) in headers" :key="head.key" :style="setStyle(index)">
        <span v-if="head.key === 'image_url'">
          <EasyImage hidePreview :src="calcStorageData.image_url"></EasyImage>
        </span>
        <span v-else>{{ head.formatter ? head.formatter(calcStorageData[head.key]) : calcStorageData[head.key] }}</span>
      </div>
    </div>
    <div class="t-body-row flex text-#333 min-h-28">
      <div class="t-body-column flex" :style="setStyle(0)">
        <span>备注</span>
      </div>
      <div class="t-body-column flex-auto">
        <span class="relative whitespace-pre-line lh-18">
          {{ calcStorageData.remark }}
        </span>
      </div>
    </div>
  </div>
  <div class="mb-10 hidden-t btn-area">
    <a-button type="primary" v-print="printObj">打印</a-button>
    <a-button type="primary" ghost class="ml-30" @click="handleClose">关闭页面</a-button>
  </div>
  <div class="h-60 hidden-t"></div>
  <div class="w-1122 m-auto" id="printBox">
    <div class="t-page" :class="['t-page-' + data.page]" v-for="data in pageData" :key="data.page">
      <!-- top -->
      <div class="t-top relative h-130 mt-15 flex text-#333">
        <span class="line-top"></span>
        <span class="line-before"></span>
        <span class="line-after"></span>
        <div class="flex flex-col" :style="{ width: `${widths.slice(0, 5).reduce((acc, cur) => (acc += cur), 0)}px` }">
          <div class="font-bold text-25px bg-#BDD7EE h-60 flex items-center justify-center">采购订单</div>
          <div class="flex flex-col justify-center h-70">
            <div class="flex text-14" :style="{ marginLeft: widths[0] + 'px' }">
              <span :style="setWidth(1, 2)">订购人：</span>
              <span class="border-b-1 max-h-40 overflow-hidden" :style="setWidth(3, 4)">{{ printData.buyer_name }}</span>
            </div>
            <div class="flex mt-5 text-14" :style="{ marginLeft: widths[0] + 'px' }">
              <span :style="setWidth(1, 2)">结算方式：</span>
              <span class="border-b-1" :style="setWidth(3, 4)">{{ printData.settlement_type }}</span>
            </div>
          </div>
        </div>
        <div class="flex flex-col" :style="{ width: `${widths.slice(5, 12).reduce((acc, cur) => (acc += cur), 0)}px` }">
          <div class="text-16px h-60 flex items-center justify-center">{{ printData.company_name }}</div>
          <div class="flex flex-col justify-center h-70">
            <div class="flex text-14">
              <span :style="setWidth(5, 7)"></span>
              <span :style="setWidth(8, 9)">订购日期：</span>
              <span class="border-b-1" :style="setWidth(10)">{{ printData.purchase_time }}</span>
            </div>
            <div class="flex mt-5 text-14">
              <span :style="setWidth(5, 7)"></span>
              <span :style="setWidth(8, 9)">联系电话：</span>
              <span class="border-b-1" :style="setWidth(10)">{{ printData.phone_number }}</span>
            </div>
          </div>
        </div>
      </div>
      <!-- header -->
      <div class="t-head relative flex h-30 text-#333 bg-#f6f6f6">
        <span class="line-top"></span>
        <div class="t-head-item relative" v-for="(head, index) in headers" :key="head.key" :style="setStyle(index)">
          <span>{{ head.text }}</span>
          <span class="line-before" v-if="index === 0"></span>
          <span class="line-after"></span>
        </div>
      </div>
      <!-- body -->
      <div class="t-body relative overflow-hidden" :style="{ maxHeight: `${maxPageBodyHeight}px` }">
        <template v-for="(dataItem, rowIndex) in data.list" :key="rowIndex">
          <div class="t-body-row relative flex text-#333 min-h-28">
            <span class="line-top"></span>
            <div class="t-body-column" v-for="(head, index) in headers" :key="head.key" :style="setStyle(index)">
              <span v-if="head.key === 'image_url'">
                <EasyImage hidePreview :src="dataItem.image_url"></EasyImage>
              </span>
              <span v-else>{{ head.formatter ? head.formatter(dataItem[head.key]) : dataItem[head.key] }}</span>
              <span class="line-before" v-if="index === 0"></span>
              <span class="line-after"></span>
            </div>
          </div>
          <div class="t-body-row relative flex text-#333" v-if="dataItem.id" :style="{ maxHeight: `${maxPageBodyHeight}px` }">
            <span class="line-top"></span>
            <div class="t-body-column bg-#f6f6f6 flex items-center min-h-28" :style="setStyle(0)">
              <span>备注</span>
              <span class="line-before"></span>
              <span class="line-after"></span>
            </div>
            <div class="t-body-column flex-auto">
              <span class="relative whitespace-pre-line lh-18">{{ dataItem.remark }}</span>
              <span class="line-after"></span>
            </div>
          </div>
        </template>
      </div>
      <!-- footer -->
      <div class="t-footer flex flex-col text-#333">
        <div class="h-90 flex relative">
          <span class="line-top"></span>
          <div :style="setWidth(0, 1)" class="flex items-center justify-center relative">
            备注
            <span class="line-before" />
            <span class="line-after"></span>
            <span class="line-bottom"></span>
          </div>
          <div :style="setWidth(2, 8)" class="px-10 py-5 relative whitespace-pre-line flex items-center lh-18">
            包装储运
            <br />
            1. 重量在20-25KG内
            <br />
            2. 包装要求：统一数量及方法包装，具体见订购合同
            <br />
            3. 装运标识：每箱产品贴标识并写订单号
            <span class="line-after"></span>
            <span class="line-bottom"></span>
          </div>
          <div>
            <div v-for="i in 3" :key="i" class="flex relative">
              <span class="line-bottom"></span>
              <div :style="setWidth(9)" class="relative h-30">
                <span class="line-after"></span>
              </div>
              <div :style="setWidth(10)" class="relative h-30 px-5 lh-14 flex items-center justify-start">
                <span v-if="i === 2">当前页金额合计</span>
                <span v-else-if="i === 3">总计</span>
                <span v-else>&nbsp;</span>
                <span class="line-after"></span>
              </div>
              <div :style="setWidth(11)" class="relative h-30 px-5 flex items-center justify-end text-right! break-all">
                <span v-if="i === 2">{{ formatAmount(data.total) }}</span>
                <span v-else-if="i === 3">{{ formatAmount(printData.total) }}</span>
                <span v-else>&nbsp;</span>
                <span class="line-after"></span>
              </div>
            </div>
          </div>
        </div>
        <div class="h-60 t-footer-grid grid grid-cols-3 gap-2 relative p-5 items-center">
          <div>负责人核准：</div>
          <div>会计：</div>
          <div>经办人：</div>
          <div>单位/公司：</div>
          <div></div>
          <div>日期：</div>
          <span class="line-bottom"></span>
          <span class="line-before"></span>
          <span class="line-after"></span>
        </div>
      </div>
      <!-- page-footer -->
      <div class="t-page-footer">{{ data.page }}/{{ pageData.length }}</div>
    </div>
  </div>
  <div class="h-30 hidden-t"></div>
</template>

<script lang="ts" setup>
import dayjs from 'dayjs'
import { message } from 'ant-design-vue'

import EasyImage from '@/components/EasyImage.vue'

import { PurchaseOrderListPrint } from '@/servers/Common'

type PageItem = {
  page: number
  height: number
  list: any[]
  total: number | string
}

const route = useRoute()

const printObj = ref({
  id: 'printBox',
  type: 'html',
  extraHead: '<meta http-equiv="Content-Language"content="zh-cn"/>',
})

const formatAmount = (value: number | string): string => {
  if (value === null || value === undefined || value === '') return ''
  const num = Number(value)
  if (Number.isNaN(num)) return String(value)
  return num.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })
}
const headers = ref([
  { text: '采购单编号', align: 'left', key: 'number', width: 110 },
  { text: '商品图', align: 'center', key: 'image_url', width: 70 },
  { text: '商品编号', align: 'left', key: 'k3_sku_id', width: 100 },
  { text: '商品名称', align: 'left', key: 'sku_name', width: 150 },
  { text: '规格', align: 'center', key: 'type_specification', width: 100 },
  { text: '标准装箱数', align: 'center', key: 'packing_qty', width: 70 },
  {
    text: '交期',
    align: 'center',
    key: 'predict_delivery_date',
    width: 110,
    formatter: (value: string | null) => value && dayjs(value).format('YYYY/M/DD HH:mm'),
  },
  { text: '采购数量', align: 'center', key: 'total_purchase_quantity', width: 80 },
  { text: '税率', align: 'center', key: 'tax_rate', width: 50, formatter: (value) => (typeof value === 'number' && `${value}%`) || '' },
  { text: '单价', align: 'center', key: 'tax_unit_price', width: 62, formatter: formatAmount },
  { text: '采购收料仓', align: 'center', key: 'warehouse_name', width: 110 },
  // { text: '备注', alignment: 'center', key: 'remark', width: 52 },
  { text: '金额', align: 'center', key: 'total_purchase_amount', width: 80, formatter: formatAmount },
])

const printData = ref<Record<string, any>>({})
const getList = () => {
  const { n, t } = route.query as any
  if (!t) {
    return message.warn('参数错误')
  }
  PurchaseOrderListPrint(decodeURIComponent(t).split(',')).then(async (res) => {
    printData.value = res.data
    document.title = `采购订单-${decodeURIComponent(n || '')}-${dayjs().format('YYYYMMDDHHmm')}`
    await calcBodyItem()
  })
}

// const maxWidth = 1092
const widths = ref(headers.value.map((f) => f.width))
const setWidth = (min: number, max?: number) => {
  const width = widths.value.slice(min, max ? max + 1 : min + 1).reduce((acc, cur) => acc + cur, 0)
  return { width: `${width}px` }
}
const setStyle = (index: number) => {
  const w = widths.value[index]
  return {
    width: w ? `${w}px` : 'auto',
    flex: w ? `0 0 ${w}px` : 'auto',
    textAlign: headers.value[index].align as 'left' | 'center' | 'right',
    justifyContent: headers.value[index].align as 'center',
  }
}

const calcItemsRef = ref()
const flag = ref(false)
const calcStorageData = ref<Record<string, any>>([])

// 根据表格body当前行计算数据
const pageData = ref<PageItem[]>([
  {
    page: 1,
    height: 0,
    list: [],
    total: 0,
  },
])

const maxPageBodyHeight = 778 - 130 - 150 - 30 - 40
const calcBodyItem = async () => {
  flag.value = true
  // console.log('printData.value', printData.value)
  for (const item of printData.value.items) {
    calcStorageData.value = { ...item }
    await nextTick()
    const h = Math.max(calcItemsRef.value.clientHeight, item.image_url ? 70 : 58)
    const curItem = pageData.value[pageData.value.length - 1]
    const newHeight = h + curItem.height
    console.log('newHeight', newHeight, h)
    if (newHeight < maxPageBodyHeight) {
      curItem.list.push({
        ...item,
        height: h,
      })
      curItem.height = newHeight
      curItem.total += item.total_purchase_amount || 0
    } else {
      pageData.value.push({
        page: pageData.value.length + 1,
        height: Math.min(maxPageBodyHeight, h),
        total: item.total_purchase_amount || 0,
        list: [
          {
            ...item,
            height: h,
          },
        ],
      })
    }
    const { list, height } = pageData.value[pageData.value.length - 1]
    if (item.id === printData.value.items[printData.value.items.length - 1].id) {
      const rows = (maxPageBodyHeight - height) / 28
      Array.prototype.push.apply(
        list,
        Array.from({ length: Math.floor(rows) }).map(() => ({})),
      )
    }
  }
  flag.value = false
}

const handleClose = () => {
  window.close()
}
onMounted(getList)
</script>

<style>
body,
html {
  height: auto;
  overflow: auto;
}
</style>
<style lang="scss" scoped>
/* 打印样式 */
@media print {
  @page {
    padding: 0;
    margin: 0;
    size: a4 landscape;
  }

  header nav,
  footer,
  video,
  audio,
  object,
  embed {
    display: none;
  }

  .hidden-t {
    display: none;
  }

  .t-page {
    width: 297mm;
    height: 210mm;
    margin-top: 0 !important;
    margin-bottom: 0 !important;
    page-break-after: always !important;
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
    border: none !important;

    &::before {
      display: none !important;
    }
  }

  .line-top {
    // top: -1px !important;
    right: 1px !important;
  }

  .line-bottom {
    right: 1px !important;
  }

  .line-before {
    left: 0 !important;
  }

  .t-page:last-child {
    page-break-after: auto;
  }
}

.btn-area {
  position: fixed;
  top: 0;
  right: 0;
  left: 0;
  z-index: 100;
  display: flex;
  justify-content: center;
  padding: 20px;
  background-color: rgb(0 0 0 / 70%);
}

.t-page {
  position: relative;
  width: 297mm;
  height: calc(210mm - 15px);
  padding: 0 15px;
  margin-top: 30px;
  overflow: hidden;
  font-size: 12px;

  &::before {
    position: absolute;
    inset: 0;
    display: block;
    content: '';
    border: 1px #ddd solid;
  }

  &-footer {
    position: absolute;
    bottom: 0;
    display: flex;
    justify-content: center;
    width: 100%;
    color: #999;
  }
}

.border-b-1 {
  border-bottom: 1px #333 solid;
}

.line-before {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  display: block;
  width: 1px;
  background: #666;
}

.line-after {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  display: block;
  width: 1px;
  background: #666;
}

.line-top {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  z-index: 100;
  display: block;
  height: 1px;
  background: #666;
}

.line-bottom {
  position: absolute;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 10;
  display: block;
  height: 1px;
  background: #666;
}

.t-top,
.t-head,
.t-body,
.t-footer {
  width: 1092px;
}

.t-head {
  z-index: 20;

  &-item {
    position: relative;
    display: inline-flex;
    align-items: center;
    padding: 0 5px;
    overflow: hidden;
  }
}

.t-body {
  z-index: 50;

  &-column {
    position: relative;
    padding: 5px;
    word-break: break-all;
  }
}

.calc-box {
  position: absolute;
  z-index: -1;
  opacity: 0;
}
</style>
