import { DatePicker, DatePickerProps } from 'ant-design-vue'

export default defineComponent({
  name: 'EasyFormDate',
  props: {
    innerProps: {
      require: true,
      type: Object as PropType<DatePickerProps>,
    },
    item: {
      require: true,
      type: Object,
      default: () => ({}),
    },
    form: {
      type: Object as PropType<Record<string, any>>,
      default: () => ({}),
    },
  },
  setup(props) {
    const renderVN = () => {
      if (!props.innerProps) return null
      const defaultParams: any = {
        placeholder: props.item.placeholder || `请选择${props.item.label}`,
        allowClear: true,
        valueFormat: 'YYYY-MM-DD',
        format: 'YYYY-MM-DD',
        class: 'w-full',
      }
      if (props.item.showTime) {
        defaultParams.valueFormat = 'YYYY-MM-DD HH:mm:ss'
        defaultParams.format = 'YYYY-MM-DD HH:mm:ss'
        defaultParams.showTime = props.item.showTime
      }
      return h(DatePicker as any, {
        ...defaultParams,
        ...props.innerProps,
      })
    }
    return {
      renderVN,
    }
  },
  render() {
    return this.renderVN()
  },
})
