<template>
  <div class="flex flex-col h-full main">
    <Form ref="formRef" v-model:form="formArr" :page-type="PageTypeEnum.PurchaseAccount" @search="tableRef?.search()" @setting="tableRef?.showTableSetting()"></Form>
    <BaseTable ref="tableRef" :page-type="PageTypeEnum.PurchaseAccount" v-model:form="formArr" :get-list="GetAliPurchaseAccountList" :form-format="formFormat">
      <template #status="{ row }">
        <span>{{ accountStatusMap[row.status] }}</span>
      </template>
      <template #authorization_status="{ row }">
        <span>{{ authorizationStatusMap[row.authorization_status] }}</span>
      </template>
      <template #fix_option="{ row, column }">
        <RightOperate :list="rightOperateList" :row="row" :column="column" />
      </template>

      <template #right-btn>
        <a-button type="primary" @click="syncAdress()" v-if="btnPermission[102006]">同步收货地址</a-button>
        <a-button type="primary" @click="openAccountDlg(1)" v-if="btnPermission[102002]">添加1688采购账号</a-button>
      </template>
    </BaseTable>
    <AccountDlg ref="AccountDlgRef" @query="tableRef?.search()" />
  </div>
</template>
<script lang="ts" setup>
import { Modal, message } from 'ant-design-vue'

import { accountStatusMap, authorizationStatusMap } from '@/common/map'

import AccountDlg from './components/AccountDlg.vue'

import { GetAliPurchaseAccountList, GetAliPurchaseAccountSelect, SyncReceiveAddressList, UpdateAliPurchaseAccountStatus, DeleteAliPurchaseAccountList } from '@/servers/PurchaseAccount'

import { PageTypeEnum } from '@/enums/tableEnum'

const { btnPermission } = usePermission()
const router = useRouter()
const AccountDlgRef = ref()
const tableRef = ref()
const formRef = ref()
const formArr = ref([
  { label: '采购账号编号', value: '', type: 'input', key: 'number' },
  { label: '授权账号', value: '', type: 'input', key: 'authorized_account' },
  { label: '账号名称', value: '', type: 'input', key: 'account_name' },
  {
    label: '启用状态',
    value: null,
    type: 'select',
    key: 'status',
    selectArr: [
      { label: '启用', value: 1 },
      { label: '停用', value: 2 },
    ],
  },
])
const saveId = ref()
onMounted(() => {
  getAliPurchaseAccountSelect()
})

const formFormat = (data) => ({
  ...data,
  sortField: 'create_at',
})

const getAliPurchaseAccountSelect = () => {
  GetAliPurchaseAccountSelect({}).then((res) => {
    if (res.success) {
      const arr = res.data.map((v) => {
        return { label: v.label, value: +v.value }
      })
      formArr.value.map((item) => {
        if (item.key == 'authorized_account') {
          item.selectArr = arr
        }
        return item
      })
    }
  })
}

const syncAdress = () => {
  SyncReceiveAddressList({}).then((res) => {
    if (res.success) {
      message.success(res.data)
      tableRef.value.search()
    } else {
      message.error(res.message)
    }
  })
}

const switchBtn = (row) => {
  const obj = { id: row.id, is_open: row.status != 1 }
  UpdateAliPurchaseAccountStatus(obj).then((res) => {
    if (res.success) {
      tableRef.value.search()
    }
  })
}

const delBtn = (row) => {
  saveId.value = row.id
  Modal.confirm({
    title: '确定要删除吗?',
    icon: () => {},
    content: '',
    async onOk() {
      delAccount()
    },
    onCancel() {},
  })
}

const delAccount = () => {
  const ids = [saveId.value]
  DeleteAliPurchaseAccountList({ ids }).then((res) => {
    if (res.success) {
      message.success('已删除')
      tableRef.value.search()
    }
  })
}

const openAccountDlg = (type, id = null) => {
  AccountDlgRef.value.open(type, id)
}

const toReceivingAddress = (row) => {
  router.push(`/receivingAddress/${row.id}`)
}

const rightOperateList = ref([
  {
    label: ({ row }) => (row.status == 1 ? '停用' : '启用'),
    show: [102003, 102004],
    onClick: ({ row }) => {
      switchBtn(row)
    },
  },
  {
    label: '收货地址',
    show: () => true,
    onClick: ({ row }) => {
      toReceivingAddress(row)
    },
  },
  {
    label: '编辑',
    show: 102001,
    onClick: ({ row }) => {
      openAccountDlg(2, row.id)
    },
  },
  {
    label: '删除',
    show: 102005,
    onClick: ({ row }) => {
      delBtn(row)
    },
  },
])
</script>
<style lang="scss" scoped></style>
