import fs from 'fs'
import { spawnSync, execSync } from 'child_process'
import path from 'path'
import { fileURLToPath } from 'url'

import readlineSync from 'readline-sync'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// 设置默认参数
const systemType = process.argv[2] || 'production'
const envAlias = {
  test: '测试环境',
  dev: '开发环境',
  prod: '正式环境',
  pre: '预发布环境',
}[process.argv[3]]

const envName = envAlias || '开发环境'
// const unEnvName = envName === '开发环境' ? '测试环境' : '开发环境'

// 文件路径设置
const envFile = `.env.${systemType}`
const tempFile = path.join(__dirname, 'env_temp.txt')

// 读取并处理环境文件
const lines = `${fs.readFileSync(envFile, 'utf8')}`.replace(/\r/g, '').split('\n')

// const skipNext = false
// const unSkipNext = false
let newVersion = null

const outputLines = lines.map((line) => {
  // 处理环境
  // if (new RegExp(`${envName}`).test(line)) {
  //   skipNext = true
  // } else if (new RegExp(`${unEnvName}`).test(line)) {
  //   unSkipNext = true
  // } else {
  //   if (skipNext) {
  //     skipNext = false
  //     return line.startsWith('#') ? line.slice(2) : line
  //   }
  //   if (unSkipNext) {
  //     unSkipNext = false
  //     return line.startsWith('#') ? line : `# ${line}`
  //   }
  // }
  // 处理版本号
  if (line.startsWith('VITE_APP_VERSION')) {
    const version = line.slice(19)
    const versionArr = version.split('.')
    const last = Number(versionArr[versionArr.length - 1]) + 1
    newVersion = [...versionArr.slice(0, versionArr.length - 1), last > 9 ? last : `0${last}`].join('.')
    line = line.replace(version, newVersion)
  }
  // 处理时间戳
  if (line.startsWith('VITE_APP_TIMESTAMP')) {
    const timestamp = Date.now()
    line = line.replace(/\d+/, timestamp)
  }
  return line
})
// 写入临时文件
fs.writeFileSync(tempFile, outputLines.join('\n'))
// 替换原文件
fs.renameSync(tempFile, envFile)

console.log(`版本号已更新为 ${newVersion}`)

// if (newVersion) {
//   process.exit(0)
// }

// 设置源文件夹和输出文件
const outputFile = path.join(__dirname, 'dist.zip')

// 删除原来的 zip 文件
if (fs.existsSync(outputFile)) {
  fs.unlinkSync(outputFile)
}

console.log(`正在运行 vite build --mode ${systemType}`)
execSync(`vite build --mode ${systemType}`, { stdio: 'inherit' })

// 更换version.json
const versionFilePath = path.join(__dirname, 'dist', 'version.json')
fs.writeFileSync(
  versionFilePath,
  JSON.stringify(
    {
      version: `${newVersion}`,
    },
    null,
    2,
  ),
  (err) => {
    if (err) {
      console.error('写入文件时出错:', err)
    } else {
      console.log('dist/version.json 已替换为新的版本数据')
    }
  },
)

// 压缩文件夹
const tar = spawnSync('tar', ['-a', '-c', '-f', outputFile, '-C', __dirname, 'dist'])
if (tar.error) {
  console.error('压缩失败，请检查错误信息。')
} else {
  console.log(`压缩完成: ${outputFile}`)
}

function gitPushMode() {
  // 乱码命令窗输入 chcp 65001
  if (readlineSync.keyInYN('Do you want to commit and push to the Git repository?')) {
    // 'Y' key was pressed.
    execSync(`git add ${envFile}`)
    console.log(`Git commit "version: ${newVersion}"`)
    execSync(`git commit -m "version: ${newVersion}"`)
    console.log('git pushing...')
    execSync(`git push`)
    console.log('git push success !!')
  } else {
    console.log('exit')
    process.exit(0)
  }
}

if (envName !== '开发环境') {
  gitPushMode()
  process.exit(0)
}

// 设置目标 URL
const url = `http://**************:1880/${systemType}upload`

// 提示用户输入 token
const token = readlineSync.question('Please enter the token: ')

// 使用 curl 上传文件和 token
console.log(`正在上传文件... 地址 ${url}`)

const result = spawnSync('curl', ['-X', 'POST', '-F', `file=@${outputFile}`, '-F', `token=${token}`, url], { encoding: 'utf8', stdio: 'pipe' })
if (result.error) {
  console.error('Upload failed!', result)
  process.exit(1)
} else if (result.status !== 0) {
  console.error('命令执行失败，状态码:', result.status)
  console.error('错误输出:', result.stderr)
} else {
  const response = JSON.parse(result.stdout.trim())
  if (response.code === '0') {
    console.log('上传失败!', response.msg)
  } else {
    console.log('上传成功!')
    gitPushMode()
  }
}
