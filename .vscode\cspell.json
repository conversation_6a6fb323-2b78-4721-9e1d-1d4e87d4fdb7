// cSpell Settings
{
  // Version of the setting file.  Always 0.2
  "version": "0.2",
  // language - current active spelling language
  "language": "en",
  "minWordLength": 3,
  // words - list of words to be always considered correct
  "words": [
    "antv",
    "Applyer",
    "Applyers",
    "befor",
    "Boxwidth",
    "btnlist",
    "cascader",
    "categorys",
    "childs",
    "chilrd",
    "cls",
    "compeny",
    "datas",
    "Dlg",
    "ele",
    "ENV",
    "failback",
    "Fillter",
    "GSQ",
    "iconfont",
    "idx",
    "maxlength",
    "MRP",
    "notbind",
    "Orderchange",
    "ordernum",
    "parentkey",
    "pinvid",
    "Pinvids",
    "PLM",
    "pmc",
    "PSQ",
    "purcharse",
    "Purchaseapplyorder",
    "purchasebatchorder",
    "Purchasein",
    "purchaseorder",
    "PWA",
    "rdered",
    "rpx",
    "sider",
    "sku",
    "sortablejs",
    "SRP",
    "SRS",
    "stockout",
    "taxpayable",
    "taxprice",
    "titem",
    "unplugin",
    "VITE",
    "vxe",
    "warehourse"
  ],
  "ignoreRegExpList": ["icon-[^\\s]{2,}", "a-[^\\s]{2,}", "uno"],
  "ignorePaths": [
    "node_modules", // this will ignore anything the node_modules directory
    "**/node_modules", // the same for this one
    "**/node_modules/**", // the same for this one
    "node_modules/**", // Doesn't currently work due to how the current working directory is determined.
    "vscode-extension", //
    ".git", // Ignore the .git directory
    "*.dll", // Ignore all .dll files.
    "**/*.dll", // Ignore all .dll files
    "*.json",
    "*.bat"
  ]
}
