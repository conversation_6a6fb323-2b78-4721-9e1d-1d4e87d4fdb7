<template>
  <a-drawer :footerStyle="{ paddingLeft: '24px' }" :footer="false" v-model:open="logVisible" width="768" title="操作界面水印日志" placement="right">
    <div class="detailBox">
      <a-spin v-show="logloading" />
      <div v-if="!logloading && log.length != 0" class="contentBox">
        <div class="detail" v-for="(item, index) in log" :key="index">
          <div class="point">
            <div class="pointItem"></div>
          </div>
          <div class="detailTime">{{ item.op_at }}</div>
          <div class="detailContentBox">
            <div class="detailTitle">
              {{ item.user_name }}
              <span v-show="item.user_department" class="description">[{{ item.user_department }}]</span>
            </div>
            <div class="detailType">{{ item.op_type }}了 此记录</div>
            <div class="detailItem" style="display: flex" v-for="(detail, detailIndex) in item.edits" :key="detailIndex">
              <div style="white-space: nowrap">· {{ detail.name }}：</div>
              <div>
                <div v-if="detail.fie_id != 'TextInfo'" class="oldVal">{{ detail.old_val ? detail.old_val : '空' }}</div>
                <template v-else>
                  <div style="color: rgb(0 0 0 / 40%); text-decoration: line-through" v-if="detail.old_val" v-html="detail.old_val.replaceAll('/n换行', '<br>')"></div>
                  <div v-else>空</div>
                </template>
              </div>
              <div class="to">></div>
              <div>
                <div v-if="detail.fie_id != 'TextInfo'" class="newVal">{{ detail.new_val ? detail.new_val : '空' }}</div>
                <template v-else>
                  <div v-if="detail.new_val" v-html="detail.new_val.replaceAll('/n换行', '<br>')"></div>
                  <div v-else>空</div>
                </template>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="emptyText" v-show="!logloading && log.length === 0">无操作日志</div>
    </div>
  </a-drawer>
</template>

<script lang="ts" setup>
import { GetOpLogInfos } from '@/servers/WaterMark'

const logVisible = ref(false)
const logloading = ref(false)
const log = ref()
const open = (page) => {
  log.value = []
  logVisible.value = true
  logloading.value = true
  GetOpLogInfos({ id: page })
    .then((res) => {
      log.value = res.data.reverse()
      log.value.forEach((x) => {
        if (x.edits && x.edits.length != 0) {
          x.edits.forEach((y) => {
            if (y.old_val && y.fie_id === 'watermark_info') {
              y.old_val = getJsonSpanArr(y.old_val)
            }
            if (y.new_val && y.fie_id === 'watermark_info') {
              y.new_val = getJsonSpanArr(y.new_val)
            }
          })
        }
      })
      logloading.value = false
    })
    .catch(() => {
      logloading.value = false
    })
}
const getJsonSpanArr = (str) => {
  const textArray = [...str.matchAll(/<span>(.*?)<\/span>/g)].map((match) => match[1].replace(/#\{|\}/g, '').trim())
  console.log(textArray)
  textArray.forEach((x) => {
    x.replace('real_name', '用户名称').replace('user_name', '账号').replace('department', '所在部门').replace('jobtitlename', '岗位').replace('date', '日期').replace('company_name', '公司名称')
  })
  const htmlStr = textArray.join('')
  return htmlStr
}
// 暴露方法
defineExpose({
  open,
})
</script>

<style lang="scss" scoped>
.contentBox {
  padding: 5px 0 5px 20px;
  margin-left: 20px;
  border-left: 1px solid #1890ff;

  .detail {
    position: relative;
    margin-bottom: 12px;

    .point {
      position: absolute;
      top: 2px;
      left: -28px;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 15px;
      height: 15px;
      background-color: rgb(***********);
      border: 1px solid #1890ff;
      border-radius: 50%;

      .pointItem {
        width: 9px;
        height: 9px;
        background-color: #1890ff;
        border-radius: 50%;
      }
    }

    .detailContentBox {
      padding: 16px;
      background: #f7f8fa;
      border-radius: 4px;

      .detailTitle {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
        color: rgb(0 0 0 / 80%);
      }

      .description {
        padding-left: 10px;
        font-size: 12px;
        color: #999;
      }

      .detailType {
        margin-bottom: 8px;
        color: #999;
      }

      .detailItem {
        line-height: 20px;
        color: rgb(0 0 0 / 80%);

        .oldVal {
          height: auto;
          color: #999;
          text-decoration: line-through;
          word-break: break-all;
        }

        .newVal {
          height: auto;
          color: #000;
          word-break: break-all;
        }

        .to {
          margin: 0 10px;
          color: #666;
        }
      }
    }

    .detailTime {
      margin-bottom: 12px;
      font-size: 12px;
      color: #999;
    }
  }
}

.emptyText {
  font-size: 12px;
  color: rgb(0 0 0 / 70%);
}
</style>
