import { Dropdown } from 'ant-design-vue'
import { CopyOutlined } from '@ant-design/icons-vue'

import EasyImage from '@/components/EasyImage.vue'
import { copyText, isEmpty } from '@/utils/index'
// 列表图片渲染
export const imageListRender = (_, params) => {
  const { column, row, options, key, size } = params
  const imgSize = { 36: 28, 56: 40, 76: 60 }[size] || 28
  let boxWidth
  let maxHeight
  return h(
    Dropdown,
    {
      color: '#fff',
      trigger: 'click',
      overlayStyle: {
        zIndex: 400,
      },
      overlayClassName: 'image-list-dropdown',
      destroyPopupOnHide: true,
      placement: 'bottomLeft',
      arrow: { pointAtCenter: true },
      align: {
        offset: [0, -5],
        overflow: { adjustX: true, adjustY: true },
        // useCssTransform: true,
        // points: ['lb', 'cc'],
      },
      openChange: (flag) => {
        if (!flag) {
          // CG回收
          boxWidth = null
          maxHeight = null
        }
      },
    },
    {
      overlay: () =>
        h(
          'div',
          {
            class: 'image-list-dropdown-menu',
            style: {
              width: `${boxWidth}px`,
              overflow: 'hidden',
              zIndex: 1,
              maxWidth: '90vw',
            },
          },
          options.render({ column, row, maxHeight }),
        ),
      default: () => {
        const imgs = (row[key || column.field] || []).filter((f) => f).slice(0, 4)
        return h(
          'div',
          {
            class: 'flex items-center',
            onClick: (event: MouseEvent) => {
              const ele = (event.target as HTMLElement).closest('.ant-dropdown-trigger')
              if (!ele) return
              const rect = ele.getBoundingClientRect()
              const domCenterPointX = rect.left + rect.width / 2
              const max = window.innerWidth * 0.76
              if (domCenterPointX > max) {
                boxWidth = domCenterPointX - 20
              } else {
                boxWidth = window.innerWidth - domCenterPointX - 20
              }
              maxHeight = event.clientY - 100
            },
          },
          [
            (imgs.length === 0 ? [undefined] : imgs).map((src) => {
              return h(EasyImage, { src, w: imgSize, h: imgSize, hidePreview: true, imgStyle: { objectFit: 'cover' }, class: 'mr-5' })
            }),
            h(
              'span',
              {
                class: 'cursor-pointer easy-ripple-button ml-auto mr-5',
              },
              row[key || column.field]?.length,
            ),
          ],
        )
      },
    },
  )
}

// 文案渲染
export const textRender = (_, params) => {
  const { column, row, options } = params
  const value = row[column.field]
  const rawOptions = column.cellRender ? column.cellRender.options : options || {}

  const _options: Record<string, any> = rawOptions && typeof rawOptions === 'object' && !Array.isArray(rawOptions) ? rawOptions : {}
  let text = value
  const color = _options.defaultColor || ''
  let style: Record<string, any> = { ...(_options.style || {}) }
  // 支持对象数据自定义样式
  if (typeof value === 'object' && value !== null) {
    text = value.text || value.label || value.value || ''
    if (value.color) style.color = value.color
    if (value.style) style = { ...style, ...value.style }
    if (value.bold) style.fontWeight = 'bold'
  }
  if (color) style.color = color
  if (_options.bold) style.fontWeight = 'bold'
  const tooltip: any = inject('tooltip')
  return h(
    'span',
    {
      style,
      onMouseenter: (event: MouseEvent) => {
        const node = (event.target as HTMLElement)?.parentNode as HTMLElement
        if (!node || (node.classList.contains('vxe-cell') && node.classList.contains('c--tooltip'))) return
        if (node.scrollWidth > node.clientWidth) {
          tooltip.open(
            {
              ...event,
              target: node,
            },
            text,
          )
        }
      },
      onMouseleave: () => {
        tooltip.close()
      },
    },
    [
      rawOptions?.icon
        ? h('span', {
            class: ['cursor-pointer mr-5', rawOptions.icon],
            onClick: () => rawOptions.iconClick({ row, column, cellValue: row[column.field] }),
          })
        : null,
      rawOptions?.textFormat ? rawOptions.textFormat({ cellValue: text }) : text,
    ],
  )
}

// 图片渲染
export const imageRender = (_, params) => {
  const imgSize = { 36: 28, 56: 40, 76: 60 }[params.column.cellRender?.height || params.$table?.props?.rowConfig?.height] || 28
  const src = params.row[params.column.field]
  return h(EasyImage, { src, h: imgSize, w: imgSize })
}

// 复制渲染
export const copyRender = (_, params) => {
  const { column, row, options } = params
  const value = row[column.field]
  const rawOptions = column.cellRender ? column.cellRender.options : options || {}
  const valueState = value instanceof Array ? value.join(',') : value
  if (!isEmpty(valueState, { rest: ['', ' ', ','] })) {
    return h('span', { class: 'w-full copy-cell flex items-center' }, [
      h('span', { class: 'overflow-hidden' }, [valueState]),
      rawOptions?.suffix,
      valueState !== '...' &&
        h(
          'span',
          { class: 'copy-cell-btn ml-auto mr-4' },
          h(CopyOutlined, {
            onClick: () => copyText(valueState),
          }),
        ),
    ])
  }
  return ''
}
