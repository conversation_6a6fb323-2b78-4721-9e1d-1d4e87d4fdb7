<template>
  <ContentPage
    :get-list="GetOaErpapiPurchaseoutList"
    @details="handleDetails"
    @sync="handleSyn"
    @export="handleExport"
    :sync-code="116002"
    :exportCode="-1"
    :btn-permission="btnPermission"
    v-model:form-arr="formArr"
    :page-type="PageTypeEnum.PurchaseOut"
    :sync-loading="syncLoading"
    ref="contentRef"
  >
    <PurchaseDetails @close="showDetails = false" ref="detailsRef" v-if="showDetails"></PurchaseDetails>
  </ContentPage>
</template>

<script lang="ts" setup>
import { message } from 'ant-design-vue'

import PurchaseDetails from './PurchaseDetails.vue'

import ContentPage from '../components/ContentPage.vue'

import { GetOaErpapiPurchaseoutList, GetPurchaseoutCreatorList, PurchaseReturnSyncNow } from '@/servers/JushuitanOrder'
import { GetWarehouses } from '@/servers/Purchaseapplyorder'
import { GetPageMRPSupplierCompanySelect } from '@/servers/BusinessCommon'

import { PageTypeEnum } from '@/enums/tableEnum'

const syncLoading = ref(false)
const showDetails = ref(false)

const { btnPermission } = usePermission()
const detailsRef = useTemplateRef('detailsRef')
const contentRef = useTemplateRef('contentRef')
//

const formArr = ref<any>([
  { label: '采购单编号', value: '', type: 'inputDlg', key: 'po_number' },
  { label: '退货单号', value: '', type: 'input', key: 'io_id' },
  { label: '商品编码', value: '', type: 'input', key: 'sku_id' },
  { label: '商品名称', value: '', type: 'input', key: 'sku_name' },
  {
    label: '供应商子公司',
    value: null,
    type: 'select-supplier',
    key: 'company_supplier_id',
    mode: 'single',
    api: GetPageMRPSupplierCompanySelect,
    apiParams: { is_contains_srs: true },
  },
  {
    label: '选择仓库',
    value: null,
    type: 'select',
    selectArr: [],
    key: 'wh_id',
  },
  {
    label: '创建人',
    value: null,
    type: 'select',
    selectArr: [],
    key: 'creator_id',
  },
  {
    label: '退货',
    value: null,
    type: 'range-picker',
    key: 'start_at',
    formKeys: ['start_time', 'end_time'],
    placeholder: ['创建开始时间', '创建结束时间'],
  },
  {
    label: '修改',
    value: null,
    type: 'range-picker',
    key: 'update_at',
    formKeys: ['update_start_time', 'update_end_time'],
    placeholder: ['修改开始时间', '修改结束时间'],
  },
])
useSearchForm(formArr)
const getOptions = async () => {
  const warehouses = await GetWarehouses()
  const creater = await GetPurchaseoutCreatorList()
  formArr.value.forEach((item) => {
    if (item.key === 'creator_id') {
      item.selectArr = creater.data.map((i) => ({ label: i.value, value: i.key })) || []
    }
    if (item.key === 'wh_id') {
      item.selectArr = warehouses.data.map((i) => ({ label: i.value, value: i.key })) || []
    }
  })
}
const handleSyn = async () => {
  syncLoading.value = true
  const res = await PurchaseReturnSyncNow().finally(() => {
    syncLoading.value = false
  })
  message.success(res.message)
  contentRef.value?.search()
}
const handleDetails = async (row: any) => {
  showDetails.value = true
  await nextTick()
  detailsRef?.value?.open(row)
}

const handleExport = () => {}

onMounted(() => {
  getOptions()
})
</script>

<style lang="scss" scoped>
//
</style>
