<template>
  <a-drawer v-model:open="show" width="700px" title="查看其他出入库单" @close="handleClose">
    <div v-if="loading" class="w-full flex h-full justify-center items-center">
      <a-spin tip="加载中..."></a-spin>
    </div>
    <div v-else>
      <div class="drawer-title">基本信息</div>
      <div class="w-full">
        <a-form :model="formData" :label-col="{ style: { width: '100px' } }">
          <a-row>
            <a-col :span="12">
              <a-form-item label="出仓单号">
                {{ formData.test }}
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="单据日期">
                {{ formData.test }}
              </a-form-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="12">
              <a-form-item label="单据状态">
                {{ formData.test }}
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="仓库编号">
                {{ formData.test }}
              </a-form-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="12">
              <a-form-item label="单据类型">
                {{ formData.test }}
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="仓库名">
                {{ formData.test }}
              </a-form-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="12">
              <a-form-item label="分仓编号">
                {{ formData.test }}
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="仓库编号">
                {{ formData.test }}
              </a-form-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="12">
              <a-form-item label="创建时间">
                {{ formData.test }}
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="修改时间">
                {{ formData.test }}
              </a-form-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="12">
              <a-form-item label="创建人">
                {{ formData.test }}
              </a-form-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="12">
              <a-form-item label="备注">
                {{ formData.test }}
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </div>
      <div class="drawer-title">明细信息</div>
      <div class="w-full">
        <a-table :pagination="false" :scroll="{ x: 1000 }" bordered :data-source="tableData" :columns="tableCoulmns"></a-table>
      </div>
    </div>
    <template #footer>
      <a-button @click="handleClose">关闭</a-button>
    </template>
  </a-drawer>
</template>

<script lang="ts" setup>
const emits = defineEmits(['close'])

const formData = ref({
  test: '*********',
})

const loading = ref(false)

const tableData = ref([{ id: 1, code: '123', styleCode: '123', name: 'test', colorAndSize: '123', qty: 1, price: 1, amount: 1, companyNumber: '321', remark: 'test' }])

const tableCoulmns = [
  { title: '明细id', dataIndex: 'id', key: 'id', width: 80 },
  { title: '商品编码', dataIndex: 'code', key: 'code', width: 120 },
  { title: '款式编码', dataIndex: 'styleCode', key: 'styleCode', width: 100 },
  { title: '商品名称', dataIndex: 'name', key: 'name', width: 100 },
  { title: '颜色及规格', dataIndex: 'colorAndSize', key: 'colorAndSize', width: 100 },
  { title: '单价', dataIndex: 'price', key: 'price', width: 100 },
  { title: '数量', dataIndex: 'qty', key: 'qty', width: 100 },
  { title: '金额', dataIndex: 'amount', key: 'amount', width: 100 },
  { title: '公司编号', dataIndex: 'companyNumber', key: 'companyNumber', width: 120 },
  { title: '备注', dataIndex: 'remark', key: 'remark', width: 150 },
]

const handleClose = () => {
  emits('close')
}
//
const show = ref(false)
const open = (data) => {
  show.value = true
  console.log(data)
}

defineExpose({
  open,
})
</script>

<style lang="scss" scoped>
//
</style>
