import { request } from './request'
// 查询列表
export const List = (data) => {
  return request({ url: '/api/LogisticsCompany/GetLogisticsCompanyList', data })
}

// 同步
export const GetAliCompany = () => {
  return request({ url: '/api/SyncData/SyncAllLogisticsCompany' }, 'GET')
}

// 获取详情
export const Detail = (data) => {
  return request({ url: `/api/LogisticsCompany/GetLogisticsCompanyById?id=${data.id}`, data }, 'GET')
}

// 编辑
export const Edit = (data) => {
  return request({ url: '/api/LogisticsCompany/Update', data })
}

// 删除
export const Delete = (data) => {
  return request({ url: '/api/LogisticsCompany/BatchDelete', data })
}
