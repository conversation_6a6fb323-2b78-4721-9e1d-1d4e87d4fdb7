<template>
  <a-drawer :footer="logVisble ? undefined : false" v-model:open="detailVisible" width="45vw" title="查看用户" placement="right" :maskClosable="false" :footer-style="{ textAlign: 'left' }">
    <div class="detailBox">
      <a-form v-if="target">
        <div class="drawer-title">帐号：{{ target.user_name }}</div>
        <a-form-item label="类型">
          <span class="detailValue">{{ target.scope == 2 ? '外' : '内' }}部联系人</span>
        </a-form-item>
        <a-form-item label="用户名称">
          <span class="detailValue">{{ target.real_name }}</span>
        </a-form-item>
        <a-form-item :label="`所属${scope == 2 ? '供应商' : '公司'}`">
          <span v-show="scope == 1" class="detailValue">{{ target.sub_company_name ? target.sub_company_name : '--' }}</span>
          <span v-show="scope == 2" class="detailValue">{{ target.customer_id ? options[target.customer_id] : '--' }}</span>
        </a-form-item>
        <a-form-item label="所在部门">
          <span class="detailValue">{{ target.deptName ? target.deptName : '--' }}</span>
        </a-form-item>
        <a-form-item label="岗位">
          <span class="detailValue">{{ target.job_title_name ? target.job_title_name : '--' }}</span>
        </a-form-item>
        <a-form-item label="电子邮箱">
          <span class="detailValue">{{ target.email ? target.email : '--' }}</span>
        </a-form-item>
        <a-form-item label="角色">
          <span class="detailValue">{{ target.role_names ? target.role_names[0] : '--' }}</span>
        </a-form-item>
        <a-form-item label="状态">
          <span class="detailValue">{{ target.status == 0 ? '停用' : '启用' }}</span>
        </a-form-item>
        <div class="drawer-title">其他信息</div>
        <div style="display: flex">
          <a-form-item label="创建时间">
            <div class="detailValue w200">{{ target.create_at }}</div>
          </a-form-item>
          <!-- <a-form-item label="创建人">
            <a-tooltip :overlayStyle="{ maxWidth: 'none' }">
              <template #title>
                <div>用户名称：{{ target.user_name }}</div>
                <div v-show="target.create_user_scope == 1">所属公司：{{ target.create_user_company_name ? target.create_user_company_name : '--' }}</div>
                <div v-show="target.create_user_scope != 1">所属客户：{{ target.customer_id ? options[target.supplier_id] : '--' }}</div>
                <div>所在部门：{{ target.create_user_department ? target.create_user_department : '--' }}</div>
                <div>
                  岗
                  <span style="visibility: hidden">占位</span>
                  位：{{ target.create_user_jobtitlename ? target.create_user_jobtitlename : '--' }}
                </div>
              </template>
              <div style="display: flex; align-items: center">
                <span class="detailValue">{{ target.user_name ? target.user_name : '--' }}</span>
                <span v-if="target.create_user_department || target.create_user_jobtitlename" class="detailValueDescription">
                  （
                  <span v-if="target.create_user_jobtitlename">{{ target.create_user_jobtitlename }}&nbsp;|&nbsp;</span>
                  <span v-if="target.create_user_department">{{ target.create_user_department.length > 10 ? target.create_user_department.slice(0, 10) + '...' : target.create_user_department }}</span>
                  ）
                </span>
              </div>
            </a-tooltip>
          </a-form-item> -->
        </div>
        <div style="display: flex">
          <a-form-item label="最后修改时间">
            <div class="detailValue w200">{{ target.update_at }}</div>
          </a-form-item>
          <!-- <a-form-item label="最后修改人">
            <a-tooltip :overlayStyle="{ maxWidth: 'none' }">
              <template #title>
                <div>用户名称：{{ target.user_name }}</div>
                <div v-show="target.update_user_scope == 1">所属公司：{{ target.update_user_company_name ? target.update_user_company_name : '--' }}</div>
                <div v-show="target.update_user_scope != 1">所属客户：{{ target.update_user_platform_user_name ? target.update_user_platform_user_name : '--' }}</div>
                <div>所在部门：{{ target.update_user_department ? target.update_user_department : '--' }}</div>
                <div>
                  岗
                  <span style="visibility: hidden">占位</span>
                  位：{{ target.update_user_jobtitlename ? target.update_user_jobtitlename : '--' }}
                </div>
              </template>
              <div style="display: flex; align-items: center">
                <span class="detailValue">{{ target.user_name ? target.user_name : '--' }}</span>
                <span v-if="target.update_user_department || target.update_user_jobtitlename" class="detailValueDescription">
                  （
                  <span v-if="target.update_user_jobtitlename">{{ target.update_user_jobtitlename }}&nbsp;|&nbsp;</span>
                  <span v-if="target.update_user_department">{{ target.update_user_department.length > 10 ? target.update_user_department.slice(0, 10) + '...' : target.update_user_department }}</span>
                  ）
                </span>
              </div>
            </a-tooltip>
          </a-form-item> -->
        </div>
        <a-form-item label="来源">
          <span class="detailValue">{{ target.source_type ? formatOptionLabel(target.source_type, sourceOption) : '--' }}</span>
        </a-form-item>
      </a-form>
    </div>
  </a-drawer>
  <!-- 日志 -->
</template>

<script lang="ts" setup>
import { sourceOption } from '@/common/options'
import { formatOptionLabel } from '@/utils'

const detailVisible = ref(false)
const logVisble = ref(false)
const target = ref<any>({})
const scope = ref(null)
const options = ref({})
const open = (row, scope1, selectMap) => {
  target.value = null
  detailVisible.value = true
  scope.value = scope1
  target.value = row
  options.value = selectMap

  // ;(scope1 == 2 ? DetailsExtra({ id }) : DetailsInner({ id }))
  //   .then((res) => {
  //     target.value = res.data
  //     detailloading.value = false
  //   })
  //   .catch(() => {
  //     detailloading.value = false
  //   })
}

// 暴露方法
defineExpose({
  open,
})
</script>

<style lang="scss" scoped>
::v-deep(.ant-form-item) {
  align-items: flex-start;

  .ant-form-item-label {
    display: flex;
    justify-content: flex-end;
    width: 110px;
    min-width: 110px;
    margin-right: 30px;

    label {
      &::after {
        display: none !important;
      }
    }
  }
}

.detailTitle {
  padding-left: 12px;
  margin-bottom: 20px;
  font-weight: bold;
  color: #000;
}

.w350 {
  width: 350px;
}

.w250 {
  width: 250px;
}

.w150 {
  width: 150px;
}

.w200 {
  width: 200px;
}

.description {
  padding-left: 20px;
  font-size: 12px;
  color: rgb(0 0 0 / 50%);
  white-space: nowrap;
}

.detailValueDescription {
  font-size: 12px;
  color: rgb(0 0 0 / 50%);
}

.detailBox {
  padding-top: 12px;

  .loadingIcon {
    font-size: 30px;
    color: #1890ff;
  }
}
</style>
