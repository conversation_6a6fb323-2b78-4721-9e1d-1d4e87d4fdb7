<template>
  <a-drawer :maskClosable="false" title="匹配" width="70vw" :visible="visible" @close="visible = false" :bodyStyle="{ padding: '0' }">
    <div class="flex h-full">
      <div class="w-full overflow-y-auto p-16">
        <a-form :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }" :model="form" ref="formRef">
          <a-row>
            <a-col :span="12">
              <a-form-item label="1688商品链接" name="ali_product_url" :rules="[{ required: true }]">
                <a-input v-model:value="form.ali_product_url" disabled />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="" name="name">
                <a-button type="primary" disabled>解析链接</a-button>
              </a-form-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="12">
              <a-form-item label="系统供应商子公司" name="company_supplier_id" :rules="[{ required: true, message: '请选择系统供应商子公司' }]">
                <a-select
                  v-model:value="form.company_supplier_id"
                  placeholder="请选择供应商子公司"
                  :filter-option="filterOption"
                  show-search
                  clearable
                  :options="supplierOptions"
                  @change="(e) => changeSupper(e, form)"
                  disabled
                />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>

        <div class="flex">
          <div class="w800px">
            <a-space class="mb-12" :size="16">
              <a-input placeholder="规格" v-model:value="queryForm.type_specification" :maxlength="200" />
              <a-button type="primary" @click="filterDetailList(queryForm.type_specification)">查询</a-button>
              <a-button type="primary" @click="filterDetailList()">重置</a-button>
            </a-space>
            <div class="flex">
              <div class="max-h-650px overflow-auto w-full">
                <SimpleTable ref="LeftTableRef" :table-key="tableColumns" :data="detailList" :isCheckbox="true" :rowHeight="100">
                  <template #image_url="{ row }">
                    <EasyImage :src="row.image_url"></EasyImage>
                  </template>
                  <template #ali_image_url="{ row }">
                    <EasyImage :src="row.ali_image_url"></EasyImage>
                  </template>
                  <template #type_specification="{ row }">
                    <div>
                      <div>{{ form.style_code }}</div>
                      <div>{{ row.k3_sku_id }}</div>
                      <div>{{ row.sku_name }}</div>
                      <div>{{ row.type_specification }}</div>
                    </div>
                  </template>
                  <template #ali_type_specification="{ row }">
                    <div>
                      <p>{{ row.ali_sku_name }}</p>
                      <p>{{ row.ali_type_specification }}</p>
                    </div>
                  </template>
                  <template #srm_purchase_quantity="{ row }">
                    <a-input-number v-model:value="row.srm_purchase_quantity" :disabled="!row.ali_type_specification" :precision="0" :min="1" :max="99999" />
                  </template>
                  <template #ali_purchase_quantity="{ row }">
                    <a-input-number v-model:value="row.ali_purchase_quantity" :disabled="!row.ali_type_specification" :precision="0" :min="1" :max="99999" />
                  </template>
                  <template #operate="{ row }">
                    <a-button v-if="row.ali_type_specification" @click="removeMapBtn(row)">取消</a-button>
                  </template>
                </SimpleTable>
              </div>
            </div>
          </div>
          <div class="flex-1 ml-10px">
            <a-space class="mb-12" :size="16">
              <a-input placeholder="规格" v-model:value="rightQueryForm.ali_type_specification" :maxlength="200" />
              <a-button type="primary" @click="filterRightList(rightQueryForm.ali_type_specification)">查询</a-button>
              <a-button type="primary" @click="filterRightList('')">重置</a-button>
            </a-space>
            <div class="flex">
              <div class="max-h-650px overflow-auto w-full">
                <SimpleTable ref="RightTableRef" :table-key="rightColumns" :data="rightList">
                  <template #ali_sku_image_url="{ row }">
                    <a-image :width="50" :src="row.ali_sku_image_url" :preview="{ maskClassName: '预览' }" referrerpolicy="no-referrer" />
                  </template>
                  <template #operate="{ row }">
                    <a-button @click="mapProductBtn(row)">选择</a-button>
                  </template>
                </SimpleTable>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <template #footer>
      <a-space :size="16">
        <a-button type="primary" @click="editBtn">确认</a-button>
        <a-button @click="visible = false">取消</a-button>
      </a-space>
    </template>
  </a-drawer>
</template>

<script setup lang="ts">
import { message } from 'ant-design-vue'

import { filterOption } from '@/utils/index'
import SimpleTable from '@/components/SimpleTable.vue'

import { GetAliProductMapInfo, GetAli1688ProductList, UpdateAliProductMapInfo } from '@/servers/ProductMapping'
import { GetK3SkuInfo } from '@/servers/Purchaseprice'
import { Get1688MRPSupplierCompanySelect } from '@/servers/BusinessCommon'

const emit = defineEmits(['search'])

const formRef = ref()
const LeftTableRef = ref()
const RightTableRef = ref()
// 表单
const form = ref<any>({
  ali_product_url: null,
  company_supplier_id: null,
  ali_seller_login_id: null,

  style_code: null,

  ali_style_code: null,
  ali_sku_name: null,
  brand: null,
})

const queryForm = ref({
  type_specification: '',
})

const rightQueryForm = ref({
  ali_type_specification: '',
})

const tableColumns = ref([
  { title: '本地图片', field: 'image_url', width: '' },
  { title: '本地商品信息', field: 'type_specification', width: '' },
  { title: '映射商品图片', field: 'ali_image_url', width: '' },
  { title: '映射商品信息', field: 'ali_type_specification', width: '' },
  { title: 'SRM系统数量', field: 'srm_purchase_quantity', width: '120' },
  { title: '1688采购数量', field: 'ali_purchase_quantity', width: '120' },
  { title: '操作', field: 'operate', width: '' },
])

const rightColumns = ref([
  { title: '1688商品图片', field: 'ali_sku_image_url', width: '' },
  { title: '1688商品信息', field: 'ali_type_specification', width: '' },
  { title: '操作', field: 'operate', width: '' },
])

const detailList = ref<any[]>([])
const detailListCache = ref<any[]>([])
const rightList = ref<any[]>([])
const rightListCache = ref<any[]>([])

// 显示
const visible = ref(false)

const supplierOptions = ref<any[]>([])

// 获取供应商
const getSupplierList = async () => {
  const res = await Get1688MRPSupplierCompanySelect({ type: 2 })
  supplierOptions.value = res.data.map((i) => ({ label: i.label, value: +i.value }))
}

const changeSupper = (e, item) => {
  item.supplier = supplierOptions.value.find((titem) => titem.value === item.company_supplier_id)?.label
}

const getSameProduct = () => {
  const obj = { page: 1, pageSize: 999, style_code: form.value.style_code }
  GetK3SkuInfo(obj).then((res) => {
    res.data.list.forEach((item) => {
      const obj = {
        k3_sku_id: item.sku_id,
        jst_sku_id: item.jst_sku_id,
        sku_name: item.sku_name,
        ali_sku_id: null,
        type_specification: item.specification,
        ali_type_specification: null,
        image_url: null,
        ali_image_url: null,
        srm_purchase_quantity: null,
        ali_purchase_quantity: null,
        id: null,
        data_status: null,
        ali_sku_name: null,
      }
      detailListCache.value.push(obj)
    })
    getHasMapList()
  })
}

const getHasMapList = () => {
  const obj = { id: form.value.id }
  GetAliProductMapInfo(obj).then((res) => {
    const arr = res.data.aliProductMapDetails
    if (arr?.length > 0) {
      detailListCache.value.forEach((item) => {
        const obj = res.data.aliProductMapDetails.find((v) => v.k3_sku_id == item.k3_sku_id)
        if (obj) {
          item.id = obj.id
          item.ali_sku_id = obj.ali_sku_id
          item.ali_image_url = obj.ali_image_url
          item.ali_type_specification = obj.ali_type_specification
          item.srm_purchase_quantity = obj.srm_purchase_quantity
          item.ali_purchase_quantity = obj.ali_purchase_quantity
          item.ali_sku_name = obj.ali_sku_name
        }
      })
      filterDetailList()
    }
  })
}

const getLeftList = async () => {
  await getSameProduct()
}

const getRightList = () => {
  const obj = { ali_product_url: form.value.ali_product_url }
  GetAli1688ProductList(obj).then((res) => {
    if (res.success) {
      rightList.value = []
      rightListCache.value = []
      rightListCache.value = res.data.ali_1688_product_map_detail_list
      filterRightList('')
    } else {
      message.error(res.message)
    }
  })
}

const filterDetailList = (val = '') => {
  if (!val) {
    queryForm.value.type_specification = ''
  }
  detailList.value = detailListCache.value.filter((v) => v.type_specification.indexOf(val) != -1)
  detailList.value = sortByIdNotEmpty(detailList.value)
}
const sortByIdNotEmpty = (arr) => {
  return arr.sort((a, b) => {
    const aValid: any = a.id !== null
    const bValid: any = b.id !== null
    return bValid - aValid
  })
}

const filterRightList = (val = '') => {
  if (!val) {
    rightQueryForm.value.ali_type_specification = ''
  }
  rightList.value = rightListCache.value.filter((v) => v.ali_type_specification.indexOf(val) != -1)
}

const mapProductBtn = (row) => {
  const ids = LeftTableRef.value.checkItemsArr.map((v) => v.k3_sku_id)
  console.log(ids, '已选的')

  if (ids.length > 0) {
    detailList.value.forEach((item) => {
      if (ids.includes(item.k3_sku_id)) {
        item.ali_sku_id = row.ali_sku_id
        item.ali_image_url = row.ali_sku_image_url
        item.ali_type_specification = row.ali_type_specification
        item.srm_purchase_quantity = 1
        item.ali_purchase_quantity = 1
        item.ali_start_quantity = row.ali_start_quantity
      }
    })
    LeftTableRef.value.checkItemsArr = []
    LeftTableRef.value.tableRef.clearCheckboxRow()
    console.log(LeftTableRef.value.checkItemsArr, '勾选的')
  } else {
    message.info('请勾选数据后再操作！')
  }
}

const removeMapBtn = (row) => {
  const obj = detailList.value.find((item) => item.k3_sku_id == row.k3_sku_id)
  obj.ali_sku_id = null
  obj.ali_image_url = null
  obj.ali_type_specification = null
  obj.srm_purchase_quantity = null
  obj.ali_purchase_quantity = null
  obj.ali_sku_name = null
}

const checkDetailList = () => {
  return detailList.value.some((item, index) => {
    if (item.ali_type_specification && (!item.srm_purchase_quantity || !item.ali_purchase_quantity)) {
      message.error(`第${index + 1}行的SRM系统数量和1688采购数量不能为空`)
      return true
    }
    if (item.ali_type_specification && !(item.srm_purchase_quantity == 1 || item.ali_purchase_quantity == 1)) {
      message.error(`第${index + 1}行的SRM系统数量和1688采购数量至少有一个为1`)
      return true
    }
    return false
  })
}

const checkNullList = () => {
  const hasMapArr = detailList.value
    .map((item) => {
      return item.ali_type_specification
    })
    .filter((item) => item)
  if (hasMapArr.length == 0) {
    message.error(`最少需要映射一个商品`)
    return true
  }
  return false
}

const editBtn = async () => {
  await filterDetailList()
  const isErrorValue = checkDetailList()
  const isErrorNull = checkNullList()
  if (!isErrorValue && !isErrorNull) {
    try {
      await formRef.value?.validateFields()
      const arr = detailList.value
        .filter((v) => !(!v.id && !v.ali_sku_id))
        .map((item) => {
          if (!item.id) {
            item.id = 0
            item.data_status = 1
          }
          if (item.id && item.ali_sku_id) {
            item.data_status = 2
          }
          if (item.id && !item.ali_sku_id) {
            item.data_status = 3
          }
          return item
        })
      const obj = {
        id: form.value.id,
        aliProductMapDetails: arr,
      }
      console.log(obj)
      const params = JSON.parse(JSON.stringify(obj))
      UpdateAliProductMapInfo(params).then(() => {
        message.success('提交成功！')
        visible.value = false
        emit('search')
      })
    } catch (errorInfo) {
      console.log('Failed:', errorInfo)
    }
  }
}

// 打开
const open = async (row) => {
  formRef.value?.resetFields()
  getSupplierList()
  form.value = {
    id: row.id,
    style_code: row.style_code,
    ali_product_url: row.ali_product_url,
    company_supplier_id: row.company_supplier_id,
  }
  queryForm.value = {
    type_specification: '',
  }
  rightQueryForm.value = {
    ali_type_specification: '',
  }
  detailList.value = []
  detailListCache.value = []
  rightList.value = []
  rightListCache.value = []
  await getLeftList()
  await getRightList()
  visible.value = true

  nextTick(() => {
    LeftTableRef.value.checkItemsArr = []
    LeftTableRef.value.tableRef.clearCheckboxRow()
    console.log(LeftTableRef.value.checkItemsArr)
  })
}

defineExpose({
  open,
})
</script>

<style scoped lang="scss"></style>
