<template>
  <CheckPaymentOrder :key="componentKey" ref="checkPaymentOrderRef" :view-type="DetailTypeEnum.ADD" :order-type="'BATCH_PAYMENT'" :bill-ids="billIds" @close="handleClose" @query="handleQuery" />
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'

import { DetailTypeEnum } from '@/common/enum'

import CheckPaymentOrder from '@/views/pageComponents/paymentOrder/components/CheckPaymentOrder.vue'

import { GetBillBuildPayOrderInfo } from '@/servers/paymentOrder'

// Props
interface Props {
  billIds?: number[]
}

const props = withDefaults(defineProps<Props>(), {
  billIds: () => [],
})

// Emits
const emits = defineEmits<{
  close: []
  query: []
}>()

// 从props获取账单ID
const billIds = computed(() => {
  return props.billIds
})

// Refs
const checkPaymentOrderRef = ref()
const componentKey = ref(0) // 用于强制重新渲染组件

// 处理关闭
const handleClose = () => {
  // 增加key值，确保下次打开时是新的组件实例，避免缓存问题
  componentKey.value++
  emits('close')
}

// 处理查询
const handleQuery = () => {
  emits('query')
}

// 初始化数据
const initBatchPaymentData = async () => {
  if (!billIds.value || billIds.value.length === 0) {
    console.warn('批量请款：没有传入账单ID')
    return
  }

  try {
    // 调用GetBillBuildPayOrderInfo接口获取账单详情
    const response = await GetBillBuildPayOrderInfo(billIds.value)

    if (response?.success && response.data) {
      const billData = response.data
      console.log('批量请款数据:', billData)

      // 先打开CheckPaymentOrder组件，然后设置数据
      if (checkPaymentOrderRef.value) {
        // 先打开抽屉
        await checkPaymentOrderRef.value.open(DetailTypeEnum.ADD)

        // 等待组件打开后再设置数据
        if (checkPaymentOrderRef.value) {
          // 传递response.data，保持原有的数据结构
          checkPaymentOrderRef.value.setBatchPaymentData(billData)
        }
      }
    } else {
      console.error('获取批量请款数据失败:', response?.message)
    }
  } catch (error) {
    console.error('批量请款初始化失败:', error)
  }
}

// 组件挂载时初始化数据
onMounted(() => {
  //   console.log('BatchPaymentOrder mounted, billIds:', billIds.value)
  initBatchPaymentData()
})
</script>

<style scoped>
.batch-payment-order {
  width: 100%;
  height: 100vh;
}
</style>
