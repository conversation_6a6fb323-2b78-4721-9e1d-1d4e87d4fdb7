import { formProps } from 'ant-design-vue/es/form/Form'
import { Form, Row, Col, Button } from 'ant-design-vue'
import { InfoCircleOutlined } from '@ant-design/icons-vue'

import { isEmpty } from '@/utils/index'
import Upload from '@/components/Upload.vue'
import FileModal from '@/components/FileModal.vue'

import type { FormItemProps, FormItemSelectProps, FormItemModelProps, FormItemUploadProps, FormItemSelectSupplierProps } from './index.d'

import SelectSupplier from '../business/SelectSupplier'

function hasTypeAndProps(item: FormItemProps): item is FormItemProps & { type: string; props?: object } {
  return 'type' in item
}

function hasTypeOfTitle(item: FormItemProps): item is FormItemProps & { type: 'title'; title?: string } {
  return 'type' in item && item.type === 'title'
}

type ComponentMap = Record<string, { default: DefineComponent }>

export default defineComponent({
  name: 'EasyForm',
  emits: ['update:form', 'search', 'reset'],
  props: {
    ...formProps(),
    mode: {
      type: String as PropType<'search' | 'detail' | 'form' | 'new'>,
      default: 'form',
    },
    formItems: {
      type: Array as PropType<FormItemProps[]>,
      default: () => [],
    },
    labelWidth: {
      type: Number,
      default: 120,
    },
    form: {
      type: Object as PropType<Record<string, any>>,
      default: () => ({}),
    },
  },
  setup(props, { slots, emit }) {
    const tooltip: any = inject('tooltip')
    const componentList: ComponentMap = import.meta.glob('./components/*.tsx', {
      eager: true,
    })

    const formMode = computed(() => props.mode)

    const validateKeys = ref<string[]>([])
    const validateTimer = ref()
    const validateFields = (keys: string[]) => {
      validateKeys.value = [...validateKeys.value, ...keys]
      clearTimeout(validateTimer.value)
      validateTimer.value = setTimeout(() => {
        formRef.value.validateFields(validateKeys.value)
        validateKeys.value = []
      }, 30)
    }

    const getApiData = async (item, params = {}, callback?: (res: any) => void) => {
      if (item.type === 'select' && item.api) {
        if (item.api instanceof Function) {
          try {
            const res = await item.api({
              ...props.form,
              ...params,
            })
            const formatter = (f) => ({
              value: `${f.key || f.value}`,
              label: f.label || f.value,
            })
            item.options = (res?.data?.list || res?.data || []).map(item.optionFormatter || formatter)
            callback && callback(res)
          } catch (error) {
            console.error('error', error)
            callback && callback(error)
          }
          return
        }
      }
      callback && callback({})
    }
    const changeMode = () => {
      for (const item of props.formItems) {
        if (props.mode === 'detail') {
          item.isDetail = true
        } else {
          item.isDetail = false
        }
        getApiData(item, {}, (res) => {
          const selectItem = item as FormItemSelectProps
          selectItem.apiCallBack && selectItem.apiCallBack(res)
        })
      }
    }
    watch(
      () => props.mode,
      () => {
        changeMode()
      },
      {
        immediate: true,
      },
    )

    const setFormItemProps = (item: FormItemProps) => {
      const { label, labelTip, labelCol, wrapperCol, rules, required, isDetail, ...other } = item
      const placeholderBefore = 'type' in item && ['select', 'date', 'radio'].includes(item.type) ? '选择' : '输入'
      const defaultRules =
        required && !isDetail
          ? [
              {
                required: true,
                message: `请${placeholderBefore}${label}`,
              },
            ]
          : []

      const result: any = {
        label:
          formMode.value === 'search'
            ? ''
            : label
              ? h(
                  'span',
                  {
                    class: ['flex items-center'],
                  },
                  [
                    labelTip &&
                      h(InfoCircleOutlined, {
                        class: ['mr-5 cursor-pointer'],
                        onMouseenter: (event) => {
                          tooltip.open(event, labelTip)
                        },
                        onMouseleave: () => {
                          tooltip.close()
                        },
                      }),
                    label,
                  ],
                )
              : null,
        labelCol: labelCol ?? { style: { width: `${props.labelWidth}px` } },
        rules: rules ?? defaultRules,
        required: Boolean(required && !isDetail),
        wrapperCol: wrapperCol || {},
      }
      result.name = other.name
      for (const key of Object.keys(other)) {
        if (key in Form.Item.props) {
          result[key] = other[key]
        }
      }
      return result
    }
    const changeItem = (name, params) => {
      const item = props.formItems.find((f) => [f.name, hasTypeOfTitle(f) && f.title].includes(name))
      if (item) {
        for (const key of Object.keys(params)) {
          item[key] = params[key]
        }
      }
    }
    const changeValue = async (name, value, isChange = false, ignoreChangeList: string[] = [], linkKeys?: string[]) => {
      // 单个时
      if (typeof name === 'string') {
        const item = props.formItems.find((f) => f.name === name)
        if (item) {
          const targetValue = item.dataType === 'string' ? (value || '').toString() : value
          await emitValue(targetValue, item, linkKeys)
          if (isChange) {
            setTimeout(() => {
              const onChange = hasTypeAndProps(item) && (item.props as any)?.onChange
              onChange && onChange(value)
            })
          }
        }
      } else {
        // 入参为对象时
        const aliasData = {}
        for (const [itemName, value] of Object.entries(name)) {
          const item = props.formItems.find((f) => f.alias === itemName)
          if (item?.alias === itemName) {
            aliasData[item.alias] = value
          }
        }
        const dataKeys = Object.keys(name)
        emit('update:form', {
          ...props.form,
          ...aliasData,
        })
        await nextTick()
        for (const [itemName, value] of Object.entries(name)) {
          const _isChange = isChange ? !ignoreChangeList.includes(itemName) : false
          await changeValue(itemName, value, _isChange, [], dataKeys)
        }
      }
    }

    // const timer = ref()
    const emitValue = async (value, item, links?: string[]) => {
      // clearTimeout(timer.value)
      const formState = {
        ...props.form,
        [item.name]: value,
      }
      const linkage = item.linkage ? [item.linkage].flat(1) : []
      if (linkage?.length) {
        for (const itemName of linkage) {
          if (!(links || []).includes(itemName)) {
            formState[itemName] = undefined
          }
          const found = props.formItems.find((f) => f.name === itemName)
          if (found) {
            const selectItem = found as FormItemSelectProps
            const apiIsFn = selectItem.api instanceof Function
            if (apiIsFn) {
              selectItem.options = []
              const { data } = await selectItem.api!(formState)
              const formatter = (f) => ({
                value: `${f.key || f.value}`,
                label: f.label || f.value,
              })
              selectItem.options = data.map(formatter)
            }
            if (found.linkageFn) {
              found.linkageFn({
                form: formState,
                items: props.formItems,
                item: found,
                option: item.options.find((f) => f.value === value),
                name: item.name,
              })
            }
          }
        }
      }
      emit('update:form', formState)
      validateFields([item.name, ...linkage])
    }

    const renderItem = (item: FormItemModelProps) => {
      const type = item.isDetail && !['upload'].includes(item.type) ? 'text' : 'type' in item ? item.type : undefined
      const innerProps: Record<string, unknown> = hasTypeAndProps(item) && item.props ? (item.props as Record<string, unknown>) : ({} as Record<string, unknown>)

      const vForm = props.form
      const formCommonProps = () => {
        return {
          disabled: item.disabled,
          ...innerProps,
          value: props.form[item.name as string],
          'onUpdate:value': async (value) => {
            await emitValue(value, item)
          },
        }
      }
      if (type === 'text') {
        return h(
          'div',
          {
            class: ['mt-4', 'min-h-24', 'break-all'],
          },
          item.textFormat
            ? item.textFormat({
                item,
                form: props.form,
                items: props.formItems,
                value: props.form[item.name as string],
              })
            : isEmpty(props.form[item.alias || item.name || ''], {
                rest: ['', item.props?.precision ? '' : 0],
                format: (flag, value) => {
                  if (flag) return item.emptyText || ''
                  if (item.props?.precision) {
                    return Number(value ?? 0).roundNext(item.props.precision)
                  }
                  return value
                },
              }),
        )
      }
      if (type === 'upload') {
        const uploadItem = item as FormItemUploadProps
        const uploadProps = {
          value: props.form[uploadItem.name as string],
          'onUpdate:value': async (value) => {
            await emitValue(value, uploadItem)
          },
          ...(uploadItem.accept && { accept: uploadItem.accept }),
          ...(uploadItem.module && { module: uploadItem.module }),
        }
        return item.isDetail ? h(FileModal, { list: props.form[uploadItem.alias as string] || [] }) : <Upload {...uploadProps}></Upload>
      }
      if (type === 'selectSupplier') {
        const _item = item as FormItemSelectSupplierProps
        const _props = {
          value: props.form[_item.name as string],
          'onUpdate:value': async (value) => {
            await emitValue(value, _item)
          },
          label: props.form[(_item.alias || `${item.name}_alias`) as string],
          'onUpdate:label': (value) => {
            const alias = {
              [_item.alias || `${item.name}_alias`]: value,
            }
            setTimeout(() => {
              emit('update:form', {
                ...props.form,
                ...alias,
              })
            })
          },
          disabled: _item.disabled,
          title: `请选择${_item.label}`,
          ...(_item.mode && { mode: _item.mode }),
          ...(typeof _item.labelInValue == 'boolean' && { labelInValue: _item.labelInValue }),
        }
        return <SelectSupplier {..._props} />
      }

      const isInput = ['textarea', 'number'].includes(item.type)
      if (componentList[`./components/${isInput ? 'input' : type}.tsx`]) {
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const { props, span, type, hide, required, ..._item } = item
        const onEmits = {
          onGetApiData: (value, callback) => {
            getApiData(item, { keyword: value }, callback)
          },
        }
        return h(componentList[`./components/${isInput ? 'input' : type}.tsx`].default, {
          innerProps: formCommonProps(),
          item: {
            ..._item,
            placeholder: formMode.value === 'search' ? _item.label : _item.placeholder,
          },
          form: vForm,
          ...(['select'].includes(type) ? onEmits : {}),
          ...(isInput ? { type } : {}),
        })
      }
    }
    const formRef = ref()
    const validate = (names?: string[], ignoreNames?: string[]) => {
      formRef.value.clearValidate()
      return new Promise((resolve, reject) => {
        const ignoreValues = {}
        const validateNames =
          names ||
          props.formItems
            .filter((f) => {
              const hasType = hasTypeAndProps(f) && !['text'].includes(f.type)
              const isPass = (ignoreNames || [])?.includes(f.name as string)
              if (isPass) {
                ignoreValues[f.name as string] = props.form[f.name as string]
              }
              return isPass ? false : f.name && (hasType || f.required) && !f.isDetail && !f.hide
            })
            .map((f) => f.name)
        formRef.value
          .validateFields(validateNames)
          .then((res) => {
            resolve({ ...res, ...ignoreValues })
          })
          .catch((err) => {
            reject(err)
          })
      })
    }

    const resetFields = () => {
      formRef.value.resetFields()
      emit('reset')
    }
    const renderVN = () => {
      const onSearch = async () => {
        const res = await validate()
        emit('search', res)
      }
      return (
        <Form ref="formRef" layout={formMode.value === 'search' ? 'inline' : 'horizontal'} model={props.form} class={['easyForm']}>
          <Row class="w-full" wrap={true} gutter={props.mode !== 'search' ? 20 : 0}>
            {props.formItems.map((item) => {
              const span = item.span || 8
              const render = [
                <Col class={{ 'mb-8': props.mode === 'search' }} span={span} key={item.name} flex={props.mode !== 'search' ? undefined : `0 1 ${(item.width || 100) + 10}px`}>
                  {{
                    default: () => {
                      return 'slot' in item && slots[item.slot]
                        ? h(Form.Item as any, setFormItemProps(item), {
                            default: () => slots[item.slot]?.({ prop: item }),
                          })
                        : hasTypeOfTitle(item)
                          ? h(
                              'div',
                              {
                                class: 'py-8 px-16 bg-#f5f7fe flex items-center text-sm font-bold my-8 rounded mt-10 mb-20',
                                style: { 'border-left': '4px solid #1890FF' },
                              },
                              item.title,
                            )
                          : hasTypeAndProps(item) && item.type === 'empty'
                            ? h('div', { class: 'h-20' })
                            : h(Form.Item as any, setFormItemProps(item), {
                                default: () => {
                                  return renderItem(item as FormItemModelProps)
                                },
                              })
                    },
                  }}
                </Col>,
              ]
              if (item.fill) {
                render.push(<Col span={item.fill}></Col>)
              }
              return item.hide ? null : render
            })}
            {formMode.value === 'search' && [
              <Button type="primary" class="ml mr" onClick={onSearch}>
                查询
              </Button>,
              <Button type="default" class="ml mr" onClick={resetFields}>
                重置
              </Button>,
            ]}
          </Row>
        </Form>
      )
    }

    return {
      renderVN,
      changeValue,
      formRef,
      validate,
      changeItem,
      resetFields,
      changeMode,
    }
  },
  render() {
    return this.renderVN()
  },
})
