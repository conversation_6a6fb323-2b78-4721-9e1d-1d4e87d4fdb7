<template>
  <a-drawer v-model:open="show" :width="showAuditRecord ? '1430px' : '1080px'" title="退库申请单" @close="handleClose">
    <template #extra>
      <a-button v-if="type == 2 && btnPermission[118008]" @click="handleShowAuditRecord">审核记录</a-button>
    </template>
    <div v-if="loading" class="w-full flex h-full justify-center items-center">
      <a-spin tip="加载中..."></a-spin>
    </div>
    <div v-else class="flex h-full">
      <div class="w-1080px overflow-y-auto overflow-x-hidden">
        <a-form :model="formData" :label-col="{ style: { width: '110px' } }" :rules="rules" ref="formRef">
          <div class="drawer-title">基本信息</div>
          <div class="w-full">
            <a-row>
              <a-col :span="12">
                <a-form-item label="退库申请编号" name="number">
                  <a-input v-model:value="formData.number" :disabled="true" :readonly="true" v-if="isEdit"></a-input>
                  <div v-else>{{ formData.number }}</div>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="退库单编号" name="jst_return_no">
                  <a-input v-model:value="formData.jst_return_no" :disabled="true" :readonly="true" v-if="isEdit"></a-input>
                  <div v-else>{{ formData.jst_return_no }}</div>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row>
              <a-col :span="12">
                <a-form-item label="采购单编号" name="purchase_order_numbers" :required="true">
                  <a-select
                    v-if="isEdit"
                    v-model:value="formData.purchase_order_numbers"
                    :placeholder="'请选择采购单'"
                    mode="multiple"
                    :open="false"
                    :showArrow="true"
                    allowClear
                    @change="handleOrderNumberChange"
                    @click="handleSelectPurchaseOrder"
                    @dropdownVisibleChange="
                      () => {
                        return false
                      }
                    "
                    :options="orderOptions"
                  ></a-select>
                  <div v-else>{{ formData.purchase_order_numbers.join('，') }}</div>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="仓库名称" name="warehouse_id">
                  <a-select
                    v-if="isEdit"
                    :filter-option="filterOption"
                    show-search
                    v-model:value="formData.warehouse_id"
                    :allow-clear="true"
                    :options="warehousesOptions"
                    placeholder="请选择"
                    @change="onChangeWarehouse"
                  ></a-select>
                  <div v-else>{{ formData.warehouse_name }}</div>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row>
              <a-col :span="12">
                <a-form-item label="供应商子公司" name="company_supplier_id">
                  <SelectSupplier
                    v-if="isEdit"
                    v-model:value="formData.company_supplier_id"
                    v-model:label="formData.company_supplier_name"
                    :title="'请选择供应商'"
                    :api="GetPageMRPSupplierCompanySelect"
                    :apiParams="{ is_contains_srs: true }"
                    :mode="'single'"
                    :labelInValue="false"
                    @Change="onCompanySupplierChange"
                  ></SelectSupplier>
                  <div v-else>{{ formData.company_supplier_name }}</div>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="仓区" name="warehouse_area">
                  <a-select v-if="isEdit" v-model:value="formData.warehouse_area" :options="warehouseAreaOptions" placeholder="请选择"></a-select>
                  <div v-else>{{ formData.warehouse_area_str }}</div>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row>
              <a-col :span="12">
                <a-form-item label="申请类型" name="application_type">
                  <a-select
                    v-if="isEdit"
                    :filter-option="filterOption"
                    show-search
                    v-model:value="formData.application_type"
                    :allow-clear="true"
                    :options="applicationTypeOptions"
                    placeholder="请选择"
                  ></a-select>
                  <div v-else>{{ formData.application_type_string }}</div>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="退货原因" name="return_reason_type">
                  <a-select
                    v-if="isEdit"
                    :filter-option="filterOption"
                    show-search
                    v-model:value="formData.return_reason_type"
                    :allow-clear="true"
                    :options="returnReasonTypeOptions"
                    @change="
                      () => {
                        formData.quality_problem_type = null
                        formData.other_quality_problem = ''
                      }
                    "
                    placeholder="请选择"
                  ></a-select>
                  <div v-else>{{ formData.return_reason_type_string }}</div>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row>
              <a-col :span="12" v-if="formData.return_reason_type == 20">
                <a-form-item label="质量问题" name="quality_problem_type">
                  <a-select
                    :filter-option="filterOption"
                    show-search
                    v-model:value="formData.quality_problem_type"
                    :allow-clear="true"
                    :options="qualityProblemTypeOptions"
                    @change="
                      (val) => {
                        if (val !== 12) {
                          formData.other_quality_problem = ''
                        }
                      }
                    "
                    placeholder="请选择"
                    v-if="isEdit"
                  ></a-select>
                  <div v-else>{{ formData.quality_problem_type_string }}</div>
                </a-form-item>
              </a-col>
              <a-col :span="12" v-if="formData.quality_problem_type == 12">
                <a-form-item label="其他质量问题" name="other_quality_problem">
                  <a-textarea v-if="isEdit" v-model:value="formData.other_quality_problem" placeholder="其他质量问题" :rows="4" :maxlength="300"></a-textarea>
                  <div v-else>{{ formData.other_quality_problem }}</div>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row>
              <a-col :span="24">
                <a-form-item name="remark" label="备注">
                  <a-input v-model:value="formData.remark" placeholder="备注" v-if="isEdit"></a-input>
                  <div v-else>{{ formData.remark }}</div>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row v-if="type !== 0">
              <a-col :span="12">
                <a-form-item label="申请人" name="creator_name_department">
                  {{ formData.creator_name_department }}
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="申请时间" name="create_at">
                  {{ formData.create_at }}
                </a-form-item>
              </a-col>
            </a-row>
            <a-row v-if="type !== 0">
              <a-col :span="12">
                <a-form-item label="状态" name="audit_status_string">
                  {{ formData.audit_status_string }}
                </a-form-item>
              </a-col>
            </a-row>
          </div>
          <div class="drawer-title">收货地址</div>
          <div class="w-full">
            <a-row>
              <a-col :span="12">
                <a-form-item label="收货人" name="consignee">
                  <a-input v-model:value="formData.consignee" placeholder="收货人" v-if="isEdit"></a-input>
                  <div v-else>{{ formData.consignee }}</div>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="联系电话" name="phone_number">
                  <a-input v-model:value="formData.phone_number" placeholder="联系电话" v-if="isEdit"></a-input>
                  <div v-else>{{ formData.phone_number }}</div>
                </a-form-item>
              </a-col>
            </a-row>
            <a-form-item label="收货地址" class="mb-0">
              <a-row :gutter="12" v-if="isEdit">
                <a-col :span="8">
                  <a-form-item name="province">
                    <a-select
                      v-model:value="formData.province"
                      @change="getCityList"
                      placeholder="省/直辖市/自治区"
                      :field-names="{ label: 'name', value: 'name' }"
                      :options="areaCity"
                      :filter-option="filterOption"
                    ></a-select>
                  </a-form-item>
                </a-col>
                <a-col :span="8">
                  <a-form-item name="city">
                    <a-select v-model:value="formData.city" placeholder="市" @change="getAreaList" :options="cityList" :filter-option="filterOption"></a-select>
                  </a-form-item>
                </a-col>
                <a-col :span="8">
                  <a-form-item name="area">
                    <a-select v-model:value="formData.area" placeholder="区/县" :options="areaOptions" :filter-option="filterOption"></a-select>
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row v-else>
                <a-col :span="24">
                  <a-form-item>
                    <div>{{ formData.province }}-{{ formData.city }}-{{ formData.area }}</div>
                  </a-form-item>
                </a-col>
              </a-row>
            </a-form-item>
            <a-row>
              <a-col :span="24">
                <a-form-item label="详细地址" name="shipments_address">
                  <a-input v-if="isEdit" v-model:value="formData.shipments_address" placeholder="详细地址"></a-input>
                  <div v-else>{{ formData.shipments_address }}</div>
                </a-form-item>
              </a-col>
            </a-row>
          </div>
          <div class="drawer-title">明细信息</div>
          <div class="w-full">
            <a-button @click="handleAddProducts" v-if="isEdit">新增商品</a-button>
            <a-table class="mt-10px" :pagination="false" :scroll="{ x: 1900 }" bordered :data-source="formData.purchaseReturnApplicationDetails" :columns="tableColumns">
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'image_url'">
                  <EasyImage :src="record.image_url"></EasyImage>
                </template>
                <template v-if="column.key === 'return_quantity'">
                  <a-input-number
                    :controls="false"
                    v-model:value="record.return_quantity"
                    :max="formData.purchase_order_numbers && formData.purchase_order_numbers.length > 0 ? Math.min(record.total_actual_inbound, record.inventory_quantity) : record.inventory_quantity"
                    :min="0"
                    class="w-full"
                    v-if="isEdit"
                    @change="() => calculateReturnAmount(record)"
                  ></a-input-number>
                  <div v-else>
                    {{ record.return_quantity }}
                  </div>
                </template>
                <template v-if="column.key === 'return_amount'">
                  <a-input-number :controls="false" v-model:value="record.return_amount" :precision="2" :min="0" v-if="isEdit" class="w-full" :disabled="true"></a-input-number>
                  <div v-else>
                    {{ record.return_amount }}
                  </div>
                </template>
                <template v-if="column.key === 'purchase_actual_unit_price'">
                  <a-input-number
                    :controls="false"
                    v-model:value="record.purchase_actual_unit_price"
                    :precision="8"
                    :min="0"
                    v-if="isEdit"
                    class="w-full"
                    :disabled="true"
                    @change="() => calculateReturnAmount(record)"
                  ></a-input-number>
                  <div v-else>
                    {{ record.purchase_actual_unit_price }}
                  </div>
                </template>
                <template v-if="column.key === 'other_fee'">
                  <a-input-number :controls="false" v-model:value="record.other_fee" :precision="2" :min="0" v-if="isEdit" class="w-full" :disabled="true"></a-input-number>
                  <div v-else>
                    {{ record.other_fee }}
                  </div>
                </template>
                <template v-if="column.key === 'actual_return_amount'">
                  <a-input-number :controls="false" v-model:value="record.actual_return_amount" :precision="2" :min="0" v-if="isEdit" class="w-full" :disabled="true"></a-input-number>
                  <div v-else>
                    {{ record.actual_return_amount }}
                  </div>
                </template>
                <template v-if="column.key === 'remark'">
                  <a-input v-model:value="record.remark" :maxlength="300" placeholder="备注" v-if="isEdit" class="w-full"></a-input>
                  <div v-else>
                    {{ record.remark }}
                  </div>
                </template>
                <template v-if="column.key === 'operation'">
                  <a-button v-if="isEdit" @click="handleDeleteProduct(record)">删除</a-button>
                </template>
              </template>
            </a-table>
          </div>
          <div class="w-full mt-20px">
            <a-row>
              <a-col :span="12">
                <a-form-item label="商品退库合计金额" name="total_product_return_amount">
                  <a-input-number v-if="isEdit" class="w-full" v-model:value="totalReturn" :readonly="true" :disabled="true" :controls="false" :min="0" :precision="2"></a-input-number>
                  <div v-else>
                    {{ formData.total_product_return_amount }}
                  </div>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row>
              <a-col :span="12">
                <a-form-item label="其他费用金额" name="other_fee_amount">
                  <a-input-number
                    v-if="isEdit"
                    class="w-full"
                    v-model:value="formData.other_fee_amount"
                    :controls="false"
                    :min="0"
                    :precision="2"
                    @change="calculateOtherFeeAllocation"
                  ></a-input-number>
                  <div v-else>
                    {{ formData.other_fee_amount }}
                  </div>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row>
              <a-col :span="12">
                <a-form-item label="其他费用备注" name="other_fee_remark">
                  <a-textarea v-if="isEdit" v-model:value="formData.other_fee_remark" placeholder="其他费用备注" :rows="4"></a-textarea>
                  <div v-else>
                    {{ formData.other_fee_remark }}
                  </div>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row>
              <a-col :span="12">
                <a-form-item label="本单合计退库总金额" name="total_return_amount" :label-col="{ style: { width: '120px' } }">
                  <a-input-number v-if="isEdit" class="w-full" v-model:value="totalReturnAmount" :readonly="true" :disabled="true" :controls="false" :min="0" :precision="2"></a-input-number>
                  <div v-else>
                    {{ formData.total_return_amount }}
                  </div>
                </a-form-item>
              </a-col>
            </a-row>
            <!-- 新增：本单合计退库总金额下方的三个字段 -->
            <a-row>
              <a-col :span="12" v-if="btnPermission[118009]">
                <a-form-item label="供应商是否已退款" name="is_refunded">
                  <a-radio-group v-model:value="formData.is_refunded" v-if="isEdit || type == 2">
                    <a-radio :value="true">是</a-radio>
                    <a-radio :value="false">否</a-radio>
                  </a-radio-group>
                  <span v-else>
                    {{ formData.is_refunded ? '是' : '否' }}
                  </span>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row>
              <a-col :span="12">
                <a-form-item label="备注" name="refunded_remark" v-if="btnPermission[118009]">
                  <a-textarea v-if="isEdit" v-model:value="formData.refunded_remark" placeholder="请输入备注" :rows="4" />
                  <div v-else>{{ formData.refunded_remark }}</div>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row>
              <a-col :span="24">
                <a-form-item label="附件" name="attachments" v-if="btnPermission[118009]">
                  <Upload v-if="isEdit" v-model:value="formData.attachments" />
                  <FileModal v-else :list="formData.attachments" />
                </a-form-item>
              </a-col>
            </a-row>
          </div>
        </a-form>
      </div>
      <div class="flex-1 p-16 bg-#f2f2f2 overflow-auto" v-if="showAuditRecord">
        <a-timeline class="ml-16" v-if="auditRecordList.length > 0">
          <a-timeline-item v-for="item in auditRecordList" :key="item.id">
            <div class="timeline-title">
              {{ item.audit_time }}
            </div>
            <div class="timeline-content">
              {{ item.message }}
            </div>
          </a-timeline-item>
        </a-timeline>
        <a-empty v-else class="c-#333" description="暂无审核记录" />
      </div>
    </div>

    <template #footer>
      <div class="flex gap-6px">
        <a-button @click="handleSubmit(true)" type="primary" :loading="loading" v-if="type == 0 || type == 1">提交审核</a-button>
        <a-button @click="handleSubmit(false)" type="primary" :loading="loading" v-if="type == 0 || type == 1">{{ `${type == 0 ? '保存暂不提交' : '修改'}` }}</a-button>
        <a-button type="primary" v-if="type == 3" @click="handleOpenAudit(true)">审核通过</a-button>
        <a-button type="primary" v-if="type == 3" @click="handleOpenAudit(false)">审核拒绝</a-button>
        <a-button v-if="type == 2 && btnPermission[118009]" @click="handleFinanceUpdate">保存修改</a-button>
        <a-button @click="handleClose">关闭</a-button>
      </div>
    </template>
    <SelectProducts @confirm="(data) => onConfirmProduct(data)" ref="selectProductsRef"></SelectProducts>
    <SelectPurchaseOrder @set-value="handleSetOrder" ref="selectPurchaseOrderRef" v-if="showSelectPurchaseOrder" @close="showSelectPurchaseOrder = false"></SelectPurchaseOrder>

    <AuditConfirm ref="auditConfirmRef" @audit="onAuditConfirm" />
  </a-drawer>
</template>

<script lang="ts" setup>
import { FormInstance, message } from 'ant-design-vue'

import { filterOption } from '@/utils'
import { areaCity } from '@/utils/address'
import SelectProducts from '@/components/business/SelectProduct.vue'
import SelectSupplier from '@/components/business/SelectSupplier'
import '@/core' // 导入银行家舍入法

import SelectPurchaseOrder from './SelectPurchaseOrder.vue'

import {
  SupplierAddressInfo,
  AddPurchaseReturnApplication,
  BatchAudit,
  GetAuditRecord,
  GetPurchaseRetrueApplicationDetail,
  UpdatePurchaseReturnApplication,
  GetPurchaseReturnSkuDetail,
  FinanceUpdatePurchaseReturnApplication,
} from '@/servers/PurchaseReturnApplication'
import { GetWarehouses } from '@/servers/Purchaseapplyorder'
import { Get1688MRPSupplierCompanySelect, GetPageMRPSupplierCompanySelect } from '@/servers/BusinessCommon'

const emits = defineEmits(['close', 'update'])
const { btnPermission } = usePermission()

const loading = ref(false)
const applicationId = ref()
const type = ref(0) // 0:新建 1:编辑 2:查看 3:审核
const isEdit = computed(() => {
  return [0, 1].includes(type.value)
})
const rules = ref({
  company_supplier_id: [{ required: true, message: '请选择供应商子公司' }],

  // purchase_order_numbers: [{ required: true, message: '请选择采购单号' }],
  warehouse_id: [{ required: true, message: '请选择仓库' }],
  warehouse_area: [{ required: true, message: '请选择仓库区域' }],
  application_type: [{ required: true, message: '请选择申请类型' }],
  return_reason_type: [{ required: true, message: '请选择退货原因' }],
  quality_problem_type: [{ required: true, message: '请选择质量问题' }],
  other_quality_problem: [{ required: true, message: '请输入其他质量问题' }],
  // consignee: [{ required: true, message: '请输入收货人' }],
  // phone_number: [{ required: true, message: '请输入联系电话' }],
  // province: [{ required: true, message: '请选择省/直辖市/自治区' }],
  // city: [{ required: true, message: '请选择市' }],
  // area: [{ required: true, message: '请选择区/县' }],
  // shipments_address: [{ required: true, message: '请输入详细地址' }],
})

const supplierOptions = ref([])

const formData = ref({
  number: '', // 退库申请编号
  jst_return_no: '', // 退库单编号
  purchase_order_numbers: [], // 采购单号(多个)
  purcharse_order_ids: [], // 采购单ID(多个)
  warehouse_id: undefined, // 仓库id
  warehouse_area: 1, // 仓库区域，默认主仓
  company_supplier_id: null, // 供应商子公司编号
  application_type: null, // 申请类型(多个)
  return_reason_type: null, // 退货原因(多个)
  quality_problem_type: null, // 质量问题
  other_quality_problem: '', // 其他质量问题
  consignee: '', // 收货人
  phone_number: '', // 联系电话
  province: undefined, // 省
  city: undefined, // 市
  area: undefined, // 区
  shipments_address: '', // 详细地址
  total_product_return_amount: '', // 商品退库合计金额
  other_fee_amount: 0, // 其他费用金额
  other_fee_remark: '', // 其他费用备注
  total_return_amount: '', // 本单合计退库总金额
  is_pass: true,
  remark: '', // 备注
  purchaseReturnApplicationDetails: [] as any[], // 明细信息
  company_supplier: '',
  // 新增字段
  is_refunded: false,
  refunded_remark: '',
  attachments: [],
}) as any

const totalReturn = computed(() => {
  return (
    formData.value.purchaseReturnApplicationDetails
      .reduce((acc, item) => {
        return acc + item.return_amount
      }, 0)
      .roundNext(2) ?? 0
  )
})
const totalReturnAmount = computed(() => {
  return (formData.value.other_fee_amount + Number(totalReturn.value)).roundNext(2) ?? 0
})
const orderOptions = ref<any[]>([])
const warehousesOptions = ref([])
const cityList = ref<any[]>([])
const areaOptions = ref<any[]>([]) // 区/县
const auditRecordList = ref<any[]>([]) // 审核记录
const qualityProblemTypeOptions = ref([
  { label: '漏液', value: 1 },
  { label: '外包装破损（影响二次销售）', value: 2 },
  { label: '非标准箱规来货', value: 3 },
  { label: '塑封异常', value: 4 },
  { label: '实物与图不符', value: 5 },
  { label: '配件不齐（说明书等）', value: 6 },
  { label: '跌落测试不过关', value: 7 },
  { label: '重量异常', value: 8 },
  { label: '内容物异常', value: 9 },
  { label: '日期或条码信息缺失', value: 10 },
  { label: '内容物不满瓶（整瓶90%）', value: 11 },
  { label: '其他', value: 12 },
])

const applicationTypeOptions = ref([
  { label: '仅退款', value: 10 },
  { label: '退货退库', value: 20 },
])

const returnReasonTypeOptions = ref([
  { label: '采购退货', value: 10 },
  { label: '质检退货', value: 20 },
])

const warehouseAreaOptions = ref([
  { label: '主仓', value: 1 },
  { label: '销退仓', value: 2 },
  { label: '进货仓', value: 3 },
  { label: '次品仓', value: 4 },
])

// 根据仓区获取库存字段名或标题
const getInventoryInfo = (warehouseArea: number, type: 'field' | 'title' = 'field') => {
  switch (warehouseArea) {
    case 1: // 主仓
      return type === 'field' ? 'inventory_quantity' : '主仓库存'
    case 2: // 销退仓
      return type === 'field' ? 'return_wh_inventory' : '销退仓库存'
    case 3: // 进货仓
      return type === 'field' ? 'purchase_wh_inventory' : '进货仓库存'
    case 4: // 次品仓
      return type === 'field' ? 'defective_inventory' : '次品库存'
    default: // 默认主仓
      return type === 'field' ? 'inventory_quantity' : '主仓库存'
  }
}

const tableColumns = ref<Record<string, any>[]>([
  { title: '序号', width: 70, customRender: ({ index }) => `${index + 1}` },
  { title: '商品图片', dataIndex: 'image_url', key: 'image_url', width: 100 },
  { title: '商品编码', dataIndex: 'k3_sku_id', key: 'k3_sku_id', width: 150 },
  { title: '采购单编号', dataIndex: 'number', key: 'number', width: 120 },
  { title: 'SRS平台商品编码', dataIndex: 'srs_platform_prod_code', key: 'srs_platform_prod_code', width: 130 },
  { title: '款式编号', dataIndex: 'style_code', key: 'style_code', width: 100 },
  { title: '商品名称', dataIndex: 'sku_name', key: 'sku_name', width: 100 },
  { title: '规格型号', dataIndex: 'type_specification', key: 'type_specification', width: 100 },
  { title: '采购总数', dataIndex: 'total_purchase_quantity', key: 'total_purchase_quantity', width: 100 },
  { title: '采购含税单价', dataIndex: 'tax_unit_price', key: 'tax_unit_price', width: 150 },
  { title: '采购总金额', dataIndex: 'total_purchase_amount', key: 'total_purchase_amount', width: 120 },
  { title: '已预付总金额', dataIndex: 'prepayment_amount', key: 'prepayment_amount', width: 150 },
  { title: '入库数量', dataIndex: 'total_actual_inbound', key: 'total_actual_inbound', width: 100 },
  { title: getInventoryInfo(formData.value.warehouse_area, 'title'), dataIndex: 'inventory_quantity', key: 'inventory_quantity', width: 100 },
  { title: '退库数量', dataIndex: 'return_quantity', key: 'return_quantity', width: 100 },
  { title: '实际单价', dataIndex: 'purchase_actual_unit_price', key: 'purchase_actual_unit_price', width: 120 },
  { title: '退库金额', dataIndex: 'return_amount', key: 'return_amount', width: 100 },
  { title: '其他费用', dataIndex: 'other_fee', key: 'other_fee', width: 120 },
  { title: '实际退库金额', dataIndex: 'actual_return_amount', key: 'actual_return_amount', width: 120 },
  { title: '实际退库数量', dataIndex: 'actual_return_quantity', key: 'actual_return_quantity', width: 150 },
  { title: '备注', dataIndex: 'remark', key: 'remark', width: 150 },
  { title: '操作', key: 'operation', dataIndex: 'operation', width: 100, fixed: 'right' as const },
])

const selectProductsRef = useTemplateRef('selectProductsRef')
const selectPurchaseOrderRef = useTemplateRef('selectPurchaseOrderRef')
const formRef = useTemplateRef<FormInstance>('formRef')
const show = ref(false)

const showSelectPurchaseOrder = ref(false)
const showAuditRecord = ref(false)
const handleClose = () => {
  emits('close')
}

// 监听仓区变化，更新表格列标题和库存值
watch(
  () => formData.value.warehouse_area,
  (newValue, oldValue) => {
    console.log('warehouse_area changed:', oldValue, '->', newValue)

    // 更新库存数量列的标题
    const inventoryColumn = tableColumns.value.find((col) => col.key === 'inventory_quantity')
    if (inventoryColumn) {
      inventoryColumn.title = getInventoryInfo(newValue, 'title')
    }

    // 实时更新表格中已有商品的库存值
    if (formData.value.purchaseReturnApplicationDetails?.length) {
      formData.value.purchaseReturnApplicationDetails = formData.value.purchaseReturnApplicationDetails.map((item) => {
        // 使用原始数据获取正确的库存值
        const originalInventoryField = getInventoryInfo(newValue, 'field')
        const originalInventoryValue = item[`_original_${originalInventoryField}`] || item[originalInventoryField] || 0

        return {
          ...item,
          // 更新库存值为当前仓区对应的库存
          inventory_quantity: originalInventoryValue,
          // 保留原始库存数据，避免数据丢失
          _original_inventory_quantity: item._original_inventory_quantity || item.inventory_quantity,
          _original_defective_inventory: item._original_defective_inventory || item.defective_inventory,
          _original_return_wh_inventory: item._original_return_wh_inventory || item.return_wh_inventory,
          _original_purchase_wh_inventory: item._original_purchase_wh_inventory || item.purchase_wh_inventory,
        }
      })
    } else {
      console.log('No items to update')
    }
  },
)
const open = async (drawerType: any, id?: any) => {
  show.value = true
  type.value = drawerType
  if (!isEdit.value) {
    tableColumns.value.pop()
  }
  if (drawerType !== 0 && id) {
    applicationId.value = id
    getDetails(id)
  }
  if (isEdit.value) {
    tableColumns.value = tableColumns.value.filter((item) => {
      return item.key !== 'actual_return_quantity'
    })
  }
  getOptions()
}
const getDetails = async (id) => {
  loading.value = true
  const res = await GetPurchaseRetrueApplicationDetail({ id }).finally(() => {
    loading.value = false
  })
  formData.value = res.data
  orderOptions.value = res.data.purcharse_order_ids.map((item, index) => {
    return {
      label: res.data.purchase_order_numbers[index],
      value: item,
    }
  })
}
const getOptions = async () => {
  const res = await GetWarehouses()
  warehousesOptions.value = res.data.map((item) => {
    return { label: item.value, value: item.key }
  })

  const supplier = await Get1688MRPSupplierCompanySelect({ type: 1 })
  supplierOptions.value = supplier.data.map((item) => ({ label: item.label, value: item.value }))
}
const getCityList = (val: any) => {
  formData.value.city = undefined
  formData.value.area = undefined
  cityList.value = []
  areaOptions.value = []
  cityList.value = areaCity.find((item) => item.name === val)?.children.map((item) => ({ label: item.name, value: item.name })) || []
}
const getAreaList = (val: any) => {
  formData.value.area = undefined
  areaOptions.value = []
  areaOptions.value =
    areaCity
      .find((item) => item.name === formData.value.province)
      ?.children.find((item) => item.name === val)
      ?.children.map((item) => ({ label: item.name, value: item.name })) || []
}

const handleAddProducts = async () => {
  if (!formData.value.warehouse_id) {
    message.warn('请选择仓库')
    return
  }
  if (!formData.value.purcharse_order_ids || !formData.value.purcharse_order_ids.length) {
    message.warn('请选择采购单')
    return
  }

  selectProductsRef.value?.open(
    {
      search: formData.value.purchase_order_numbers.length > 0 ? ['商品名称', '商品编号', '聚水潭商品编号', '采购单编号'] : ['商品名称', '商品编号', '聚水潭商品编号'],
      left: ['商品主图', '商品名称', '商品编码K3', '聚水潭编号', '颜色规格', '计价单位', '库存数量'],
      right: ['商品名称', '商品编码K3', '库存数量'],
      width: 1300,
      keyField: 'uniqueKey',
      params: {
        purchase_order_id: formData.value.purcharse_order_ids,
        warehouse_id: formData.value.warehouse_id,
        warehouse_area: formData.value.warehouse_area, // 传递仓区参数
      },
      api: GetPurchaseReturnSkuDetail,
      dataFormat: (data) => {
        return data.map((i) => {
          const inventoryField = getInventoryInfo(formData.value.warehouse_area, 'field')
          return {
            ...i,
            purchase_order_detail_id: `${i.purchase_order_detail_id}`,
            k3_sku_id: `${i.k3_sku_id}`,
            uniqueKey: `${i.k3_sku_id}_${i.purchase_order_detail_id}`,
            // 根据仓区设置对应的库存数量
            inventory_quantity: i[inventoryField] || 0,
          }
        })
      },
    },
    formData.value.purchaseReturnApplicationDetails.map((item) => ({
      ...item,
      purchase_order_detail_id: `${item.purchase_order_detail_id}`,
      k3_sku_id: `${item.k3_sku_id}`,
      uniqueKey: `${item.k3_sku_id}_${item.purchase_order_detail_id}`,
    })),
  )
}
const onConfirmProduct = (val: any, isSwitch = false) => {
  const ids = formData.value.purchaseReturnApplicationDetails.reduce((acc, item: any) => {
    const { return_amount, return_quantity, id, remark, purchase_actual_unit_price, other_fee, actual_return_amount } = item
    return {
      ...acc,
      [`${item.k3_sku_id}_${item.purchase_order_detail_id}`]: isSwitch
        ? {
            return_amount,
            return_quantity,
            id,
            remark,
            purchase_actual_unit_price,
            other_fee,
            actual_return_amount,
          }
        : item,
    }
  }, {})

  formData.value.purchaseReturnApplicationDetails = val.map((item) => {
    const uniqueKey = `${item.k3_sku_id}_${item.purchase_order_detail_id}`
    const inventoryField = getInventoryInfo(formData.value.warehouse_area, 'field')

    return {
      return_quantity: 0,
      return_amount: 0,
      remark: '',
      // 初始化新增的三个字段
      purchase_actual_unit_price: 0,
      other_fee: 0,
      actual_return_amount: 0,
      ...item,
      uniqueKey,
      // 根据仓区设置对应的库存数量
      inventory_quantity: item[inventoryField] || 0,
      // 保留原始库存数据，避免数据丢失
      _original_inventory_quantity: item.inventory_quantity,
      _original_defective_inventory: item.defective_inventory,
      _original_return_wh_inventory: item.return_wh_inventory,
      _original_purchase_wh_inventory: item.purchase_wh_inventory,
      ...(ids[uniqueKey] || {}),
      ...(!formData.value.purchase_order_numbers?.length
        ? {
            total_purchase_quantity: 0,
            tax_unit_price: 0,
            total_purchase_amount: 0,
            prepayment_amount: 0,
            total_actual_inbound: 0,
          }
        : {}),
    }
  })

  // 初始化完成后，计算其他费用分摊
  calculateOtherFeeAllocation()
}

const handleSelectPurchaseOrder = async () => {
  showSelectPurchaseOrder.value = true
  await nextTick()
  selectPurchaseOrderRef.value?.open(formData.value.purchase_order_numbers)
}

const handleSetOrder = (val: any) => {
  formData.value.purchase_order_numbers = val.map((item) => item.number)
  orderOptions.value = val.map((item) => ({ label: item.number, value: item.id }))
  formData.value.warehouse_id = val[0].warehouse_id
  formData.value.company_supplier_id = val[0].company_supplier_id
  formData.value.purcharse_order_ids = val.map((f) => f.id)
  formData.value.company_supplier_name = val[0].company_supplier_name
  if (formData.value.purchase_order_numbers) {
    formRef.value?.clearValidate('purchase_order_numbers')
  }
  if (formData.value.purchaseReturnApplicationDetails?.length) {
    formData.value.purchaseReturnApplicationDetails = formData.value.purchaseReturnApplicationDetails.filter((f) => (formData.value.purchase_order_numbers || []).includes(f.number))
  }
}

const getAddress = async () => {
  const res = await SupplierAddressInfo(formData.value.company_supplier_id)
  formData.value.shipments_address = res.data.shipments_address
  formData.value.phone_number = res.data.phone_number
  formData.value.consignee = res.data.company_supplier_name
  formData.value.province = res.data.province
  getCityList(formData.value.province)
  formData.value.city = res.data.city
  getAreaList(formData.value.city)
  formData.value.area = res.data.area
}

const handleShowAuditRecord = async () => {
  showAuditRecord.value = !showAuditRecord.value
  if (showAuditRecord.value) {
    await getAuditRecord()
  }
}
const handleDeleteProduct = ({ k3_sku_id, purchase_order_detail_id }: any) => {
  const uniqueKey = `${k3_sku_id}_${purchase_order_detail_id}`
  formData.value.purchaseReturnApplicationDetails = formData.value.purchaseReturnApplicationDetails.filter((item) => `${item.k3_sku_id}_${item.purchase_order_detail_id}` !== uniqueKey)
}

// 计算退库金额和其他费用分摊
const calculateReturnAmount = (record: any) => {
  const returnQuantity = Number(record.return_quantity) || 0
  const actualUnitPrice = Number(record.purchase_actual_unit_price) || 0
  record.return_amount = Number((returnQuantity * actualUnitPrice).roundNext(2))

  // 重新计算所有商品的其他费用分摊
  calculateOtherFeeAllocation()
}

// 计算其他费用分摊
const calculateOtherFeeAllocation = () => {
  const details = formData.value.purchaseReturnApplicationDetails
  if (!details || details.length === 0) return

  const totalReturnAmount = details.reduce((sum, item) => sum + Number(item.return_amount || 0), 0)
  const otherFeeAmount = Number(formData.value.other_fee_amount || 0)

  if (totalReturnAmount === 0) {
    // 如果总退库金额为0，所有商品的其他费用都为0
    details.forEach((item) => {
      item.other_fee = 0
      item.actual_return_amount = Number(item.return_amount || 0)
    })
    return
  }

  // 按比例分摊其他费用
  let allocatedOtherFee = 0
  details.forEach((item, index) => {
    const returnAmount = Number(item.return_amount || 0)

    if (index === details.length - 1) {
      // 最后一个商品：其他费用金额 - 前面商品分摊到的其他费用
      item.other_fee = Number((otherFeeAmount - allocatedOtherFee).roundNext(2))
    } else {
      // 其他商品：按退库金额比例分摊
      const proportion = returnAmount / totalReturnAmount
      item.other_fee = Number((otherFeeAmount * proportion).roundNext(2))
      allocatedOtherFee += item.other_fee
    }

    // 计算实际退库金额 = 退库金额 + 其他费用
    item.actual_return_amount = Number((returnAmount + item.other_fee).roundNext(2))
  })
}

// 审核
const auditConfirmRef = useTemplateRef('auditConfirmRef')
const handleOpenAudit = async (pass: boolean) => {
  auditConfirmRef.value?.open(pass)
}
const onAuditConfirm = async (data, callback, failback) => {
  try {
    const res = await BatchAudit({ ...data, ids: [applicationId.value] })
    if (res.success) {
      message.success('操作成功')
      emits('update')
      callback()
    } else {
      message.error(res.message)
      failback()
    }
  } catch {
    failback()
  }
}

const handleOrderNumberChange = async (val: any) => {
  formData.value.purchaseReturnApplicationDetails = formData.value.purchaseReturnApplicationDetails.filter((f) => (val || []).includes(f.number))
  formData.value.purcharse_order_ids = orderOptions.value.filter((f) => (val || []).includes(f.label)).map((f) => f.value)
}

// 获取审核记录
const getAuditRecord = async () => {
  const res = await GetAuditRecord({ id: applicationId.value, type: 8 })
  auditRecordList.value = res.data
}
defineExpose({
  open,
})

const onCompanySupplierChange = () => {
  getAddress()
  formRef.value?.validateFields(['company_supplier_id'])
}

// watch(
//   () => formData.value.company_supplier_id,
//   () => {
//     if (formData.value.company_supplier_id) {
//       getAdrees()
//     }
//   },
// )

const handleFinanceUpdate = async () => {
  await FinanceUpdatePurchaseReturnApplication({
    id: applicationId.value,
    is_refunded: formData.value.is_refunded,
  })
  message.success('保存成功')
  emits('update')
}
const onChangeWarehouse = (v) => {
  if (formData.value.purchaseReturnApplicationDetails?.length && formData.value.purcharse_order_ids?.length) {
    GetPurchaseReturnSkuDetail({
      warehouse_id: v,
      purchase_order_id: formData.value.purcharse_order_ids,
      k3_sku_ids: formData.value.purchaseReturnApplicationDetails.map((item) => item.k3_sku_id),
      warehouse_area: formData.value.warehouse_area, // 传递仓区参数
    }).then(({ data }) => {
      onConfirmProduct(data.list, true)
    })
  }
}

const handleSubmit = async (isPass: boolean) => {
  await formRef.value?.validateFields()
  if (formData.value.purchaseReturnApplicationDetails.length == 0) {
    message.warning('请选择商品')
    return
  }
  if (type.value == 1) {
    formData.value.purchaseReturnApplicationDetails.forEach((item) => {
      item.purchase_return_application_id = applicationId.value
    })
  }
  const params = {
    ...formData.value,
    total_return_amount: totalReturnAmount.value,
    total_product_return_amount: totalReturn.value,
    is_pass: isPass,
  }

  await [AddPurchaseReturnApplication, UpdatePurchaseReturnApplication][type.value](params)
  message.success('提交成功')
  emits('update')
}
</script>
