import { InputProps, SelectProps, DatePickerProps, CheckboxProps, RadioProps, SwitchProps, FormItemProps as AntFormItemProps, textAreaProps, InputNumberProps } from 'ant-design-vue'
import type { RadioGroupChildOption } from 'ant-design-vue/es/radio/Group'

type WithCommonProps<T> = {
  disabled?: boolean
  props?: Partial<T> & { class?: string }
}

type OcTypeProps = { slot: string } | { type: 'text' } | { type: 'empty' } | { type: 'title'; title?: string }

type FormItemSelectTypeProps = {
  type: 'select'
  options?: Record<string, unknown>[] // 选项数据
  optionFormatter?: (value: any) => any // 选项数据格式化
  api?: (value: any) => any // 获取选项数据
  apiCallBack?: (value: any) => any // 获取选项数据回调
  isAlias?: boolean
} & WithCommonProps<SelectProps>

type FormItemDateTypeProps = {
  type: 'date'
  showTime?: Record<string, any> | boolean
} & WithCommonProps<DatePickerProps>

type FormItemRadioTypeProps = {
  type: 'radio'
  options?: (string | number | (RadioGroupChildOption & { hide?: boolean }))[]
} & WithCommonProps<RadioProps>

type FormItemUploadTypeProps = {
  type: 'upload'
  accept?: string
  module?: number
} & WithCommonProps<{ btn: boolean }>

type FormItemSelectSupplierTypeProps = {
  type: 'selectSupplier'
  mode?: string
  labelInValue?: boolean
} & WithCommonProps<{ btn: boolean }>

type FormItemModelTypeProps =
  | ({ type: 'input' } & WithCommonProps<InputProps>)
  | ({ type: 'textarea' } & WithCommonProps<textAreaProps>)
  | ({ type: 'number' } & WithCommonProps<InputNumberProps>)
  | ({ type: 'checkbox' } & WithCommonProps<CheckboxProps>)
  | ({ type: 'switch' } & WithCommonProps<SwitchProps>)
  | FormItemSelectTypeProps
  | FormItemDateTypeProps
  | FormItemRadioTypeProps
  | FormItemUploadTypeProps
  | FormItemSelectSupplierTypeProps

// 表单项类
type FormItemTypeProps = OcTypeProps | FormItemModelTypeProps

type WithTextFormat = {
  item: any
  form: Record<string, unknown>
  items: FormItemProps[]
  value: any
}
interface BaseFormItem extends AntFormItemProps {
  span?: number
  width?: number
  fill?: number
  placeholder?: string
  name?: string
  alias?: string
  label?: string
  labelTip?: string
  dataType?: string
  hide?: boolean
  isDetail?: boolean
  linkage?: string | string[]
  linkageFn?: (params?: any) => any
  textFormat?: (value: WithTextFormat) => any
  emptyText?: string
}

type FormItemSelectProps = BaseFormItem & FormItemSelectTypeProps
type FormItemDateProps = BaseFormItem & FormItemDateTypeProps
type FormItemRadioProps = BaseFormItem & FormItemRadioTypeProps
type FormItemUploadProps = BaseFormItem & FormItemUploadTypeProps
type FormItemSelectSupplierProps = BaseFormItem & FormItemSelectSupplierTypeProps

type FormItemModelProps = BaseFormItem & FormItemModelTypeProps

export type FormItemProps = BaseFormItem & FormItemTypeProps
