import { request } from './request'
// 查询列表
export const GetAliPurchaseAccountList = (data) => {
  return request({ url: '/api/Ali1688PurchaseAccount/GetAliPurchaseAccountList', data })
}
// 获取账号下拉框
export const GetAliPurchaseAccountSelect = (data) => {
  return request({ url: '/api/Ali1688PurchaseAccount/GetAliPurchaseAccountSelect', data }, 'GET')
}
// 获取1688采购账号收货地址下拉框
export const GetAliPurchaseAccountReceiveAddressSelect = (data) => {
  return request({ url: '/api/Ali1688PurchaseAccount/GetAliPurchaseAccountReceiveAddressSelect', data })
}
// 获取1688采购账号明细
export const GetAli1PurchaseAccountInfo = (data) => {
  return request({ url: `/api/Ali1688PurchaseAccount/GetAli1PurchaseAccountInfo?id=${data.id}`, data }, 'GET')
}
// 添加1688采购账号
export const AddAliPurchaseAccountInfo = (data) => {
  return request({ url: '/api/Ali1688PurchaseAccount/AddAliPurchaseAccountInfo', data })
}
// 修改1688采购账号
export const UpdateAliPurchaseAccountInfo = (data) => {
  return request({ url: '/api/Ali1688PurchaseAccount/UpdateAliPurchaseAccountInfo', data })
}

// 修改1688采购账号状态
export const UpdateAliPurchaseAccountStatus = (data) => {
  return request({ url: '/api/Ali1688PurchaseAccount/UpdateAliPurchaseAccountStatus', data })
}

// 根据采购账号ID获取收货地址列表
export const GetAliPurchaseAccountReceiveAddressList = (data) => {
  return request({ url: '/api/Ali1688PurchaseAccount/GetAliPurchaseAccountReceiveAddressList', data })
}

// 根据id获取1688账号收货地址详情
export const GetAliPurchaseAccountReceiveAddress = (data) => {
  return request({ url: `/api/Ali1688PurchaseAccount/GetAliPurchaseAccountReceiveAddress?id=${data.id}`, data }, 'GET')
}

// 修改1688采购账号收货地址状态
export const UpdateAliPurchaseAccountReceiveAddressStatus = (data) => {
  return request({ url: '/api/Ali1688PurchaseAccount/UpdateAliPurchaseAccountReceiveAddressStatus', data })
}

// 同步账号
export const SyncReceiveAddressList = (data) => {
  return request({ url: '/api/Ali1688PurchaseAccount/SyncReceiveAddressList', data })
}

// 批量删除
export const DeleteAliPurchaseAccountList = (data) => {
  return request({ url: '/api/Ali1688PurchaseAccount/DeleteAliPurchaseAccountList', data })
}
