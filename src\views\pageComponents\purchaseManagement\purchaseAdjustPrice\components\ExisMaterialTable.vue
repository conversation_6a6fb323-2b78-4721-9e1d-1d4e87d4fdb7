<template>
  <a-drawer :maskClosable="false" title="材质" width="50vw" :visible="visible" @close="handleClose">
    <a-space class="mb-12">
      <a-input placeholder="材质名称" v-model:value="queryParams.material_name" :maxlength="200" />
      <a-button type="primary" @click="handleSearch">查询</a-button>
    </a-space>
    <vxe-table
      v-if="visible"
      :border="true"
      ref="productTableRef"
      size="mini"
      :row-config="{ isHover: true, keyField: 'sku_id', height: 40 }"
      :custom-config="{ mode: 'popup' }"
      :data="data"
      :show-overflow="true"
      :show-header-overflow="true"
      :show-footer-overflow="true"
      :column-config="{ resizable: true }"
      class="tableBoxwidth"
      :checkbox-config="{ reserve: true, checkRowKeys }"
      min-height="0"
      stripe
      v-bind="$attrs"
      @checkbox-all="selectChangeEvent"
      @checkbox-change="selectChangeEvent"
    >
      <vxe-column type="checkbox" field="checkbox" width="50" fixed="left"></vxe-column>
      <slot name="column">
        <template v-for="i in tableKey" :key="i.field">
          <vxe-column v-bind="i">
            <template v-if="$slots[String(i.field)]" #default="attr">
              <slot :name="i.field" v-bind="attr" :item="i" />
            </template>
          </vxe-column>
        </template>
      </slot>
    </vxe-table>
    <div class="flex justify-end mt-10">
      <a-pagination
        v-model:current="queryParams.page"
        v-model:page-size="queryParams.pageSize"
        show-quick-jumper
        :total="queryParams.total"
        show-size-changer
        :page-size-options="['20', '50', '100', '250']"
        @change="getProductList"
        size="small"
        :showTotal="(total) => `共 ${total} 条`"
      >
        <template #buildOptionText="props">
          <span>{{ props.value }}条/页</span>
        </template>
      </a-pagination>
    </div>
    <template #footer>
      <a-space>
        <a-button type="primary" @click="handleSelectProduct">确定</a-button>
        <a-button @click="handleClose">取消</a-button>
      </a-space>
    </template>
  </a-drawer>
</template>

<script setup lang="ts">
import { VxeTableInstance } from 'vxe-table'

import { GetPurchaseMaterialDetail } from '@/servers/Purchaseprice'

const productTableEl = useTemplateRef<VxeTableInstance>('productTableRef')
const checkRowKeys = ref<string[]>([])
const emits = defineEmits(['selectProduct'])

const visible = ref(false)

const data = ref<any[]>([])

const selectProductList = ref<any[]>([])

const queryParams = ref<any>({
  page: 1,
  pageSize: 20,
})

const tableKey = ref([
  { title: '材质名称', field: 'material_name', width: 100 },
  { title: '数量（从）', field: 'quantity_from', width: 120 },
  { title: '数量（至）', field: 'quantity_to', width: 120 },
  { title: '单价', field: 'str_price', width: 120 },
  { title: '生效时间', field: 'take_effect_time', width: 200 },
  { title: '失效时间', field: 'lose_efficacy_time', width: 200 },
])

// 打开
const open = (id: number) => {
  selectProductList.value = []
  queryParams.value.id = id
  checkRowKeys.value = []
  getProductList()
  visible.value = true
}
// 关闭
const handleClose = () => {
  queryParams.value = {
    page: 1,
    pageSize: 20,
  }
  visible.value = false
  selectProductList.value = []
}

// 获取商品列表
const getProductList = async (page = 1) => {
  const params: any = {}
  for (const key in queryParams.value) {
    if (queryParams.value[key]) {
      params[key] = queryParams.value[key]
    }
  }
  params.page = page
  const res = await GetPurchaseMaterialDetail(params)
  queryParams.value.page = page
  queryParams.value.total = res.data.total
  data.value = res.data.list
}
// 分页
// const handlePageChange = (page: number, pageSize: number) => {
//   queryParams.value.page = page
//   queryParams.value.pageSize = pageSize
//   getProductList()
// }

// 查询
const handleSearch = () => {
  getProductList()
}
// 选中事件
const selectChangeEvent = () => {
  const $table = productTableEl.value
  // 当前页选中的数据
  const currentSelectedRows = $table?.getCheckboxRecords() || []
  // 其他页选中的数据
  const otherSelectedRows = $table?.getCheckboxReserveRecords() || []

  selectProductList.value = [...currentSelectedRows, ...otherSelectedRows]
}

// 确定
const handleSelectProduct = () => {
  const list = data.value.filter((i) => selectProductList.value.some((j) => j.k3_sku_id === i.k3_sku_id))
  emits('selectProduct', list)
  handleClose()
}

defineExpose({
  open,
})
</script>
