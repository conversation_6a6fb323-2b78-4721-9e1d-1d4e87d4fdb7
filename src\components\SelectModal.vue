<template>
  <a-modal :zIndex="10000" style="top: 15vh" :width="width" :closable="false" class="selectModal" :title="null" :footer="null" v-model:open="visible">
    <div class="modal-header">
      <div class="title">{{ title }}</div>
      <CloseOutlined class="close-btn" @click="handleCancel" />
    </div>

    <div class="modal-body">
      <!-- 搜索区域 -->
      <div class="search-area" v-if="showSearch && searchFields.length > 0">
        <a-form layout="inline" :model="searchForm" @finish="handleSearch">
          <a-form-item v-for="item in searchFields" :key="item.field" :name="item.field">
            <a-input v-if="item.type === 'input'" v-model:value="searchForm[item.field]" :placeholder="item.placeholder || `请输入${item.label}`" allow-clear style="width: 200px" />
            <a-select v-else-if="item.type === 'select'" v-model:value="searchForm[item.field]" :placeholder="item.placeholder || `请选择${item.label}`" allow-clear style="width: 200px">
              <a-select-option v-for="option in item.options" :key="option.value" :value="option.value">
                {{ option.label }}
              </a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item>
            <a-space>
              <a-button type="primary" html-type="submit" :loading="loading">搜索</a-button>
              <a-button @click="handleReset">重置</a-button>
            </a-space>
          </a-form-item>
        </a-form>
      </div>

      <!-- 表格区域 -->
      <div class="table-area">
        <a-table
          :columns="columns"
          :data-source="tableData"
          :loading="loading"
          :pagination="pagination"
          :row-selection="rowSelection"
          :row-key="getItemKey"
          :scroll="{ y: 400 }"
          size="small"
          @change="handleTableChange"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'action'">
              <slot name="action" :record="record" :column="column">
                <a-button type="link" size="small" @click="handleSelect(record)">选择</a-button>
              </slot>
            </template>
            <template v-else>
              <slot :name="column.dataIndex" :record="record" :column="column">
                <span v-if="Array.isArray(record[column.dataIndex as string])">
                  {{ record[column.dataIndex as string].join(', ') }}
                </span>
                <span v-else>
                  {{ record[column.dataIndex as string] }}
                </span>
              </slot>
            </template>
          </template>
        </a-table>
      </div>

      <!-- 已选择项显示 -->
      <div class="selected-area" v-if="internalSelectedItems.length > 0">
        <div class="selected-title">
          <InfoCircleFilled class="selected-icon" />
          <span>已选择 {{ internalSelectedItems.length }} 项</span>
          <a-button type="link" size="small" @click="clearSelected">清空</a-button>
        </div>
        <div class="selected-list">
          <a-tag v-for="item in internalSelectedItems" :key="getItemKey(item)" closable @close="removeSelected(item)" class="selected-tag">
            {{ getItemDisplay(item) }}
          </a-tag>
        </div>
      </div>
    </div>

    <div class="modal-footer">
      <a-space>
        <a-button @click="handleCancel">{{ cancelText }}</a-button>
        <a-button type="primary" @click="handleConfirm" :loading="confirmLoading">
          {{ confirmText }}
        </a-button>
      </a-space>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { CloseOutlined, InfoCircleFilled } from '@ant-design/icons-vue'
import type { TablePaginationConfig } from 'ant-design-vue'
import { watch } from 'vue'

interface SearchField {
  field: string
  label: string
  type: 'input' | 'select'
  placeholder?: string
  options?: Array<{ label: string; value: any }>
}

interface Column {
  title: string
  dataIndex: string
  key: string
  width?: number
  fixed?: 'left' | 'right'
  align?: 'left' | 'center' | 'right'
}

interface SelectModalProps {
  title?: string
  width?: number
  showSearch?: boolean
  searchFields?: SearchField[]
  columns: Column[]
  api?: (params: any) => Promise<any>
  dataSource?: any[]
  multiple?: boolean
  initialSelectedItems?: any[]
  selectedItems?: any[]
  keyField?: string
  displayField?: string
  cancelText?: string
  confirmText?: string
  pageSize?: number
  apiParams?: Record<string, any>
  pageConfig?: { page: number; pageSize: number; total?: number }
}

interface SelectModalEmits {
  (e: 'confirm', items: any[]): void
  (e: 'cancel'): void
  (e: 'update:selectedItems', items: any[]): void
}

const props = withDefaults(defineProps<SelectModalProps>(), {
  title: '选择数据',
  width: 800,
  showSearch: true,
  searchFields: () => [],
  multiple: false,
  initialSelectedItems: () => [],
  keyField: 'id',
  displayField: 'name',
  cancelText: '取消',
  confirmText: '确定',
  pageSize: 10,
  apiParams: () => ({}),
})

const emit = defineEmits<SelectModalEmits & { (e: 'update:pageConfig', val: any): void }>()

// 响应式数据
const visible = ref(false)
const loading = ref(false)
const confirmLoading = ref(false)
const tableData = ref<any[]>([])
const searchForm = ref<Record<string, any>>({})
const internalSelectedItems = ref<any[]>([])

// 分页配置
const pagination = computed(() => {
  if (!props.pageConfig) return false
  return {
    current: props.pageConfig.page,
    pageSize: props.pageConfig.pageSize,
    total: props.pageConfig.total || 0,
    showSizeChanger: false,
    showQuickJumper: false,
    size: 'small' as const,
  }
})

// 行选择配置
const rowSelection = computed(() => {
  const selectedKeys = internalSelectedItems.value.map((item) => getItemKey(item))

  return {
    type: props.multiple ? ('checkbox' as const) : ('radio' as const),
    selectedRowKeys: selectedKeys,
    getCheckboxProps: (record: any) => ({
      disabled: false,
      name: getItemKey(record),
    }),
    onChange: (_: (string | number)[], selectedRows: any[]) => {
      internalSelectedItems.value = selectedRows
      emit('update:selectedItems', internalSelectedItems.value)
    },
  }
})

// 方法
const getItemKey = (item: any) => {
  // 优先使用props中指定的keyField
  if (props.keyField && item[props.keyField] !== undefined && item[props.keyField] !== null) {
    return item[props.keyField]
  }

  // 如果没有指定keyField或值为空，使用purchase_return_application_id作为key
  if (item.purchase_return_application_id !== undefined && item.purchase_return_application_id !== null) {
    return item.purchase_return_application_id
  }

  // 如果keyField对应的值是0，使用其他唯一字段
  const keyValue = item[props.keyField]
  if (keyValue === 0 || keyValue === null || keyValue === undefined) {
    // 尝试使用其他字段作为key
    return item.id || item.number || item.key || JSON.stringify(item)
  }
  return keyValue
}

const getItemDisplay = (item: any) => {
  // 优先使用displayField，如果没有则使用其他合适的字段
  const display = item[props.displayField] || item.purchase_return_application_number || item.number || item.name || getItemKey(item)
  return display
}

const loadData = async (params: any = {}) => {
  if (!props.api && !props.dataSource) {
    console.warn('SelectModal: 需要提供 api 或 dataSource')
    return
  }

  loading.value = true
  try {
    if (props.api) {
      const requestParams = {
        ...props.apiParams,
        ...params,
      }
      if (props.pageConfig) {
        requestParams.page = props.pageConfig.page
        requestParams.pageSize = props.pageConfig.pageSize
      }
      const response = await props.api(requestParams)
      if (response && typeof response === 'object') {
        tableData.value = response.data || response.list || []
        // total 由父组件 pageConfig 控制
      } else {
        tableData.value = response || []
      }
    } else if (props.dataSource) {
      tableData.value = props.dataSource
    }
  } catch (error) {
    console.error('SelectModal loadData error:', error)
    tableData.value = []
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  if (props.pageConfig) {
    emit('update:pageConfig', { ...props.pageConfig, page: 1 })
  }
  loadData(searchForm.value)
}

const handleReset = () => {
  searchForm.value = {}
  if (props.pageConfig) {
    emit('update:pageConfig', { ...props.pageConfig, page: 1 })
  }
  loadData()
}

const handleTableChange = (pag: TablePaginationConfig) => {
  if (props.pageConfig) {
    emit('update:pageConfig', { ...props.pageConfig, page: pag.current || 1, pageSize: pag.pageSize || props.pageConfig.pageSize })
  }
  loadData(searchForm.value)
}

const handleSelect = (record: any) => {
  if (props.multiple) {
    const index = internalSelectedItems.value.findIndex((item) => getItemKey(item) === getItemKey(record))
    if (index > -1) {
      internalSelectedItems.value.splice(index, 1)
    } else {
      internalSelectedItems.value.push(record)
    }
  } else {
    internalSelectedItems.value = [record]
  }
  emit('update:selectedItems', internalSelectedItems.value)
}

const removeSelected = (item: any) => {
  const index = internalSelectedItems.value.findIndex((selected) => getItemKey(selected) === getItemKey(item))
  if (index > -1) {
    internalSelectedItems.value.splice(index, 1)
    emit('update:selectedItems', internalSelectedItems.value)
  }
}

const clearSelected = () => {
  internalSelectedItems.value = []
  emit('update:selectedItems', internalSelectedItems.value)
}

const handleConfirm = () => {
  confirmLoading.value = true
  emit('confirm', internalSelectedItems.value)
  confirmLoading.value = false
  visible.value = false
}

const handleCancel = () => {
  emit('cancel')
  visible.value = false
}

// 打开弹窗
const open = (initialSelected?: any[], apiParams?: Record<string, any>) => {
  visible.value = true
  internalSelectedItems.value = initialSelected ? [...initialSelected] : []
  emit('update:selectedItems', internalSelectedItems.value)
  if (apiParams) {
    Object.assign(props.apiParams, apiParams)
  }
  // 重置分页
  if (props.pageConfig) {
    emit('update:pageConfig', { ...props.pageConfig, page: 1 })
  }
  loadData()
}

// 关闭弹窗
const close = () => {
  visible.value = false
}

// 监听props中的selectedItems变化，同步到内部状态
watch(
  () => props.selectedItems,
  (newSelectedItems) => {
    if (newSelectedItems && newSelectedItems.length > 0) {
      internalSelectedItems.value = [...newSelectedItems]
    }
  },
  { immediate: true, deep: true },
)

// 监听tableData变化，确保选中状态同步
watch(
  tableData,
  () => {
    // 表格数据变化时的处理逻辑可以在这里添加
  },
  { immediate: true },
)

// 暴露方法
defineExpose({
  open,
  close,
  loadData,
  selectedItems: readonly(internalSelectedItems),
})
</script>

<style lang="scss" scoped>
.selectModal {
  .modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 24px;
    border-bottom: 1px solid #f0f0f0;

    .title {
      font-size: 16px;
      font-weight: 500;
      color: #000000d9;
    }

    .close-btn {
      font-size: 16px;
      color: #00000073;
      cursor: pointer;
      transition: color 0.2s;

      &:hover {
        color: #000000d9;
      }
    }
  }

  .modal-body {
    max-height: 600px;
    padding: 16px 24px;
    overflow-y: auto;

    .search-area {
      padding: 16px;
      margin-bottom: 16px;
      background-color: #fafafa;
      border-radius: 6px;
    }

    .table-area {
      margin-bottom: 16px;
    }

    .selected-area {
      padding-top: 16px;
      border-top: 1px solid #f0f0f0;

      .selected-title {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
        font-size: 14px;
        color: #000000d9;

        .selected-icon {
          margin-right: 8px;
          color: #1890ff;
        }
      }

      .selected-list {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;

        .selected-tag {
          margin: 0;
        }
      }
    }
  }

  .modal-footer {
    display: flex;
    justify-content: flex-end;
    padding: 16px 24px;
    border-top: 1px solid #f0f0f0;
  }
}

// 自定义滚动条样式
.modal-body::-webkit-scrollbar {
  width: 6px;
}

.modal-body::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.modal-body::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.modal-body::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
