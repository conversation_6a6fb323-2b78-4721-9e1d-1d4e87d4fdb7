<template>
  <a-drawer title="选择采购单" width="85vw" :visible="visible" @close="handleClose" :maskClosable="false" destroyOnClose>
    <div class="flex h-full overflow-auto">
      <div class="p-16px h-full overflow-auto w65vw">
        <a-space class="mb-12">
          <a-input placeholder="采购单号" v-model:value="queryParams.number" :maxlength="200" allowClear />
          <a-select v-model:value="queryParams.type" placeholder="请选择采购类型" allowClear>
            <a-select-option v-for="item in purchaseTypes" :key="item.value" :value="item.value">
              {{ item.label }}
            </a-select-option>
          </a-select>
          <a-input placeholder="供应商子公司名称" v-model:value="queryParams.company_supplier_name" :maxlength="200" allowClear />
          <a-select v-model:value="queryParams.warehourse_id" placeholder="请选择收料仓库" allowClear>
            <a-select-option v-for="item in warehouseList" :key="item.key" :value="item.key">{{ item.value }}</a-select-option>
          </a-select>
          <!-- <a-input placeholder="付款账号" v-model:value="queryParams.payment_account" :maxlength="200" allowClear /> -->
          <a-button type="primary" @click="handleSearch">查询</a-button>
          <a-button @click="handleReset">重置</a-button>
        </a-space>
        <vxe-table
          v-if="leftVisible"
          :border="true"
          ref="productTableRef"
          size="mini"
          :row-config="{ isHover: true, keyField: 'id', height: 40 }"
          :custom-config="{ mode: 'popup' }"
          :data="data"
          :show-overflow="true"
          :show-header-overflow="true"
          :show-footer-overflow="true"
          :column-config="{ resizable: true }"
          class="tableBoxwidth"
          :checkbox-config="{
            checkField: 'checked',
            trigger: 'row',
          }"
          min-height="0"
          stripe
          v-bind="$attrs"
          @checkbox-all="handleSelectAll"
          @checkbox-change="handleSelectChange"
        >
          <vxe-column type="checkbox" width="50" fixed="left"></vxe-column>
          <slot name="column">
            <template v-for="i in tableKey" :key="i.field">
              <vxe-column v-bind="i">
                <template #default="{ row }">
                  <span>{{ row[i.field] }}</span>
                </template>
              </vxe-column>
            </template>
          </slot>
        </vxe-table>
        <div class="paginationBox">
          <div class="pagination">
            <a-pagination
              show-quick-jumper
              :total="total"
              show-size-changer
              v-model:current="queryParams.page"
              v-model:page-size="queryParams.pageSize"
              :page-size-options="['10', '20', '30', '40', '50']"
              @change="handlePageChange"
              size="small"
            >
              <template #buildOptionText="props">
                <span>{{ props.value }}条/页</span>
              </template>
            </a-pagination>
          </div>
          <div class="totalBox">
            <div class="text">总数:</div>
            <div class="total">{{ total }}</div>
          </div>
        </div>
      </div>
      <div class="flex-1 overflow-y-auto p-16 text-black">
        <div class="flex mb-12 h30px line-height-30px h-30px">
          <div class="text-16px">已选{{ selectProductList.length }}个采购单</div>
          <a-button class="ml-auto" type="primary" @click="cleanCheck">清空</a-button>
        </div>
        <vxe-table
          v-if="leftVisible"
          :border="true"
          ref="selectTableRef"
          size="mini"
          :row-config="{ isHover: true, keyField: 'purcharse_order_id', height: 40 }"
          :custom-config="{ mode: 'popup' }"
          :data="selectProductList"
          :show-overflow="true"
          :show-header-overflow="true"
          :show-footer-overflow="true"
          :column-config="{ resizable: true }"
          class="tableBoxwidth"
          min-height="0"
          stripe
          v-bind="$attrs"
        >
          <vxe-column type="seq" title="序号" width="60" fixed="left"></vxe-column>
          <template v-for="i in selectTableKey" :key="i.field">
            <vxe-column v-bind="i">
              <template #default="{ row }">
                <div class="flex items-center justify-between">
                  <span>{{ row[i.field] }}</span>
                  <a-button type="link" class="ml-2" @click="handleUnselectRow(row)">取消选中</a-button>
                </div>
              </template>
            </vxe-column>
          </template>
        </vxe-table>
      </div>
    </div>
    <template #footer>
      <a-space>
        <a-button type="primary" @click="handleSelectProduct" :disabled="hasSupplierConflict">确定</a-button>
        <a-button @click="handleClose">取消</a-button>
      </a-space>
    </template>
  </a-drawer>
</template>

<script setup lang="ts">
import { VxeTableInstance } from 'vxe-table'
import { ref, computed } from 'vue'
import { message } from 'ant-design-vue'

// eslint-disable-next-line import/extensions
import { GetPurchaseOrderListSmall } from '@/servers/paymentOrder.ts'
// eslint-disable-next-line import/extensions
import { GetWarehouses } from '@/servers/Purchaseapplyorder.ts'

interface WarehouseItem {
  key: string | number
  value: string
}

interface ProductItem {
  id: number
  number: string
  [key: string]: any
}

const productTableEl = useTemplateRef<VxeTableInstance>('productTableRef')

const emits = defineEmits(['selectProduct', 'updateProduct'])

const visible = ref(false)
const leftVisible = ref(false)

const data = ref<ProductItem[]>([])

const total = ref(0)

const selectProductList = ref<ProductItem[]>([])

const queryParams = ref<any>({
  page: 1,
  pageSize: 10,
  sortField: null,
  sortType: null,
  number: null,
  company_supplier_name: null,
  warehourse_id: null,
  company_supplier_id: null,
  payment_account: null,
})

const warehouseList = ref<WarehouseItem[]>([])

const tableKey = ref([
  {
    field: 'number',
    title: '采购单号',
    width: 140,
  },
  {
    field: 'company_supplier_name',
    title: '供应商名称',
    width: 160,
  },
  {
    field: 'type_string',
    title: '采购类型',
    width: 120,
  },
  {
    field: 'product_type_string',
    title: '商品类型',
    width: 100,
  },
  {
    field: 'buyer_name',
    title: '采购员',
    width: 100,
  },
  {
    field: 'purchase_time',
    title: '采购时间',
    width: 160,
  },
  {
    field: 'warehourse_name',
    title: '采购收料仓',
    width: 160,
  },
  {
    field: 'remark',
    title: '备注',
    minWidth: 160,
  },
])

const selectTableKey = ref([
  {
    field: 'number',
    title: '采购单编号',
    width: '100%',
  },
])

// 添加临时选中列表
const tempSelectedList = ref<any[]>([])

// 添加一个变量来存储初始选中状态
const initialSelection = ref<any[]>([])

// 采购类型选项
const purchaseTypes = [
  { value: '1', label: '标准采购申请' },
  { value: '2', label: '1688采购订单' },
  // 以后可扩展更多类型
]

// 判断当前临时选中采购单是否存在不同供应商
const hasSupplierConflict = computed(() => {
  if (tempSelectedList.value.length <= 1) return false

  // 优先检查供应商子公司ID，如果没有则检查供应商ID
  const firstItem = tempSelectedList.value[0]
  const firstCompanySupplierId = firstItem.company_supplier_id
  const firstSupplierId = firstItem.supplier_id

  // 如果有供应商子公司ID，使用供应商子公司进行比较
  if (firstCompanySupplierId !== undefined && firstCompanySupplierId !== null) {
    return tempSelectedList.value.some((item) => item.company_supplier_id !== firstCompanySupplierId)
  }

  // 如果没有供应商子公司ID，则使用供应商ID进行比较
  if (firstSupplierId !== undefined && firstSupplierId !== null) {
    return tempSelectedList.value.some((item) => item.supplier_id !== firstSupplierId)
  }

  return false
})

const handleSelectChange = () => {
  selectChangeEvent('change')
}

const handleSelectAll = () => {
  selectChangeEvent('all')
}
// 修改 selectChangeEvent 函数
const selectChangeEvent = (type: string) => {
  const $table = productTableEl.value
  if (!$table) return

  // 获取当前页面选中的行
  const currentPageSelectedRows = $table.getCheckboxRecords() || []

  // 找出新选中的行（当前选中但不在临时选中列表中的）
  const newlySelectedRows = currentPageSelectedRows.filter((row) => !tempSelectedList.value.some((selected) => selected.id === row.id || selected.purcharse_order_id === row.id))

  // 如果有新选中的行，且已有选中的采购单，需要校验供应商
  if (newlySelectedRows.length > 0 && tempSelectedList.value.length > 0) {
    // 获取当前已选中的供应商信息
    const existingItem = tempSelectedList.value[0]
    const existingCompanySupplierId = existingItem.company_supplier_id
    const existingSupplierId = existingItem.supplier_id

    // 检查新选中的采购单是否与已选中的供应商一致
    let hasDifferentSupplier = false

    // 如果有供应商子公司ID，使用供应商子公司进行比较
    if (existingCompanySupplierId !== undefined && existingCompanySupplierId !== null) {
      hasDifferentSupplier = newlySelectedRows.some((item) => item.company_supplier_id !== existingCompanySupplierId)
    } else if (existingSupplierId !== undefined && existingSupplierId !== null) {
      // 如果没有供应商子公司ID，则使用供应商ID进行比较
      hasDifferentSupplier = newlySelectedRows.some((item) => item.supplier_id !== existingSupplierId)
    }

    if (hasDifferentSupplier) {
      message.warning(`供应商不一致，请重新选择`)
      // 只取消新选中的行
      if (type == 'change') {
        newlySelectedRows.forEach((row) => {
          let shouldUnselect = false
          if (existingCompanySupplierId !== undefined && existingCompanySupplierId !== null) {
            shouldUnselect = row.company_supplier_id !== existingCompanySupplierId
          } else if (existingSupplierId !== undefined && existingSupplierId !== null) {
            shouldUnselect = row.supplier_id !== existingSupplierId
          }
          if (shouldUnselect) {
            $table.setCheckboxRow(row, false)
          }
        })
      }
      return
    }
  }

  // 更新临时选中列表
  // 1. 保留不在当前页的已选中项
  // 2. 添加当前页新选中的项
  // 3. 移除当前页取消选中的项
  tempSelectedList.value = [
    ...tempSelectedList.value.filter((selected) => !data.value.some((row) => row.id === selected.id || row.id === selected.purcharse_order_id)),
    ...currentPageSelectedRows.map((row) => ({ ...row, purcharse_order_id: row.id })),
  ]

  // 更新当前页数据的选择状态
  const currentPageIds = new Set(currentPageSelectedRows.map((row) => row.id))
  data.value = data.value.map((row) => ({
    ...row,
    checked: currentPageIds.has(row.id),
  }))

  // 更新显示列表（右侧面板）
  selectProductList.value = JSON.parse(JSON.stringify(tempSelectedList.value))
}

// open方法初始化临时选中列表
const open = (selectedProducts: any[] = [], supplierId: any = null, supplierName: any = null) => {
  console.log('要知道ids', selectedProducts)
  getWarehouseList()

  // 先重置查询条件
  queryParams.value = {
    page: 1,
    pageSize: 10,
    sortField: null,
    sortType: null,
    number: null,
    company_supplier_name: null,
    warehourse_id: null,
    company_supplier_id: null,
    payment_account: null,
  }

  // 用父组件传入的 selectedProducts 初始化临时选中列表
  initialSelection.value = JSON.parse(JSON.stringify(selectedProducts))
  tempSelectedList.value = JSON.parse(JSON.stringify(selectedProducts))
  selectProductList.value = JSON.parse(JSON.stringify(selectedProducts))
  data.value = []

  // 设置查询参数（覆盖重置后的默认值）
  queryParams.value = {
    ...queryParams.value,
    company_supplier_id: supplierId,
    company_supplier_name: supplierName,
  }
  visible.value = true
  leftVisible.value = true

  // 如果有供应商信息，自动执行查询
  if (supplierId || supplierName) {
    // 构建查询参数
    const searchParams = {
      ...queryParams.value,
      page: 1,
    }

    // 移除空值和未定义的参数
    Object.keys(searchParams).forEach((key) => {
      if (searchParams[key] === null || searchParams[key] === undefined || searchParams[key] === '') {
        delete searchParams[key]
      }
    })

    // 更新查询参数
    queryParams.value = searchParams
  }

  // 获取列表并设置选中状态
  getProductList().then(() => {
    // 更新表格数据选中状态
    data.value = data.value.map((row) => ({
      ...row,
      checked: tempSelectedList.value.some((selected) => selected.purcharse_order_id === row.id),
    }))

    // 确保表格选中状态与数据一致
    const $table = productTableEl.value
    if ($table) {
      data.value.forEach((row) => {
        if (row.checked) {
          $table.setCheckboxRow(row, true)
        }
      })
    }
  })
}

// 修改 handleSelectProduct 函数
const handleSelectProduct = () => {
  // 无论是否有选择，都触发事件并关闭抽屉
  emits('selectProduct', tempSelectedList.value, null, null)
  visible.value = false
  leftVisible.value = false
}

// 修改 handleClose 方法
const handleClose = () => {
  // 恢复初始选中状态
  tempSelectedList.value = JSON.parse(JSON.stringify(initialSelection.value))
  selectProductList.value = JSON.parse(JSON.stringify(initialSelection.value))

  // 只关闭抽屉
  visible.value = false
  leftVisible.value = false
}

// 获取收料仓库列表
const getWarehouseList = async () => {
  const res = await GetWarehouses()
  warehouseList.value = res.data.map((item: any) => ({
    key: item.key,
    value: item.value,
  }))
}

// 获取商品列表
const getProductList = async () => {
  const res = await GetPurchaseOrderListSmall(queryParams.value)

  // 为每一行数据添加选中状态
  const newList = res.data.list.map((row) => ({
    ...row,
    checked: selectProductList.value.some((selected) => selected.id === row.id || selected.purcharse_order_id === row.id || selected.number === row.number),
  }))

  data.value = newList
  total.value = res.data.total

  // 设置选中状态
  const $table = productTableEl.value
  if ($table) {
    // 遍历当前页数据，设置选中状态
    data.value.forEach((row) => {
      if (row.checked) {
        $table.setCheckboxRow(row, true)
      }
    })
  }
}

// 分页
const handlePageChange = (page: number, pageSize: number) => {
  queryParams.value.page = page
  queryParams.value.pageSize = pageSize
  getProductList()
}

// 查询
const handleSearch = () => {
  // 构建查询参数
  const searchParams = {
    ...queryParams.value,
    page: 1,
  }

  // 移除空值和未定义的参数
  Object.keys(searchParams).forEach((key) => {
    if (searchParams[key] === null || searchParams[key] === undefined || searchParams[key] === '') {
      delete searchParams[key]
    }
  })

  // 更新查询参数
  queryParams.value = searchParams
  getProductList()
}

// 修改 handleUnselectRow 方法
const handleUnselectRow = (row: any) => {
  // 从临时选中列表中移除
  tempSelectedList.value = tempSelectedList.value.filter((item) => item.purcharse_order_id !== row.purcharse_order_id)

  // 更新左侧表格的选中状态
  const $table = productTableEl.value
  if ($table) {
    const targetRow = data.value.find((item) => item.id === row.purcharse_order_id)
    if (targetRow) {
      $table.setCheckboxRow(targetRow, false)
    }
  }

  // 更新当前页数据的选中状态
  data.value = data.value.map((dataRow) => ({
    ...dataRow,
    checked: tempSelectedList.value.some((selected) => selected.purcharse_order_id === dataRow.id),
  }))

  // 更新显示列表（但不触发父组件更新）
  selectProductList.value = JSON.parse(JSON.stringify(tempSelectedList.value))
}

// 修改 cleanCheck 方法
const cleanCheck = async () => {
  // 只清空临时选中列表，保持 initialSelection 不变
  tempSelectedList.value = []
  const $table = productTableEl.value
  if ($table) {
    $table.clearCheckboxRow()
  }

  // 更新当前页数据的选中状态
  data.value = data.value.map((row) => ({
    ...row,
    checked: false,
  }))

  // 更新显示列表（但不触发父组件更新）
  selectProductList.value = []
}

// 修改重置方法
const handleReset = () => {
  const company_supplier_id = queryParams.value.company_supplier_id
  queryParams.value = {
    page: 1,
    pageSize: 10,
    sortField: null,
    sortType: null,
    number: null,
    company_supplier_name: null,
    warehourse_id: null,
    payment_account: null,
    company_supplier_id,
  }
  getProductList()
}

// 暴露open和close方法供父组件调用
defineExpose({
  open,
  close: () => {
    visible.value = false
    leftVisible.value = false
  },
})
</script>

<style scoped lang="scss">
.paginationBox {
  display: flex;
  align-items: center;
  margin-top: 0.83rem;

  .totalBox {
    display: flex;
    align-items: flex-end;
    margin-left: 20px;
    color: #000;

    .text {
      margin-right: 8px;
      font-size: 14px;
    }

    .total {
      font-size: 16px;
      color: #1890ff;
    }
  }
}
</style>
