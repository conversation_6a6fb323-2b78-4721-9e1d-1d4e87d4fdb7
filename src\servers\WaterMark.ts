// 水印接口
import { request } from './request'

// 获取角色下拉框
export const GetRoleSelectOption = (data) => {
  return request({ url: '/api/Watermark/GetRoleSelectOption', data })
}
// 获取水印配置信息
export const GetWatermarkInfo = (data) => {
  return request({ url: '/api/Watermark/GetWatermarkInfo', data })
}
// 保存ui水印配置
export const SaveUIConfig = (data) => {
  return request({ url: '/api/Watermark/SaveUIConfig', data })
}
// 获取系统界面水印
export const GetSystemWatermark = (data) => {
  return request({ url: '/api/Watermark/GetSystemWatermark', data }, 'GET')
}
// 保存预览水印配置
export const SaveReviewConfig = (data) => {
  return request({ url: '/api/Watermark/SaveReviewConfig', data })
}
// 保存下载水印配置
export const SaveDownloadConfig = (data) => {
  return request({ url: '/api/Watermark/SaveDownloadConfig', data })
}
// 获取日志
export const GetOpLogInfos = (data) => {
  return request({ url: '/api/Watermark/GetOpLogInfos', data })
}
// 预览水印
export const PreviewWatermark = (data) => {
  return request({ url: '/api/Watermark/PreviewWatermark', data })
}
