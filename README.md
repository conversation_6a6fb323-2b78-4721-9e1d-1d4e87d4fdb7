# OAoverseas

#### 项目描述

版本号: v1.7.0
描述：SRM系统
内容：

1、价目表和调价表新增材质类型的新增和调价，校验的交互效果修改
2、申请单支持不同供应商的申请单预处理后生成采购单，申请单增加预处理页面
3、采购单，变更单，预约入库单把采购总数拆分成采购数量和赠品数量，所有金额计算都依赖采购数量和换算值来计算
4、修改采购单，变更单，预约入库单的字表分摊数量规则
5、增加下载中心页面
6、项目的供应商、供应商子公司、人员下拉框整体替换为新的通用接口
7、新增商品库界面

#### 软件架构

vite脚手架
vue3 Ts弱校验
ant-design-vue ui框架
antv 蚂蚁图表
vxe-table ui表格结合
sortablejs 拖拽插件

#### 安装教程

1.  npm install 安装依赖
2.  npm run dev 运行
3.  npm run build 打包

#### 开发使用说明 - UI规范

##### 全局

1. 禁止使用Number.toFixed()，改用[Number.roundNext()](./src/core/index.ts)，因为toFixed不是正确的四舍五入法，如需使用银行家舍入则使用Number.bankRound()

##### 头部

1. Tabs默认使用 [StatusTabs组件](/src/components/StatusTabs.vue)

##### Table组件（VxeTable）

1. <span style="color: red;">禁止使用ant-table（将逐渐废弃原来的组件，不符合UI规范）</span>
2. 若包含点击跳转的样式 使用 .link | .link-default
3. 字段显示状态的使用特殊渲染 { cellRender: { name: 'status' } } [详细参数请阅读代码](./src/core/VxeUi.ts)
4. 字段需复制的使用渲染 { cellRender: { name: 'copy' } }
5. 字段使用图片的使用渲染 { cellRender: { name: 'image' } }
6. 字段显示金额（需要货币符号）的使用右对齐format { formatter: 'price', align: 'right' }
7. 字段显示数量且整数的使用右对齐format { formatter: 'number', align: 'right' }
8. 操作列使用 [RightOperate组件（可用AI辅助生成代码）](/src/components/EasyTable/RightOperate.vue)

#### 参与贡献

1.  Fork 本仓库
2.  新建 Feat_xxx 分支
3.  提交代码
4.  新建 Pull Request

#### 特技

1.  使用 Readme_XXX.md 来支持不同的语言，例如 Readme_en.md, Readme_zh.md
2.  Gitee 官方博客 [blog.gitee.com](https://blog.gitee.com)
3.  你可以 [https://gitee.com/explore](https://gitee.com/explore) 这个地址来了解 Gitee 上的优秀开源项目
4.  [GVP](https://gitee.com/gvp) 全称是 Gitee 最有价值开源项目，是综合评定出的优秀开源项目
5.  Gitee 官方提供的使用手册 [https://gitee.com/help](https://gitee.com/help)
6.  Gitee 封面人物是一档用来展示 Gitee 会员风采的栏目 [https://gitee.com/gitee-stars/](https://gitee.com/gitee-stars/)
