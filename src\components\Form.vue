<template>
  <div class="flex justify-between items-center mb-12 mt-10" v-if="$slots?.header">
    <slot name="header"></slot>
  </div>
  <div v-if="count" @keypress.enter="getList">
    <div class="relative">
      <div class="formBox" v-show="!isConmmence">
        <div class="quickBox" v-if="isQuicks">
          <slot name="formTop"></slot>
          <template v-for="item in quicks" :key="item.key">
            <div class="quickLine" :style="{ width: item.width + 'px', flex: item.flex || undefined }">
              <div class="quickLabel" :style="item.quickLabelWidth ? { minWidth: `${item.quickLabelWidth}px`, flex: `0 0 ${item.quickLabelWidth}px` } : {}">{{ item.quickLabel || item.label }}</div>
              <div class="quickContent">
                <div
                  class="easy-checkbox-btn mb-6"
                  :class="{
                    active: item.value === null || item?.value?.length === 0,
                  }"
                  @click="handleQuick(item, null)"
                  v-if="!item.quickNotFull"
                >
                  全部
                </div>
                <template v-for="v in item.quicks ? item.quicks : item.selectArr" :key="v.value">
                  <div
                    class="easy-checkbox-btn mb-6"
                    :class="{
                      active: isQuickActive(item, v),
                    }"
                    @click="handleQuick(item, v)"
                  >
                    {{ v.label || v }}
                  </div>
                </template>
              </div>
              <div v-if="item.line" class="partingLine"></div>
            </div>
          </template>
        </div>
        <div class="flex flex-wrap">
          <template v-for="item in form.filter((f) => !f.isFreeze)" :key="item.label">
            <div class="item" v-show="item.isShow">
              <div class="li" v-if="item.type == 'inputDlg'">
                <a-input-group compact class="!flex">
                  <a-input v-model:value="item.value" :placeholder="item.label" allow-clear @blur="item.value = `${item.value || ''}`.trim()" />
                  <a-button @click="showInputDlg(item)" class="!flex items-center justify-center !w-38px">
                    <template #icon>
                      <span class="iconfont icon-chazhao_find text-14px c-#000"></span>
                    </template>
                  </a-button>
                </a-input-group>
              </div>
              <div class="li" v-if="item.type == 'input'">
                <a-input v-model:value="item.value" :placeholder="item.label" allow-clear v-bind="item" :maxlength="200" />
              </div>
              <div class="li" v-if="item.type == 'inputNumber'">
                <a-input-number class="w-full" v-model:value="item.value" :placeholder="item.label" allow-clear v-bind="item" :controls="false" :max="999999999" @change="changeInput(item)" />
              </div>
              <div class="li" v-if="item.type == 'select'">
                <a-tooltip v-if="item.multiple" :title="getSelectedLabels(item)" placement="top" :overlay-style="{ maxWidth: '300px', maxHeight: '200px', overflowY: 'auto', position: 'relative' }">
                  <div class="select-wrapper">
                    <a-select
                      v-model:value="item.value"
                      :mode="item.multiple ? 'multiple' : null"
                      style="width: 100%"
                      :placeholder="item.label"
                      maxTagCount="responsive"
                      :options="item.selectArr"
                      :filter-option="filterOption"
                      :show-search="item.multiple ? false : true"
                      allowClear
                      showArrow
                      v-bind="item"
                    ></a-select>
                  </div>
                </a-tooltip>
                <a-select
                  v-else
                  v-model:value="item.value"
                  :mode="item.multiple ? 'multiple' : null"
                  style="width: 100%"
                  :placeholder="item.label"
                  maxTagCount="responsive"
                  :options="item.selectArr"
                  :filter-option="filterOption"
                  :show-search="item.multiple ? !!item.api : true"
                  allowClear
                  v-bind="item"
                ></a-select>
              </div>
              <div class="li" v-if="item.type == 'search'">
                <a-auto-complete
                  v-model:value="item.value"
                  style="width: 100%"
                  allowClear
                  :placeholder="item.label"
                  :options="item.searchArr || item.selectArr"
                  @search="handleSearch(item)"
                  v-bind="item"
                ></a-auto-complete>
              </div>
              <div class="li" v-if="item.type == 'select_Tree'">
                <a-tooltip v-if="item.multiple" :title="getTreeSelectLabels(item)" placement="top" :overlay-style="{ maxWidth: '300px', maxHeight: '200px', overflowY: 'auto', position: 'relative' }">
                  <a-tree-select
                    v-if="item.showDescription != 1"
                    v-model:value="item.value"
                    style="width: 100%"
                    :placeholder="item.label"
                    allow-clear
                    showArrow
                    :tree-data="item.selectArr"
                    :fieldNames="item.fieldNames ? item.fieldNames : ''"
                    :maxTagCount="0"
                    :listHeight="400"
                    :dropdownMatchSelectWidth="250"
                    :treeCheckable="!!item.multiple"
                    :multiple="!!item.multiple"
                  />
                </a-tooltip>
                <a-select v-else style="width: 100%" :placeholder="item.label">
                  <template #dropdownRender="{ menuNode: menu }">
                    <component :is="menu" />
                    <div style="padding: 8px 0; text-align: center">
                      {{ item.description }}
                    </div>
                  </template>
                </a-select>
              </div>
              <div class="li2" v-if="item.type == 'range-picker'">
                <div class="rangeBox">
                  <!-- <div class="labelText">{{ item.label }}:</div> -->
                  <div class="nubInputBox">
                    <a-range-picker
                      :placeholder="item.placeholder"
                      v-model:value="item.value"
                      :picker="item.picker"
                      :valueFormat="item.valueFormat || 'YYYY-MM-DD HH:mm:ss'"
                      :show-time="item.showTime"
                      @change="
                        () =>
                          (item.value = [
                            dayjs(item.value[0])
                              .startOf('day')
                              .format(item.valueFormat || 'YYYY-MM-DD HH:mm:ss'),
                            dayjs(item.value[1])
                              .endOf('day')
                              .format(item.valueFormat || 'YYYY-MM-DD HH:mm:ss'),
                          ])
                      "
                      v-bind="item"
                    />
                  </div>
                </div>
              </div>
              <div class="li" v-if="item.type == 'supplier'">
                <Supplier
                  v-model="item.value"
                  :label="item.label"
                  :api="item.api"
                  :pageSize="item.pageSize"
                  :keyword="item.keyword"
                  :formatter="item.formatter"
                  :onBeforeConfirm="item.onBeforeConfirm"
                  @clear="item.onClear && item.onClear()"
                >
                  <template #topSlot v-if="item.topSlot">
                    <slot :name="item.topSlot" />
                  </template>
                </Supplier>
              </div>

              <div class="li" v-if="item.type === 'select-supplier'">
                <SelectSupplier
                  v-model:value="item.value"
                  :title="item.label"
                  :api="item.api"
                  :apiParams="typeof item.apiParams === 'function' ? item.apiParams(formState) : item.apiParams"
                  :mode="item.mode"
                ></SelectSupplier>
              </div>

              <div class="li" v-if="item.type === 'cascader'">
                <a-cascader class="w-full" v-model:value="item.value" :options="item.selectArr" :show-search="{ filter: cascaderFilter }" :placeholder="`${item.label}`" />
              </div>
              <div class="li flex items-center" v-if="item.type == 'input-range'">
                <div class="flex">
                  <a-input-number :controls="false" class="w-50%" v-model:value="item.value" :placeholder="item.label == '销量' ? '数量' : '下限'" :min="-999999999" :max="999999999" />
                  <div class="w-2"></div>
                  <a-input-number :controls="false" class="w-50%" v-model:value="item.value2" :placeholder="item.label == '销量' ? '数量' : '上限'" :min="-999999999" :max="999999999" />
                </div>
              </div>

              <div class="li2" v-if="item.type === 'number-range'">
                <EasyNumberRange v-model:value="item.value" :placeholder="item.placeholder" />
              </div>
            </div>
          </template>

          <div>
            <a-button type="primary" @click="getList" class="mr-4">查询</a-button>
            <a-button @click="resetForm" class="mr-4">重置</a-button>
            <a-button :icon="h(SettingOutlined)" @click="emit('setting')" class="mr-4">自定义设置</a-button>
            <a-button class="mr-4" @click="handleShowQuick">另存为快捷查询</a-button>
          </div>
          <div @click="getForm(item)" v-for="(item, i) in shortcutQueryArr" :key="item.recordText" class="easy-tag h-28 ml-0! mr-4">
            {{ item.recordText || '快捷查询' + (i + 1) }}
            <CloseOutlined class="ml-5 text-#C0C0C0" @click.prevent="tapCloseRome(i)" />
          </div>
        </div>
      </div>
      <div class="unfold">
        <a-button class="unfold-btn" size="small" type="primary" @click="isConmmence = !isConmmence">{{ isConmmence ? '展开' : '收起' }}</a-button>
      </div>
    </div>
    <a-modal v-model:open="isShowInputDlg" title="批量输入" okText="确定" @ok="closeInputDlg" @cancel="isShowInputDlg = false" style="top: 200px">
      <a-form-item
        :label="inputLabel"
        :label-col="{
          style: {
            width: '130px',
          },
        }"
      >
        <a-textarea v-model:value="inputText" :placeholder="`多个${inputLabel}，以逗号分隔或每行一个${inputLabel}`" :rows="10" />
      </a-form-item>
      <template #footer>
        <a-button style="float: left" @click="inputText = ''">清空</a-button>
        <a-button @click="isShowInputDlg = false">取消</a-button>
        <a-button type="primary" @click="closeInputDlg">确认</a-button>
      </template>
    </a-modal>
    <div class="btnlist">
      <slot name="btnlist"></slot>
    </div>

    <a-modal :width="350" @afterOpenChange="formRef.clearValidate()" v-model:open="isRecordModal" title="另存为快捷查询" okText="确定" cancelText="取消" @ok="tapRecordModalOk">
      <a-form style="margin-top: 20px" :colon="false" :label-col="{ style: { width: '130px', marginRight: '20px' } }" ref="formRef" :model="formData">
        <a-form-item
          label=""
          name="label"
          :rules="[
            { required: true, message: '请输入快捷查询命名' },
            { max: 20, message: '输入内容不可超过20字符' },
          ]"
        >
          <a-input auto-focus v-model:value="formData.label" placeholder="请输入快捷查询命名" />
        </a-form-item>
      </a-form>
    </a-modal>
    <Screening ref="screeningRef" @screeningConfirmed="screeningConfirmed"></Screening>
  </div>
</template>

<script setup lang="ts">
import dayjs from 'dayjs'
import { message } from 'ant-design-vue'
import { SettingOutlined, CloseOutlined } from '@ant-design/icons-vue'
import { h } from 'vue'

import { filterOption } from '@/utils/index'

import { SrmSaveViewQuickQuery, SrmGetViewQuickQuery } from '@/servers/Common'

const props = defineProps({
  pageType: {
    type: Number,
    default: 0,
  },
  clearCb: {
    type: Function,
    default: () => {},
  },
})

const isConmmence = ref(false)

// 批量输入
const isShowInputDlg = ref(false)
const inputLabel = ref(null)
const inputText = ref('')
const inputKey = ref(null)

const formRef = ref()
const form = defineModel<any>('form')
const formData = ref({
  label: '',
})

const formState = computed(() => {
  return form.value.reduce((acc, cur) => {
    let value = cur.value
    if (cur.type === 'select-supplier') {
      value = (cur?.value || []).map((f) => {
        return JSON.parse(f)?.value
      })
      if (cur.mode === 'single') {
        value = value?.[0]
      }
    }
    if (cur.type === 'cascader') {
      value = cur.value?.[cur.value.length - 1] || undefined
    }
    return {
      ...acc,
      [cur.key]: value,
    }
  })
})

const isQuicks = computed(() => form.value.some((v) => v.isQuicks && v.isShow))
const quicks = computed(() => form.value.filter((v) => v.isQuicks && v.isShow))
const emit = defineEmits(['search', 'setting', 'resetForm'])
const path = useRoute().path
onMounted(() => {
  if (props.pageType) {
    SrmGetViewQuickQuery({
      page_type: props.pageType,
    }).then(({ data }) => {
      if (data?.quick_query_data && typeof data.quick_query_data === 'string') {
        shortcutQueryArr.value = JSON.parse(data.quick_query_data)
      } else {
        shortcutQueryArr.value = []
      }
    })
  }

  initScreening()
})
const changeInput = (item) => {
  setTimeout(() => {
    if (item.value >= 999999999) {
      item.value = null
    }
  })
}
const initScreening = async () => {
  const obj = localStorage.getItem('screeningObj') ? JSON.parse(localStorage.getItem('screeningObj') || '') : {}
  if (obj[path]) {
    const arr = [] as any
    form.value.forEach((y) => {
      obj[path].forEach((x) => {
        if (x.key === y.key) {
          y.isShow = x.isShow
          arr.push(y)
        }
      })
    })
    form.value = arr
  } else {
    form.value = form.value.map((v: any) => {
      const newItem = { ...v, isShow: true }

      handleApiData(v, newItem)

      return newItem
    })
  }
}

const cascaderFilter = (inputValue, path) => {
  return path.some((option) => option.label.toLowerCase().indexOf(inputValue.toLowerCase()) > -1)
}

const handleSearch = (item) => {
  item.searchArr = item.selectArr?.filter((v) => v.label.toLowerCase().indexOf(item.value.toLowerCase()) >= 0 || v.value.toLowerCase().indexOf(item.value.toLowerCase()) >= 0) || []
}

// 获取选中项的标签文本
const getSelectedLabels = (item) => {
  if (!item.value || !item.selectArr) return ''

  const selectedLabels = item.value.map((value) => {
    const option = item.selectArr.find((opt) => opt.value === value)
    return option ? option.label : value
  })

  return selectedLabels.join('、')
}

const isQuickActive = (quickItem, v: Record<string, any>) => {
  if (Array.isArray(quickItem.value)) {
    if (quickItem.value.length > 0) {
      return quickItem.value == v.value || quickItem?.value.includes(v.value ?? v)
    }
    return false
  }
  return quickItem.value == (v.value ?? v)
}

const handleQuick = (item, v) => {
  const value = v?.value ?? v

  if (v === null) item.value = item.multiple ? [] : null
  else {
    if (item.multiple) {
      if (item.value && item.value.includes(value)) {
        item.value = item.value.filter((x) => x != value)
      } else {
        item.value = item.value ? item.value.concat(value) : [value]
      }
    } else item.value = value
  }

  if (item.onChange) item.onChange()

  nextTick(() => getList())
}
const count = computed(() => form.value.filter((v) => v.isShow).length)

const clearForm = () => {
  form.value.forEach((item) => {
    item.value = item.defaultValue || item.defaultValue >= 0 ? item.defaultValue : item.multiple || /range/.test(item.type) ? [] : null
  })
  props.clearCb()
}

const showInputDlg = (item) => {
  isShowInputDlg.value = true
  inputText.value = item.value
  inputLabel.value = item.label
  inputKey.value = item.key
}

const closeInputDlg = () => {
  form.value.forEach((item) => {
    if (item.key == inputKey.value) {
      item.value = dealStr(inputText.value)
    }
  })
  isShowInputDlg.value = false
}
const dealStr = (val) => {
  if (val) {
    val = val.replace(/\n/g, ',')
    val = val.replace(/，/g, ',')
    val = val.replace(/;/g, ',')
    val = val.replace(/；/g, ',')
    let arr = []
    let str = ''
    arr = val.split(',')
    arr.forEach((it: string) => {
      if (it) {
        str += `${it.trim()},`
      }
    })
    str = str.slice(0, -1)
    return str
  }
}

// 重置表单
const resetForm = () => {
  clearForm()
  getList()
  emit('resetForm')
}
// 获取快捷记录
const shortcutQueryArr = ref([] as any)
const getForm = (item) => {
  const arr = item.formArr
  const formArr = form.value.map((item) => {
    const obj = arr.find((v) => v.key === item.key)
    if (item.multiple) {
      item.value = obj ? obj.value || [] : undefined
    } else {
      item.value = obj ? obj.value : undefined
    }
    return item
  })
  form.value = formArr
  nextTick(() => getList())
}
// 删除快捷记录
const tapCloseRome = (i) => {
  shortcutQueryArr.value.splice(i, 1)
  saveViewQuickQuery()
}
// 查询

const getList = () => {
  emit('search')
}

const isRecordModal = ref(false)
const saveViewQuickQuery = async () => {
  await SrmSaveViewQuickQuery({
    page_type: props.pageType,
    quick_query_data: JSON.stringify(shortcutQueryArr.value),
  })
}
const tapRecordModalOk = async () => {
  try {
    const _form = form.value.reduce((acc, cur) => {
      return [
        ...acc,
        {
          value: cur.value,
          key: cur.key,
          type: cur.type,
        },
      ]
    }, [])
    const obj = {
      recordText: formData.value.label,
      formArr: JSON.parse(JSON.stringify(_form)),
    }
    await formRef.value.validateFields()
    if (shortcutQueryArr.value.find((e) => e.recordText === formData.value.label)) {
      message.warning('存在相同命名的快捷查询')
      return
    }

    shortcutQueryArr.value.push(obj)
    saveViewQuickQuery()
    isRecordModal.value = false
    formData.value.label = ''
    await formRef.value?.clearValidate()
  } catch (e) {
    console.log(e)
  }
}

const handleShowQuick = async () => {
  await formRef.value?.clearValidate()
  isRecordModal.value = true
  formData.value.label = ''
}

const screeningRef = ref()
const openScreening = () => {
  screeningRef.value.init({
    formArr: form.value,
  })
}
const screeningConfirmed = (newForm) => {
  form.value = newForm

  const arr = [] as any
  newForm.forEach((x) => {
    arr.push({ key: x.key, isShow: x.isShow })
  })
  console.log(arr)

  const obj = localStorage.getItem('screeningObj') ? JSON.parse(localStorage.getItem('screeningObj') || '') : {}
  obj[path] = arr
  localStorage.setItem('screeningObj', JSON.stringify(obj))
  resetForm()
  getList()
}

const handleApiData = (v, newItem) => {
  // 处理带有api/select类型，selectArr赋值方式
  const params = {}
  if (typeof v.api === 'function' && (!v.selectArr || v.selectArr.length === 0)) {
    let result
    try {
      result = v.api(params)
    } catch (err) {
      console.error(v.key, err)
      return
    }

    // 判断 result 是否为 Promise
    if (result && typeof result.then === 'function') {
      // 异步函数，返回 Promise
      result
        .then((res) => {
          if (v.type === 'select_Tree') {
            newItem.selectArr = Array.isArray(res.data) ? JSON.parse(JSON.stringify(res.data)) : []
            newItem.fieldNames = { label: 'value', value: 'key', children: 'children' }
          } else {
            newItem.selectArr = Array.isArray(res.data) ? res.data : []
          }
        })
        .catch((error) => {
          console.error(v.key, error)
        })
    } else if (result && Array.isArray(result)) {
      // 同步函数，直接返回数组
      newItem.selectArr = result
    } else if (result && typeof result === 'object' && result.data) {
      // 同步函数，返回对象且有data字段
      const data = Array.isArray(result.data) ? result.data : []
      if (v.type === 'select_Tree') {
        newItem.selectArr = JSON.parse(JSON.stringify(data))
        newItem.fieldNames = { label: 'value', value: 'key', children: 'children' }
      } else {
        newItem.selectArr = data
      }
    } else {
      // 其他情况，设置为空数组
      newItem.selectArr = []
    }
  }
}

// 递归查找所有选中 key 的 label
const getTreeSelectLabels = (item) => {
  const valueArr = Array.isArray(item.value) ? item.value : []
  const labels: string[] = []
  function findLabels(nodes: any[]) {
    if (!Array.isArray(nodes)) return
    nodes.forEach((node) => {
      if (valueArr.includes(node.key)) {
        labels.push(node.value)
      }
      if (node.children && node.children.length) {
        findLabels(node.children)
      }
    })
  }
  findLabels(item.selectArr || [])
  return labels.join('，')
}
defineExpose({
  openScreening,
  clearForm,
})
</script>

<style lang="scss" scoped>
.quickBox {
  display: inline-flex;
  flex-wrap: wrap;
  color: #666;

  .quickLine {
    display: flex;

    .quickLabel {
      display: inline-flex;
      align-items: center;
      height: 26px;
      margin-right: 4px;
      font-weight: 700;
      color: #333;
      white-space: nowrap;
    }

    .quickContent {
      display: inline-flex;

      // flex: 1;
      flex-wrap: wrap;
    }
  }

  .partingLine {
    width: 1px;
    height: 12px;
    margin-inline: 12px;
    margin-top: 7px;
    background-color: #dcdcdc;
  }
}

.formBox {
  position: relative;
  padding: 8px;
  background-color: #fafafa;
  border: 1px solid #eaecee;
  border-radius: 4px;

  .rangeBox {
    display: flex;
    justify-content: space-between;

    .labelText {
      box-sizing: border-box;
      display: flex;
      align-items: center;
      width: 100px;
      padding-left: 8px;
      color: #c0c4cc;
      background-color: #fff;
      border: 0.0625rem solid #d9d9d9;
      border-right: none;
    }

    .nubInputBox {
      flex: 1;

      .ant-picker {
        flex: 1;
      }

      .nubInputLi {
        margin: 0 8px;
      }
    }
  }

  .item {
    .nubInputBox {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 12.5rem;

      .nubInput {
        width: 5.42rem;
      }

      .nubInputLi {
        width: 0.42rem;
        height: 0.08rem;
        background: #ccc;
      }
    }

    .li {
      width: 12.5rem;
      margin-right: 4px;
      margin-bottom: 6px;

      .select {
        width: 150px;

        &.colorActive {
          color: #ccc;
        }
      }

      .cascader {
        width: 100%;
      }
    }

    .li2 {
      width: 25.334rem;
      margin-right: 4px;
      margin-bottom: 6px;

      .treeSelectBox {
        padding-right: 1.33rem;
      }
    }

    .selectAndRangeBox {
      display: flex;

      .select {
        width: 12.5rem;
        margin-right: 8px;
        border-top: none;
        border-right: none;

        :deep(.ant-select-selector) {
          border-top: none;
          border-right: none;
        }

        &.colorActive {
          color: #ccc;
        }
      }
    }

    .timeBox {
      width: 26.25rem;

      .timeTextSelector {
        width: 40%;

        .ant-select {
          width: 100%;
        }
      }

      .timeSelector {
        width: 60%;
      }
    }
  }

  .li3 {
    width: 40rem;
    margin-right: 4px;
  }

  .li5 {
    width: 41.33rem;
    margin-bottom: 6px;

    .treeSelectBox {
      padding-right: 1.33rem;
    }

    .rangeBox {
      display: flex;
      justify-content: space-between;
      padding-right: 1.33rem;

      .labelText {
        box-sizing: border-box;
        display: flex;
        align-items: center;
        padding: 0 0.83rem;
        font-size: 14px;
        color: #777;
        border: 0.0625rem solid #d9d9d9;
        border-right: none;
      }

      .nubInputBox {
        flex: 1;

        .ant-picker {
          flex: 1;
        }

        .nubInputLi {
          margin: 0 8px;
        }
      }
    }

    .selectAndRangeBox {
      display: flex;

      .select {
        width: 12.5rem;
        margin-right: 8px;
        border-top: none;
        border-right: none;

        :deep(.ant-select-selector) {
          border-top: none;
          border-right: none;
        }

        &.colorActive {
          color: #ccc;
        }
      }
    }

    .timeBox {
      width: 26.25rem;

      .timeTextSelector {
        width: 40%;

        .ant-select {
          width: 100%;
        }
      }

      .timeSelector {
        width: 60%;
      }
    }
  }
}

.btnlist {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  margin-top: 12px;
}

:deep(.ant-tag-has-color) {
  margin-top: 4px;
  line-height: 22px;
  color: #666;
}

.unfold {
  position: absolute;
  right: 0;
  bottom: -10px;
  left: 0;
  display: flex;
  align-items: center;
  height: 21px;

  &::before {
    display: block;
    flex: 1;
    height: 2px;
    content: '';
    background: linear-gradient(-90deg, #1890ff, #d8d8d8, transparent);
    border-top-left-radius: 50%;
    border-bottom-left-radius: 50%;
  }

  &::after {
    display: block;
    flex: 1;
    height: 2px;
    content: '';
    background: linear-gradient(90deg, #1890ff, #d8d8d8, transparent);
    border-top-right-radius: 50%;
    border-bottom-right-radius: 50%;
  }

  &-btn {
    width: 40px;
    height: 20px;
    font-size: 12px;
    border-radius: 4px;

    @apply flex items-center justify-center;
  }
}

:deep(.ant-tag-close-icon) {
  color: #666 !important;
}

.select-wrapper {
  width: 100%;
  cursor: pointer;
}
</style>
