<template>
  <a-drawer :maskClosable="false" title="推单到1688" width="70vw" :visible="visible" @close="visible = false" :bodyStyle="{ padding: '0' }">
    <div class="flex h-full">
      <div class="w-full overflow-y-auto p-16">
        <a-radio-group v-model:value="method" :options="methodList" optionType="button" button-style="solid" @change="changeTab" />

        <EasyForm ref="formRef" :formItems="formItems" v-model:form="formData" />

        <div class="flex mb-10px" v-if="method == 0">
          <a-button class="ml-auto" @click="getInventoryBtn">获取1688库存</a-button>
        </div>
        <div class="flex">
          <div class="h-full overflow-auto w-full">
            <SimpleTable ref="SimpleTableRef" :table-key="tableColumns" :data="detailList" :rowHeight="100" :loading="loading" v-bind="tableConfig">
              <template #image_url="{ row }">
                <EasyImage :src="row.image_url"></EasyImage>
              </template>
              <template #ali_image_url="{ row }">
                <EasyImage :src="row.ali_image_url"></EasyImage>
              </template>
              <template #type_specification="{ row }">
                <div class="pt-4 pb-4">
                  <div>{{ row.style_code }}</div>
                  <div>{{ row.k3_sku_id }}</div>
                  <div>{{ row.sku_name }}</div>
                  <div>{{ row.type_specification }}</div>
                </div>
              </template>
              <template #ali_type_specification="{ row }">
                <div v-if="row.ali_type_specification">
                  <p>{{ row.ali_sku_name }}</p>
                  <p>{{ row.ali_type_specification }}</p>
                </div>
                <span v-else>{{ '暂时未匹配1688商品信息' }}</span>
              </template>
              <template #purchase_quantity="{ row, rowIndex }">
                <a-input-number
                  v-model:value="row.purchase_quantity"
                  :min="0"
                  step="1"
                  :precision="0"
                  @change="changeQuantity(row, rowIndex)"
                  :disabled="!row.ali_type_specification && formData.operation_type === 2 && method === 1"
                />
              </template>
              <template #remark="{ row }">
                <a-input v-model:value="row.remark" :maxlength="300" allow-clear :placeholder="'请填写备注'" />
              </template>
              <template #operate="{ row, rowIndex }">
                <a-button v-if="formData.operation_type == 1 || method === 0" @click="delBtn(row)">删除</a-button>
                <a-button v-if="!row.ali_type_specification" class="ml-5px" @click="mapBtn(row, rowIndex)">立即匹配</a-button>
              </template>
            </SimpleTable>
          </div>
        </div>
      </div>
    </div>
    <template #footer>
      <a-space :size="16">
        <a-button type="primary" @click="editBtn" :loading="btnLoading">提交</a-button>
        <a-button @click="visible = false">取消</a-button>
      </a-space>
    </template>
  </a-drawer>
  <pushAliMatchDlg ref="PushAliMatchDlgRef" @refresh="refreshList" />
</template>

<script setup lang="ts">
import { Modal, message } from 'ant-design-vue'

// import { filterOption } from '@/utils/index'
import SimpleTable from '@/components/SimpleTable.vue'
import { Enum2Options } from '@/utils'

import pushAliMatchDlg from './pushAliMatchDlg.vue'

import { GetAliProductMapInfoByPurchaseOrderId } from '@/servers/ProductMapping'
import { GetAliPurchaseAccountSelect, GetAliPurchaseAccountReceiveAddressSelect } from '@/servers/PurchaseAccount'
import { GetInventoryBy1688, RecommendOrder } from '@/servers/PurchaseManage'

const emit = defineEmits(['search'])

const formRef = ref()
const loading = ref(false)
const PushAliMatchDlgRef = ref()
// 表单项
const formItems = ref<EasyFormItemProps[]>([
  { type: 'title', title: '基本信息', span: 24 },
  {
    type: 'select',
    label: '1688采购账号',
    required: true,
    name: 'ali_purchase_account_list_id',
    linkage: 'ali_purchase_account_receive_address_id',
    api: GetAliPurchaseAccountSelect,
    span: 13,
  },
  {
    label: '1688订单编号',
    type: 'input',
    name: 'order_number',
    required: true,
    span: 13,
    hide: true,
    props: {
      maxlength: 30,
      onInput: (e) => {
        const value = (e.target.value || '').replace(/[^\d]/g, '')
        formRef.value.changeValue('order_number', value)
        e.target.value = value
      },
    },
  },
  {
    type: 'select',
    label: '收货地址',
    required: true,
    name: 'ali_purchase_account_receive_address_id',
    api: ({ ali_purchase_account_list_id }) => GetAliPurchaseAccountReceiveAddressSelect({ id: ali_purchase_account_list_id }),
    span: 13,
  },
  {
    type: 'text',
    label: 'SRM供应商子公司',
    name: 'supplier_conpany_name',
    span: 13,
  },
  {
    type: 'radio',
    label: '操作类型',
    required: true,
    name: 'operation_type',
    options: Enum2Options(RecommendOrderOperationTypeEnum),
    span: 13,
    hide: true,
  },
  {
    type: 'radio',
    label: '订单类型',
    required: true,
    name: 'order_type',
    options: Enum2Options(TakeOrderTypeEnum),
    span: 13,
  },

  { type: 'title', title: '采购单商品信息', span: 24 },
])
const formData = ref<Record<string, any>>({})

const tableConfig = ref({
  virtualYConfig: { enabled: true, gt: 0 },
  cellConfig: {},
})
const tableColumns = ref(
  [
    { title: '系统图片', field: 'image_url', width: '' },
    { title: '系统商品信息', field: 'type_specification', width: '' },
    { title: '映射商品图片', field: 'ali_image_url', width: '' },
    { title: '映射商品信息', field: 'ali_type_specification', width: '' },
    { title: '起批量', field: 'ali_start_quantity', width: '' },
    { title: '申请采购数量', field: 'total_purchase_quantity', width: '' },
    { title: '已拍单数量', field: 'take_quantity', width: '' },
    { title: '1688已推单数量', field: 'ali_take_quantity', width: '' },
    { title: '1688库存数量', field: 'ali_inventory_quantity', width: '' },
    { title: '本次采购数量', field: 'purchase_quantity', width: '' },
    { title: '本次1688推单数量', field: 'now_take_quantity', width: '' },
    { title: '备注', field: 'remark', width: 170 },
    { title: '操作', field: 'operate', width: 140 },
  ].map((f) => ({ ...f, width: f.width || 100, showOverflow: false })),
)

const detailList = ref<any[]>([])

// 显示
const visible = ref(false)

const method = ref(0)
const methodList = ref([
  { label: '推单1688', value: 0 },
  { label: '关联1688订单', value: 1 },
])
const changeTab = () => {
  formRef.value.changeItem('operation_type', {
    hide: method.value == 0,
  })
  formRef.value.changeItem('ali_purchase_account_receive_address_id', {
    hide: method.value == 1,
  })
  formRef.value.changeItem('order_number', {
    hide: method.value == 0,
  })
  detailList.value.forEach(changeQuantity)
}

// 只刷新单条数据
const refreshList = async () => {
  GetAliProductMapInfoByPurchaseOrderId({ order_id: formData.value.id }).then(async (res) => {
    detailList.value[saveIdx.value].ali_image_url = res.data.details[saveIdx.value].ali_image_url
    detailList.value[saveIdx.value].ali_type_specification = res.data.details[saveIdx.value].ali_type_specification
    detailList.value[saveIdx.value].ali_start_quantity = res.data.details[saveIdx.value].ali_start_quantity
    detailList.value[saveIdx.value].total_purchase_quantity = res.data.details[saveIdx.value].total_purchase_quantity
    detailList.value[saveIdx.value].take_quantity = res.data.details[saveIdx.value].take_quantity
    detailList.value[saveIdx.value].ali_take_quantity = res.data.details[saveIdx.value].ali_take_quantity
    detailList.value[saveIdx.value].ali_inventory_quantity = res.data.details[saveIdx.value].ali_inventory_quantity
    detailList.value[saveIdx.value].purchase_quantity = res.data.details[saveIdx.value].purchase_quantity
    detailList.value[saveIdx.value].now_take_quantity = res.data.details[saveIdx.value].now_take_quantity
    detailList.value[saveIdx.value].ali_sku_name = res.data.details[saveIdx.value].ali_sku_name
    detailList.value[saveIdx.value].remark = res.data.details[saveIdx.value].remark
    detailList.value[saveIdx.value].ali_sku_id = res.data.details[saveIdx.value].ali_sku_id
    detailList.value[saveIdx.value].ali_style_code = res.data.details[saveIdx.value].ali_style_code
    detailList.value[saveIdx.value].map_list_id = res.data.details[saveIdx.value].map_list_id
    detailList.value[saveIdx.value].map_list_id = res.data.details[saveIdx.value].map_list_id
    detailList.value[saveIdx.value].offer_id = res.data.details[saveIdx.value].offer_id
    detailList.value[saveIdx.value].purchase_order_detail_id = res.data.details[saveIdx.value].purchase_order_detail_id
    detailList.value[saveIdx.value].spec_id = res.data.details[saveIdx.value].spec_id
    detailList.value[saveIdx.value].srm_purchase_quantity = res.data.details[saveIdx.value].srm_purchase_quantity
    detailList.value[saveIdx.value].system_quantity = res.data.details[saveIdx.value].system_quantity
    detailList.value[saveIdx.value].ali_purchase_quantity = res.data.details[saveIdx.value].ali_purchase_quantity
    detailList.value.forEach((item, index) => {
      if (index == saveIdx.value) {
        item.purchase_quantity = item.total_purchase_quantity - item.take_quantity
        item.idx = index
        changeQuantity(item, index)
      }
    })
  })
}

const getInventoryBtn = async () => {
  const formState = await formRef.value.validate()
  const arr = detailList.value
    .filter((v) => v.spec_id)
    .map((v) => {
      return { spec_id: v.spec_id, office_id: v.offer_id }
    })
  const obj = {
    ali_purchase_account_list_id: formState.ali_purchase_account_list_id,
    details: arr,
  }
  GetInventoryBy1688(obj).then((res) => {
    if (res.success) {
      if (res.data.length > 0) {
        message.success('获取1688库存成功！')
        detailList.value.forEach((item) => {
          const obj = res.data.find((v) => v.spec_id == item.spec_id)
          if (obj) {
            item.ali_inventory_quantity = obj.quantity
          }
        })
      } else {
        message.info('无可获取库存商品!')
      }
    }
  })
}

const delBtn = (row) => {
  Modal.confirm({
    title: '确定要删除吗?',
    icon: () => {},
    content: '',
    async onOk() {
      detailList.value = detailList.value.filter((v) => {
        console.log(row)
        return v.idx !== row.idx
      })
    },
    onCancel() {},
  })
}
const saveIdx = ref(0)
const mapBtn = (row, index) => {
  saveIdx.value = index
  if (row.map_list_id === 0) {
    message.error(`当前供应商未添加该款号1688映射，请添加后再进行推单`)
    return
  }
  PushAliMatchDlgRef.value.open(row)
}

const checkDetailList = () => {
  return detailList.value.some((item, index) => {
    if (item.purchase_quantity > item.total_purchase_quantity - item.take_quantity) {
      message.error(`第${index + 1}行的本次采购数量不能大于待拍单数量`)
      return true
    }
    if (method.value === 0) {
      if (!item.ali_sku_id) {
        message.error(`第${index + 1}行未匹配,请匹配商品后再进行提交`)
        return true
      }
      if (!item.is_not_info && item.now_take_quantity > item.ali_inventory_quantity) {
        message.error(`第${index + 1}行的本次1688推单数量不能大于1688库存数量`)
        return true
      }
      if (item.now_take_quantity < item.ali_start_quantity) {
        message.error(`第${index + 1}行的本次1688推单数量必须大于等于起批量`)
        return true
      }
    }

    if (!item.purchase_quantity) {
      message.error(`第${index + 1}行未填写本次采购数量,请匹配商品后再进行提交`)
      return true
    }
    if (!item.now_take_quantity) {
      message.error(`第${index + 1}行的本次1688推单数量不能为0`)
      return true
    }
    return false
  })
}

const editBtn = () => {
  Modal.confirm({
    title: '确定要提交吗?',
    icon: () => {},
    content: '',
    async onOk() {
      submitData()
    },
    onCancel() {},
  })
}

const btnLoading = ref(false)
const submitData = async () => {
  const isError = checkDetailList()
  if (!isError) {
    try {
      const formState = await formRef.value.validate()
      const params = {
        ...formState,
        method: method.value,
        id: formData.value.id,
        details: detailList.value.map((item) => {
          return {
            detail_id: item.purchase_order_detail_id,
            spec_id: item.spec_id,
            office_id: item.offer_id,
            quantity: item.purchase_quantity,
            take_quantity: item.take_quantity,
            system_quantity: item.system_quantity || 0,
            ali_purchase_quantity: item.ali_purchase_quantity || 0,
            total_purchase_quantity: item.total_purchase_quantity,
            k3_sku_id: item.k3_sku_id,
            ali_sku_id: item.ali_sku_id,
            remark: item.remark,
            now_take_quantity: item.now_take_quantity,
          }
        }),
      }
      RecommendOrder(params, { loading: btnLoading }).then(() => {
        message.success('推单成功')
        visible.value = false
        emit('search')
      })
    } catch (errorInfo) {
      console.log('Failed:', errorInfo)
    }
  }
}

const getQuotientAndRemainder = (dividend, divisor) => {
  return {
    quotient: Math.trunc(dividend / divisor),
    remainder: dividend % divisor,
  }
}

const changeQuantity = (row, index) => {
  console.log(row, 'row')
  console.log(index, 'index')
  const srm_purchase_quantity = row.srm_purchase_quantity
  const ali_purchase_quantity = row.ali_purchase_quantity || (method.value === 1 ? 1 : 0)
  if (srm_purchase_quantity > ali_purchase_quantity) {
    if (row.purchase_quantity < srm_purchase_quantity) {
      detailList.value[index].now_take_quantity = 0
      return
    }
    const result = getQuotientAndRemainder(row.purchase_quantity, srm_purchase_quantity)
    detailList.value[index].now_take_quantity = result.quotient
    if (result.remainder > 0) {
      detailList.value[index].purchase_quantity = row.purchase_quantity - result.remainder
    }
  } else {
    detailList.value[index].now_take_quantity = row.purchase_quantity * ali_purchase_quantity
  }
}

// 打开
const open = async (row) => {
  formData.value = {
    order_type: 2,
    operation_type: 1,
    id: row.id,
  }
  detailList.value = []
  const res = await GetAliProductMapInfoByPurchaseOrderId({ order_id: formData.value.id })
  visible.value = true
  await nextTick()
  formRef.value.changeValue({
    ...res.data,
    ali_purchase_account_list_id: `${res.data.ali_purchase_account_list_id || ''}`,
    ali_purchase_account_receive_address_id: `${res.data.ali_purchase_account_receive_address_id || ''}`,
  })
  formRef.value.changeItem('ali_purchase_account_list_id', { disabled: !res.data.is_change_account })
  detailList.value = res.data.details
  detailList.value.forEach((item, index) => {
    item.purchase_quantity = item.total_purchase_quantity - item.take_quantity
    item.idx = index
    changeQuantity(item, index)
  })
}

defineExpose({
  open,
})
</script>

<style scoped lang="scss"></style>
