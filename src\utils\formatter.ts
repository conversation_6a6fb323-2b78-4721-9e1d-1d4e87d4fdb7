import dayjs from 'dayjs'

import { number2 } from '@/utils/index'

export const IGNORE_CHARACTERS = ['...']

export const joinFormat = ({ cellValue }) => {
  return cellValue instanceof Array ? cellValue.join(',').replace(/^,/, '') : cellValue
}

export const bankFormat = ({ cellValue }) => {
  return IGNORE_CHARACTERS.includes(cellValue) ? cellValue : (cellValue ?? 0).roundNext(2)
}

export const infinityFormat = ({ cellValue }) => {
  const value = cellValue ?? 0
  const v = Math.abs(value) >= ********* ? '∞' : value
  const fx = Math.min(value, 0) === 0 ? '+' : '-'
  return `${v === '∞' ? fx : ''}${v}`
}

export const dateFormat = ({ cellValue }) => {
  return IGNORE_CHARACTERS.includes(cellValue) ? cellValue : cellValue ? dayjs(cellValue).format('YYYY-MM-DD') : ''
}

export const dateTimeFormat = ({ cellValue }) => {
  return IGNORE_CHARACTERS.includes(cellValue) ? cellValue : cellValue ? dayjs(cellValue).format('YYYY-MM-DD HH:mm:ss') : ''
}

export const numberFormat = ({ cellValue }) => {
  return number2(cellValue, 2)
}

export const priceFormat = ({ cellValue, column }) => {
  return IGNORE_CHARACTERS.includes(cellValue) ? cellValue : `￥${(cellValue ?? 0).roundNext(column?.params?.precision ?? 2)}`
}

export const number4Format = ({ cellValue }) => {
  return IGNORE_CHARACTERS.includes(cellValue) ? cellValue : number2(cellValue, 4)
}

export const rateFormat = ({ cellValue }) => {
  return (typeof cellValue === 'number' && `${cellValue}%`) || cellValue
}

export const formatMap = {
  join: joinFormat,
  bank: bankFormat,
  infinity: infinityFormat,
  date: dateFormat,
  dateTime: dateTimeFormat,
  number: numberFormat,
  number4: number4Format,
  rate: rateFormat,
  price: priceFormat,
}
