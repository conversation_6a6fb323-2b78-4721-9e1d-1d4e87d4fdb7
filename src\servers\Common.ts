// 公共接口
import { request } from './request'
// 获取列表动态字段（列名、字段、排序、固定类型）
export const GetTableConfig = (data) => {
  return request({ url: '/api/Common/GetViewFiledMapsV2', data })
}
export const SetTableConfig = (data) => {
  return request({ url: '/api/Common/SaveViewFiledMapsV2', data })
}
// 获取所有枚举
export const GetEnum = () => {
  return request({ url: '/api/Common/GetEnum' })
}
// 上传文件
export const FileUpload = (data) => {
  // return request({ url: '/api/File/Upload', data, isFormData: true })
  return request({ url: '/api/File/UploadOss', data, isFormData: true })
}
// 获取文件预览
export const FilePreview = (data) => {
  return request({ url: '/api/File/Preview', data }, 'GET')
}

export const GetFileUrl = (data) => {
  return request({ url: '/api/File/GetFileUrl', data }, 'GET')
}
// 查询文件下载链接
export const GetDownloadUrl = (data) => {
  return request({ url: '/api/File/GetDownloadUrl', data }, 'GET')
}

export const Info = () => {
  return request({ url: '/api/Info/Sys' }, 'GET')
}

export const PurchaseOrderListPrint = (data) => {
  return request({ url: '/api/PurchaseOrderPrint/PurchaseOrderListPrint', data })
}

// 保存快捷查询信息
export const SrmSaveViewQuickQuery = (data) => {
  return request({ url: '/api/Common/SrmSaveViewQuickQuery', data })
}

// 查询快捷查询信息
export const SrmGetViewQuickQuery = (data) => {
  return request({ url: '/api/Common/SrmGetViewQuickQuery', data })
}
