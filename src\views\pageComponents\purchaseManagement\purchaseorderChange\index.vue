<template>
  <div class="flex flex-col h-full main">
    <Form ref="formRef" v-model:form="formArr" :page-type="PageTypeEnum.PurchaseChange" @search="tableRef?.search()" @setting="tableRef?.showTableSetting()">
      <template #header>
        <StatusTabs v-model:status="audit_status" :options="purchaseAdjustPriceStatusOption" :count-map="labelStatusCountMap" @change="tableRef?.search()" />
      </template>
    </Form>
    <BaseTable ref="tableRef" :page-type="PageTypeEnum.PurchaseChange" v-model:form="formArr" :get-list="GetPurchaseOrderChangeList" :isCheckbox="true" :form-format="formatData">
      <template #left-btn>
        <a-button @click="tapBatchAudit" v-if="[85003, 85004, 85005, 85006].some((f) => btnPermission[f])">批量审核</a-button>
      </template>
      <template #right-btn>
        <div class="ml-auto" v-if="btnPermission[85009]">
          <a-select v-model:value="exportType" :dropdownMatchSelectWidth="false" style="width: 120px; margin-right: 10px" @change="onExportTypeChange" :disabled="exportLoading">
            <a-select-option value="" disabled>导出</a-select-option>
            <a-select-option v-for="item in exportOptions" :key="item.value">{{ item.label }}</a-select-option>
          </a-select>
        </div>
      </template>
      <template #fix_option="{ row, column }">
        <RightOperate :list="rightOperateList" :row="row" :column="column" />
      </template>
    </BaseTable>
    <AuditConfirm ref="auditConfirmRef" batch @audit="onAuditConfirm" />
    <OperationLog ref="operationLogRef"></OperationLog>
  </div>
</template>
<script lang="ts" setup>
import { message } from 'ant-design-vue'

import eventBus from '@/utils/eventBus'
import { purchaseAdjustPriceStatusOption } from '@/common/options'
import { Enum2Options, checkFormParams } from '@/utils'

import { GetPurchaseOrderChangeList, BatchAudit, Close, ExportPurchaseChangeOrder } from '@/servers/PurchaseOrderchange'
import { GetWarehouses, GetApplyers } from '@/servers/Purchaseapplyorder'
import { GetPageSuppliersSelect, GetPageMRPSupplierCompanySelect } from '@/servers/BusinessCommon'

import { PageTypeEnum } from '@/enums/tableEnum'

const { btnPermission } = usePermission()
const { pushPage } = usePage({ refresh: () => tableRef.value.search() })
const { labelStatusCountMap, labelStatusRefresh } = useLabelStatus(PageTypeEnum.PurchaseChange)

const rightOperateList = ref([
  {
    label: '查看',
    show: 85001,
    onClick: ({ row }) => {
      LookAlterOrder(row)
    },
  },
  {
    label: '审核',
    show: ({ row }) => {
      return (
        row.audit_status != 10 &&
        row.audit_status != 90 &&
        row.audit_status != 95 &&
        ((btnPermission.value[85003] && row.audit_status == 20) ||
          (btnPermission.value[85004] && row.audit_status == 30) ||
          (btnPermission.value[85005] && row.audit_status == 40) ||
          (btnPermission.value[85006] && row.audit_status == 50))
      )
    },
    onClick: ({ row }) => {
      ReviewAlterOrder(row)
    },
  },
  {
    label: '编辑',
    show: ({ row }) => {
      return (btnPermission.value[85002] && PurchaseOrderAuditStatusEnum.待提审 === row.audit_status) || PurchaseOrderAuditStatusEnum.已拒绝 === row.audit_status
    },
    onClick: ({ row }) => {
      EditAlterOrder(row)
    },
  },
  {
    label: '日志',
    show: () => true,
    onClick: ({ row }) => {
      openLog(row)
    },
  },
  {
    label: '关闭',
    show: ({ row }) => {
      return (
        btnPermission.value[85008] &&
        !row.is_closed &&
        [PurchaseOrderAuditStatusEnum.待一级审核, PurchaseOrderAuditStatusEnum.待二级审核, PurchaseOrderAuditStatusEnum.待三级审核, PurchaseOrderAuditStatusEnum.待四级审核].includes(row.audit_status)
      )
    },
    onClick: ({ row }) => {
      handleClose(row)
    },
  },
])

const tableRef = ref()
const audit_status = ref('')
const formRef = ref()
const formArr = ref([
  { label: '采购单编号', value: '', type: 'inputDlg', key: 'purchase_order_number' },
  { label: '变更单编号', value: '', type: 'inputDlg', key: 'number' },
  {
    label: '供应商',
    value: null,
    type: 'select-supplier',
    key: 'supplier_id',
    mode: 'single',
    api: GetPageSuppliersSelect,
  },
  {
    label: '供应商子公司',
    value: undefined,
    type: 'select-supplier',
    key: 'company_supplier_id',
    mode: 'single',
    api: GetPageMRPSupplierCompanySelect,
    apiParams: { is_contains_srs: true },
  },
  { label: '选择采购收料仓', value: null, type: 'select', key: 'warehourse_id', selectArr: [] },
  { label: '选择申请人', value: null, type: 'select', key: 'creator_id', selectArr: [] },
  { label: '采购时间', value: null, type: 'range-picker', key: 'purchase_time', formKeys: ['purchase_start_time', 'purchase_end_time'], placeholder: ['采购开始时间', '采购结束时间'] },
  { label: '审核时间', value: null, type: 'range-picker', key: 'audit_time', formKeys: ['start_time', 'end_time'], placeholder: ['审核开始时间', '审核结束时间'] },
  { label: '申请时间', value: null, type: 'range-picker', key: 'apply_time', formKeys: ['apply_start_time', 'apply_end_time'], placeholder: ['申请开始时间', '申请结束时间'] },
])
const operationLogRef = ref()

const formatData = (data: any) => {
  labelStatusRefresh()
  return {
    ...data,
    audit_status: audit_status.value,
  }
}

const getWarehouseOption = async () => {
  const res = await GetWarehouses()
  const item = formArr.value.find((item) => item.key === 'warehourse_id')
  if (item) {
    item.selectArr = res.data.map((i) => ({ label: i.value, value: i.key })) || []
  }
}

const getApplyerOption = async () => {
  const res = await GetApplyers()
  const item = formArr.value.find((item) => item.key === 'creator_id')
  if (item) {
    item.selectArr = res.data.map((i) => ({ label: i.value, value: i.key })) || []
  }
}

const LookAlterOrder = (row) => {
  pushPage(`/purchaseOrderAlterLook/${row.id}`, { source: true })
}

const ReviewAlterOrder = (row) => {
  pushPage(`/purchaseOrderAlterReview/${row.id}`, { source: true })
}

const EditAlterOrder = (row) => {
  pushPage(`/purchaseOrderAlterEdit/${row.id}`, { source: true })
}

// 批量审核
const auditConfirmRef = ref()
const tapBatchAudit = () => {
  if (tableRef.value?.checkItemsArr?.length === 0) {
    message.warning('请选择要审核的采购变更单')
    return
  }
  const audit_status = Array.from(
    new Set(
      tableRef.value.checkItemsArr.reduce((acc, v) => {
        if (v.audit_status) acc.push(v.audit_status)
        return acc
      }, []),
    ),
  )

  if (audit_status.length > 1) {
    message.error('请选择同一状态的审核数据')
    return
  }
  if ([20, 30, 40, 50].includes(audit_status[0] as number)) {
    auditConfirmRef.value?.open()
  }
}
const onAuditConfirm = async (data, close) => {
  const { audit_opinion, is_pass } = data
  const params = {
    is_pass,
    audit_opinion,
    ids: tableRef.value?.checkItemsArr.map((item) => item.id),
  }
  const res = await BatchAudit(params)
  if (res.success) {
    message.success('操作成功')
    close()
    tableRef.value.search()
  } else {
    message.error(res.message)
  }
}

const openLog = (row) => {
  operationLogRef.value.open({
    params: {
      id: row.id,
      pageType: OpLogPageTypeEnum.采购单变更,
    },
  })
}

const modal: any = inject('modal')
const handleClose = ({ id }) => {
  modal.open({
    title: '是否关闭该变更单?',
    async onOk() {
      await Close({ id })
      message.success('操作成功')
      tableRef.value.search()
    },
  })
}

// 导出
enum CurExportTypeEnum {
  '根据勾选导出' = 1,
  '根据筛选结果导出' = 2,
  '全部导出' = 3,
}
const exportOptions = ref(Enum2Options(CurExportTypeEnum))
const exportType = ref('')
const exportLoading = ref(false)
const onExportTypeChange = (value) => {
  const count = tableRef.value.checkItemsArr.length
  const type: CurExportTypeEnum = value
  let msg = ''
  if ([1, 2, 3].includes(type) && count > 50000) {
    msg = '单次操作不得大于5万条！'
  }
  if ([1].includes(type) && !count) {
    msg = '请勾选数据'
  }
  if (msg) {
    exportType.value = ''
    return message.info(msg)
  }
  const params = {
    exportType: type,
    ids: [...new Set(tableRef.value.checkItemsArr.map((f) => f.id))],
  }
  modal.open({
    title: '是否确定导出变更单数据?',
    async onOk() {
      exportData(params)
    },
  })
}
const exportData = (params) => {
  const obj = {
    sortField: tableRef.value.orderby,
    sortType: tableRef.value.ordersort ? 'asc' : 'desc',
  }
  checkFormParams({ formArr: formArr.value, obj })
  exportLoading.value = true
  ExportPurchaseChangeOrder({
    ...params,
    param: formatData(obj),
  })
    .then((res) => {
      eventBus.emit('downLoadId', res.data as number | string)
    })
    .finally(() => {
      exportLoading.value = false
    })
}

onMounted(() => {
  getWarehouseOption()
  getApplyerOption()
})
</script>
<style lang="scss" scoped></style>
