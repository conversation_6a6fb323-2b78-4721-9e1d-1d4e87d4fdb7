import { request } from './request'
// 查询列表
export const List = (data) => {
  return request({ url: '/api/Warehouse/GetWarehouseList', data })
}

// 同步
export const SyncWarehouse = () => {
  return request({ url: '/api/SyncData/SyncAllWarehouse' }, 'GET')
}

// 编辑
export const Edit = (data) => {
  return request({ url: '/api/Warehouse/UpdateWarehouseInfo', data })
}

// 删除
export const Delete = (data) => {
  return request({ url: '/api/Warehouse/DeleteWarehouseInfo', data })
}
// 获取仓库信息
export const GetWarehouse = (data) => {
  return request({ url: `/api/Warehouse/GetWarehouse?id=${data.id}`, data }, 'GET')
}
