import { request } from './request'

// 获取商品库
export const GetProductSkuList = (data) => {
  return request({ url: '/api/ProductSku/GetProductSkuList', data }, 'POST')
}

// 获取金蝶商品库同步状态
export const GetProductSkuInfoSyncState = () => {
  return request({ url: '/api/SyncData/GetProductSkuInfoSyncState' }, 'GET')
}

// 同步金蝶商品库数据
export const SyncProductSkuInfo = () => {
  return request({ url: '/api/SyncData/SyncProductSkuInfo' }, 'GET')
}

// 获取商品库存
export const GetProductInventoryList = (data) => {
  return request({ url: '/api/ProductInventory/GetProductInventoryList', data }, 'POST')
}

// 同步金蝶商品库存
export const SyncProductSkuInventoryInfo = () => {
  return request({ url: '/api/SyncData/SyncProductSkuInventoryInfo' }, 'GET')
}

// 获取金蝶商品信息
export const GetK3SkuInfo = (data) => {
  return request({ url: '/api/BusinessCommon/GetK3SkuInfo', data }, 'POST')
}

// 获取商品标签下拉列表
export const GetProductLabelSelect = (data) => {
  return request({ url: '/api/BusinessCommon/GetProductLabelSelect', data }, 'POST')
}

// 获取商品分类下拉列表
export const GetProductCategorySelect = (data) => {
  return request({ url: '/api/BusinessCommon/GetProductCategorySelect', data }, 'POST')
}

// 获取商品材质下拉列表
export const GetProductCaizhiSelect = (data) => {
  return request({ url: '/api/BusinessCommon/GetProductCaizhiSelect', data }, 'POST')
}

// 按部门区分获取采购员
export const GetPurchaseByDeptSelect = (data) => {
  return request({ url: '/api/BusinessCommon/GetPurchaseByDeptSelect', data }, 'POST')
}

// 获取供应商子公司下拉框
export const GetMRPSupplierCompanySelect = (data) => {
  return request({ url: '/api/BusinessCommon/GetMRPSupplierCompanySelect', data }, 'POST')
}

// 获取供应商下拉列表（分页）
export const GetPageSuppliersSelect = (data) => {
  return request({ url: '/api/BusinessCommon/GetPageSuppliersSelect', data }, 'POST')
}

// 获取供应商子公司下拉列表（分页）
export const GetPageMRPSupplierCompanySelect = (data) => {
  return request({ url: '/api/BusinessCommon/GetPageMRPSupplierCompanySelect', data }, 'POST')
}

// 获取1688类型的子供应商
type Get1688MRPSupplierCompanySelectParams = {
  type?: 1 | 2 // 线下供应商 | 一六八八线上供应商
  is_chilrd_query?: boolean // 数据权限卡控，为true时，查询当前用户及下属的供应商
  keyword?: string
}
export const Get1688MRPSupplierCompanySelect = (data: Get1688MRPSupplierCompanySelectParams) => {
  return request({ url: '/api/BusinessCommon/Get1688MRPSupplierCompanySelect', data }, 'POST')
}

// 获取部门下拉列表
export const GetDeptSelect = (data) => {
  return request({ url: '/api/BusinessCommon/GetDeptSelect', data }, 'GET')
}

// 获取树形部门列表
export const GetDeptByTreeSelect = (data) => {
  return request({ url: '/api/BusinessCommon/GetDeptByTreeSelect', data }, 'GET')
}

// 获取供应商分组下拉框-根据父级
export const GetMRPSupplierGroupSelectByParent = (data) => {
  return request({ url: '/api/BusinessCommon/GetMRPSupplierGroupSelectByParent', data }, 'GET')
}

// 获取税率下拉列表
export const GetTaxRateSelect = (data) => {
  return request({ url: '/api/BusinessCommon/GetTaxRateSelect', data }, 'GET')
}

// 获取PLM加工方式下拉选择
export const GetPLMMachiningType = (data) => {
  return request({ url: '/api/BusinessCommon/GetPLMMachiningType', data }, 'GET')
}

// 获取审核记录
export const GetAuditRecord = (data) => {
  return request({ url: '/api/BusinessCommon/GetAuditRecord', data })
}

// 获取供应商分类下拉列表
export const GetSupplierCategorySelect = (data) => {
  return request({ url: '/api/BusinessCommon/GetSupplierCategorySelect', data }, 'GET')
}

// 获取操作日志
export const GetOpLogInfos = (data: any, config?) => {
  return request({ url: '/api/BusinessCommon/GetOpLogInfos', data, config }, 'GET')
}

// 获取指定页面标签通用状态的数量
export const GetLabelStatusCount = (data) => {
  return request({ url: '/api/BusinessCommon/GetLabelStatusCount', data })
}
// 获取菜单统计数量
export const GetMenuStatusCountList = (data) => {
  return request({ url: '/api/BusinessCommon/GetMenuStatusCountList', data })
}

// 获取菜单统计数量
export const ProductBrandSelect = (data) => {
  return request({ url: '/api/BusinessCommon/ProductBrandSelect', data })
}

// 获取商品分类
export const AllProductCategorySelect = (data) => {
  return request({ url: '/api/BusinessCommon/AllProductCategorySelect', data }, 'GET')
}

// 获取采购申请单标签下拉列表
export const GetPurchaseApplyOrderTagsSelect = (data) => {
  return request({ url: '/api/BusinessCommon/GetPurchaseApplyOrderTagsSelect', data }, 'GET')
}
