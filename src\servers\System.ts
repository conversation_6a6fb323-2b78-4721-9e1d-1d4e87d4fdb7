import { request } from './request'

// 获取系统参数配置列表
export const GetSystemParamConfigList = (data) => {
  return request({ url: '/api/System/GetSystemParamConfigList', data })
}

// 新增系统参数配置
export const AddSystemParamConfig = (data) => {
  return request({ url: '/api/System/AddSystemParamConfig', data })
}

// 修改系统参数配置
export const UpdateSystemParamConfig = (data) => {
  return request({ url: '/api/System/UpdateSystemParamConfig', data })
}
// /api/System/GetSystemConfigValue
export const GetSystemConfigValue = (data) => {
  return request({ url: '/api/System/GetSystemConfigValue', data })
}

export const RefreshByKey = (data) => {
  return request({ url: '/api/Hangfires/RefreshByKey', data })
}
