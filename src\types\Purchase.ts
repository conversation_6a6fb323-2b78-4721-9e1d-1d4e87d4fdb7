// 申请单单据状态
export enum PurchaseApplyOrderStatusEnum {
  待开始 = 0,
  进行中 = 1,
  已完成 = 2,
  已关闭 = 3,
  已执行完成 = 4,
}

// 采购申请单类型
export enum PurchaseApplyOrderEnum {
  标准采购申请 = 1,
  委外加工申请 = 2,
  寄售采购单 = 3,
}

// 订单状态
export enum PurchaseOrderStatusEnum {
  待开始 = 0,
  进行中 = 1,
  已完成 = 2,
  已关闭 = 3,
  已作废 = 4,
  变更中 = 5,
}

// 采购单 & 变更单 审核状态
export enum PurchaseOrderAuditStatusEnum {
  待提审 = 10,
  待一级审核 = 20,
  待二级审核 = 30,
  待三级审核 = 40,
  待四级审核 = 50,
  已通过 = 90,
  已拒绝 = 95,
  已完成 = 100,
}

// 采购单类型
export enum PurchaseTypeEnum {
  '线下采购订单' = 1,
  '1688线上采购订单' = 2,
  '委外加工单' = 3,
  '寄售订单' = 4,
}

// 领料状态
export enum PurchaseOrderReceivedStatusEnum {
  未领料 = 0,
  部分领料 = 1,
  领料审核中 = 2,
  领料完成 = 3,
}

// 采购申请单来源
export enum PurchaseApplyOrderSourceTypeEnum {
  SRM = 1,
  MRP = 2,
}

// 付款状态
export enum PurchasePaymentOrderStatusEnum {
  进行中 = 1,
  已完成 = 2,
}

// 打款状态
export enum PurchasePaymentStatusEnum {
  未打款 = 1,
  已打款 = 2,
}

// 商品类型
export enum PurchaseProductTypeEnum {
  成品 = 1,
  半成品 = 2,
  原材料 = 3,
  包材 = 4,
}

// 结算方式
export enum PurchaseSettlementTypeEnum {
  月结 = 1,
  半月结 = 2,
  周结 = 3,
  预付 = 4,
  线上付款 = 5,
}

// 退库原因
export enum PurchaseReturnReasonTypeEnum {
  采购退货 = 10,
  质检退货 = 20,
}

// 退库类型
export enum PurchaseReturnApplicationTypeEnum {
  仅退款 = 10,
  退货退库 = 20,
}

// 供应商分类
export enum PurchaseReturnSupplierCategoryEnum {
  内协工厂 = 1,
  其他 = 99,
}

// 价格趋势
export enum PurchaseReturnPriceTrendEnum {
  涨价 = 1,
  价优 = 2,
}

// 入库状态
export enum PurchaseOrderWarehousedStatusEnum {
  待入库 = 0,
  部分入库 = 1,
  完全入库 = 2,
}

export enum PurchaseOrderAbnormalStatusEnum {
  正常 = 0,
  超时 = 1,
  即将超时 = 2,
  无交期 = 3,
}

// 采购对账状态
export enum PurchaseReconciliationStatusEnum {
  未对账 = 0,
  已对账售后 = 1,
  已对账 = 2,
}
