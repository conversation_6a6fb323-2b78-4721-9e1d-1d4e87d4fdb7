import axios from 'axios'
import { message } from 'ant-design-vue'

import router from '@/router'
import { beforLogout } from '@/utils'

axios.defaults.headers['Content-Type'] = 'application/json;charset=utf-8'

// 创建axios实例
const service = axios.create({
  // axios中请求配置有baseURL选项，表示请求URL公共部分
  baseURL: window.localStorage.getItem('BASE_URL') || (import.meta.env.VITE_APP_ENV === 'development' ? '/api' : (window as any).serverConfig.VITE_APP_BASE_API),
  withCredentials: true,
  timeout: 60000,
})

type ConfigOptions = {
  loading?: Ref // loading开关
  loadingCloseDelay?: number // 请求成功loading关闭延时 单位ms
  timeout?: number
}
class HttpRequest {
  baseOptions = (params?: any, method?: string, header?: any): Promise<any> => {
    if (!params) params = {}
    if (!method) method = 'POST'

    let userData = localStorage.getItem('userData') || ('' as any)
    let LoginToken = ''
    if (userData) {
      userData = JSON.parse(userData) as any
      LoginToken = userData.login_token || ''
    }
    const { url, data, responseType = '', isFormData = false } = params
    const { loading, loadingCloseDelay, timeout }: ConfigOptions = params?.config || {}
    const headers = { LoginToken, ...header }
    if (isFormData) {
      headers['Content-Type'] = 'multipart/form-data'
      // headers['Accept'] = 'text/plain';
      // 'Accept': 'text/plain'
    }
    if (loading) {
      loading.value = true
    }
    const option = {
      url,
      data: method === 'POST' ? data : {},
      params: method === 'GET' ? data : {},
      method,
      responseType,
      headers,
      timeout: timeout || 60000,
    }

    return new Promise((resolve, reject) => {
      service(option)
        .then((res) => {
          if (responseType === 'blob') {
            return resolve(res)
          }

          const result = res.data || {}
          if ((result.code === 0 || result.code === 1) && result.success) {
            setTimeout(() => {
              loading && (loading.value = false)
            }, loadingCloseDelay || 1000)
            return resolve(result)
          }
          message.error({ content: result.message || '请求失败', key: 'msg' })
          loading && (loading.value = false)
          return reject(result)
        })
        .catch((err) => {
          if (err?.response?.data?.code === 1001) {
            message.error({ content: err.response.data.message || '登录过期，请重新登录~', key: 'msg' })
            beforLogout()
            setTimeout(() => {
              router.replace('/login')
            }, 1000)
            return reject(err)
          }
          message.error({ content: err?.response?.data?.message || '请求异常', key: 'msg' })
          loading && (loading.value = false)
          return reject(err)
        })
    })
  }
}

export const request = new HttpRequest().baseOptions
