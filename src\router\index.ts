import { createRouter, createWebHashHistory } from 'vue-router'

import plmMune from './plmMune'

const routes = [
  {
    path: '/',
    name: '首页',
    component: () => import('@/views/index.vue'),
    children: [
      {
        path: '/supplierManagement',
        name: '供应商管理',
        children: [
          {
            path: '/querySupplier',
            name: '供应商库',
            component: () => import('@/views/pageComponents/supplierManagement/querySupplier/index.vue'),
            meta: { KeepAlive: true },
          },
          {
            path: '/querySupplierCompany',
            name: '供应商子公司库',
            component: () => import('@/views/pageComponents/supplierManagement/querySupplierCompany/index.vue'),
            meta: { KeepAlive: true },
          },
          {
            path: '/supplierAuditList',
            name: '供应商审核',
            component: () => import('@/views/pageComponents/supplierManagement/supplierAuditList/index.vue'),
            meta: { KeepAlive: true },
          },
        ],
      },
      ...plmMune,
    ],
  },
  {
    path: '/login',
    name: '登录',
    component: () => import('@/views/login.vue'),
  },
  {
    path: '/print',
    name: '打印',
    component: () => import('@/views/pageComponents/common/print.vue'),
  },
  {
    path: '/401',
    name: '暂无权限',
    component: () => import('@/views/pageComponents/common/401.vue'),
  },
  {
    path: '/:catchAll(.*)',
    name: '找不到页面',
    component: () => import('@/views/pageComponents/common/404.vue'),
  },
]

const router = createRouter({
  history: createWebHashHistory(),
  routes,
  scrollBehavior() {
    return { top: 0 }
  },
})

export default router
