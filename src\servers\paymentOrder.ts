// 付款单模块
import { request } from './request'

// 获取付款单列表
export const List = (data) => {
  return request({ url: '/api/PaymentOrder/GetPaymentOrderList', data })
}

// 添加付款单
export const AddPaymentOrder = (data) => {
  return request({ url: '/api/PaymentOrder/AddPaymentOrder', data })
}

// 获取申请人筛选选项
export const ApplicantFillterOptions = (data) => {
  return request({ url: '/api/PaymentOrder/ApplicantFillterOptions', data }, 'GET')
}

// 获取供应商财务信息
export const GetSupplierFinanceInfo = (data) => {
  return request({ url: '/api/PaymentOrder/GetSupplierFinanceInfo', data })
}

// 获取供应商财务列表
export const GetSupplierFinanceList = (data) => {
  return request({ url: '/api/PaymentOrder/GetSupplierFinanceList', data }, 'GET')
}

// 采购单列表
export const GetPurchaseOrderListSmall = (data) => {
  return request({ url: '/api/PaymentOrder/GetPurchaseOrderListSmall', data })
}

// 获取采购单详情
export const GetPurchaseOrderInfo = (data) => {
  return request({ url: '/api/PaymentOrder/GetPurchaseOrderInfo', data })
}

// 获取1688供应商列表
export const Get1688MRPSupplierCompanySelect = (data) => {
  return request({ url: '/api/BusinessCommon/Get1688MRPSupplierCompanySelect', data })
}

// 获取付款账户列表
export const GetPaymentAccount = (data) => {
  return request({ url: '/api/PaymentOrder/GetPaymentAccount', data }, 'GET')
}

// 获取付款订单总金额
export const GetPaymentOrderTotal = (data) => {
  return request({ url: '/api/PaymentOrder/GetPaymentOrderTotal', data })
}

// 获取付款单详情
export const GetPaymentOrderDetail = (data) => {
  return request({ url: '/api/PaymentOrder/GetPaymentOrderDetail', data })
}

// 批量审核
export const BatchAudit = (data) => {
  return request({ url: '/api/PaymentOrder/BatchAudit', data })
}

// 审核
export const AuditRejected = (data) => {
  return request({ url: '/api/PaymentOrder/AuditRejected', data })
}

// 反审核
export const RejectToOriginator = (data) => {
  return request({ url: '/api/PaymentOrder/RejectToOriginator', data })
}

// 获取审核记录
export const GetAuditRecord = (data) => {
  return request({ url: '/api/PaymentOrder/GetAuditRecord', data }, 'GET')
}

// 导出
export const ExportPaymentOrderList = (data) => {
  return request({ url: '/api/PaymentOrder/ExportPaymentOrderList', data })
}

// 删除付款单
export const DeletePayment = (data) => {
  return request({ url: '/api/PaymentOrder/DeletePayment', data })
}

// 修改付款单
export const UpdatePaymentOrder = (data) => {
  return request({ url: '/api/PaymentOrder/UpdatePaymentOrder', data })
}

// 修改付款单财务信息
export const UpdatePaymentFinanceOrder = (data) => {
  return request({ url: '/api/PaymentOrder/UpdatePaymentFinanceOrder', data })
}

// 获取付款账户筛选选项
export const PaymentAccountFillterOption = (data) => {
  return request({ url: '/api/PaymentOrder/PaymentAccountFillterOptions', data }, 'GET')
}

// 获取内部用户列表
export const GetInnerUsers = (data) => {
  return request({ url: '/api/PaymentOrder/GetInnerUsers', data }, 'GET')
}

// 获取应付单列表
export const GetJstPayPayableList = (data) => {
  return request({ url: '/api/PaymentOrder/GetJstPayPayableList', data }, 'POST')
}

// 获取应付单列表明细
export const GetBillDrawerList = (data) => {
  return request({ url: '/api/PaymentOrder/GetBillDrawerList', data }, 'POST')
}

// 获取账单列表
export const GetBillListResultByPayOrder = (data) => {
  return request({ url: '/api/Bill/GetBillListResultByPayOrder', data }, 'POST')
}

// 申请付款单详情
export const GetBillBuildPayOrderInfo = (data) => {
  return request({ url: '/api/bill/GetBillBuildPayOrderInfo ', data }, 'POST')
}

// 上传发票
export const UploadInvoice = (data) => {
  return request({ url: '/api/PaymentOrder/UploadInvoice', data }, 'POST')
}

// 批量发票审核
export const BatchInvoiceAudit = (data) => {
  return request({ url: '/api/PaymentOrder/BatchInvoiceAudit', data }, 'POST')
}

// 获取关联预付单选择列表
export const GetPrePaymentOrderSelect = (data) => {
  return request({ url: '/api/BusinessCommon/GetPrePaymentOrderSelect', data }, 'GET')
}

// 获取关联预付单合计金额
export const GetPrePaymentOrderDetailThisDeductionAmountTotal = (data) => {
  return request({ url: '/api/BusinessCommon/GetPrePaymentOrderDetailThisDeductionAmountTotal', data }, 'POST')
}
