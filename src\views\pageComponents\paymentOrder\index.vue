<template>
  <div class="main">
    <Form ref="formRef" v-model:form="formArr" @search="search" @setting="tableRef?.showTableSetting()" :page-type="PageTypeEnum.paymentOrder" @resetForm="handleFormReset">
      <template #header>
        <StatusTabs v-model:status="selectTip" :options="paymentOrderStatusOption" :count-map="statusCounts" @change="search" />
      </template>

      <template #formTop>
        <div class="flex items-center text-#333 text-12 mb-6 w-full" v-if="selectTip == '' || selectTip == '2'">
          <span class="font-bold w-108 mr-4">审核进度</span>
          <div class="flex">
            <span
              v-for="item in reviewProgressOptions"
              :key="item.value"
              class="easy-checkbox-btn"
              @click="handleTagClick(item)"
              :class="{ active: item.value === 'all' ? selectedReviewProgress.length === 0 : selectedReviewProgress.includes(item.value) }"
            >
              {{ item.label }}
              <span class="count" v-if="item.code && labelStatusCountMap[item.code]">{{ labelStatusCountMap[item.code] }}</span>
            </span>
          </div>
          <div class="w-1 h-12 m-ie-12 m-is-12 mt-4 bg-[#dcdcdc]"></div>
        </div>
        <div class="flex items-center text-#333 text-12 mb-6 w-full" v-if="selectTip == '' || selectTip == '2'">
          <span class="font-bold w-108 mr-4">发票进度</span>
          <div class="flex">
            <span
              v-for="item in selectedInvoiceProgress"
              :key="item.value"
              class="easy-checkbox-btn"
              @click="handleInvoiceTagClick(item)"
              :class="{ active: item.value === 'all' ? selectedInvoiceProgressValues.length === 0 : selectedInvoiceProgressValues.includes(item.value) }"
            >
              {{ item.label }}
              <span class="count" v-if="item.code && labelStatusCountMap[item.code]">{{ labelStatusCountMap[item.code] }}</span>
            </span>
          </div>
          <div class="w-1 h-12 m-ie-12 m-is-12 mt-4 bg-[#dcdcdc]"></div>
        </div>
      </template>
    </Form>

    <BaseTable
      ref="tableRef"
      :page-type="PageTypeEnum.paymentOrder"
      v-model:form="formArr"
      :get-list="List"
      :form-format="formFormat"
      show-footer
      :footer-data="footerData"
      :check-cb="statTotal"
      :isCheckbox="true"
      :scroll-x="{ enabled: true }"
      :auto-resize="true"
      :row-height="50"
      :virtualX="false"
      :virtual-y-config="{ enabled: false }"
      :column-config="{
        resizable: true,
        minWidth: 100,
      }"
      :table-layout="'fixed'"
      :footer-span-method="footerSpanMethod"
    >
      <template #left-btn>
        <a-button @click="handleShowAuditModal(true)" v-if="selectTip == '' || selectTip == '2'">批量审核通过</a-button>
        <a-button @click="rejectAllBtn" v-if="selectTip == '' || selectTip == '2'">批量审核拒绝</a-button>
      </template>
      <template #right-btn>
        <a-button type="primary" @click="handleShowDetail('', DetailTypeEnum.ADD, 0)" v-if="btnPermission[131002]">添加付款单</a-button>
        <a-select
          v-model:value="exportType"
          :dropdownMatchSelectWidth="false"
          style="width: 120px; margin-right: 10px"
          @change="onExportTypeChange"
          :disabled="exportLoading"
          placeholder="导出"
          v-if="btnPermission[131015]"
        >
          <a-select-option v-for="item in exportOptions" :key="item.value" :value="item.value">{{ item.label }}</a-select-option>
        </a-select>
      </template>

      <template #bill_purchase_numbers="{ row }">
        <a @click="openPaymentOrderDetail(row.id)" class="link">{{ row.bill_purchase_numbers?.join(',') }}</a>
      </template>

      <!-- <template #attachment_array="{ row }">
        <div class="bcall">
          <a-tooltip v-for="item in row.attachment_array" :key="item">
            <div class="fileContent" @click="previewBtn(item)">
              <div class="bcspan">{{ item.org_name }}</div>
              <div class="status">
                <CheckCircleFilled style="color: #49cc90" />
              </div>
            </div>
          </a-tooltip>
        </div>
      </template> -->

      <template #fix_option="{ row, column }">
        <RightOperate :list="rightOperateList" :row="row" :column="column" />
      </template>

      <template #purchase_number="{ row }">
        <a @click="handlePurchaseNumberClick(row)" style="color: #1890ff; cursor: pointer">{{ row.number }}</a>
      </template>
    </BaseTable>

    <a-modal v-model:open="modelShow" :title="modalArr[modalIdx].title" okText="确定" cancelText="取消" @ok="modalArr[modalIdx].okFn" @cancel="modalIdx = 0" style="top: 200px">
      <span v-if="modalIdx != 2 && modalIdx != 4">{{ modalArr[modalIdx].cont }}</span>

      <a-form ref="auditFormRef" :model="rejectForm">
        <a-form-item label="驳回原因" :label-col="{ span: 5 }" v-if="modalIdx == 2 || modalIdx == 4" v-bind="rejectInfos.rejectRemark" name="rejectRemark">
          <a-textarea v-model:value="rejectForm.rejectRemark" placeholder="请填写驳回原因" />
        </a-form-item>
      </a-form>
    </a-modal>

    <CheckPaymentOrder ref="checkPaymentOrderRef" @query="handleAfterAudit" />
    <audit-modal ref="auditModalRef" @audit="handleFinishAudit" />
    <reject-audit ref="rejectAuditRef" @audit="handleRejectAudit" />

    <!-- 付款单详情抽屉 -->
    <PaymentOrderDetailDrawer ref="paymentOrderDetailDrawerRef" title="付款单明细" @close="handleDetailDrawerClose" @purchase-order-click="handlePurchaseOrderClick" />

    <!-- 上传发票模态框 -->
    <a-modal v-model:open="uploadInvoiceModalVisible" title="上传发票" :width="600" @ok="handleUploadInvoiceConfirm" @cancel="handleUploadInvoiceCancel" :confirmLoading="uploadInvoiceLoading">
      <div class="mb-4">
        <p class="text-gray-600 mb-2">付款单号：{{ currentPaymentOrder?.payment_order_number }}</p>
        <!-- <p class="text-gray-600">供应商：{{ currentPaymentOrder?.supplier_subsidiary_name }}</p> -->
      </div>
      <Upload
        v-model:value="invoiceAttachments"
        :module="UploadFileModuleEnum.PaymentOrder"
        :multiple="true"
        :accept="'.jpg,.png,.pdf,.docx,.xlsx,.xls,.csv'"
        @change="handleInvoiceAttachmentsChange"
      />
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import { ExclamationCircleOutlined } from '@ant-design/icons-vue'
import { Form as antForm, message, Modal } from 'ant-design-vue'
import dayjs from 'dayjs'
import { debounce } from 'lodash'

import { DetailTypeEnum } from '@/common/enum'
import { paymentOrderStatusOption } from '@/common/options'
import eventBus from '@/utils/eventBus'
import { checkFormParams, Enum2Options } from '@/utils'
import { UploadFileModuleEnum } from '@/types/enums'
import Upload from '@/components/Upload.vue'

import AuditModal from '@/views/pageComponents/purchaseManagement/components/AuditModal.vue'

import CheckPaymentOrder from './components/CheckPaymentOrder.vue'
import RejectAudit from './components/RejectAudit.vue'
import PaymentOrderDetailDrawer from './components/PaymentOrderDetailDrawer.vue'

import { GetPageSuppliersSelect, GetPageMRPSupplierCompanySelect } from '@/servers/BusinessCommon'
import { AuditPass } from '@/servers/PaymentApply'
import {
  List,
  RejectToOriginator,
  GetPaymentOrderTotal,
  BatchAudit,
  DeletePayment,
  ExportPaymentOrderList,
  ApplicantFillterOptions,
  AuditRejected,
  PaymentAccountFillterOption,
  UploadInvoice,
  BatchInvoiceAudit,
} from '@/servers/paymentOrder'

import { PageTypeEnum } from '@/enums/tableEnum'

const useForm = antForm.useForm

const { btnPermission } = usePermission()
const { labelStatusCountMap, labelStatusRefresh } = useLabelStatus(PageTypeEnum.paymentOrder)
const checkPaymentOrderEl = useTemplateRef('checkPaymentOrderRef')
const rejectAuditRef = ref()

const tableRef = ref()
const rightOperateList = ref([
  {
    label: '查看',
    show: 131001,
    onClick: ({ row }) => {
      handleShowDetail(row.id, DetailTypeEnum.VIEW, 0)
    },
  },
  {
    label: '编辑',
    show: ({ row }) => row.audit_status === 10 && btnPermission.value[131003],
    onClick: ({ row }) => {
      handleShowDetail(row.id, DetailTypeEnum.EDIT, 0)
    },
  },
  {
    label: '审核',
    show: ({ row }) => btnPermission.value[getPermission(row.audit_status)],
    onClick: ({ row }) => {
      handleShowDetail(row.id, DetailTypeEnum.AUDIT, row.audit_status)
    },
  },
  {
    label: '反审核',
    show: ({ row }) => row.audit_status === 90 && row.payment_status_string === '已打款' && btnPermission.value[131005],
    onClick: ({ row }) => {
      rejectAuditMethod(row.id)
    },
  },
  {
    label: '删除',
    show: ({ row }) => [10, 95].includes(row.audit_status) && row.payment_status_string === '未打款' && btnPermission.value[131004],
    onClick: ({ row }) => {
      deleteBtn(row.id)
    },
  },
  {
    label: '上传发票',
    show: ({ row }) => row.audit_status === 90 && row.invoice_status === 1,
    onClick: ({ row }) => {
      handleUploadInvoice(row)
    },
  },
  {
    label: '发票审核',
    show: ({ row }) => row.audit_status === 90 && row.invoice_status === 2,
    onClick: ({ row }) => {
      handleInvoiceAudit(row)
    },
  },
])

const formRef = ref()
const auditFormRef = ref()

const search = debounce(() => {
  labelStatusRefresh()
  tableRef.value?.search()
}, 300)

const getPermission = (status: number) => {
  const permissionMap = {
    20: 131007, // 部门主管审核权限
    21: 131008, // 部门经理审核权限
    22: 131009, // 部门总监审核权限
    30: 131010, // 会计审核权限
    40: 131011, // 财务一级审核权限
    41: 131012, // 财务二级审核权限
    50: 131013, // CEO审核权限
    70: 131014, // 会计复审权限
  }
  return permissionMap[status]
}

const selectedReviewProgress = ref<(string | number)[]>([])

const reviewProgressOptions = ref([
  { label: '全部', value: 'all' },
  { label: '部门主管审核', value: 20, code: 'one_auditing_count' },
  { label: '部门经理审核', value: 21, code: 'two_auditing_count' },
  { label: '部门总监审核', value: 22, code: 'three_auditing_count' },
  { label: '会计审核', value: 30, code: 'four_auditing_count' },
  { label: '财务一级审核', value: 40, code: 'five_auditing_count' },
  { label: '财务二级审核', value: 41, code: 'six_auditing_count' },
  { label: 'CEO审核', value: 50, code: 'seven_auditing_count' },
  { label: '待FMS处理', value: 60, code: 'eight_auditing_count' },
  { label: '会计复审', value: 70, code: 'nine_auditing_count' },
])

const selectedInvoiceProgress = ref([
  { label: '全部', value: 'all' },
  { label: '上传发票', value: 1, code: 'other_one_count' },
  { label: '发票审核', value: 2, code: 'other_two_count' },
])

// 存储已选择的发票进度值
const selectedInvoiceProgressValues = ref<(string | number)[]>([])

const formFormat = (data: any) => {
  const obj = {
    listOrExport: true,
    audit_status: selectTip.value,
    review_progress: selectedReviewProgress.value,
    Invoice_status: selectedInvoiceProgressValues.value,
    ...data,
    bill_start_time: data.bill_start_time ? dayjs(data.bill_start_time).startOf('month').format('YYYY-MM-DD') : null,
    bill_end_time: data.bill_end_time ? dayjs(data.bill_end_time).endOf('month').format('YYYY-MM-DD') : null,
  }
  GetPaymentOrderTotal({
    totalField: tableRef.value?.tableKey?.length ? totalField.filter((v) => tableRef.value.tableKey.find((k) => k.is_total && k.key === v)) : totalField,
    ...obj,
  }).then((res) => {
    totalData.value = res.data
    statTotal()
  })
  return obj
}

const auditModalRef = ref()

const handleFinishAudit = async (auditForm) => {
  try {
    // 检查是否是发票审核
    if (currentInvoiceAuditOrder.value) {
      // 发票审核
      const params = {
        audit_opinion: auditForm.audit_opinion,
        is_pass: auditForm.is_pass,
        ids: [currentInvoiceAuditOrder.value.id],
      }

      const res = await BatchInvoiceAudit(params)
      if (res.success) {
        message.success(auditForm.is_pass ? '发票审核通过成功' : '发票审核拒绝成功')
        // 清空当前发票审核订单
        currentInvoiceAuditOrder.value = null
        // 刷新列表
        search()
      } else {
        message.error(res.message || '发票审核失败')
      }
    } else {
      // 原有的批量审核逻辑
      const ids = tableRef.value.checkItemsArr.map((item) => item.id)
      const res = await BatchAudit({
        ...auditForm,
        ids,
        audit_opinion: auditForm.audit_opinion,
        is_pass: auditForm.is_pass,
      })
      if (res.success) {
        message.success(auditForm.is_pass ? '审核通过成功' : '审核拒绝成功')
        search()
      } else {
        message.error(res.message || '操作失败')
      }
    }
  } catch (error) {
    console.error('审核操作失败:', error)
    message.error('操作失败')
  }
}

const formArr: any = ref([
  {
    label: '付款单编号',
    value: '',
    type: 'inputDlg',
    key: 'payment_order_number',
    isShow: true,
  },
  {
    label: '应付单号',
    value: '',
    type: 'inputDlg',
    key: 'payable_order_number',
    isShow: true,
  },
  {
    label: '供应商',
    value: null,
    type: 'select-supplier',
    key: 'supplier_id',
    mode: 'single',
    api: GetPageSuppliersSelect,
  },
  {
    label: '供应商子公司',
    value: null,
    type: 'select-supplier',
    mode: 'single',
    key: 'company_supplier_id',
    api: GetPageMRPSupplierCompanySelect,
    apiParams: { is_contains_srs: true },
  },
  {
    label: '结算方式',
    value: [],
    type: 'select',
    multiple: true,
    selectArr: Enum2Options(SettlementTypeEnum),
    key: 'settlement_method',
    isShow: false,
    isQuicks: true,
    quickIndex: 2,
    quickLabelWidth: 108,
    line: true,
  },
  {
    label: '申请部门',
    value: [],
    type: 'select',
    key: 'applicant_department_id',
    selectArr: [],
    multiple: true,
    isShow: true,
  },
  {
    label: '申请人',
    value: [],
    type: 'select',
    key: 'applicant_id',
    selectArr: [],
    multiple: true,
    isShow: true,
  },
  {
    label: '打款状态',
    value: null,
    type: 'select',
    key: 'payment_status',
    selectArr: [
      { label: '已打款', value: 2 },
      { label: '未打款', value: 1 },
    ],
    isShow: true,
    isQuicks: false,
  },
  {
    label: '支付方式',
    value: [],
    type: 'select',
    multiple: true,
    key: 'payment_method',
    selectArr: [
      {
        label: '公户',
        value: '1',
      },
      {
        label: '私户',
        value: '2',
      },
    ],
    isShow: true,
    isQuicks: true,
    quickIndex: 1,
    line: true,
  },
  {
    label: '单据状态',
    value: [],
    type: 'select',
    key: 'order_status',
    selectArr: [
      { label: '进行中', value: 1 },
      { label: '已完成', value: 2 },
    ],
    isShow: true,
    isQuicks: false,
    line: true,
  },
  {
    label: '付款类型',
    value: [],
    type: 'select',
    multiple: true,
    key: 'payment_type',
    selectArr: Enum2Options(PaymentTypeEnum),
    isShow: true,
    isQuicks: false,
    line: true,
  },
  {
    label: '付款账户名称(公户)',
    value: [],
    type: 'select',
    key: 'payment_account_name_public',
    selectArr: [],
    multiple: true,
    isShow: true,
    isQuicks: true,
    quicks: ['广东采云月供应链管理有限公司', '广东省简素净生物科技有限公司', '广东依欧好生物科技有限公司', '广东云月仓供应链管理有限公司'],
    quickIndex: 4,
    flex: '100%',
    quickLabelWidth: 108,
  },
  {
    label: '付款账户名称(私户)',
    value: [],
    type: 'select',
    key: 'payment_account_name_private',
    selectArr: [],
    multiple: true,
    isShow: true,
    isQuicks: true,
    quicks: [
      { label: '周婵柔(中信7401)', value: '周婵柔|****************' },
      { label: '张育玲(中信7419)', value: '张育玲|****************' },
      { label: '张娜娜(民生8013)', value: '张娜娜|****************' },
      { label: '月仓一吴伟民(中信3965)', value: '吴伟民|****************' },
      { label: '月仓一郑熙悦(民生9898)', value: '郑熙悦|****************' },
      { label: '加工厂一陈静(中信7393)', value: '陈静|****************' },
      { label: '加工厂一周婵柔(9171)', value: '周婵柔|****************' },
    ],
    quickIndex: 5,
    flex: '100%',
    quickLabelWidth: 108,
  },
  {
    label: '申请时间',
    value: null,
    type: 'range-picker',
    key: 'apply_time',
    formKeys: ['apply_start_time', 'apply_end_time'],
    placeholder: ['申请开始时间', '申请结束时间'],
  },
  {
    label: '账单时间',
    value: null,
    type: 'range-picker',
    key: 'bill_month_time',
    formKeys: ['bill_month_start_time', 'bill_month_end_time'],
    placeholder: ['账单开始时间', '账单结束时间'],
  },
  {
    label: '期望付款日期',
    value: null,
    type: 'date-picker',
    key: 'expected_payment_date',
    showTime: false,
    valueFormat: 'YYYY-MM-DD',
    isShow: true,
  },
])

// const childrenTableKeys = ref([
//   { title: '应付单号', field: 'payable_order_number', width: 150 },
//   {
//     title: '采购单号',
//     field: 'number',
//     width: 150,
//     render: ({ row }) => {
//       return h('a', { class: 'link', onClick: () => handlePurchaseNumberClick(row) }, row.number)
//     },
//   },
//   { title: '商品名称', field: 'sku_name', width: 200 },
//   { title: '商品编号', field: 'style_code', width: 120 },
//   { title: '采购总数', field: 'total_purchase_quantity', width: 100 },
//   { title: '成本数量', field: 'cost_num', width: 100 },
//   { title: '含税单价', field: 'tax_unit_price', width: 100 },
//   { title: '采购总金额', field: 'total_purchase_amount', width: 120 },
//   { title: '已预约入库总数量', field: 'total_scheduled_quantity', width: 140 },
//   { title: '实际入库总数量', field: 'total_actual_inbound', width: 140 },
// ])

const tableRecords = computed(() => {
  return tableRef?.value?.tableRef?.getCheckboxRecords() || []
})

const tableAllid = ref()
const modalIdx = ref(0)
const modelShow = ref(false)
const modalArr = reactive([
  {
    title: '',
    cont: '',
    okFn: () => {},
  },
  {
    title: '提示',
    cont: '确认通过此单？',
    okFn: () => {
      passFlow()
    },
  },
  {
    title: '驳回',
    cont: '确认驳回此单？',
    okFn: () => {
      rejectFlow()
    },
  },
  {
    title: '提示',
    cont: '确认批量审批？',
    okFn: () => {
      passAllFlow()
    },
  },
  {
    title: '驳回',
    cont: '确认批量驳回？',
    okFn: () => {
      rejectAllFlow()
    },
  },
])

const selectTip = ref<string | number>('')

const router = useRouter()

const handlePurchaseNumberClick = (row) => {
  router.push(`/purchaseOrderLook/${row.purcharse_order_id}`)
}

// 付款单详情抽屉相关
const paymentOrderDetailDrawerRef = ref()

// 处理详情抽屉关闭
const handleDetailDrawerClose = () => {
  console.log('详情抽屉已关闭')
}

// 处理采购单点击
const handlePurchaseOrderClick = (row) => {
  console.log('点击采购单:', row)
  // 可以在这里跳转到采购单详情页面
  router.push(`/purchaseOrderLook/${row.purchase_order_number}`)
}

// 打开付款单详情抽屉
const openPaymentOrderDetail = (paymentOrderId: string) => {
  // 从当前行数据中获取bill_purchase_numbers和payment_type
  // 使用正确的方法获取行数据
  const currentRow = tableRef.value?.tableData?.find((row) => row.id === paymentOrderId) || tableRef.value?.checkItemsArr?.find((row) => row.id === paymentOrderId)

  if (currentRow) {
    // 传递付款单ID和必要的数据
    paymentOrderDetailDrawerRef.value?.open(paymentOrderId, {
      bill_purchase_numbers: currentRow.bill_purchase_numbers,
      payment_type: currentRow.payment_type,
    })
  } else {
    // 如果找不到行数据，只传递ID
    paymentOrderDetailDrawerRef.value?.open(paymentOrderId)
  }
}

onMounted(() => {
  selectTip.value = paymentOrderStatusOption[0].value
  initApi()
  search()
})

const rejectForm = ref({
  rejectRemark: '',
})

const {
  validate: rejectValidate,
  validateInfos: rejectInfos,
  resetFields: rejectResetFields,
} = useForm(
  rejectForm,
  reactive({
    rejectRemark: [
      { required: true, message: '请输入驳回原因' },
      { max: 200, message: '驳回原因不能超过200字' },
    ],
  }),
)
watch(modalIdx, (newvalue) => {
  if (newvalue != 0) {
    modelShow.value = true
    rejectResetFields()
  } else {
    modelShow.value = false
  }
})

const totalData = ref({})
const totalField = ['the_actual_amount', 'deduct_amount']

const debouncedGetTotal = debounce(() => {
  statTotal()
}, 300)

watch(
  [() => tableRef.value?.checkItemsArr, () => selectTip.value, () => selectedReviewProgress.value, () => selectedInvoiceProgressValues.value, () => formArr.value],
  ([checkItems, tip, progress, form]) => {
    const needRefresh = (checkItems && checkItems.length > 0) || tip !== undefined || progress !== undefined || (form && Object.values(form).every((v) => !v || (Array.isArray(v) && v.length === 0)))

    if (needRefresh) {
      debouncedGetTotal()
    }
  },
  { deep: true },
)

const initApi = async () => {
  const departmentRes = await ApplicantFillterOptions({ applicantOptionType: 2 })
  formArr.value.find((v) => v.key === 'applicant_department_id').selectArr = departmentRes.data.map((v) => ({ label: v.value, value: v.key }))

  const applicantRes = await ApplicantFillterOptions({ applicantOptionType: 1 })
  formArr.value.find((v) => v.key === 'applicant_id').selectArr = applicantRes.data.map((v) => ({ label: v.value, value: v.key }))

  PaymentAccountFillterOption({ paymentAccountOptionType: 1 }).then((res) => {
    formArr.value.find((v) => v.key === 'payment_account_name_public').selectArr = res.data.map((v) => ({ label: v.value, value: v.key }))
  })
  PaymentAccountFillterOption({ paymentAccountOptionType: 2 }).then((res) => {
    formArr.value.find((v) => v.key === 'payment_account_name_private').selectArr = res.data.map((v) => ({ label: v.value, value: v.key }))
  })
  // getCount()
}

const rejectAuditMethod = (id) => {
  Modal.confirm({
    title: '是否确定反审核?',
    icon: () => {},
    content: '',
    async onOk() {
      const res = await RejectToOriginator({ id })
      if (res.success) {
        tableRef.value.search()
        message.success('操作成功')
      } else {
        message.error(res.message)
      }
    },
    onCancel() {},
  })
}

const passFlow = () => {
  const data = { ids: tableAllid.value }
  modalIdx.value = 0
  message.info('正在执行中，请稍等...')

  AuditPass(data).then((res) => {
    if (res.success) {
      tableRef.value.refresh()
      // getCount()
      tableAllid.value = ''
      message.success('已通过此单')
    } else {
      message.error(res.data.msg)
    }
  })
}

const rejectFlow = () => {
  rejectValidate().then(() => {
    const data = { ids: tableAllid.value, remark: rejectForm.value.rejectRemark, is_pass: false }
    modalIdx.value = 0
    message.info('正在执行中，请稍等...')
    BatchAudit(data).then((res) => {
      if (res.success) {
        tableRef.value.refresh()
        // getCount()
        tableAllid.value = ''
        message.success('已驳回此单')
      } else {
        message.error(res.data.msg)
      }
    })
  })
}

const passAllFlow = () => {
  const data = { ids: [] } as any
  tableRecords.value.forEach((item) => {
    data.ids.push(item.id)
  })
  data.ids = Array.from(new Set(data.ids))
  modalIdx.value = 0
  message.info('正在执行中，请稍等...')
  AuditPass(data).then((res) => {
    if (res.success) {
      message.success('批量审批通过')
      tableRef.value.refresh()
      // getCount()
    } else {
      message.error(res.data.msg)
    }
  })
}

const rejectAllFlow = () => {
  rejectValidate().then(() => {
    const data = { ids: tableAllid.value, remark: rejectForm.value.rejectRemark, is_pass: false }
    modalIdx.value = 0
    message.info('正在执行中，请稍等...')
    BatchAudit(data).then((res) => {
      if (res.success) {
        message.success('已批量驳回')
        tableRef.value.refresh()
        // getCount()
      } else {
        message.error(res.data.msg)
      }
    })
  })
}

const handleShowAuditModal = (type: boolean) => {
  if (tableRef.value?.checkItemsArr?.length === 0) {
    message.warning('请勾选要通过审核的付款单')
    return
  }
  if (tableRef.value?.checkItemsArr.some((i) => [10, 90, 95].includes(i.audit_status))) {
    message.warning('请选择正确的需审核的付款单')
    return
  }
  let haveAlike = false
  const noPreAudit = false
  tableRef.value?.checkItemsArr.forEach((i) => {
    tableRef.value?.checkItemsArr.forEach((j) => {
      if (i.audit_status !== j.audit_status) {
        haveAlike = true
      }
    })
  })
  if (haveAlike) {
    message.warning('请选择相同状态的单据进行批量审核操作')
    return
  }
  if (noPreAudit) {
    message.warning('您没有该状态的审核权限')
    return
  }
  auditModalRef.value?.open(type)
}

const rejectAllBtn = () => {
  if (tableRecords.value.length) {
    let minAuditStatus = Infinity
    let minAmount
    tableRecords.value.forEach((record) => {
      if (record.audit_status < minAuditStatus) {
        minAuditStatus = record.audit_status
      }
      if (minAmount === undefined || record.the_actual_amount < minAmount) {
        minAmount = record.the_actual_amount
      }
    })
    rejectAuditRef.value?.open(minAuditStatus, minAmount)
  } else {
    message.warning('请勾选要拒绝审核的付款单')
  }
}

const deleteBtn = (id) => {
  Modal.confirm({
    title: '是否删除选中的申请单',
    icon: h(ExclamationCircleOutlined),
    async onOk() {
      try {
        await DeletePayment({ id })
        message.success('删除成功')
        search()
      } catch (_err) {
        message.error('删除失败')
      }
    },
  })
}

const footerData = ref<any>([{}])

const exportType = ref<number | undefined>(undefined)
const exportOptions = ref([
  {
    label: '根据勾选导出',
    value: 1,
  },
  {
    label: '根据筛选结果导出',
    value: 2,
  },
  {
    label: '全部导出',
    value: 3,
  },
])

// 上传发票相关状态
const uploadInvoiceModalVisible = ref(false)
const currentPaymentOrder = ref<any>(null)
const invoiceAttachments = ref<any[]>([])
const uploadInvoiceLoading = ref(false)
const exportLoading = ref(false)

// 发票审核相关状态
const currentInvoiceAuditOrder = ref<any>(null)

const onExportTypeChange = (value) => {
  const count = tableRef.value.checkItemsArr.length
  const type = value
  let msg = ''
  if ([1, 2, 3].includes(type) && count > 50000) {
    msg = '单次操作不得大于5万条！'
  }
  if ([4, 5, 6].includes(type) && count > 5000) {
    msg = '单次操作不得大于5千条！'
  }
  if ([1, 4].includes(type) && !count) {
    msg = '请勾选数据'
  }
  if (msg) {
    exportType.value = undefined
    return message.info(msg)
  }
  const params = {
    exportType: type > 3 ? type - 3 : type,
    isExportImg: [4, 5, 6].includes(type),
    paymentOrderIds: tableRef.value.checkItemsArr.map((f) => f.id),
  }
  Modal.confirm({
    title: '是否确定导出付款单数据?',
    icon: () => {},
    content: '',
    async onOk() {
      exportData(params)
    },
    onCancel() {},
  })
}

const exportData = (params) => {
  const obj = {
    audit_status: selectTip.value,
  }
  checkFormParams({ formArr: formArr.value, obj })
  exportLoading.value = true
  ExportPaymentOrderList({
    ...params,
    searchListParam: obj,
  })
    .then((res) => {
      eventBus.emit('downLoadId', res.data)
    })
    .finally(() => {
      exportLoading.value = false
    })
}

const handleShowDetail = (id: string, type: DetailTypeEnum, audit_status: number) => {
  checkPaymentOrderEl.value?.open(type, id, audit_status)
}

const statusCounts = computed(() => {
  if (!labelStatusCountMap.value) return {}

  const auditingTotal =
    (labelStatusCountMap.value.one_auditing_count || 0) +
    (labelStatusCountMap.value.two_auditing_count || 0) +
    (labelStatusCountMap.value.three_auditing_count || 0) +
    (labelStatusCountMap.value.four_auditing_count || 0) +
    (labelStatusCountMap.value.five_auditing_count || 0) +
    (labelStatusCountMap.value.six_auditing_count || 0) +
    (labelStatusCountMap.value.seven_auditing_count || 0) +
    (labelStatusCountMap.value.eight_auditing_count || 0) +
    (labelStatusCountMap.value.nine_auditing_count || 0)

  return {
    await_arraigned_count: labelStatusCountMap.value.await_arraigned_count || 0,
    auditing_count: auditingTotal,
    deleted_count: labelStatusCountMap.value.declined_count || 0,
  } as Record<string, number>
})

const handleTagClick = (item: { value: string | number }) => {
  if (item.value === 'all') {
    selectedReviewProgress.value = []
  } else {
    if (selectedReviewProgress.value.includes(item.value)) {
      selectedReviewProgress.value = selectedReviewProgress.value.filter((v) => v !== item.value)
    } else {
      selectedReviewProgress.value = [...selectedReviewProgress.value, item.value]
    }
  }
  search()
}
const handleInvoiceTagClick = (item: { label: string; value: string | number; code?: string }) => {
  if (item.value === 'all') {
    selectedInvoiceProgressValues.value = []
  } else {
    // 检查是否已经选择了这个选项
    const isSelected = selectedInvoiceProgressValues.value.includes(item.value)

    if (isSelected) {
      // 如果已选择，则移除
      selectedInvoiceProgressValues.value = selectedInvoiceProgressValues.value.filter((v) => v !== item.value)
    } else {
      // 如果未选择，则添加
      selectedInvoiceProgressValues.value = [...selectedInvoiceProgressValues.value, item.value]
    }
  }
  search()
}

const handleRejectAudit = (auditForm) => {
  AuditRejected({ ...auditForm, ids: tableRecords.value.map((v) => v.id) }).then((res) => {
    if (res.success) {
      message.success('批量驳回成功')
      tableRef.value.refresh()
      // getCount()
    } else {
      message.error(res.message)
    }
  })
}

// 处理上传发票
const handleUploadInvoice = (row) => {
  console.log('上传发票:', row)
  // 设置当前选中的付款单
  currentPaymentOrder.value = row
  // 打开上传发票模态框
  uploadInvoiceModalVisible.value = true
}

// 处理发票审核
const handleInvoiceAudit = (row) => {
  console.log('发票审核:', row)
  // 设置当前选中的付款单
  currentInvoiceAuditOrder.value = row
  // 打开审核模态框（默认审核通过）
  auditModalRef.value?.open(true)
}

// 处理发票附件变化
const handleInvoiceAttachmentsChange = (files: any[]) => {
  invoiceAttachments.value = files
}

// 处理上传发票确认
const handleUploadInvoiceConfirm = async () => {
  if (!currentPaymentOrder.value) {
    message.error('请选择付款单')
    return
  }

  if (!invoiceAttachments.value || invoiceAttachments.value.length === 0) {
    message.error('请选择要上传的发票文件')
    return
  }

  try {
    uploadInvoiceLoading.value = true

    const params = {
      id: currentPaymentOrder.value.id,
      Invoice_attachments: invoiceAttachments.value.map((file) => file.id),
    }

    const res = await UploadInvoice(params)
    if (res.success) {
      message.success('发票上传成功')
      uploadInvoiceModalVisible.value = false
      // 刷新列表
      search()
    } else {
      message.error(res.message || '发票上传失败')
    }
  } catch (error) {
    console.error('上传发票失败:', error)
    message.error('发票上传失败')
  } finally {
    uploadInvoiceLoading.value = false
  }
}

// 处理上传发票取消
const handleUploadInvoiceCancel = () => {
  uploadInvoiceModalVisible.value = false
  currentPaymentOrder.value = null
  invoiceAttachments.value = []
}

const footerSpanMethod = ({ $columnIndex }) => {
  if ($columnIndex === 0) {
    return { rowspan: 1, colspan: 2 }
  }
  if ($columnIndex === 1) {
    return { rowspan: 1, colspan: 0 }
  }
  return { rowspan: 1, colspan: 1 }
}

const statTotal = () => {
  const $table = tableRef?.value?.tableRef
  if (!$table) return []
  const keys = tableRef.value.tableKey

  const columns = $table.getColumns()
  const isCheck = tableRef.value?.checkItemsArr.length
  const obj = {} as any

  columns.forEach((column, _columnIndex) => {
    if (_columnIndex === 0) {
      obj.checkbox = isCheck ? '勾选合计' : '合计'
      return
    }

    if (!keys.find((v) => v.key === column.field)?.is_total) return

    if (isCheck) {
      if (totalField.includes(column.field)) {
        const sum = tableRef.value?.checkItemsArr.reduce((acc, cur) => {
          acc[column.field] = (acc[column.field] || 0) + cur[column.field]
          return acc
        }, obj)

        obj[column.field] = `￥${sum[column.field].roundNext(2)}`
      }
    } else {
      obj[column.field] = totalData.value[column.field] ? `￥${totalData.value[column.field].roundNext(2)}` : null
    }
  })

  footerData.value = [obj]
}

const handleFormReset = () => {
  selectedReviewProgress.value = []
  selectedInvoiceProgressValues.value = []
}

const handleAfterAudit = () => {
  formRef.value?.clearForm()
  selectedReviewProgress.value = []
  selectedInvoiceProgressValues.value = []
  labelStatusRefresh()
  search()
}
</script>

<style lang="scss" scoped>
:deep(.ant-space) {
  gap: 8px !important;
}

.bcall {
  display: flex;
  overflow: hidden;

  .fileContent {
    display: flex;
    margin: 0 0.125rem;
    cursor: pointer;
    border: 0.0625rem solid #c8d3e7;

    .bcspan {
      width: 70px;
      padding: 0.075rem 0.1875rem;
      overflow: hidden;
      font-size: 12px;
      color: #1890ff;
      text-overflow: ellipsis;
      white-space: nowrap;
      background-color: white;
      border-right: 0.0625rem solid #c8d3e7;
    }

    .status {
      width: 20px;
      font-size: 16px;
      text-align: center;
    }
  }
}
</style>
