<template>
  <a-drawer title="选择物料" :width="width" :open="openFlag" :mask-closable="false" :body-style="{ display: 'flex', padding: '16px', justifyContent: 'space-between' }" @close="handleClose">
    <div class="h-full flex flex-col flex-1">
      <a-space class="mb-12">
        <a-input v-for="item in searchFormList" :key="item.name" :placeholder="item.placeholder" v-model:value="queryParams[item.name]" :maxlength="200" allowClear />
        <a-button type="primary" @click="tableRef.search()">查询</a-button>
      </a-space>
      <BaseTable ref="tableRef" v-bind="tableConfig" @loadData="setCheckedRows" />
    </div>
    <div class="w-24% flex flex-col ml-12" v-if="selectTableConfig.tableColumns?.length">
      <div class="flex mb-12 h-30 items-center">
        <div class="text-16px text-#333">已选{{ selectRowsMapCount }}个物料</div>
        <a-button v-if="selectRowsMapCount" class="ml-auto" type="primary" @click="handleClear">清空</a-button>
      </div>
      <BaseTable ref="selectTableRef" v-bind="selectTableConfig" />
    </div>
    <template #footer>
      <a-space>
        <a-button type="primary" @click="handleConfirm">确定</a-button>
        <a-button @click="handleClose">取消</a-button>
      </a-space>
    </template>
  </a-drawer>
</template>

<script lang="ts" setup>
import { GetK3SkuInfo } from '@/servers/BusinessCommon'

const emits = defineEmits(['confirm'])
const props = defineProps({
  syncConfirm: {
    type: Function,
    default: null,
  },
})

const processWidth = ref()
const width = computed(() => {
  return Math.min(processWidth.value, window.innerWidth - 180)
})
const openFlag = ref(false)

const searchFormList = ref<Record<string, any>[]>([])
const tableRef = ref()
const selectTableRef = ref()
const watcher = ref()

const baseParams = ref({})
const queryParams = ref<Record<string, any>>({})
const formFormat = (data) => {
  return {
    ...baseParams.value,
    ...data,
    ...queryParams.value,
  }
}

const formMap = {
  分仓仓库编码: { type: 'input', name: 'warehouse_sku_id' },
  商品名称: { type: 'input', name: 'sku_name' },
  商品编号: { type: 'input', name: 'sku_id' },
}

const defaultColumns = ref<Record<string, any>[]>(
  [
    { key: 'warehouse_sku_id', name: '分仓仓库编码' },
    { key: 'sku_name', name: '商品名称' },
    { key: 'jst_sku_id', name: '聚水潭编码' },
    { key: 'sku_id', name: '商品编码' },
    { key: 'type_specification', name: '规格型号' },
    { key: 'cost_unit', name: '计价单位' },
    { key: 'actual_inventory_num', name: '实际库存数' },
    { type: 'seq', name: '序号', width: 60 },
  ].map((f) => ({ ...f })),
)
useEasyTable(defaultColumns)

const tableConfig = ref<Record<string, any>>({
  isCheckbox: true,
  isIndex: false,
  hideTop: true,
  autoSearch: false,
  tableColumns: [],
  formFormat,
  rowConfig: {},
  checkboxConfig: {},
  clearCheckboxFlag: false,
})

const selectTableConfig = ref<Record<string, any>>({
  hideTop: true,
  hidePagination: true,
  autoSearch: false,
  tableColumns: [],
  rowConfig: {},
})

const selectRowsMap = ref({})
const selectRowsMapCount = computed(() => Object.keys(selectRowsMap.value).length)
const setCheckedRows = () => {
  const $table = tableRef.value.tableRef
  // $table.setCheckboxRowKey(Object.keys(selectRowsMap.value), true)
  const rows: any[] = []
  for (const item of $table.getData()) {
    if (selectRowsMap.value[item[tableKeyField.value]]) {
      rows.push(item)
    }
  }
  $table.setCheckboxRow(rows, true)
}

const tableKeyField = ref('sku_id')
const open = async ({ left, right, search, params, checkMethod, width, api, keyField }: any, sourceRows = []) => {
  processWidth.value = width || 1050
  queryParams.value = {}
  // 接口的混入参数 -> formFormat
  baseParams.value = params || {}
  searchFormList.value = search.map((placeholder) => ({ ...formMap[placeholder], placeholder }))
  tableRef.value && tableRef.value.clearCheckbox()
  tableConfig.value.getList = api || GetK3SkuInfo

  tableKeyField.value = keyField || 'sku_id'
  tableConfig.value.rowConfig.keyField = tableKeyField.value
  selectTableConfig.value.rowConfig.keyField = tableKeyField.value
  tableConfig.value.tableColumns = left.map((key: string) => {
    const found = defaultColumns.value.find((f) => [f.name, f.key].includes(key))
    if (!found) throw new Error(`tableConfig columns ${key} not found`)
    return {
      ...found,
      name: found.alias || found.name,
    }
  })
  selectTableConfig.value.tableColumns = (right || []).map((key: string) => {
    const found = defaultColumns.value.find((f) => [f.name, f.key].includes(key))
    if (!found) throw new Error(`selectTableConfig columns ${key} not found`)
    return {
      ...found,
      name: found.alias || found.name,
    }
  })
  // 是否允许勾选的方法，该方法，的返回值用来决定这一行的 checkbox 是否可以勾选
  if (checkMethod) {
    tableConfig.value.checkboxConfig.checkMethod = checkMethod
  }
  openFlag.value = true
  selectRowsMap.value = sourceRows.reduce((acc, item: any) => {
    return { ...acc, [item[tableKeyField.value]]: item }
  }, {})
  await nextTick()
  const $table = tableRef.value.tableRef
  await tableRef.value?.search()
  setTimeout(async () => {
    await nextTick()
    watcher.value = watch(
      () => tableRef.value.checkItemsArr,
      () => {
        const checkItems = $table.getCheckboxRecords()
        const tableData = tableRef.value.tableData || []
        const keys = Object.keys(selectRowsMap.value)
        for (const key of keys) {
          // 是否存在当前表格里
          const hasCurTable = tableData.find((f) => f[tableKeyField.value] == key)
          // 是否存在当前已选CHECK里
          const hasCurSelect = checkItems.find((f) => f[tableKeyField.value] == key)
          if (hasCurTable) {
            if (!hasCurSelect) {
              delete selectRowsMap.value[key]
            }
          }
        }
        for (const item of checkItems) {
          selectRowsMap.value[item[tableKeyField.value]] = item
        }
        selectTableRef?.value?.tableRef?.reloadData(Object.values(selectRowsMap.value))
      },
      { immediate: true },
    )
  }, 0)
}
const handleClear = () => {
  selectRowsMap.value = {}
  tableRef.value?.clearCheckbox()
}

const handleClose = () => {
  watcher.value && watcher.value()
  openFlag.value = false
}
const handleConfirm = async () => {
  console.log('handleConfirm', Object.values(selectRowsMap.value))
  if (props.syncConfirm) {
    const flag = await props.syncConfirm(Object.values(selectRowsMap.value))
    if (flag) handleClose()
  } else {
    emits('confirm', Object.values(selectRowsMap.value))
    handleClose()
  }
}

onMounted(() => {
  /**
   * 使用方式
   */
  // const formData = ref<Record<string, unknown>>({})
  // const listCache = ref<Record<string, string|number>[]>([])
  // const row: Record<string, unknown> = {}
  // 根据ali_sku_id匹配出不可选商品
  // const unSelectSkuIds = listCache.value.reduce((acc, cur) => {
  //   if (cur.ali_sku_id !== row.ali_sku_id) {
  //     return [...acc, ...cur.productList.map((f) => f.sku_id)]
  //   }
  //   return acc
  // }, [])
  // open({
  //   search: ['商品名称', '商品编码', '聚水潭商品编号', '采购单编号'],
  //   left: ['商品主图', '商品名称', '商品编码K3', '聚水潭编号', '颜色规格'],
  //   right: ['商品名称', '商品编码K3'],
  //   width: 1300,
  //   keyField: 'id',
  //   api: GetBookingPurchaseOrderDetail,
  //   params: {
  //     numbers: formData.value.purchase_order_numbers,
  //   },
  //   checkMethod: ({ row }) => {
  //     return !unSelectSkuIds.includes(row.sku_id)
  //   },
  // })
})

defineExpose({
  open,
})
</script>

<style lang="scss" scoped>
//
</style>
