<template>
  <a-drawer :maskClosable="false" title="采购价目表" :width="showAuditRecord ? '1750px' : '1400px'" :visible="visible" @close="handleClose" :bodyStyle="{ padding: '0' }">
    <template #extra>
      <a-button @click="handleShowAuditRecord" v-if="[DetailTypeEnum.VIEW, DetailTypeEnum.AUDIT].includes(viewType) && btnPermission[81007]">审核记录</a-button>
    </template>
    <div class="flex h-full">
      <div class="w-1400px overflow-y-auto p-16 flex flex-col">
        <div class="drawer-title">基本信息</div>
        <a-form :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }" :rules="rules" :model="form" ref="formRef">
          <a-row>
            <a-col :span="12">
              <a-form-item label="单据编号" name="code">
                <span v-if="[DetailTypeEnum.VIEW, DetailTypeEnum.AUDIT].includes(viewType)">{{ form.number }}</span>
                <a-input v-else v-model:value="form.number" :disabled="true" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="价目表名称" name="name">
                <span v-if="[DetailTypeEnum.VIEW, DetailTypeEnum.AUDIT].includes(viewType)">{{ form.name }}</span>
                <a-input v-else v-model:value="form.name" placeholder="请输入价目表名称" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="12">
              <a-form-item label="价目类型" name="price_type">
                <span v-if="[DetailTypeEnum.VIEW, DetailTypeEnum.AUDIT].includes(viewType)">{{ PurchasePriceTypeMap[form.price_type] }}</span>
                <a-select v-else v-model:value="form.price_type" placeholder="请选择" :dropdownMatchSelectWidth="false">
                  <a-select-option :value="1">采购</a-select-option>
                  <a-select-option :value="2">加工</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="币别" name="currency_type">
                <span v-if="[DetailTypeEnum.VIEW, DetailTypeEnum.AUDIT].includes(viewType)">{{ form.currency_type === 1 ? '人民币' : '' }}</span>
                <a-select v-else v-model:value="form.currency_type" placeholder="请选择">
                  <a-select-option :value="1">人民币</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="12">
              <a-form-item label="供应商子公司">
                <span v-if="[DetailTypeEnum.VIEW, DetailTypeEnum.AUDIT].includes(viewType)">{{ form.supplier_name }}</span>
                <SelectSupplier
                  v-else
                  v-model:value="form.company_supplier_id"
                  mode="single"
                  :api="GetPageMRPSupplierCompanySelect"
                  :api-params="{ is_contains_srs: true }"
                  v-model:label="form.supplier_name"
                  :labelInValue="false"
                ></SelectSupplier>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="定价员" name="rater_name">
                <span v-if="[DetailTypeEnum.VIEW, DetailTypeEnum.AUDIT].includes(viewType)">{{ form.rater_name }}</span>
                <a-select v-else v-model:value="form.rater_name" placeholder="请选择">
                  <a-select-option v-for="item in userInfoOption" :key="item.value" :value="item.value">{{ item.label }}</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="12">
              <a-form-item label="是否默认" name="is_default">
                <span v-if="[DetailTypeEnum.VIEW, DetailTypeEnum.AUDIT].includes(viewType)">{{ form.is_default ? '是' : '否' }}</span>
                <a-radio-group v-else v-model:value="form.is_default">
                  <a-radio :value="true">是</a-radio>
                  <a-radio :value="false">否</a-radio>
                </a-radio-group>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="是否含税" name="is_contain_tax">
                <span v-if="[DetailTypeEnum.VIEW, DetailTypeEnum.AUDIT].includes(viewType)">{{ form.is_contain_tax ? '是' : '否' }}</span>
                <a-radio-group v-else v-model:value="form.is_contain_tax" @change="setTaxRate">
                  <a-radio :value="true">是</a-radio>
                  <a-radio :value="false">否</a-radio>
                </a-radio-group>
              </a-form-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="12">
              <a-form-item label="状态" name="status">
                <span>{{ PurchaseOrderAuditStatusEnum[form.audit_status] || '未发布' }}</span>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
        <div class="drawer-title">明细信息</div>
        <a-tabs v-model:activeKey="active" type="card">
          <a-tab-pane key="1" tab="单品价目"></a-tab-pane>
          <a-tab-pane key="2" tab="材质价目"></a-tab-pane>
        </a-tabs>
        <!-- 单品价目 -->
        <template v-if="active === '1'">
          <a-flex justify="space-between" class="mb-8" v-if="[DetailTypeEnum.EDIT, DetailTypeEnum.ADD].includes(viewType)">
            <a-space>
              <a-button @click="handleDownloadTemplate">下载导入模板</a-button>
              <a-upload :showUploadList="false" :before-upload="handleBeforeUpload">
                <a-button :loading="exportFlag">导入</a-button>
              </a-upload>
            </a-space>
            <a-button type="primary" @click="handleShowAddProduct">新增</a-button>
          </a-flex>
          <a-flex justify="space-between" class="mb-8" v-if="[DetailTypeEnum.VIEW, DetailTypeEnum.AUDIT].includes(viewType)">
            <a-space :size="16" v-if="[DetailTypeEnum.VIEW, DetailTypeEnum.AUDIT].includes(viewType)">
              <a-input placeholder="商品编码/聚水潭编码" v-model:value="queryForm.sku_id" :maxlength="200" allowClear />
              <a-input placeholder="商品名称" v-model:value="queryForm.sku_name" :maxlength="200" allowClear />
              <a-select placeholder="商品分类" v-model:value="queryForm.category" :options="categoryOptions" allowClear />
              <a-button type="primary" @click="getProductList">查询</a-button>
            </a-space>
            <a-button @click="handleExport" class="ml-auto">导出</a-button>
          </a-flex>
          <div class="flex flex-1 overflow-hidden" v-if="[DetailTypeEnum.EDIT, DetailTypeEnum.ADD].includes(viewType)">
            <div class="w200px mr-5px flex flex-col">
              <div class="bg-#EAECEE">
                <a-input-search v-model:value="productName" placeholder="根据商品编号搜索" @input="filterTag(productName)" />
              </div>
              <RecycleScroller class="scroller flex-1" :items="leftTagList" :item-size="110" key-field="iid" v-slot="{ item }">
                <div
                  class="h100px bg-#f2f2f2 mt-5 mb-5 text-#3D3D3D pl-10px pr-10px overflow-hidden cursor-pointer"
                  :class="{ isClick: select_sku_id == item.sku_id, isRed: item.isRed }"
                  style="text-overflow: ellipsis; white-space: nowrap; border: 1px solid #c6e2ff"
                  @click="changeSkuId(item.sku_id)"
                >
                  <div class="h20px">{{ item.sku_id }}</div>
                  <div class="h20px">{{ item.jst_sku_id }}</div>
                  <div class="h20px text-ellipsis overflow-hidden">{{ item.sku_name }}</div>
                  <div class="h20px text-ellipsis overflow-hidden">
                    <span>{{ item.type_specification }}</span>
                    <span class="ml-10px">{{ item.valuation_unit }}</span>
                  </div>
                  <div class="h20px">{{ productTypeMap[item.category] }}</div>
                </div>
              </RecycleScroller>
            </div>
            <div class="h-full overflow-auto w1200px">
              <price-table ref="priceTableRef" :table-key="tableColumns" :data="filterProductList" :isCheckbox="[DetailTypeEnum.VIEW, DetailTypeEnum.AUDIT].includes(viewType)" :type="1">
                <template #process_type="{ row }" v-if="[DetailTypeEnum.EDIT, DetailTypeEnum.ADD].includes(viewType)">
                  <a-select v-model:value="row.process_type" :options="machiningTypeList" @change="checkLeftList()" class="w-full" placeholder="请选择加工方式" :dropdownMatchSelectWidth="false" />
                </template>
                <template #quantity_from="{ row }" v-if="[DetailTypeEnum.EDIT, DetailTypeEnum.ADD].includes(viewType)">
                  <a-input-number
                    class="w-full"
                    :controls="false"
                    v-model:value="row.quantity_from"
                    :min="0"
                    :max="99999999"
                    :parser="(value) => value.replace('.', '')"
                    :class="{ isRed: row.isFromRed }"
                    @change="checkLeftList()"
                  />
                </template>
                <template #quantity_to="{ row }" v-if="[DetailTypeEnum.EDIT, DetailTypeEnum.ADD].includes(viewType)">
                  <a-input-number
                    class="w-full"
                    :controls="false"
                    v-model:value="row.quantity_to"
                    :min="0"
                    :max="99999999"
                    :parser="(value) => value.replace('.', '')"
                    :class="{ isRed: row.isToRed }"
                    @change="checkLeftList()"
                  />
                </template>
                <template #str_material_price="{ row }" v-if="[DetailTypeEnum.EDIT, DetailTypeEnum.ADD].includes(viewType)">
                  <a-input-number
                    class="w-full"
                    :controls="false"
                    v-model:value="row.str_material_price"
                    :min="0.0"
                    :max="99999"
                    step="0.00000000"
                    :precision="8"
                    stringMode
                    :class="{ isRed: row.isPriceRed }"
                    @change="(checkLeftList(), setPrice())"
                  />
                </template>
                <template #str_process_fee="{ row }" v-if="[DetailTypeEnum.EDIT, DetailTypeEnum.ADD].includes(viewType)">
                  <a-input-number class="w-full" :controls="false" v-model:value="row.str_process_fee" :min="0.0" :max="99999" step="0.00000001" :precision="8" stringMode @change="setPrice()" />
                </template>
                <template #tax_rate_id="{ row }" v-if="[DetailTypeEnum.EDIT, DetailTypeEnum.ADD].includes(viewType)">
                  <a-select class="w-full" v-model:value="row.tax_rate_id" :options="taxRateOption" @change="setPrice()" placeholder="请选择税率" :disabled="!form.is_contain_tax" />
                </template>
                <template #take_effect_time="{ row }" v-if="[DetailTypeEnum.EDIT, DetailTypeEnum.ADD].includes(viewType)">
                  <a-date-picker class="w-full" show-time v-model:value="row.take_effect_time" valueFormat="YYYY-MM-DD HH:mm:ss" :disabled-date="disabledDate" @change="checkTime(row, 'start')" />
                </template>
                <template #lose_efficacy_time="{ row }" v-if="[DetailTypeEnum.EDIT, DetailTypeEnum.ADD].includes(viewType)">
                  <a-date-picker
                    class="w-full"
                    show-time
                    v-model:value="row.lose_efficacy_time"
                    valueFormat="YYYY-MM-DD HH:mm:ss"
                    :disabled-date="(current) => disabledDate2(current, row.take_effect_time)"
                    @change="checkTime(row, 'end')"
                  />
                </template>
                <template #cost_num_from="{ row }">
                  <span>{{ (row.quantity_from * row.conversion_value || 1).roundNext(5) }}</span>
                </template>
                <template #cost_num_to="{ row }">
                  <span>{{ (row.quantity_to * row.conversion_value || 1).roundNext(5) }}</span>
                </template>
                <template #operate="{ row }" v-if="[DetailTypeEnum.EDIT, DetailTypeEnum.ADD].includes(viewType)">
                  <a-button class="mr-5px" @click="addSetting(row)">新增价格</a-button>
                  <a-button @click="delSetting(row)">删除</a-button>
                </template>
              </price-table>
            </div>
          </div>
          <div class="flex flex-col overflow-hidden flex-1" v-else>
            <div class="flex-1 overflow-hidden">
              <price-table
                ref="priceTableRef"
                :table-key="tableColumnsCache"
                :data="selectProductList"
                :isCheckbox="[DetailTypeEnum.VIEW, DetailTypeEnum.AUDIT].includes(viewType)"
                v-bind="productTableProps"
              >
                <template #category="{ row }">
                  {{ productTypeMap[row.category] }}
                </template>
                <template #image_url="{ row }">
                  <EasyImage :src="row.image_url"></EasyImage>
                </template>
                <template #process_type="{ row }">
                  <span>{{ machiningTypeList.find((v) => v.value == row.process_type)?.label }}</span>
                </template>
                <template #tax_rate_id="{ row }">
                  <span>{{ taxRateOption.find((v) => v.value == row.tax_rate_id)?.label }}</span>
                </template>

                <template #cost_num_from="{ row }">
                  <span>{{ (row.quantity_from * row.conversion_value || 0).roundNext(5) }}</span>
                </template>
                <template #cost_num_to="{ row }">
                  <span>{{ (row.quantity_to * row.conversion_value || 0).roundNext(5) }}</span>
                </template>
              </price-table>
            </div>
            <div class="flex justify-end mt-10">
              <a-pagination
                v-model:current="productPage"
                v-model:page-size="productPageSize"
                show-quick-jumper
                :total="productTotal"
                show-size-changer
                :page-size-options="['20', '50', '100', '250', '500']"
                @change="getProductListByPage"
                size="small"
                :showTotal="(total) => `共 ${total} 条`"
              >
                <template #buildOptionText="props">
                  <span>{{ props.value }}条/页</span>
                </template>
              </a-pagination>
            </div>
          </div>
        </template>
        <!-- 材质价目 -->
        <template v-if="active === '2'">
          <a-flex justify="space-between" class="mb-8" v-if="[DetailTypeEnum.EDIT, DetailTypeEnum.ADD].includes(viewType)">
            <a-space>
              <a-button @click="handleMaterialTemplate">下载导入模板</a-button>
              <a-upload :showUploadList="false" :before-upload="handleMaterialUpload">
                <a-button>导入</a-button>
              </a-upload>
            </a-space>
            <a-button type="primary" @click="handleShowAddMaterial">新增</a-button>
          </a-flex>
          <a-flex justify="space-between" class="mb-8" v-if="[DetailTypeEnum.VIEW, DetailTypeEnum.AUDIT].includes(viewType)">
            <a-space :size="16" v-if="[DetailTypeEnum.VIEW, DetailTypeEnum.AUDIT].includes(viewType)">
              <a-input placeholder="材质名称" v-model:value="materialForm.material_name" :maxlength="200" />
              <a-button type="primary" @click="getMaterialList">查询</a-button>
            </a-space>
            <a-button @click="handleMaterialExport" class="ml-auto">导出</a-button>
          </a-flex>
          <div class="flex flex-1 overflow-hidden" v-if="[DetailTypeEnum.EDIT, DetailTypeEnum.ADD].includes(viewType)">
            <div class="w200px mr-5px flex flex-col">
              <div class="bg-#EAECEE">
                <a-input-search v-model:value="materialName" placeholder="根据材质名称搜索" @input="filterMaterialTag(materialName)" />
              </div>
              <div class="flex-1 overflow-auto">
                <div
                  v-for="(item, index) in materialTagList"
                  :key="index"
                  class="h23px bg-#f2f2f2 mt-10px text-#3D3D3D pl-10px pr-10px overflow-hidden cursor-pointer"
                  :class="{ isClick: select_material_name == item.material_name, isRed: item.isRed }"
                  style="text-overflow: ellipsis; white-space: nowrap; border: 1px solid #c6e2ff"
                  @click="changeMaterialId(item.material_name)"
                >
                  <div class="h23px">{{ item.material_name }}</div>
                </div>
              </div>
            </div>
            <div class="h-full overflow-auto w1200px">
              <price-table ref="materialTableRef" :table-key="materialColumns" :data="filterMaterialList" :isCheckbox="[DetailTypeEnum.VIEW, DetailTypeEnum.AUDIT].includes(viewType)" :type="2">
                <template #process_type="{ row }" v-if="[DetailTypeEnum.EDIT, DetailTypeEnum.ADD].includes(viewType)">
                  <a-select v-model:value="row.process_type" :options="machiningTypeList" @change="checkLeftMaterialList()" class="w-full" placeholder="请选择加工方式" />
                </template>
                <template #quantity_from="{ row }" v-if="[DetailTypeEnum.EDIT, DetailTypeEnum.ADD].includes(viewType)">
                  <a-input-number
                    class="w-full"
                    :controls="false"
                    v-model:value="row.quantity_from"
                    :min="0"
                    :max="99999999"
                    :parser="(value) => value.replace('.', '')"
                    :class="{ isRed: row.isFromRed }"
                    @change="checkLeftMaterialList()"
                  />
                </template>
                <template #quantity_to="{ row }" v-if="[DetailTypeEnum.EDIT, DetailTypeEnum.ADD].includes(viewType)">
                  <a-input-number
                    class="w-full"
                    :controls="false"
                    v-model:value="row.quantity_to"
                    :min="0"
                    :max="99999999"
                    :parser="(value) => value.replace('.', '')"
                    :class="{ isRed: row.isToRed }"
                    @change="checkLeftMaterialList()"
                  />
                </template>
                <template #str_material_price="{ row }" v-if="[DetailTypeEnum.EDIT, DetailTypeEnum.ADD].includes(viewType)">
                  <a-input-number
                    class="w-full"
                    :controls="false"
                    v-model:value="row.str_material_price"
                    :min="0.0"
                    :max="99999"
                    step="0.00000000"
                    :precision="8"
                    stringMode
                    :class="{ isRed: row.isPriceRed }"
                    @change="(checkLeftMaterialList(), setPrice())"
                  />
                </template>
                <template #str_process_fee="{ row }" v-if="[DetailTypeEnum.EDIT, DetailTypeEnum.ADD].includes(viewType)">
                  <a-input-number :controls="false" v-model:value="row.str_process_fee" :min="0.0" :max="99999" step="0.00000001" :precision="8" stringMode @change="setPrice()" class="w-full" />
                </template>
                <template #tax_rate_id="{ row }" v-if="[DetailTypeEnum.EDIT, DetailTypeEnum.ADD].includes(viewType)">
                  <a-select v-model:value="row.tax_rate_id" :options="taxRateOption" @change="setPrice()" class="w-full" placeholder="请选择税率" :disabled="!form.is_contain_tax" />
                </template>
                <template #take_effect_time="{ row }" v-if="[DetailTypeEnum.EDIT, DetailTypeEnum.ADD].includes(viewType)">
                  <a-date-picker show-time v-model:value="row.take_effect_time" valueFormat="YYYY-MM-DD HH:mm:ss" :disabled-date="disabledDate" @change="checkTime(row, 'start')" class="w-full" />
                </template>
                <template #lose_efficacy_time="{ row }" v-if="[DetailTypeEnum.EDIT, DetailTypeEnum.ADD].includes(viewType)">
                  <a-date-picker
                    show-time
                    v-model:value="row.lose_efficacy_time"
                    valueFormat="YYYY-MM-DD HH:mm:ss"
                    :disabled-date="(current) => disabledDate2(current, row.take_effect_time)"
                    @change="checkTime(row, 'end')"
                    class="w-full"
                  />
                </template>
                <template #operate="{ row }" v-if="[DetailTypeEnum.EDIT, DetailTypeEnum.ADD].includes(viewType)">
                  <a-button class="mr-5px" @click="addMaterialSetting(row)">新增价格</a-button>
                  <a-button @click="delMaterialSetting(row)">删除</a-button>
                </template>
              </price-table>
            </div>
          </div>
          <div class="flex flex-col overflow-hidden flex-1" v-else>
            <div class="flex-1 overflow-hidden">
              <price-table
                ref="materialTableRef"
                :table-key="materialColumnsCache"
                :data="selectMaterialList"
                :isCheckbox="[DetailTypeEnum.VIEW, DetailTypeEnum.AUDIT].includes(viewType)"
                v-bind="materialTableProps"
              >
                <template #process_type="{ row }">
                  <span>{{ machiningTypeList.find((v) => v.value == row.process_type)?.label }}</span>
                </template>
                <template #tax_rate_id="{ row }">
                  <span>{{ taxRateOption.find((v) => v.value == row.tax_rate_id)?.label }}</span>
                </template>
              </price-table>
            </div>

            <div class="flex justify-end mt-10">
              <a-pagination
                v-model:current="materialPage"
                v-model:page-size="materialPageSize"
                show-quick-jumper
                :total="materialTotal"
                show-size-changer
                :page-size-options="['20', '50', '100', '250', '500']"
                @change="getMaterialListByPage"
                size="small"
                :showTotal="(total) => `共 ${total} 条`"
              >
                <template #buildOptionText="props">
                  <span>{{ props.value }}条/页</span>
                </template>
              </a-pagination>
            </div>
          </div>
        </template>
      </div>
      <div class="flex-1 p-16 bg-#f2f2f2 overflow-auto" v-if="showAuditRecord">
        <a-timeline class="ml-16" v-if="auditRecordList.length > 0">
          <a-timeline-item v-for="item in auditRecordList" :key="item.id">
            <div class="timeline-title">
              {{ item.audit_time }}
            </div>
            <div class="timeline-content">
              {{ item.message }}
            </div>
          </a-timeline-item>
        </a-timeline>
        <a-empty v-else class="c-#333" description="暂无审核记录" />
      </div>
    </div>
    <template #footer>
      <a-space :size="16" v-if="[DetailTypeEnum.ADD, DetailTypeEnum.EDIT].includes(viewType)">
        <a-button type="primary" @click="handleSubmitAudit(true)">提交审核</a-button>
        <a-button @click="handleSubmitAudit(false)">保存暂不提交</a-button>
        <a-button @click="handleClose">取消</a-button>
      </a-space>
      <a-space :size="16" v-if="viewType === DetailTypeEnum.AUDIT">
        <a-button type="primary" @click="handleAudit(true)">通过</a-button>
        <a-button @click="handleAudit(false)">拒绝</a-button>
        <a-button @click="handleClose">关闭</a-button>
      </a-space>
      <a-space v-if="viewType === DetailTypeEnum.VIEW">
        <a-button v-if="form.audit_status === 90 && btnPermission[81004]" type="primary" @click="handleApplyAdjustPrice">申请调价</a-button>
        <a-button @click="handleClose">关闭</a-button>
      </a-space>
      <a-button v-if="[DetailTypeEnum.VIEW].includes(viewType) && $route.path === '/querySupplierCompany' && !form.company_supplier_id" class="ml-10" @click="changePurchasePrice">更换价目表</a-button>
    </template>
    <product-drawer ref="productDrawerRef" @select-product="handleSelectProduct"></product-drawer>
    <material-drawer ref="materialDrawerRef" @select-material="handleSelectMaterial" />
    <audit-modal ref="auditModalRef" @audit="handleFinishAudit" />
    <adjust-price-drawer ref="adjustPriceDrawerRef" />
  </a-drawer>
</template>

<script setup lang="ts">
import { message, Modal, Select } from 'ant-design-vue'
import { RecycleScroller } from 'vue-virtual-scroller'
import 'vue-virtual-scroller/dist/vue-virtual-scroller.css'

import { cloneDeep, download } from '@/utils/index'
import { DetailTypeEnum } from '@/common/enum'
import { PurchasePriceTypeMap, productTypeMap } from '@/common/map'
import eventBus from '@/utils/eventBus'

import AuditModal from '@/views/pageComponents/purchaseManagement/components/AuditModal.vue'
import ProductDrawer from '@/views/pageComponents/purchaseManagement/components/ProductDrawer.vue'
import AdjustPriceDrawer from '@/views/pageComponents/purchaseManagement/purchaseAdjustPrice/components/AdjustPriceDrawer.vue'
import MaterialDrawer from '@/views/pageComponents/purchaseManagement/components/MaterialDrawer.vue'

import PriceTable from './PriceTable.vue'

import { GetPageMRPSupplierCompanySelect, GetPLMMachiningType, GetTaxRateSelect, GetAuditRecord } from '@/servers/BusinessCommon'
import { ChangePurchasePrice } from '@/servers/Supplier'
import {
  AddPurchasePrice,
  UpdatePurchasePrice,
  GetPurchasePrice,
  GetPurchasePriceDetail,
  GetPurchaseMaterialDetail,
  DownloadImportTemplate,
  Audit,
  ImportK3SkuInfo,
  ImportMaterialInfo,
  ExportPurchasePriceDetail,
  ExportPurchasePriceMaterialDetail,
  GetNotCorrelationPurchasePriceList,
} from '@/servers/Purchaseprice'

const { btnPermission } = usePermission()

const emits = defineEmits(['query'])
const active = ref('1')
const viewType = ref<DetailTypeEnum>(DetailTypeEnum.ADD)
const priceTableRef = ref()
const materialTableRef = ref()
const auditModalRef = ref()
const productDrawerRef = ref()
const materialDrawerRef = ref()
const adjustPriceDrawerRef = ref()
const formRef = ref()
// 表单验证
const rules = ref<any>({
  name: [
    { required: true, message: '请输入价目表名称', trigger: 'blur' },
    { max: 100, message: '价目表名称最多100个字符', trigger: 'blur' },
  ],
  company_supplier_id: [{ required: true, message: '请选择供应商', trigger: 'blur' }],
  rater_name: [{ required: true, message: '请选择定价员', trigger: 'blur' }],
  price_type: [{ required: true, message: '请选择价目类型', trigger: 'blur' }],
  currency_type: [{ required: true, message: '请选择币别', trigger: 'blur' }],
  is_default: [{ required: true, message: '请选择是否默认', trigger: 'blur' }],
  is_contain_tax: [{ required: true, message: '请选择是否含税', trigger: 'blur' }],
})

// 表单
const form = ref<any>({
  is_default: false,
  is_contain_tax: false,
  currency_type: 1,
})

const queryForm = ref({
  sku_id: '',
  sku_name: '',
  category: undefined,
})

const materialForm = ref({
  material_name: '',
})

const categoryOptions = ref<any[]>([
  { label: '成品', value: 1 },
  { label: '半成品', value: 2 },
  { label: '原材料', value: 3 },
  { label: '包材', value: 4 },
])
const tableColumns = ref([
  { title: '加工方式', field: 'process_type', width: 0 },
  { title: '采购数量（从）', field: 'quantity_from', width: 0 },
  { title: '采购数量（至）', field: 'quantity_to', width: 0 },
  { title: '换算值', field: 'conversion_value' },
  {
    title: '成本数量（从）',
    field: 'cost_num_from',
    width: 0,
  },
  {
    title: '成本数量（至）',
    field: 'cost_num_to',
    width: 0,
  },
  { title: '材料单价', field: 'str_material_price', width: 0 },
  { title: '加工费', field: 'str_process_fee', width: 0 },
  { title: '合计单价', field: 'str_price', width: 0 },
  { title: '税率', field: 'tax_rate_id', width: 0 },
  { title: '合计含税单价', field: 'str_total_rate_price', width: 0 },
  { title: '生效时间', field: 'take_effect_time', width: 0 },
  { title: '失效时间', field: 'lose_efficacy_time', width: 0 },
  { title: '操作', field: 'operate', width: 160 },
])

const tableColumnsCache = ref([
  { title: '商品主图', field: 'image_url', width: 0 },
  { title: '商品编码', field: 'sku_id', width: 0 },
  { title: '聚水潭编码', field: 'jst_sku_id', width: 0 },
  { title: '商品名称', field: 'sku_name', width: 0 },
  { title: '规格型号', field: 'type_specification', width: 0 },
  { title: '采购单位', field: 'valuation_unit', width: 0 },
  { title: '商品分类', field: 'category', width: 0 },
  { title: '加工方式', field: 'process_type', width: 0 },
  { title: '采购数量（从）', field: 'quantity_from', width: 0 },
  { title: '采购数量（至）', field: 'quantity_to', width: 0 },
  { title: '换算值', field: 'conversion_value' },
  {
    title: '成本数量（从）',
    field: 'cost_num_from',
    width: 0,
  },
  {
    title: '成本数量（至）',
    field: 'cost_num_to',
    width: 0,
  },
  { title: '材料单价', field: 'str_material_price', width: 0 },
  { title: '加工费', field: 'str_process_fee', width: 0 },
  { title: '合计单价', field: 'str_price', width: 0 },
  { title: '税率', field: 'tax_rate_id', width: 0 },
  { title: '合计含税单价', field: 'str_total_rate_price', width: 0 },
  { title: '生效时间', field: 'take_effect_time', width: 0 },
  { title: '失效时间', field: 'lose_efficacy_time', width: 0 },
])

const materialColumns = ref([
  { title: '加工方式', field: 'process_type', width: 0 },
  { title: '数量（从）', field: 'quantity_from', width: 0 },
  { title: '数量（至）', field: 'quantity_to', width: 0 },
  { title: '材料单价', field: 'str_material_price', width: 0 },
  { title: '加工费', field: 'str_process_fee', width: 0 },
  { title: '合计单价', field: 'str_price', width: 0 },
  { title: '税率', field: 'tax_rate_id', width: 0 },
  { title: '合计含税单价', field: 'str_total_rate_price', width: 0 },
  { title: '生效时间', field: 'take_effect_time', width: 0 },
  { title: '失效时间', field: 'lose_efficacy_time', width: 0 },
  { title: '操作', field: 'operate', width: 160 },
])

const materialColumnsCache = ref([
  { title: '材质', field: 'material_name', width: 0 },
  { title: '加工方式', field: 'process_type', width: 0 },
  { title: '数量（从）', field: 'quantity_from', width: 0 },
  { title: '数量（至）', field: 'quantity_to', width: 0 },
  { title: '材料单价', field: 'str_material_price', width: 0 },
  { title: '加工费', field: 'str_process_fee', width: 0 },
  { title: '合计单价', field: 'str_price', width: 0 },
  { title: '税率', field: 'tax_rate_id', width: 0 },
  { title: '合计含税单价', field: 'str_total_rate_price', width: 0 },
  { title: '生效时间', field: 'take_effect_time', width: 0 },
  { title: '失效时间', field: 'lose_efficacy_time', width: 0 },
])

const userData = JSON.parse(localStorage.getItem('userData') || '')

const userInfoOption = ref<any[]>([])
// 显示审核记录
const showAuditRecord = ref(false)
// 显示
const visible = ref(false)
// 选中商品
const selectProductList = ref<any[]>([])
const productTotal = ref(0)
const productPage = ref(1)
const productPageSize = ref(20)
const productTableProps = ref({
  type: 1,
  minHeight: 230,
  height: '100%',
  // maxHeight: 400,
  virtualYConfig: {
    enabled: true,
  },
})
const getProductListByPage = async (page) => {
  const { data } = await GetPurchasePriceDetail({ id: form.value.id, ...queryForm.value, page, pageSize: productPageSize.value })
  if (data.list) {
    productTotal.value = data.total
    productPage.value = page
    selectProductList.value = data.list.map((v, index) => ({
      ...v,
      iid: index + v.sku_id,
    }))
  }
  return Promise.resolve(cloneDeep(selectProductList.value))
}

// 材质
const selectMaterialList = ref<any[]>([])
const materialTotal = ref(0)
const materialPage = ref(1)
const materialPageSize = ref(20)
const materialTableProps = ref({
  type: 2,
  minHeight: 230,
  height: '100%',
  virtualYConfig: {
    enabled: true,
  },
})
const getMaterialListByPage = async (page) => {
  const { data } = await GetPurchaseMaterialDetail({ id: form.value.id, page, pageSize: materialPageSize.value, ...materialForm.value })
  if (data.list) {
    materialPage.value = page
    materialTotal.value = data.total
    selectMaterialList.value = data.list.map((v, index) => ({
      ...v,
      iid: index + v.material_id,
    }))
  }
  return Promise.resolve(cloneDeep(selectMaterialList.value))
}

const leftTagListCache = ref<any[]>([])

const materialTagListCache = ref<any[]>([])

const leftTagList = ref<any[]>([])

const materialTagList = ref<any[]>([])

const select_sku_id = ref()

const select_material_name = ref()

const filterProductList = computed(() => selectProductList.value.filter((v) => v.sku_id == select_sku_id.value))

const filterMaterialList = computed(() => selectMaterialList.value.filter((v) => v.material_name == select_material_name.value))

const auditRecordList = ref<any[]>([])

const productName = ref('')

const materialName = ref('')

const resArr = ref<any[]>([])

const selectProductCache = ref<any[]>([])

const selectMaterialCache = ref<any[]>([])

const machiningTypeList = ref<any>([])

const taxRateOption = ref<any>([])

const disabledDate = (current) => {
  if (!current) return false
  const currentDate = new Date(current)
  const now = new Date()
  now.setHours(0, 0, 0, 1)
  return currentDate.getTime() < now.getTime()
}

const disabledDate2 = (current, A = null) => {
  if (!current) return false
  // 如果 A 存在，则解析为 Date，否则使用当前时间
  const referenceDate = A ? new Date(A) : new Date()
  referenceDate.setHours(0, 0, 0, 1) // 重置为当天的开始时间

  const currentDate = new Date(current)
  return currentDate.getTime() < referenceDate.getTime()
}

const checkTime = (row, type) => {
  const time1 = row.take_effect_time
  const time2 = row.lose_efficacy_time
  if (time1 && time2) {
    if (time1 > time2) {
      message.error('生效时间不可在失效时间之后')
      if (type == 'start') {
        row.take_effect_time = null
      } else {
        row.lose_efficacy_time = null
      }
    }
  }
}

// 去重
const uniqueData = (data, key) => {
  const uniqueArr = data.filter((item, index) => data.findIndex((i) => i[key] === item[key]) === index)
  return uniqueArr
}

// 打开
const supplierId = ref()
const open = async (type: DetailTypeEnum, id?: number, company_supplier_id?: number) => {
  visible.value = true
  viewType.value = type
  active.value = '1'
  formRef.value?.resetFields()
  supplierId.value = company_supplier_id
  if (typeof id === 'number') {
    await getPriceDetail(id)
  } else {
    userInfoOption.value = [{ label: userData.display_name, value: userData.display_name }]
  }
  if (viewType.value === DetailTypeEnum.ADD) form.value.rater_name = userData.display_name
  getMachiningType()
  getTaxRateList()
  // 显示审核记录
  if ([DetailTypeEnum.VIEW, DetailTypeEnum.AUDIT].includes(viewType.value)) {
    showAuditRecord.value = localStorage.getItem('showAuditRecord') === '1'
    if (showAuditRecord.value) {
      getAuditRecord()
    }
  } else {
    showAuditRecord.value = false
  }
}

// 关闭
const handleClose = () => {
  showAuditRecord.value = false
  active.value = '1'
  visible.value = false
  formRef.value?.resetFields()
  priceTableRef.value?.clearCheckbox()
  materialTableRef.value?.clearCheckbox()
  selectProductList.value = []
  selectMaterialList.value = []
  selectProductCache.value = []
  selectMaterialCache.value = []
  leftTagList.value = []
  materialTagList.value = []
  queryForm.value = {
    sku_id: '',
    sku_name: '',
    category: undefined,
  }
  materialForm.value = {
    material_name: '',
  }
  form.value = {
    is_default: false,
    is_contain_tax: false,
    currency_type: 1,
  }
}

const getPriceDetail = async (id: number) => {
  const infoRes = await GetPurchasePrice(id)
  form.value = {
    ...infoRes.data,
  }
  if (infoRes.data?.rater_name) {
    userInfoOption.value = [{ label: infoRes.data.rater_name, value: infoRes.data.rater_name }]
  }
  // await getPurchasePriceList()
  selectProductCache.value = await getProductListByPage(1)
  selectMaterialCache.value = await getMaterialListByPage(1)
  //   // 前端过滤时使用
  form.value.is_contain_tax = [...selectProductList.value, ...selectMaterialList.value].some((v) => v.tax_rate_id > 0)
}
// 获取价目表明细
// const getPurchasePriceList = async () => {
//   for (const key in queryForm.value) {
//     if (!queryForm.value[key]) {
//       queryForm.value[key] = null
//     }
//   }
//   const listRes = await GetPurchasePriceDetail({ id: form.value.id, ...queryForm.value, page: 1, pageSize: 9999 })
//   selectProductList.value = listRes.data?.list || []
//   selectProductList.value.forEach((v, index) => {
//     v.iid = index + v.sku_id
//   })
//   const listRes2 = await GetPurchaseMaterialDetail({ id: form.value.id, ...queryForm.value, page: 1, pageSize: 9999 })
//   selectMaterialList.value = listRes2.data?.list || []
//   selectMaterialList.value.forEach((v, index) => {
//     v.iid = index + v.material_id
//   })
//   // 前端过滤时使用
//   selectProductCache.value = JSON.parse(JSON.stringify(selectProductList.value))
//   selectMaterialCache.value = JSON.parse(JSON.stringify(selectMaterialList.value))
//   form.value.is_contain_tax = [...selectProductList.value, ...selectMaterialList.value].some((v) => v.tax_rate_id > 0)
// }
const getProductList = async () => {
  if ([DetailTypeEnum.VIEW, DetailTypeEnum.AUDIT].includes(viewType.value)) {
    getProductListByPage(1)
    return
  }
  let arr = selectProductCache.value
  if (queryForm.value.sku_name) {
    arr = selectProductCache.value.filter((v) => v.sku_name.indexOf(queryForm.value.sku_name) > -1)
  }
  if (queryForm.value.category) {
    arr = arr.filter((v) => v.category == queryForm.value.category)
  }
  if (queryForm.value.sku_id) {
    arr = arr.filter((v) => v.sku_id.indexOf(queryForm.value.sku_id) > -1 || v.jst_sku_id.indexOf(queryForm.value.sku_id) > -1)
  }
  selectProductList.value = arr
}

const getMaterialList = async () => {
  if ([DetailTypeEnum.VIEW, DetailTypeEnum.AUDIT].includes(viewType.value)) {
    getMaterialListByPage(1)
    return
  }
  selectMaterialList.value = selectMaterialCache.value.filter((v) => v.material_name.indexOf(materialForm.value.material_name) > -1)
}

// 下载导入模板
const handleDownloadTemplate = () => {
  DownloadImportTemplate(false).then((res) => {
    download(res, '价目表单品导入模板.xlsx')
  })
}

const handleMaterialTemplate = () => {
  DownloadImportTemplate(true).then((res) => {
    download(res, '价目表材质导入模板.xlsx')
  })
}
// 导入金蝶商品明细
const exportFlag = ref(false)
const handleBeforeUpload = (file: any) => {
  const formdata = new FormData()
  formdata.append('param', file)
  exportFlag.value = true
  ImportK3SkuInfo(formdata)
    .then((res) => {
      const list = res.data.datas
      const errorNum = res.data.errors.length
      const downLoadId = res.data.error_download_id
      selectProductList.value = [...selectProductList.value, ...list]
      selectProductList.value = selectProductList.value.map((v, index) => {
        // v.data_status = [DetailTypeEnum.EDIT].includes(viewType.value) ? 3 : v.data_status
        v.iid = index + v.sku_id
        return v
      })
      Modal.info({
        title: '提示',
        content: h('div', {}, [h('p', `共导入${list.length + errorNum}条数据，已成功导入${list.length}条数据，${errorNum ? `导入失败${errorNum}条数据` : ''}`)]),
        okText: downLoadId ? '下载失败原因' : '确认',
        onOk: () => {
          return new Promise((resolve) => {
            if (downLoadId) {
              eventBus.emit('downLoadId', {
                id: downLoadId as number,
                resolve,
                download: true,
              })
            } else {
              resolve(true)
            }
          })
        },
      })
    })
    .finally(() => {
      exportFlag.value = false
    })

  return false
}

const handleMaterialUpload = (file: any) => {
  const formdata = new FormData()
  formdata.append('file', file)
  ImportMaterialInfo(formdata).then((res) => {
    const list = res.data.datas
    const errorNum = res.data.errors.length
    const downLoadId = res.data.error_download_id
    selectMaterialList.value = [...selectMaterialList.value, ...list]
    selectMaterialList.value = selectMaterialList.value.map((v, index) => {
      // v.data_status = [DetailTypeEnum.EDIT].includes(viewType.value) ? 3 : v.data_status
      v.iid = index + v.material_id
      return v
    })
    Modal.info({
      title: '提示',
      content: h('div', {}, [h('p', `共导入${list.length + errorNum}条数据，已成功导入${list.length}条数据，${errorNum ? `导入失败${errorNum}条数据` : ''}`)]) as any,
      okText: downLoadId ? '下载失败原因' : '确认',
      onOk: () => {
        return new Promise((resolve) => {
          if (downLoadId) {
            eventBus.emit('downLoadId', {
              id: downLoadId as number,
              resolve,
              download: true,
            })
          } else {
            resolve(true)
          }
        })
      },
    })
  })

  return false
}

// 导出
const handleExport = async () => {
  const selectList = priceTableRef.value?.checkItemsArr
  const res = await ExportPurchasePriceDetail({ id: form.value.id, detail_ids: selectList.map((i) => i.id) })
  download(res, '价目表单品导出.xlsx')
}

const handleMaterialExport = async () => {
  const selectList = materialTableRef.value?.checkItemsArr
  const res = await ExportPurchasePriceMaterialDetail({ id: form.value.id, detail_ids: selectList.map((i) => i.id) })
  download(res, '价目表材质导出.xlsx')
}

// 申请调价
const handleApplyAdjustPrice = () => {
  adjustPriceDrawerRef.value?.applyOpen(
    form.value.id,
    selectProductList.value.map((i) => {
      const { id, ...args } = i
      args.price_detail_id = id
      args.data_status = 3
      args.adjust_tax_rate_id = args.tax_rate_id
      args.str_adjust_process_fee = '0'
      return args
    }),
    selectMaterialList.value.map((i) => {
      const { id, ...args } = i
      args.price_detail_id = id
      args.data_status = 3
      args.adjust_tax_rate_id = args.tax_rate_id
      args.str_adjust_process_fee = '0'
      return args
    }),
  )
  handleClose()
}

// 打开新增商品
const handleShowAddProduct = () => {
  productDrawerRef.value?.open(selectProductList.value.map((i) => i.sku_id))
}

const handleShowAddMaterial = () => {
  materialDrawerRef.value?.open(selectMaterialList.value.map((i) => i.material_id))
}

const handleSelectProduct = (list: any[]) => {
  list = list.map((item: any) => ({
    ...item,
    product_id: item.id,
    sku_id: item.sku_id,
    jst_sku_id: item.jst_sku_id,
    category: item.category,
    sku_name: item.sku_name,
    image_url: item.image_url,
    type_specification: item.type_specification,
    valuation_unit: item.valuation_unit,
    quantity_from: null,
    quantity_to: null,
    price: null,
    process_type: machiningTypeList.value[0].value,
    str_material_price: null,
    str_process_fee: '0',
    str_price: null,
    tax_rate_id: taxRateOption.value[0].value,
    str_total_rate_price: null,
    take_effect_time: null,
    lose_efficacy_time: null,
    isFromRed: false,
    isToRed: false,
    isPriceRed: false,
    conversion_value: item.conversion_value || 1,
    id: 0,
  }))
  selectProductList.value = [...selectProductList.value, ...list]
  selectProductList.value.forEach((v, index) => {
    v.iid = index + v.sku_id
  })
}

const handleSelectMaterial = (list: any[]) => {
  list = list.map((item: any) => ({
    ...item,
    material_id: Number(item.value),
    material_name: item.label,
    quantity_from: null,
    quantity_to: null,
    process_type: machiningTypeList.value[0].value,
    str_material_price: null,
    str_process_fee: '0',
    str_price: null,
    tax_rate_id: taxRateOption.value[0].value,
    str_total_rate_price: null,
    isFromRed: false,
    isToRed: false,
    isPriceRed: false,
    id: 0,
  }))
  selectMaterialList.value = [...selectMaterialList.value, ...list]
  selectMaterialList.value.forEach((v, index) => {
    v.iid = index + v.material_id
  })
}

watch(
  () => selectProductList.value.length,
  () => {
    productName.value = ''
    leftTagListCache.value = uniqueData(selectProductList.value, 'sku_id')
    checkLeftList()
  },
  { deep: true },
)

watch(
  () => leftTagListCache.value.length,
  () => {
    if (leftTagListCache.value?.length > 0) {
      filterTag(productName.value)
    } else {
      leftTagList.value = []
    }
  },
)

watch(
  () => selectMaterialList.value.length,
  () => {
    materialName.value = ''
    materialTagListCache.value = uniqueData(selectMaterialList.value, 'material_id')
    checkLeftMaterialList()
  },
  { deep: true },
)

watch(
  () => materialTagListCache.value.length,
  () => {
    if (materialTagListCache.value?.length > 0) {
      filterMaterialTag(materialName.value)
    } else {
      materialTagList.value = []
    }
  },
)

const filterTag = (value) => {
  leftTagList.value = leftTagListCache.value.filter((item) => item.sku_id.indexOf(value) >= 0)
  if (leftTagList.value.length > 0) {
    select_sku_id.value = leftTagList.value[0].sku_id
  }
}

const filterMaterialTag = (value) => {
  materialTagList.value = materialTagListCache.value.filter((item) => item.material_name.indexOf(value) >= 0)
  if (materialTagList.value.length > 0) {
    select_material_name.value = materialTagList.value[0].material_name
  }
}

// 提交审核
const handleSubmitAudit = async (is_pass: boolean) => {
  try {
    message.destroy()
    await formRef.value?.validate()
    if (selectProductList.value.length == 0 && selectMaterialList.value.length == 0) {
      message.info('请至少选择一个单品或者材质')
      return
    }
    resArr.value = []
    const isPassProduct = checkProductList()
    const isPassMaterial = checkMaterialList()
    const errorLen = resArr.value.filter((v) => !v?.isValid).length
    if (resArr.value?.length > 0 && errorLen > 0) {
      resArr.value.forEach((item) => {
        if (!item?.isValid) {
          message.error(item.message, 10)
        }
      })
      return
    }
    if (isPassProduct || isPassMaterial) {
      const details = cloneDeep(selectProductList.value)
      if (viewType.value === DetailTypeEnum.EDIT) {
        details.forEach((item) => {
          item.price_list_id = form.value.id
        })
      }
      const material_details = cloneDeep(selectMaterialList.value)
      if (viewType.value === DetailTypeEnum.EDIT) {
        material_details.forEach((item) => {
          item.price_list_id = form.value.id
        })
      }
      // 结构form.value
      const { ...rest } = form.value
      const submitForm = {
        ...rest,
        details,
        material_details,
        is_pass,
        // company_supplier_id: rest?.company_supplier_id ? JSON.parse(rest?.company_supplier_id)?.value : null,
      }
      const fn = viewType.value === DetailTypeEnum.ADD ? AddPurchasePrice : UpdatePurchasePrice
      await fn(submitForm)
      message.success('提交成功')
      handleClose()
      emits('query')
    }
  } catch (error) {
    console.log(error)
  }
}

const checkProductList = () => {
  leftTagListCache.value.forEach((item) => {
    const arr = selectProductList.value.filter((v) => v.sku_id == item.sku_id)
    const res = validateIntervals(arr, '商品', 'sku_name')
    resArr.value.push(res)
  })
  const failArr = resArr.value.filter((item) => !item.isValid)
  return failArr.length == 0
}

const checkMaterialList = () => {
  materialTagListCache.value.forEach((item) => {
    const arr = selectMaterialList.value.filter((v) => v.material_id == item.material_id)
    const res = validateIntervals(arr, '材质', 'material_name')
    resArr.value.push(res)
  })
  const failArr = resArr.value.filter((item) => !item.isValid)
  return failArr.length == 0
}

// 打开审核
const handleAudit = async (is_pass: boolean) => {
  if (form.value.audit_status === 20 && !btnPermission.value[81005]) {
    message.error('您没有该状态的审核权限')
    return
  }
  if (form.value.audit_status === 30 && !btnPermission.value[81006]) {
    message.error('您没有该状态的审核权限')
    return
  }
  auditModalRef.value?.open(is_pass)
}
// 审核操作
const handleFinishAudit = async (auditForm: any) => {
  await Audit({ ...auditForm, id: form.value.id })
  message.success('审核成功')
  auditModalRef.value?.close()
  handleClose()
  emits('query')
}

// 显示审核记录
const handleShowAuditRecord = () => {
  showAuditRecord.value = !showAuditRecord.value
  localStorage.setItem('showAuditRecord', showAuditRecord.value ? '1' : '0')
  if (showAuditRecord.value) {
    getAuditRecord()
  }
}
// 获取审核记录
const getAuditRecord = async () => {
  const res = await GetAuditRecord({ id: form.value.id, type: 1 })
  auditRecordList.value = res.data
}

const addSetting = (row) => {
  const idx = selectProductList.value.findIndex((v) => v.iid == row.iid)
  const obj = {
    ...selectProductList.value[idx],
    quantity_from: null,
    quantity_to: null,
    str_material_price: null,
    str_price: null,
    str_process_fee: '0',
    str_total_rate_price: null,
    take_effect_time: null,
    lose_efficacy_time: null,
    iid: Math.random(),
    isFromRed: false,
    isToRed: false,
    isPriceRed: false,
    id: 0,
  }
  selectProductList.value.splice(idx + 1, 0, obj)
  console.log(selectProductList.value, 'selectProductList')
}

const addMaterialSetting = (row) => {
  const idx = selectMaterialList.value.findIndex((v) => v.iid == row.iid)
  const obj = {
    ...selectMaterialList.value[idx],
    quantity_from: null,
    quantity_to: null,
    str_material_price: null,
    str_price: null,
    str_process_fee: '0',
    str_total_rate_price: null,
    take_effect_time: null,
    lose_efficacy_time: null,
    iid: Math.random(),
    isFromRed: false,
    isToRed: false,
    isPriceRed: false,
    id: 0,
  }
  selectMaterialList.value.splice(idx + 1, 0, obj)
  console.log(selectMaterialList.value, 'selectMaterialList')
}

const delSetting = (row) => {
  const idx = selectProductList.value.findIndex((v) => v.iid == row.iid)
  selectProductList.value.splice(idx, 1)
  console.log(selectProductList.value, 'selectProductList')
}

const delMaterialSetting = (row) => {
  const idx = selectMaterialList.value.findIndex((v) => v.iid == row.iid)
  selectMaterialList.value.splice(idx, 1)
  console.log(selectMaterialList.value, 'selectMaterialList')
}

const changeSkuId = (val) => {
  select_sku_id.value = val
}

const changeMaterialId = (val) => {
  select_material_name.value = val
}

const validateBasicFields = (interval, title, key, line) => {
  // 检查空值情况
  if (interval.quantity_from === null && interval.quantity_to === null) {
    interval.isFromRed = true
    interval.isToRed = true
    return { isValid: false, message: `${title}${interval[key]}第${line}行：采购数量（从）和采购数量（至）不能同时为空` }
  }
  if (interval.str_material_price === null) {
    interval.isPriceRed = true
    return { isValid: false, message: `${title}${interval[key]}第${line}行：材料单价不能为空` }
  }
  if (
    interval.quantity_from !== null &&
    interval.quantity_to !== null &&
    (typeof interval.quantity_from !== 'number' || typeof interval.quantity_to !== 'number' || interval.quantity_from > interval.quantity_to)
  ) {
    interval.isFromRed = true
    interval.isToRed = true
    return { isValid: false, message: `${title}${interval[key]}第${line}行：采购数量（从）必须小于采购数量（至）` }
  }
  return { isValid: true }
}

const getConflictDetail = (current, next, arr, title, key, line1, line2) => {
  const currentMin = current.quantity_from === null ? -Infinity : current.quantity_from
  const currentMax = current.quantity_to === null ? Infinity : current.quantity_to
  const nextMin = next.quantity_from === null ? -Infinity : next.quantity_from
  const nextMax = next.quantity_to === null ? Infinity : next.quantity_to

  if (currentMax === nextMin) {
    return `${title}${arr[0][key]}第${line1}行的采购数量（至） ${currentMax} 与 第${line2}行的采购数量（从） ${nextMin} 重合`
  }
  if (nextMax === currentMin) {
    return `${title}${arr[0][key]}第${line2}行的采购数量（至） ${nextMax} 与 第${line1}行的采购数量（从） ${currentMin} 重合`
  }
  if (currentMin <= nextMin && currentMax >= nextMax) {
    return `${title}${arr[0][key]}第${line1}行区间完全包含第${line2}行区间`
  }
  if (nextMin <= currentMin && nextMax >= currentMax) {
    return `${title}${arr[0][key]}第${line2}行区间完全包含第${line1}行区间`
  }
  return `${title}${arr[0][key]}第${line1}行区间与第${line2}行区间存在交叉重叠`
}

const validateIntervals = (intervals, title, key) => {
  // 基础字段校验
  for (let i = 0; i < intervals.length; i++) {
    const result = validateBasicFields(intervals[i], title, key, i + 1)
    if (!result.isValid) return result
  }

  // 按process_type分组处理
  const processTypes = [...new Set(intervals.map((item) => item.process_type))]

  for (const processType of processTypes) {
    const arr = intervals.filter((item) => item.process_type === processType)

    // 排序并保留原始行号
    const sorted = arr
      .map((item, index) => ({ ...item, originalIndex: index }))
      .sort((a, b) => {
        const aMin = a.quantity_from === null ? -Infinity : a.quantity_from
        const bMin = b.quantity_from === null ? -Infinity : b.quantity_from
        return aMin - bMin
      })

    // 重叠检查
    for (let i = 0; i < sorted.length - 1; i++) {
      const current = sorted[i]
      const currentMin = current.quantity_from === null ? -Infinity : current.quantity_from
      const currentMax = current.quantity_to === null ? Infinity : current.quantity_to

      for (let j = i + 1; j < sorted.length; j++) {
        const next = sorted[j]
        const nextMin = next.quantity_from === null ? -Infinity : next.quantity_from

        // 提前终止条件
        if (nextMin > currentMax) break

        const nextMax = next.quantity_to === null ? Infinity : next.quantity_to
        if (currentMax >= nextMin && nextMax >= currentMin) {
          const line1 = current.originalIndex + 1
          const line2 = next.originalIndex + 1

          arr[line1 - 1].isFromRed = true
          arr[line1 - 1].isToRed = true
          arr[line2 - 1].isFromRed = true
          arr[line2 - 1].isToRed = true

          return {
            isValid: false,
            message: `区间冲突：${getConflictDetail(current, next, arr, title, key, line1, line2)}`,
          }
        }
      }
    }
  }

  return { isValid: true, message: '区间校验通过' }
}

const checkLeftList = () => {
  selectProductList.value.map((i) => {
    i.isFromRed = false
    i.isToRed = false
    i.isPriceRed = false
    return i
  })
  leftTagListCache.value.forEach((item) => {
    item.isRed = false
    const arr = selectProductList.value.filter((v) => v.sku_id == item.sku_id)
    const res = validateIntervals(arr, '商品', 'sku_name')

    if (!res.isValid) {
      item.isRed = true
    }
  })
}

const setTaxRate = () => {
  if (!form.value.is_contain_tax) {
    const tax_tate_id = taxRateOption.value[0].value
    selectProductList.value.forEach((v) => {
      v.tax_rate_id = tax_tate_id
    })
    selectMaterialList.value.forEach((v) => {
      v.tax_rate_id = tax_tate_id
    })
  }
  setPrice()
}

const setPrice = () => {
  const calculatePrice = (item) => {
    const materialPrice = Number(item.str_material_price || 0)
    const processFee = Number(item.str_process_fee || 0)
    const price = materialPrice + processFee
    const taxRate = taxRateOption.value.find((i) => i.value == item.tax_rate_id)?.tax_rate || 0
    const taxAmount = (price * taxRate) / 100

    return {
      str_price: price.roundNext(8),
      str_total_rate_price: (price + taxAmount).roundNext(8),
    }
  }

  selectProductList.value.forEach((v) => Object.assign(v, calculatePrice(v)))
  selectMaterialList.value.forEach((v) => Object.assign(v, calculatePrice(v)))
}

const checkLeftMaterialList = () => {
  selectMaterialList.value.map((i) => {
    i.isFromRed = false
    i.isToRed = false
    i.isPriceRed = false
    return i
  })
  materialTagListCache.value.forEach((item) => {
    item.isRed = false
    const arr = selectMaterialList.value.filter((v) => v.material_id == item.material_id)
    const res = validateIntervals(arr, '材质', 'material_name')
    if (!res.isValid) {
      item.isRed = true
    }
  })
}

const changePurchasePrice = async () => {
  const res = await GetNotCorrelationPurchasePriceList()
  let value: any = ''
  Modal.confirm({
    title: '选择关联价目表',
    icon: null,
    content: h(Select, {
      placeholder: '请选择关联价目表',
      onChange: (key) => {
        value = key
      },
      options: res.data.map((f) => ({ label: f.value, value: f.key })),
      style: {
        width: '100%',
        margin: '10px 0',
      },
    }),
    onOk: () =>
      new Promise((resolve, reject) => {
        if (!value) {
          message.warn('请选择关联价目表')
          return reject('error')
        }
        const params = {
          company_supplier_id: supplierId.value,
          purchase_price_id: value,
        }
        ChangePurchasePrice(params)
          .then(() => {
            message.success('更换完成')
            open(viewType.value, value, supplierId.value)
            emits('query')
            resolve('ok')
          })
          .catch(reject)
      }),
  })
}
const getMachiningType = async () => {
  const res = await GetPLMMachiningType({})
  machiningTypeList.value = res.data.map((v) => ({
    label: v.label,
    value: Number(v.value),
  }))
}

const getTaxRateList = async () => {
  const res = await GetTaxRateSelect({})
  if (res.success) {
    taxRateOption.value = res.data
      .map((v) => {
        return {
          label: v.label,
          value: Number(v.value),
          tax_rate: v.tax_rate,
        }
      })
      .sort((a, b) => a.tax_rate - b.tax_rate)
  }
}

// onMounted(() => {
//   Modal.info({
//     title: '提示',
//     content: '23213123123',
//     okText: '确认',
//     onOk: () => {
//       return new Promise((resolve) => {
//         setTimeout(() => {
//           eventBus.emit('downLoadId', {
//             id: 6,
//             resolve,
//             download: true,
//           })
//         }, 4000)
//       })
//     },
//   })
// })
defineExpose({
  open,
})
</script>

<style scoped lang="scss">
.isClick {
  background-color: #c6e2ff;
}

.isRed {
  border-color: #ff4d4f !important;
  box-shadow: 0 0 0 2px rgb(255 77 79 / 20%) !important;
}
</style>
