<template>
  <a-modal v-model:open="visible" title="请选择" okText="确定" cancelText="取消" :width="px2(800)" @ok="ok">
    <a-input-search v-model:value="searchText" placeholder="请输入" @search="getList" allowClear />
    <a-radio-group v-model:value="value" name="radioGroup" class="w-full mt-4 max-h-[400px]">
      <a-row>
        <a-col :span="8" v-for="item in list" :key="item.supplier_id">
          <a-radio :value="item">
            {{ item.supplier_name }}
          </a-radio>
        </a-col>
      </a-row>
    </a-radio-group>
  </a-modal>
</template>

<script lang="ts" setup>
import { GetSupplierList } from '@/servers/UserManager'
import { px2 } from '@/utils'

const visible = ref(false)
const open = (val) => {
  visible.value = true
  searchText.value = ''
  defaultValue.value = val
  console.log(defaultValue.value)
  getList()
}

const searchText = ref()
const list = ref<{ supplier_id: string; supplier_name: string }[]>([])

const getList = () => {
  GetSupplierList({
    supplier_name: searchText.value,
    page: 1,
    pageSize: 9999,
  }).then((res) => {
    list.value = res.data.list
    value.value = res.data.list.find((v) => v.supplier_id === defaultValue.value?.supplier_id) || null
    console.log(value.value)
  })
}

const defaultValue = ref()
const value = ref()

const emit = defineEmits(['change'])
const ok = () => {
  visible.value = false
  emit('change', value.value)
}

defineExpose({
  open,
})
</script>

<style lang="scss" scoped>
:deep(.ant-radio-wrapper) {
  width: 100%;

  .ant-radio {
    flex: none;
    flex-shrink: 0;
  }

  span {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
</style>
