<template>
  <a-drawer
    v-model:open="formVisible"
    @afterOpenChange="formRef?.clearValidate()"
    width="30vw"
    :title="`${drawerStatus == 1 ? '新增' : '编辑'}线上采购账号`"
    placement="right"
    :maskClosable="false"
    :footer-style="{ textAlign: 'left' }"
  >
    <LoadingOutlined v-show="formLoading" class="loadingIcon" />
    <a-form v-if="!formLoading" ref="formRef" :model="formData">
      <a-form-item v-if="drawerStatus == 2" label="采购账号编号" name="number" :rules="[{ required: drawerStatus == 2 ? true : false }, { max: 50, message: '输入内容不可超过50字符' }]">
        <a-input v-model:value="formData.number" placeholder="请输入采购账号编号" class="w240" disabled />
      </a-form-item>
      <a-form-item label=" 账号名称" name="account_name" :rules="[{ required: true }, { max: 50, message: '输入内容不可超过50字符' }]">
        <a-input v-model:value="formData.account_name" placeholder="请输入账号名称" class="w240" :disabled="drawerStatus == 2" :maxlength="50" />
      </a-form-item>
      <a-form-item label="账号简称" name="account_abbreviation" :rules="[{ max: 50, message: '输入内容不可超过50字符' }]">
        <a-input v-model:value="formData.account_abbreviation" placeholder="请输入账号简称" class="w240" :maxlength="50" />
      </a-form-item>
      <a-form-item label="账号类型" name="account_type" :rules="[{ required: true }]">
        <a-select v-model:value="formData.account_type" placeholder="请选择账号类型" clearable :options="accountTypeOptions" class="w240" />
      </a-form-item>
      <a-form-item label="授权账号" name="authorized_account" :rules="[{ required: true }, { max: 50, message: '输入内容不可超过50字符' }]">
        <a-input v-model:value="formData.authorized_account" placeholder="请输入授权账号" class="w240" :maxlength="50" />
      </a-form-item>
    </a-form>
    <template #footer>
      <a-button style="margin-right: 10px" type="primary" @click="drawerStatus == 1 ? beforeIncrease() : beforeEdit()">确认</a-button>
      <a-button @click="formVisible = false">取消</a-button>
    </template>
  </a-drawer>
</template>

<script lang="ts" setup>
import { LoadingOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'

import { GetAli1PurchaseAccountInfo, AddAliPurchaseAccountInfo, UpdateAliPurchaseAccountInfo } from '@/servers/PurchaseAccount'

const emits = defineEmits(['query'])
const formVisible = ref(false)
const formLoading = ref(false)
const formData = ref<any>(null)
const drawerStatus = ref(1)
const formRef = ref<FormInstance>()
const accountTypeOptions = ref([
  { label: '主账号', value: 1 },
  { label: '子账号', value: 2 },
])
const open = (status, id = null) => {
  formVisible.value = true
  drawerStatus.value = status
  if (status == 2) {
    // 编辑
    formLoading.value = true
    formData.value = null
    GetAli1PurchaseAccountInfo({ id })
      .then((res) => {
        formData.value = res.data
        formLoading.value = false
      })
      .catch(() => {
        formLoading.value = false
      })
  } else {
    formLoading.value = false
    // 新增
    formData.value = {
      id: null,
      number: null,
      account_name: null,
      account_abbreviation: null,
      account_type: 2,
      authorized_account: null,
    }
  }
}

const beforeIncrease = async () => {
  try {
    await formRef.value?.validateFields()
    const obj = JSON.parse(JSON.stringify(formData.value))
    delete obj.id
    delete obj.number
    AddAliPurchaseAccountInfo(obj).then((res) => {
      if (res.success) {
        message.success('新增成功')
        formVisible.value = false
        emits('query')
      }
    })
  } catch (errorInfo) {
    console.log('Failed:', errorInfo)
  }
}
const beforeEdit = async () => {
  try {
    await formRef.value?.validateFields()
    const obj = JSON.parse(JSON.stringify(formData.value))
    delete obj.number
    UpdateAliPurchaseAccountInfo(obj).then((res) => {
      if (res.success) {
        message.success('编辑成功')
        formVisible.value = false
        emits('query')
      }
    })
  } catch (errorInfo) {
    console.log('Failed:', errorInfo)
  }
}
// 暴露方法
defineExpose({
  open,
})
</script>

<style lang="scss" scoped>
::v-deep(.ant-form-item) {
  align-items: flex-start;

  .ant-form-item-label {
    display: flex;
    justify-content: flex-end;
    width: 110px;
    min-width: 110px;
    margin-right: 30px;

    label {
      &::after {
        display: none !important;
      }
    }
  }
}

.detailTitle {
  padding-left: 12px;
  margin-bottom: 20px;
  font-weight: bold;
  color: #000;
}

.w350 {
  width: 350px;
}

.w300 {
  width: 300px;
}

.w240 {
  width: 240px;
}

.w150 {
  width: 150px;
}

.w200 {
  width: 200px;
}

.description {
  padding-left: 20px;
  font-size: 12px;
  color: rgb(0 0 0 / 50%);
  white-space: nowrap;
}

.detailValueDescription {
  font-size: 12px;
  color: rgb(0 0 0 / 50%);
}

.detailBox {
  padding-top: 12px;

  .loadingIcon {
    font-size: 30px;
    color: #1890ff;
  }
}

.descriptionBtn {
  color: rgb(0 0 0 / 70%);
  text-decoration: underline;
  cursor: pointer;

  &:hover {
    color: #1890ff;
  }
}

.modalContent {
  font-size: 14px;
  word-break: break-word;
  white-space: pre-wrap;
}
</style>
