<template>
  <div class="flex flex-col h-full main">
    <Form ref="formRef" v-model:form="formArr" :page-type="PageTypeEnum.BillPayable" @search="tableRef?.search()" @setting="tableRef?.showTableSetting()">
      <template #header>
        <StatusTabs v-model:status="status" :options="statusList" @change="tableRef?.search()" />
      </template>
    </Form>

    <BaseTable ref="tableRef" :page-type="PageTypeEnum.BillPayable" v-model:form="formArr" :get-list="GetBillPayableOrderList" :isCheckbox="true" :form-format="formatData">
      <template #left-btn>
        <!-- <a-button v-if="btnPermission[84004] || btnPermission[84005] || btnPermission[84006] || btnPermission[84007]" @click="handlerBatchAudit">批量审核</a-button> -->
      </template>
      <template #right-btn>
        <a-dropdown v-if="btnPermission[135008]" placement="bottomLeft" class="ml-auto">
          <template #overlay>
            <a-menu @click="onExportTypeChange">
              <a-menu-item v-for="item in exportOptions" :key="item.value">{{ item.label }}</a-menu-item>
            </a-menu>
          </template>
          <a-button>
            导出
            <DownOutlined />
          </a-button>
        </a-dropdown>
        <a-button v-if="btnPermission[135002]" type="primary" @click="handleOption({})">新增应付单</a-button>
      </template>
      <template #fix_option="{ row, column }">
        <RightOperate :list="rightOperateList" :row="row" :column="column" />
      </template>
    </BaseTable>
    <!-- 操作日志组件 -->
    <OperationLog ref="operationLogRef"></OperationLog>
  </div>
</template>

<script lang="ts" setup>
import { DownOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'

import { Enum2Options } from '@/utils'
import eventBus from '@/utils/eventBus'

import { GetPageSuppliersSelect, GetPageMRPSupplierCompanySelect } from '@/servers/BusinessCommon'
import { GetBillPayableOrderList, DeleteBillPayableOrder, AntiAuditBillPayableOrder, ExportBillPayableOrders, GetBillPayableCreatorNameList } from '@/servers/BillPayable'

import { PageTypeEnum } from '@/enums/tableEnum'

const { btnPermission } = usePermission()
const { pushPage } = usePage({ refresh: () => tableRef.value.search() })

const status = ref('')
const statusList = ref([{ label: '全部', value: '' }, ...Enum2Options(BillPayableOrderAuditEnum).slice(0, 2)])
// const statusList = ref([...Enum2Options(BillPayableOrderAuditEnum).slice(0, 2), { label: '全部', value: '' }])

const formArr = ref([
  { label: '应付单号', value: '', type: 'inputDlg', key: 'number' },
  {
    label: '供应商',
    value: undefined,
    type: 'select-supplier',
    key: 'supplier_ids',
    mode: 'multiple',
    api: GetPageSuppliersSelect,
  },
  {
    label: '供应商子公司',
    value: undefined,
    type: 'select-supplier',
    key: 'company_supplier_ids',
    mode: 'multiple',
    api: GetPageMRPSupplierCompanySelect,
  },
  {
    label: '结算状态',
    value: [],
    type: 'select',
    key: 'settlement_status',
    selectArr: Enum2Options(BillPayableOrderSettlementEnum),
    multiple: true,
    isShow: true,
    isQuicks: true,
  },
  {
    label: '发票类型',
    value: undefined,
    type: 'select',
    key: 'invoice_type',
    selectArr: Enum2Options(InvoiceTypeEnum),
  },
  { label: '发票号码', value: '', type: 'input', key: 'invoice_number' },
  { label: '发票主体', value: '', type: 'input', key: 'invoice_subject' },
  {
    label: '创建人',
    value: undefined,
    type: 'select',
    key: 'creator_id',
    api: GetBillPayableCreatorNameList,
  },
  { label: '备注', value: '', type: 'input', key: 'remark' },
  { label: '单据时间', value: null, type: 'range-picker', formKeys: ['pay_order_star_date', 'pay_order_end_date'], placeholder: ['单据开始时间', '单据结束时间'] },
])
useSearchForm(formArr)

const tableRef = ref()
const operationLogRef = ref()
const rightOperateList = ref([
  {
    label: '查看',
    show: 135001, // 访问 btnPermission.value
    onClick: ({ row }) => {
      handleOption(row, '查看')
    },
  },
  {
    label: '编辑',
    show: ({ row }) =>
      btnPermission.value[135003] &&
      row.audit_status === BillPayableOrderAuditEnum.待提审 &&
      (row.settlement_status === BillPayableOrderSettlementEnum.未结算 || row.settlement_status === BillPayableOrderSettlementEnum.未对账),
    onClick: ({ row }) => {
      handleOption(row, '编辑')
    },
  },
  {
    label: '删除',
    show: ({ row }) =>
      btnPermission.value[135004] &&
      row.audit_status === BillPayableOrderAuditEnum.待提审 &&
      (row.settlement_status === BillPayableOrderSettlementEnum.未结算 || row.settlement_status === BillPayableOrderSettlementEnum.未对账),
    onClick: ({ row }) => {
      handleOption(row, '删除')
    },
  },
  {
    label: '反审核',
    show: ({ row }) =>
      btnPermission.value[135005] &&
      row.audit_status === BillPayableOrderAuditEnum.已通过 &&
      (row.settlement_status === BillPayableOrderSettlementEnum.未结算 || row.settlement_status === BillPayableOrderSettlementEnum.未对账),
    onClick: ({ row }) => {
      handleOption(row, '反审核')
    },
  },
  {
    label: '日志',
    show: 135001, // 访问权限
    onClick: ({ row }) => {
      handleLog(row)
    },
  },
])

const handleLog = (row: any) => {
  // 打开操作日志组件
  operationLogRef.value.open({
    params: {
      id: row.id,
      pageType: OpLogPageTypeEnum.应付单,
    },
  })
}

const formData = ref({})
const formatData = (data: any) => {
  formData.value = {
    ...data,
    audit_status: status.value,
  }
  return formData.value
}

// 操作
const modal = inject('modal') as any
const handleOption = async (row, key?: string) => {
  switch (key) {
    case '查看':
      pushPage(`/billPayable/view/${row.id}`, { source: true })
      break
    case '编辑':
      pushPage(`/billPayable/edit/${row.id}`, { source: true })
      break
    case '删除':
      modal.open({
        title: '是否确定删除?',
        async onOk() {
          await DeleteBillPayableOrder({ id: row.id })
          message.success('删除成功')
          tableRef.value.search()
        },
      })
      break
    case '反审核':
      modal.open({
        title: '是否确定反审核?',
        async onOk() {
          await AntiAuditBillPayableOrder({ id: row.id })
          message.success('操作成功')
          tableRef.value.search()
        },
      })
      break
    // 新增
    default:
      pushPage(`/billPayable/new`, { source: true })
      break
  }
}

// 导出
enum BillPayableOrderExportTypeEnum {
  根据勾选导出 = 1,
  根据筛选结果导出 = 2,
  全部导出 = 3,
}
const exportOptions = ref([
  ...Enum2Options(BillPayableOrderExportTypeEnum),
  ...Enum2Options(BillPayableOrderExportTypeEnum).map((f) => ({
    label: `${f.label} (含明细)`,
    value: f.value + 10,
  })),
])
const onExportTypeChange = ({ key }) => {
  const exportType = key > 10 ? key - 10 : key
  const count = tableRef.value.checkItemsArr.length
  let billPayableOrderIds
  let searchListParam
  if (exportType === BillPayableOrderExportTypeEnum.根据勾选导出) {
    if (!count) return message.info('请勾选数据')
    billPayableOrderIds = tableRef.value.checkItemsArr.map((f) => f.id)
  }
  if (exportType === BillPayableOrderExportTypeEnum.根据筛选结果导出) {
    searchListParam = formData.value
  }
  const params = {
    isExportDetail: key > 10,
    exportType,
    billPayableOrderIds,
    searchListParam,
  }
  modal.open({
    title: '是否确定导出应付单数据?',
    onOk() {
      return new Promise((resolve) => {
        ExportBillPayableOrders(params).then(({ data }) => {
          eventBus.emit('downLoadId', {
            id: data as number,
            resolve,
            download: true,
          })
        })
      })
    },
  })
}
</script>
