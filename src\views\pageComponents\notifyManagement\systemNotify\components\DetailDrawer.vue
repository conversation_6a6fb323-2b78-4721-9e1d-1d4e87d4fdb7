<template>
  <a-drawer
    :headerStyle="{ paddingTop: '0.57vw', paddingBottom: '0.84vw' }"
    v-model:open="detailVisible"
    width="45vw"
    title="查看通知"
    placement="right"
    :maskClosable="false"
    :footer-style="{ textAlign: 'left' }"
  >
    <div class="detailBox">
      <LoadingOutlined v-show="detailloading" class="loadingIcon" />
      <a-form v-if="!detailloading && target">
        <div class="drawer-title">通知编码：{{ target.no }}</div>
        <a-form-item label="通知标题">
          <span class="detailValue">{{ target.title }}</span>
        </a-form-item>
        <a-form-item label="通知内容">
          <span class="detailValue">{{ target.content }}</span>
        </a-form-item>
        <a-form-item label="通知范围">
          <span class="detailValue">{{ target.scope || '--' }}</span>
        </a-form-item>
        <a-form-item label="发布计划">
          <span class="detailValue">{{ target.publish_type || '--' }}</span>
        </a-form-item>
        <a-form-item label="计划发布时间">
          <span class="detailValue">{{ target.plan_publish_time ? target.plan_publish_time : '--' }}</span>
        </a-form-item>
        <a-form-item label="发布时间">
          <span class="detailValue">{{ target.publish_time ? target.publish_time : '--' }}</span>
        </a-form-item>
        <a-form-item label="过期时间">
          <span class="detailValue">{{ target.exp_time ? target.exp_time : '--' }}</span>
        </a-form-item>
        <a-form-item label="状态">
          <span class="detailValue">{{ target.status || '--' }}</span>
        </a-form-item>
        <div class="drawer-title">其他信息</div>
        <div style="display: flex">
          <a-form-item label="创建时间">
            <div class="detailValue w200">{{ target.created }}</div>
          </a-form-item>
          <a-form-item label="创建人">
            <a-tooltip :overlayStyle="{ maxWidth: 'none' }">
              <template #title>
                <div>用户名称：{{ target.creator }}</div>
                <!-- <div v-show="target.create_user_scope == 1">所属公司：{{ target.create_user_company_name ? target.create_user_company_name : '--' }}</div>
                <div v-show="target.create_user_scope != 1">所属客户：{{ target.create_user_customer_name ? target.create_user_customer_name : '--' }}</div> -->
                <div>所在部门：{{ target.depart_of_creator ? target.depart_of_creator : '--' }}</div>
                <div>
                  岗
                  <span style="visibility: hidden">占位</span>
                  位：{{ target.job_of_creator ? target.job_of_creator : '--' }}
                </div>
              </template>
              <div style="display: flex; align-items: center">
                <span class="detailValue">{{ target.creator ? target.creator : '--' }}</span>
                <span v-if="target.depart_of_creator || target.job_of_creator" class="detailValueDescription">
                  （
                  <span v-if="target.job_of_creator">{{ target.job_of_creator }}&nbsp;|&nbsp;</span>
                  <span v-if="target.depart_of_creator">{{ target.depart_of_creator.length > 10 ? target.depart_of_creator.slice(0, 10) + '...' : target.depart_of_creator }}</span>
                  ）
                </span>
              </div>
            </a-tooltip>
          </a-form-item>
        </div>
        <div style="display: flex">
          <a-form-item label="最后修改时间">
            <div class="detailValue w200">{{ target.modified }}</div>
          </a-form-item>
          <a-form-item label="最后修改人">
            <a-tooltip :overlayStyle="{ maxWidth: 'none' }">
              <template #title>
                <div>用户名称：{{ target.modifier }}</div>
                <!-- <div v-show="target.update_user_scope == 1">所属公司：{{ target.update_user_company_name ? target.update_user_company_name : '--' }}</div>
                <div v-show="target.update_user_scope != 1">所属客户：{{ target.update_user_customer_name ? target.update_user_customer_name : '--' }}</div> -->
                <div>所在部门：{{ target.depart_of_modifier ? target.depart_of_modifier : '--' }}</div>
                <div>
                  岗
                  <span style="visibility: hidden">占位</span>
                  位：{{ target.job_of_modifier ? target.job_of_modifier : '--' }}
                </div>
              </template>
              <div style="display: flex; align-items: center">
                <span class="detailValue">{{ target.modifier ? target.modifier : '--' }}</span>
                <span v-if="target.depart_of_modifier || target.job_of_modifier" class="detailValueDescription">
                  （
                  <span v-if="target.job_of_modifier">{{ target.job_of_modifier }}&nbsp;|&nbsp;</span>
                  <span v-if="target.depart_of_modifier">{{ target.depart_of_modifier.length > 10 ? target.depart_of_modifier.slice(0, 10) + '...' : target.depart_of_modifier }}</span>
                  ）
                </span>
              </div>
            </a-tooltip>
          </a-form-item>
        </div>
      </a-form>
    </div>
  </a-drawer>
</template>

<script lang="ts" setup>
import { LoadingOutlined } from '@ant-design/icons-vue'
import { GetNotice } from '@/servers/Notice'

const detailVisible = ref(false)
const detailloading = ref(false)
const target = ref<any>(null)
const open = (id) => {
  target.value = null
  detailloading.value = true
  detailVisible.value = true
  GetNotice(id)
    .then((res) => {
      target.value = res.data
      detailloading.value = false
    })
    .catch(() => {
      detailloading.value = false
    })
}

// 暴露方法
defineExpose({
  open,
})
</script>

<style lang="scss" scoped>
::v-deep(.ant-form-item) {
  align-items: flex-start;

  .ant-form-item-label {
    display: flex;
    justify-content: flex-end;
    width: 110px;
    min-width: 110px;
    margin-right: 30px;

    label {
      &::after {
        display: none !important;
      }
    }
  }
}

.w350 {
  width: 350px;
}

.w250 {
  width: 250px;
}

.w150 {
  width: 150px;
}

.w200 {
  width: 200px;
}

.description {
  padding-left: 20px;
  font-size: 12px;
  color: rgb(0 0 0 / 50%);
  white-space: nowrap;
}

.detailValueDescription {
  font-size: 12px;
  color: rgb(0 0 0 / 50%);
}

.detailBox {
  padding-top: 12px;

  .loadingIcon {
    font-size: 30px;
    color: #1890ff;
  }
}
</style>
