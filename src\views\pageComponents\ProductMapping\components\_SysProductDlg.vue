<template>
  <a-drawer title="选择商品" width="40vw" :visible="visible" @close="handleClose" :maskClosable="false">
    <div class="flex h-full overflow-auto">
      <div class="p-16px h-full overflow-auto">
        <a-space class="mb-12">
          <a-input placeholder="商品编码" v-model:value="queryParams.k3_sku_id" :maxlength="200" />
          <a-input placeholder="商品编码（聚水潭）" v-model:value="queryParams.jst_sku_id" :maxlength="200" />
          <a-input placeholder="商品名称" v-model:value="queryParams.sku_name" :maxlength="200" />
          <!-- <a-select class="w-200" placeholder="请选择供应商" allowClear v-model:value="queryParams.k3_supplier_number" show-search :filter-option="filterOption" :options="supplierOptions"></a-select> -->
          <a-button type="primary" @click="handleSearch">查询</a-button>
        </a-space>
        <vxe-table
          :border="true"
          ref="productTableRef"
          size="mini"
          :row-config="{ isHover: true, keyField: 'k3_sku_id', height: 40 }"
          :custom-config="{ mode: 'popup' }"
          :data="data"
          :show-overflow="true"
          :show-header-overflow="true"
          :show-footer-overflow="true"
          :column-config="{ resizable: true }"
          class="tableBoxwidth"
          min-height="0"
          stripe
          v-bind="$attrs"
          @radio-change="radioChangeEvent"
        >
          <vxe-column type="radio" field="checkbox" width="50" fixed="left"></vxe-column>
          <slot name="column">
            <template v-for="i in tableKey" :key="i.field">
              <vxe-column v-bind="i">
                <template v-if="$slots[String(i.field)]" #default="attr">
                  <slot :name="i.field" v-bind="attr" :item="i" />
                </template>
              </vxe-column>
            </template>
          </slot>
        </vxe-table>
        <div class="paginationBox">
          <div class="pagination">
            <a-pagination
              show-quick-jumper
              :total="total"
              show-size-changer
              v-model:current="queryParams.page"
              v-model:page-size="queryParams.pageSize"
              :page-size-options="['10', '20', '30', '40', '50']"
              @change="handlePageChange"
              size="small"
            >
              <template #buildOptionText="props">
                <span>{{ props.value }}条/页</span>
              </template>
            </a-pagination>
          </div>
          <div class="totalBox">
            <div class="text">总数:</div>
            <div class="total">{{ total }}</div>
          </div>
        </div>
      </div>
    </div>
    <template #footer>
      <a-space>
        <a-button type="primary" @click="handleSelectProduct">确定</a-button>
        <a-button @click="handleClose">取消</a-button>
      </a-space>
    </template>
  </a-drawer>
</template>

<script setup lang="ts">
import { VxeTableEvents } from 'vxe-table'
import { message } from 'ant-design-vue'

// import { filterOption } from '@/utils'

import { GetK3SkuInfo } from '@/servers/Purchaseprice'

const emit = defineEmits(['selectProduct'])

const visible = ref(false)

// const supplierOptions = ref<any[]>([])

const data = ref([])

const total = ref(0)

const queryParams = ref<any>({
  page: 1,
  pageSize: 10,
  style_code: null,
})

const selectRow = ref()

const tableKey = ref([
  {
    field: 'sku_id',
    title: '商品编码',
  },
  {
    field: 'style_code',
    title: '款式编号',
  },
  {
    field: 'sku_name',
    title: '商品名称',
  },
  {
    field: 'specification',
    title: '规格型号',
  },
  {
    field: 'price_unit',
    title: '计价单位',
  },
  {
    field: 'jst_sku_id',
    title: '聚水潭编号',
  },
  {
    field: 'supplier_name',
    title: '默认供应商',
  },
])

const hasSelectArr = ref<any>([])
// 打开
const open = async (style_code = null, company_supplier_id = null, arr) => {
  hasSelectArr.value = arr
  queryParams.value.style_code = style_code
  queryParams.value.company_supplier_id = company_supplier_id
  data.value = []
  selectRow.value = null
  await getProductList()
  // await getSupplierList()
  visible.value = true
}
// 关闭
const handleClose = () => {
  visible.value = false
  queryParams.value = {
    page: 1,
    pageSize: 10,
    style_code: null,
    company_supplier_id: null,
  }
}

// 获取商品列表
const getProductList = async () => {
  for (const key in queryParams.value) {
    if (!queryParams.value[key]) {
      queryParams.value[key] = null
    }
  }
  const res = await GetK3SkuInfo(queryParams.value)
  data.value = res.data.list
  total.value = res.data.total
}
// 分页
const handlePageChange = (page: number, pageSize: number) => {
  queryParams.value.page = page
  queryParams.value.pageSize = pageSize
  getProductList()
}

// 获取供应商列表
// const getSupplierList = async () => {
//   const res = await GetSupplierOption()
//   supplierOptions.value = res.data.map((i) => ({
//     label: i.value,
//     value: i.value,
//   }))
// }

// 查询
const handleSearch = () => {
  queryParams.value.page = 1
  getProductList()
}

// 选中事件
const radioChangeEvent: VxeTableEvents.RadioChange = ({ row }) => {
  selectRow.value = row
}

// 确定
const handleSelectProduct = () => {
  if (hasSelectArr.value.includes(selectRow.value.sku_id)) {
    message.error('不可重复选择!')
  } else {
    emit('selectProduct', selectRow.value)
    handleClose()
  }
}

defineExpose({
  open,
})
</script>

<style scoped lang="scss">
.paginationBox {
  display: flex;
  align-items: center;
  margin-top: 0.83rem;

  .totalBox {
    display: flex;
    align-items: flex-end;
    margin-left: 20px;
    color: #000;

    .text {
      margin-right: 8px;
      font-size: 14px;
    }

    .total {
      font-size: 16px;
      color: #1890ff;
    }
  }
}
</style>
