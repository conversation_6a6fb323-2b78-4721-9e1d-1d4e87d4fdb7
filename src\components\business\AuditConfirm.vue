<template>
  <a-modal v-model:open="visible" :footer="null" :title="props.batch ? '批量审核' : form.is_pass ? '审核通过' : '审核拒绝'">
    <a-radio-group v-if="props.batch" v-model:value="form.is_pass" class="mb-12">
      <a-radio :value="true">通过</a-radio>
      <a-radio :value="false">拒绝</a-radio>
    </a-radio-group>
    <a-textarea v-model:value="form.audit_opinion" :rows="4" placeholder="请输入审核意见" />
    <a-flex justify="center" class="mt-16">
      <a-space :size="16">
        <a-button type="primary" :loading="submitLoading" :disabled="submitLoading" @click="handleOk">确定{{ form.is_pass ? '通过' : '拒绝' }}</a-button>
        <a-button @click="handleCancel">取消</a-button>
      </a-space>
    </a-flex>
  </a-modal>
</template>

<script lang="ts" setup>
import { message } from 'ant-design-vue'

const visible = ref(false)

const props = defineProps({
  batch: <PERSON><PERSON><PERSON>,
})

const emits = defineEmits(['audit'])

const form = ref({
  audit_opinion: '',
  is_pass: false,
})
// 确定
const submitLoading = ref(false)
const handleOk = async () => {
  if (form.value?.audit_opinion?.length > 500) {
    message.warning('输入内容不能超过500个字')
    return
  }
  submitLoading.value = true
  emits(
    'audit',
    form.value,
    () => {
      visible.value = false
      submitLoading.value = false
    },
    () => {
      submitLoading.value = false
    },
  )
}
// 取消
const handleCancel = () => {
  visible.value = false
  form.value.audit_opinion = ''
}

// 打开
const open = (auditStatus: boolean) => {
  form.value.audit_opinion = ''
  submitLoading.value = false
  visible.value = true
  form.value.is_pass = auditStatus ?? true
}

defineExpose({
  open,
  close: handleCancel,
})
</script>
