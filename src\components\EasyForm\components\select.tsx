import { SelectProps, Select, Empty } from 'ant-design-vue'
import { LoadingOutlined } from '@ant-design/icons-vue'

import type { FormItemSelectProps } from '../index.d'

export default defineComponent({
  name: 'EasyFormSelect',
  props: {
    innerProps: {
      require: true,
      type: Object as PropType<SelectProps>,
      default: () => ({}),
    },
    item: {
      require: true,
      type: Object as PropType<FormItemSelectProps>,
      default: () => ({}),
    },
    form: {
      type: Object as PropType<Record<string, any>>,
      default: () => ({}),
    },
  },
  emits: ['getApiData'],
  setup(props, { emit }) {
    const timer = ref()
    const debounceFn = (func, delay) => {
      if (timer.value) clearTimeout(timer.value)
      timer.value = setTimeout(func, delay)
    }
    const loading = ref(false)
    const started = ref(false)

    const renderVN = () => {
      if (!props.innerProps) return null
      const { innerProps, item } = props
      const _options = (item?.options || []).filter((f) => !f.hide)
      const isShowSearch = _options?.length > 20
      const defaultParams = {
        class: 'w-full',
        options: _options,
        placeholder: props.item.placeholder || `请选择${item.label}`,
        allowClear: true,
      }

      if (innerProps?.showSearch ?? isShowSearch) {
        if (item?.optionFormatter) {
          innerProps.filterOption = false
          innerProps.notFoundContent = loading.value
            ? h('div', { class: 'flex items-center justify-center mt-20 mb-20' }, [h(LoadingOutlined, { style: { fontSize: '16px', marginRight: '10px' } }), '加载中...'])
            : h(Empty, { image: Empty.PRESENTED_IMAGE_SIMPLE, description: '搜索不到数据' })
        } else {
          innerProps.notFoundContent = null
          innerProps.filterOption = innerProps.filterOption || ((input, option) => option?.label?.toLowerCase().indexOf(input.toLowerCase()) >= 0)
        }
        innerProps.onSearch = (value) => {
          debounceFn(() => {
            if (item.optionFormatter) {
              loading.value = true
              item.options = []
              emit('getApiData', value, () => {
                loading.value = false
              })
            }
          }, 300)
        }
      }

      if ((item.isAlias || (item.options?.length && item.api)) && innerProps.value && !started.value) {
        const found = (item.options as Record<string, any>[]).find((f) => f.value == innerProps.value)

        if (!found) {
          ;(item.options as Record<string, any>[]).push({
            value: props.form[item.name as string],
            label: props.form[item.alias as string],
          })
          started.value = true
        } else {
          found.value = innerProps.value
        }
      }
      return [
        h(Select, {
          ...defaultParams,
          ...innerProps,
          showSearch: innerProps.showSearch ?? isShowSearch,
          onClick: (e: MouseEvent) => {
            const clickFn: any = innerProps.onClick
            if (clickFn) {
              clickFn({ item: { ...item, props: innerProps }, event: e })
            }
          },
        } as SelectProps),
      ]
    }
    return {
      renderVN,
    }
  },
  render() {
    return this.renderVN()
  },
})
