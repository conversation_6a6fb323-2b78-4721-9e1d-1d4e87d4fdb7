<template>
  <a-drawer v-model:open="openFlag" title="批量更换采购员" placement="right" width="1000" :bodyStyle="{ padding: '0' }" destroyOnClose>
    <div class="pageContent">
      <div class="pageContentBox">
        <a-form :model="formState" name="basic" ref="formRef" :label-col="{ span: 3 }">
          <a-form-item label="更换方式" name="type" :rules="[{ required: true, message: '请选择更换方式' }]" :wrapper-col="{ span: 9 }">
            <a-radio-group v-model:value="formState.type" @change="onTypeChange">
              <a-radio v-for="item in typeOptions" :key="item.value" :value="item.value">{{ item.label }}</a-radio>
            </a-radio-group>
          </a-form-item>
          <a-form-item label="原对接采购员" name="sourceUser" :rules="[{ required: true, message: '请选择对接采购员' }]" :wrapper-col="{ span: 9 }" v-if="[1, 3].includes(formState.type)">
            <a-select v-model:value="formState.sourceUser" class="w-200!" placeholder="请选择" :options="sourceOptions" @change="modeData"></a-select>
          </a-form-item>
          <a-form-item label="最新对接采购员" name="newUser" :rules="[{ required: true, message: '请选择最新对接采购员' }]" :wrapper-col="{ span: 9 }" v-if="[1, 2].includes(formState.type)">
            <div v-if="formState.newUser?.value" class="flex items-center">
              <span class="mr-5 easy-tag default">{{ formState.newUser.label }}</span>
              <a-button type="link" @click="selectBuyer">修改</a-button>
            </div>
            <a-button v-else type="primary" @click="selectBuyer">选择对接采购员</a-button>
          </a-form-item>
        </a-form>
        <div class="flex-1">
          <SimpleTable :show-overflow="false" :tableKey="tableKeys" :data="dataList" max-height="100%" :auto-resize="true">
            <template #source_buyers="{ row }">
              <div v-for="item in row.source_buyers" :key="item.value">{{ item.label }}</div>
            </template>
            <template #new_buyers="{ row }">
              <div v-for="item in row.new_buyers" :key="item.value" :class="{ 'text-red': item.new }">{{ item.label }}</div>
            </template>
          </SimpleTable>
        </div>
      </div>
      <div class="footerBox">
        <a-button @click="submitPro">提交</a-button>
        <a-button @click="openFlag = false">关闭</a-button>
      </div>
    </div>
    <PurchaseModal ref="PurchaseModalRef" @confirm="onPurchaseModalConfirm"></PurchaseModal>
  </a-drawer>
</template>
<script setup lang="ts">
import { message } from 'ant-design-vue'

import PurchaseModal from '../../components/PurchaseModal.vue'

import { BindTransfer, GetChangeSupplierCompanyMessage } from '@/servers/Supplier'

const emit = defineEmits(['submitPro'])
const openFlag = ref(false)
const formRef = ref()
const formState = ref<any>({})

const dataList = ref([])
const tableKeys = ref([
  {
    title: '供应商编码',
    field: 'supplier_id',
    width: 100,
  },
  {
    title: '子公司编码',
    field: 'company_supplier_id',
    width: 100,
  },
  {
    title: '供应商子公司',
    field: 'company_supplier_name',
  },
  {
    title: '供应商名称',
    field: 'supplier_name',
  },
  {
    title: '当前对接采购员',
    field: 'source_buyers',
  },
  {
    title: '最新对接采购员',
    field: 'new_buyers',
  },
])

const sourceOptions = ref<Record<string, any>[]>([])
const ids = ref<number[]>([])

const open = (bol: boolean, arr: Record<string, any>[] = []) => {
  openFlag.value = bol
  if (bol) {
    formState.value = {
      type: 1,
    }
    const sourceIdMap: Record<number, boolean> = {}
    sourceOptions.value = []
    ids.value = arr.map((item) => item.company_supplier_id)
    GetChangeSupplierCompanyMessage(ids.value).then((res) => {
      dataList.value = res.data.map((item) => {
        for (const { id, name } of item.source_buyers || []) {
          const buyerId = id as number
          if (!sourceIdMap[buyerId]) {
            sourceIdMap[buyerId] = true
            sourceOptions.value.push({
              value: `${buyerId}`,
              label: name,
            })
          }
        }
        return {
          ...item,
          new_buyers: [],
          source_buyers: item.source_buyers.map((f) => ({ label: f.name, value: `${f.id}` })),
        }
      })
    })
  }
}

const typeOptions = ref([
  { label: '替换', value: 1 },
  { label: '追绑', value: 2 },
  { label: '解绑', value: 3 },
])

const PurchaseModalRef = ref()
const onPurchaseModalConfirm = ref()

// 最新的采购员选择
const selectBuyer = () => {
  if ([1, 3].includes(formState.value.type) && !formState.value.sourceUser) {
    return message.warn('请先选择原对接采购员')
  }
  const type = formState.value.type
  PurchaseModalRef.value.open([], { single: true, disabledList: type === 2 ? [] : [formState.value.sourceUser] })
  onPurchaseModalConfirm.value = ([newItem]) => {
    formState.value.newUser = newItem
    modeData()
  }
}
const onTypeChange = () => {
  modeData()
}
const modeData = () => {
  dataList.value.map((item: any): Record<string, any> => {
    const { type, sourceUser, newUser } = formState.value
    let sourceList = item.source_buyers
    // 在旧的列表中找sourceUser
    const index1 = item.source_buyers.findIndex(({ value }) => value === sourceUser)
    // 在旧的列表中找newUser
    const index2 = item.source_buyers.findIndex(({ value }) => value === newUser?.value)
    let newList: any[] = []
    if ([1, 3].includes(type)) {
      sourceList = sourceList.filter(({ value }) => value !== sourceUser)
    }
    if ((type === 1 && ~index1 && !~index2) || (type === 2 && !~index2)) {
      newList = [{ ...newUser, new: true }]
    }
    item.new_buyers = [...sourceList, ...newList]
    return item
  })
}

const submitPro = () => {
  formRef.value?.validate().then(async () => {
    const params = {
      type: formState.value.type,
      ids: ids.value,
      source_user_id: formState.value.sourceUser,
      new_user_id: formState.value.newUser?.value,
    }
    const res = await BindTransfer(params)
    if (res.success) {
      message.success('更改成功')
      emit('submitPro')
      open(false)
    } else {
      message.error(res.message)
    }
  })
}
defineExpose({ open })
</script>

<style scoped lang="scss">
.pageContent {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  height: 100%;
  font-size: 12px;
  color: #000;

  .pageContentBox {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: calc(100vh - 140px);
    padding-top: 24px;
    padding-right: 24px;
    padding-left: 24px;
    overflow: hidden;
  }

  .footerBox {
    position: absolute;
    bottom: 0;
    left: 0;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    width: 100%;
    height: 60px;
    padding-left: 24px;
    background: #fff;
    border-top: 1px solid #dedede;

    .ant-btn {
      margin-right: 20px;
    }
  }
}
</style>
