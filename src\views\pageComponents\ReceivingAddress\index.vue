<template>
  <div class="flex flex-col h-full main">
    <Form ref="formRef" v-model:form="formArr" :page-type="PageTypeEnum.ReceivingAddress" @search="tableRef?.search()" @setting="tableRef?.showTableSetting()"></Form>
    <BaseTable ref="tableRef" :page-type="PageTypeEnum.ReceivingAddress" v-model:form="formArr" :get-list="GetAliPurchaseAccountReceiveAddressList" :form-format="formFormat">
      <template #status="{ row }">
        <span>{{ adressStatusMap[row.status] }}</span>
      </template>
      <template #fix_option="{ row, column }">
        <RightOperate :list="rightOperateList" :row="row" :column="column" />
      </template>
    </BaseTable>
    <AdressInfo ref="AdressInfoRef" />
  </div>
</template>
<script lang="ts" setup>
import { message, Modal } from 'ant-design-vue'

import { adressStatusMap } from '@/common/map'

import AdressInfo from './components/AdressInfo.vue'

import { GetAliPurchaseAccountReceiveAddressList, UpdateAliPurchaseAccountReceiveAddressStatus } from '@/servers/PurchaseAccount'

import { PageTypeEnum } from '@/enums/tableEnum'

const route = useRoute()
const queryId: any = ref()
const tableRef = ref()
const AdressInfoRef = ref()

const formRef = ref()
const formArr = ref([
  {
    label: '地址编号',
    value: '',
    type: 'input',
    key: 'number',
  },
  {
    label: '收货人姓名',
    value: '',
    type: 'input',
    key: 'full_name',
  },
  {
    label: '状态',
    value: null,
    type: 'select',
    key: 'status',
    selectArr: [
      { label: '启用', value: 1 },
      { label: '未启用', value: 2 },
    ],
  },
])

const formFormat = (data) => ({
  ...data,
  id: queryId.value,
})

const switchStatusBtn = (row) => {
  Modal.confirm({
    title: `确定要${row.status == 1 ? '禁用' : '启用'}吗?`,
    icon: () => {},
    content: '',
    async onOk() {
      const params = {
        id: row.id,
        is_open: row.status !== 1,
      }
      const res = await UpdateAliPurchaseAccountReceiveAddressStatus(params)
      if (res.success) {
        tableRef.value.search()
        message.success('操作成功')
      } else {
        message.error(res.message)
      }
    },
    onCancel() {},
  })
}

const rightOperateList = ref([
  {
    label: ({ row }) => (row.status == 1 ? '禁用' : '启用'),
    show: [102007, 102008],
    onClick: ({ row }) => {
      switchStatusBtn(row)
    },
  },
  {
    label: '查看',
    show: 102009,
    onClick: ({ row }) => {
      AdressInfoRef.value.open(row)
    },
  },
])

onMounted(() => {
  queryId.value = route.params.id
})
</script>
<style lang="scss" scoped></style>
