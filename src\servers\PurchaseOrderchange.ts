import { request } from './request'
// 获取采购单变更列表
export const GetPurchaseOrderChangeList = (data: any) => {
  return request({ url: '/api/PurchaseOrderChange/GetPurchaseOrderChangeList', data })
}
// 添加采购变更单
export const AddPurchaseOrderChange = (data: any) => {
  return request({ url: '/api/PurchaseOrderChange/AddPurchaseOrderChange', data })
}
// 编辑采购变更单
export const UpdatePurchaseOrderChange = (data: any, config?) => {
  return request({ url: '/api/PurchaseOrderChange/UpdatePurchaseOrderChange', data, config })
}
// 获取采购变更单
export const GetPurchaseOrderChange = (data: any) => {
  return request({ url: '/api/PurchaseOrderChange/GetPurchaseOrderChange', data })
}
// 审核通过/拒绝
export const Audit = (data: any) => {
  return request({ url: '/api/PurchaseOrderChange/Audit', data })
}

// 批量审核通过/拒绝
export const BatchAudit = (data: any) => {
  return request({ url: '/api/PurchaseOrderChange/BatchAudit', data })
}

// 关闭变更单
export const Close = (data: any) => {
  return request({ url: '/api/PurchaseOrderChange/Close', data }, 'GET')
}

// 导出变更单
export const ExportPurchaseChangeOrder = (data: any) => {
  return request({ url: '/api/PurchaseOrderChange/ExportPurchaseChangeOrder', data })
}
