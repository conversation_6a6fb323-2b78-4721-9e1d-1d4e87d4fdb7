<template>
  <a-drawer
    v-model:open="formVisible"
    @afterOpenChange="formRef?.clearValidate()"
    width="40vw"
    :title="`${drawerStatus == 1 ? '新增' : '编辑'}外部用户`"
    placement="right"
    :maskClosable="false"
    :footer-style="{ textAlign: 'left' }"
  >
    <LoadingOutlined v-show="formLoading" class="loadingIcon" />
    <a-form v-if="!formLoading" ref="formRef" :model="formData">
      <a-form-item label="类型" name="type" :rules="[{ required: true }]">
        <a-select id="type" :options="[{ label: '外部联系人', value: 2 }]" v-model:value="formData.type" class="w240" disabled />
      </a-form-item>
      <a-form-item label="账号" name="user_name" :rules="[{ required: true }, { max: 50, message: '输入内容不可超过50字符' }]">
        <a-input id="user_name" v-model:value="formData.user_name" :disabled="drawerStatus == 2" placeholder="请输入帐号" class="w240" />
        <span v-show="drawerStatus == 1" class="description">账号保存后不可修改</span>
      </a-form-item>
      <a-form-item v-if="drawerStatus == 1" label="密码" name="password" :rules="[{ required: true }, { max: 50, message: '输入内容不可超过50字符' }]">
        <a-input-password id="password" v-model:value="formData.password" placeholder="请输入密码" class="w240" />
      </a-form-item>
      <a-form-item
        v-if="drawerStatus == 1"
        label="确认密码"
        name="passwordStr"
        :rules="[{ required: true }, { max: 50, message: '输入内容不可超过50字符' }, { validator: checkPassRule, trigger: 'change', message: '两次输入的密码不一致' }]"
      >
        <a-input-password id="passwordStr" v-model:value="formData.passwordStr" placeholder="请输入确认密码" class="w240" />
      </a-form-item>
      <a-form-item label="用户名称" name="real_name" :rules="[{ required: true }, { max: 50, message: '输入内容不可超过50字符' }]">
        <a-input id="real_name" :disabled="formData.source === '选品网'" v-model:value="formData.real_name" placeholder="请输入用户名称" class="w240" />
      </a-form-item>
      <a-form-item label="所属供应商" name="customer_id" :rules="[{ required: true, message: '请选择所属供应商' }]">
        <div style="display: flex; flex-wrap: nowrap; align-items: center">
          <a-select
            id="customer_id"
            :disabled="formData.source === '选品网'"
            class="w350"
            v-if="customerOptions.findIndex((e) => e.value == formData.customer_id) != -1"
            @change="
              (val) => {
                formData.department_id = null
                getDepartmentSelectOption(val)
              }
            "
            placeholder="选择客户"
            show-search
            :filter-option="filterOption"
            v-model:value="formData.customer_id"
            :options="customerOptions"
          ></a-select>
          <a-select
            id="customer_id"
            :disabled="formData.source === '选品网'"
            class="w350"
            v-else
            placeholder="选择客户"
            show-search
            :filter-option="filterOption"
            @change="
              (val) => {
                formData.customer_id = val
                formData.department_id = null
                getDepartmentSelectOption(val)
              }
            "
            v-model:value="formData.customer_name"
            :options="customerOptions"
          ></a-select>
          <!-- <span class="description">
            找不到客户？
            <span @click="linkToAddCustomer" class="descriptionBtn">去创建</span>
          </span> -->
        </div>
      </a-form-item>
      <a-form-item label="所在部门" name="department_id">
        <a-tree-select
          id="department_id"
          v-if="formData.customer_id || formData.customer_id == 0"
          class="w350"
          v-model:value="formData.department_id"
          placeholder="选择部门"
          allow-clear
          tree-default-expand-all
          :tree-data="departmentOptions"
          :fieldNames="{
            children: 'children',
            label: 'value',
            value: 'key',
          }"
          :maxTagCount="1"
        ></a-tree-select>
        <a-select id="department_id" class="w350" :options="[]" v-else placeholder="选择部门">
          <template #dropdownRender="{ menuNode: menu }">
            <v-nodes :vnodes="menu" />
            <div style="padding: 8px 0; text-align: center">请先选择所属供应商</div>
          </template>
        </a-select>
      </a-form-item>
      <a-form-item label="岗位" name="job_name" :rules="[{ max: 200, message: '输入内容不可超过200字符' }]">
        <a-input id="job_name" class="select w240" v-model:value="formData.job_name" placeholder="请输入岗位"></a-input>
      </a-form-item>
      <a-form-item
        label="电子邮箱"
        name="email"
        :rules="[
          { type: 'email', message: '请输入正确的电子邮箱' },
          { max: 200, message: '输入内容不可超过200字符' },
        ]"
      >
        <a-input id="email" class="w240" v-model:value="formData.email" placeholder="请输入电子邮箱" />
      </a-form-item>
      <a-form-item label="角色" name="role_ids" :rules="[{ required: formData.status == 1, message: '请选择角色' }]">
        <a-select id="role_ids" allow-clear v-model:value="formData.role_ids" class="w240" placeholder="选择角色" :maxTagCount="1" :options="rolesOptions"></a-select>
      </a-form-item>
      <a-form-item label="状态" name="status">
        <a-switch id="status" v-model:checked="[false, true][formData.status]" @click="startStopForm()">
          <template #checkedChildren>
            <span class="iconfont icon-zhengquede_correct"></span>
          </template>
          <template #unCheckedChildren>
            <span class="iconfont icon-guanbi_close"></span>
          </template>
        </a-switch>
      </a-form-item>
      <a-form-item v-if="drawerStatus == 2" label="来源" name="source_type">
        <a-select id="source_type" disabled v-model:value="formData.source_type" class="w240" :maxTagCount="1" :options="sourceOption"></a-select>
      </a-form-item>
    </a-form>
    <template #footer>
      <a-button id="userListsFormComfirm" style="margin-right: 10px" type="primary" @click="drawerStatus == 1 ? beforeIncrease() : beforeEdit()">确认</a-button>
      <a-button id="userListsFormCancel" @click="formVisible = false">取消</a-button>
    </template>
  </a-drawer>
  <!-- 确认弹窗 -->
  <a-modal :zIndex="10000" v-model:open="modalData.isShow" :title="modalData.title">
    <div class="modalContent">{{ modalData.content }}</div>
    <template #footer>
      <a-button v-if="modalData.isConfirmBtn" :danger="modalData.okType === 'danger'" type="primary" style="margin-right: 10px" @click="modalData.okFn">{{ modalData.confirmBtnText }}</a-button>
      <a-button v-if="modalData.isCancelBtn" @click="modalData.isShow = false">取消</a-button>
    </template>
  </a-modal>
</template>

<script lang="ts" setup>
import { LoadingOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'

import { sourceOption } from '@/common/options'
import { filterOption } from '@/utils/index'

import { GetSupplierList, DetailsExtraByEdit, GetDepartmentSelectOption, Add, UpdateExtra } from '@/servers/UserManager'
import { GetRoleSelectOption } from '@/servers/Role'

const emits = defineEmits(['query'])
// const router = useRouter()
const formVisible = ref(false)
const formLoading = ref(false)
const formData = ref<any>(null)
const drawerStatus = ref(1)
const customerOptions = ref<any>([])
const rolesOptions = ref([])
const departmentOptions = ref([])
const formRef = ref<FormInstance>()

const modalData = reactive({
  isShow: false,
  isConfirmBtn: true,
  isCancelBtn: true,
  confirmBtnText: '确定',
  okType: 'primary',
  title: '',
  content: '',
  okFn: () => {
    modalData.isShow = false
  },
})

const open = (status, id = null) => {
  getCustomerSelectOption()
  getRoleSelectOption()
  formVisible.value = true
  drawerStatus.value = status
  if (status == 2) {
    // 编辑
    formLoading.value = true
    formData.value = null
    DetailsExtraByEdit({ id })
      .then((res) => {
        getDepartmentSelectOption(res.data.customer_id)
        res.data.type = 2
        res.data.role_ids = res.data.role_ids ? Number(res.data.role_ids) : null
        res.data.oldStatus = res.data.status
        res.data.source = res.data.source_format
        formData.value = res.data
        formLoading.value = false
      })
      .catch(() => {
        formLoading.value = false
      })
  } else {
    formLoading.value = false
    // 新增
    formData.value = {
      type: 2,
      user_name: null,
      password: null,
      passwordStr: null,
      real_name: null,
      customer_id: null,
      role_ids: null,
      status: 1,
      source: 1,
      email: null,
      department_id: null,
      job_name: null,
    }
  }
}
const startStopForm = () => {
  if (formData.value.status == 1) {
    formData.value.status = 0
  } else {
    formData.value.status = 1
  }
}

const checkPassRule = async (_rule, value) => {
  if (value !== formData.value.password) {
    return Promise.reject()
  }
  return Promise.resolve()
}
const beforeIncrease = async () => {
  try {
    await formRef.value?.validateFields()
    if (!formData.value.role_ids) {
      formData.value.role_ids = ''
    }
    Add(formData.value).then(() => {
      message.success('新增成功')
      formVisible.value = false
      emits('query')
    })
  } catch (errorInfo) {
    console.log('Failed:', errorInfo)
  }
}
const beforeEdit = async () => {
  try {
    await formRef.value?.validateFields()
    const fn = () => {
      formData.value.user_id = formData.value.id
      UpdateExtra(formData.value).then(() => {
        message.success('编辑成功')
        formVisible.value = false
        emits('query')
      })
    }
    if (formData.value.status == 0 && formData.value.oldStatus == 1) {
      modalData.isShow = true
      modalData.title = '停用用户'
      modalData.content = `停用后，该用户将无法访问系统的任何功能或数据。

确定要停用该用户的帐号吗？`
      modalData.confirmBtnText = '确定'
      modalData.isCancelBtn = true
      modalData.okType = 'danger'
      modalData.okFn = () => {
        modalData.isShow = false
        fn()
      }
    } else {
      fn()
    }
  } catch (errorInfo) {
    console.log('Failed:', errorInfo)
  }
}
const getCustomerSelectOption = () => {
  customerOptions.value = []
  GetSupplierList({ page: 1, pageSize: 9999 }).then((res) => {
    customerOptions.value = res.data.list.map((i) => ({ label: i.supplier_name, value: i.supplier_id }))
  })
}
const getRoleSelectOption = () => {
  rolesOptions.value = []
  GetRoleSelectOption({ scope: 2 }).then((res) => {
    res.data.forEach((e) => {
      e.label = e.role_name
      e.value = e.role_id
    })
    rolesOptions.value = res.data.filter((e) => e.status == 1)
  })
}
const getDepartmentSelectOption = (val) => {
  departmentOptions.value = []
  GetDepartmentSelectOption({ id: val }).then((res) => {
    departmentOptions.value = res.data
  })
}
// 暴露方法
defineExpose({
  open,
})
</script>

<style lang="scss" scoped>
::v-deep(.ant-form-item) {
  align-items: flex-start;

  .ant-form-item-label {
    display: flex;
    justify-content: flex-end;
    width: 110px;
    min-width: 110px;
    margin-right: 30px;

    label {
      &::after {
        display: none !important;
      }
    }
  }
}

.detailTitle {
  padding-left: 12px;
  margin-bottom: 20px;
  font-weight: bold;
  color: #000;
}

.w350 {
  width: 350px;
}

.w300 {
  width: 300px;
}

.w240 {
  width: 240px;
}

.w150 {
  width: 150px;
}

.w200 {
  width: 200px;
}

.description {
  padding-left: 20px;
  font-size: 12px;
  color: rgb(0 0 0 / 50%);
  white-space: nowrap;
}

.detailValueDescription {
  font-size: 12px;
  color: rgb(0 0 0 / 50%);
}

.detailBox {
  padding-top: 12px;

  .loadingIcon {
    font-size: 30px;
    color: #1890ff;
  }
}

.descriptionBtn {
  color: rgb(0 0 0 / 70%);
  text-decoration: underline;
  cursor: pointer;

  &:hover {
    color: #1890ff;
  }
}

.modalContent {
  font-size: 14px;
  word-break: break-word;
  white-space: pre-wrap;
}
</style>
