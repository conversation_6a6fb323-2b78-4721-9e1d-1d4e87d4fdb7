interface Option {
  label: string
  value: string | number
  code?: string
}

export const settleAccountOption: Option[] = [
  { label: '月结', value: 1 },
  { label: '半月结', value: 2 },
  { label: '周结', value: 3 },
  { label: '预付', value: 4 },
]

export const sourceOption: Option[] = [
  { value: 1, label: 'OA' },
  { value: 2, label: 'SRM' },
]

export const adjustPriceReasonOption: Option[] = [
  { value: 1, label: '定期询价' },
  { value: 2, label: '新品询价' },
  { value: 3, label: '临时询价' },
]

export const currencyOption: Option[] = [{ value: 1, label: '人民币' }]

export const purchaseAuditstatusOption: Option[] = [
  { value: '', label: '全部' },
  { value: 10, label: '待提审', code: 'await_arraigned_count' },
  { value: 20, label: '待一级审核', code: 'one_auditing_count' },
  { value: 30, label: '待二级审核', code: 'two_auditing_count' },
  { value: 90, label: '已通过' },
  { value: 95, label: '已拒绝' },
]

export const purchaseAdjustPriceStatusOption: Option[] = [
  { value: '', label: '全部' },
  { value: 10, label: '待提审', code: 'await_arraigned_count' },
  { value: 20, label: '待一级审核', code: 'one_auditing_count' },
  { value: 30, label: '待二级审核', code: 'two_auditing_count' },
  { value: 40, label: '待三级审核', code: 'three_auditing_count' },
  { value: 50, label: '待四级审核', code: 'four_auditing_count' },
  { value: 90, label: '已通过' },
  { value: 95, label: '已拒绝' },
]
export const purchaseMyOrderOption: Option[] = [
  { value: 10, label: '待确认' },
  { value: 20, label: '已确认' },
]

export const reservationStatusOption: Option[] = [
  { value: '', label: '全部' },
  { value: 10, label: '待提审', code: 'await_arraigned_count' },
  { value: 20, label: '待审核', code: 'one_auditing_count' },
  { value: 90, label: '已通过' },
  { value: 95, label: '已拒绝' },
  { value: 100, label: '已完成' },
  { value: 200, label: '已作废' },
]

export const paymentOrderStatusOption = [
  { value: '', label: '全部' },
  { value: 1, label: '待提审', code: 'await_arraigned_count' },
  { value: 2, label: '审核中', code: 'auditing_count' },
  { value: 4, label: '已通过', code: 'declined_count' },
]
