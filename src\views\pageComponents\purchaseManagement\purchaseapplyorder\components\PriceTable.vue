<template>
  <vxe-table
    :border="true"
    ref="tableRef"
    size="mini"
    :row-config="{ keyField: keyField || '_X_ROW_KEY', isHover: true, height: 40 }"
    :custom-config="{ mode: 'popup' }"
    :data="data"
    :show-overflow="true"
    :show-header-overflow="true"
    :show-footer-overflow="true"
    :column-config="{ resizable: true }"
    class="tableBoxwidth"
    :checkbox-config="{ reserve: true }"
    min-height="0"
    stripe
    v-bind="$attrs"
  >
    <slot name="column">
      <vxe-column type="seq" width="50"></vxe-column>
      <template v-for="i in tableKey" :key="i.field">
        <vxe-column v-bind="i">
          <template v-if="$slots[String(i.field)]" #default="attr">
            <slot :name="i.field" v-bind="attr" :item="i" />
          </template>
          <template v-if="i.required" #header="attr">
            <span class="text-red">*</span>
            {{ attr.column.title }}
          </template>
        </vxe-column>
      </template>
    </slot>
  </vxe-table>
</template>

<script setup lang="ts">
import { VxeColumnProps, VxeTableInstance } from 'vxe-table'

const tableRef = ref<VxeTableInstance>()

withDefaults(
  defineProps<{
    data: any[]
    tableKey: VxeColumnProps[] | any[]
    keyField?: string
  }>(),
  {
    data: () => [],
    tableKey: () => [],
  },
)

onMounted(() => {
  window.addEventListener('resize', updateWindowWidth)
})
const windowScreenWidth = ref(window.innerWidth)
const updateWindowWidth = () => {
  windowScreenWidth.value = window.innerWidth
}

onBeforeUnmount(() => {
  window.removeEventListener('resize', updateWindowWidth)
})

const checkItemsArr = ref([] as any[])

defineExpose({ checkItemsArr, tableRef })
</script>
<style lang="scss" scoped>
.tableBox {
  position: relative;
  flex: 1;
  border-bottom: 1px solid #ddd;

  .toolbarBtn {
    position: absolute;
    right: 0;
    bottom: 100%;
    padding: 0;
    padding-bottom: 0.6em;
    margin-block: -5px;
  }

  .box {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;

    // overflow-y: scroll;
    .editbtn {
      color: #1890ff;
      cursor: pointer;
    }

    .movesea {
      margin-left: 20px;
      color: #1890ff;
      cursor: pointer;
    }
  }
}

.required {
  color: #f56c6c;
}

.required-text {
  margin-right: 4px;
}
</style>
