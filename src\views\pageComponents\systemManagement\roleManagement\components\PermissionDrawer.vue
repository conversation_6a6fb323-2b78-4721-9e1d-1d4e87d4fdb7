<template>
  <a-drawer :maskClosable="false" class="permissionDrawer" :open="visible" :width="scope == 1 ? '52vw' : '44vw'" title="权限设置" @close="visible = false">
    <div class="title">
      角色：{{ role_name }}
      <span class="description">（{{ scope == 1 ? '内' : '外' }}部联系人）</span>
    </div>
    <TagFilter v-model:value="activeTag" class="filterArr" v-show="filterArr.length != 0" :data="filterArr" />
    <a-tree
      v-if="treeData.length != 0"
      defaultExpandAll
      :tree-data="activeTag.length === 0 ? treeData : treeData.filter((e) => e.id === activeTag[0])"
      :field-names="fieldNames"
      show-line
      blockNode
      :showIcon="false"
      :selectable="false"
    >
      <!-- :defaultExpandedKeys="expandedKeys" -->
      <template #switcherIcon="{ switcherCls }">
        <down-outlined :class="switcherCls" />
      </template>
      <template #title="{ data }">
        <div class="textBox">
          <div class="checkBox">
            <a-checkbox @change="checkBoxClick(data)" v-model:checked="data.checked">
              <div class="label">
                {{ data.permissions_name }}
                <span class="description" v-show="data.remark">( {{ data.remark }} )</span>
              </div>
            </a-checkbox>
          </div>
          <div v-if="data.has_permissions_range" class="permissionBox">
            <div class="label">数据范围：</div>
            <a-radio-group
              @change="
                () => {
                  data.checked = true
                  update()
                  updateParentChecked(data.p_id)
                }
              "
              v-model:value="data.radiocheck"
            >
              <a-radio v-for="(item, tindex) in [radiodataInner, radiodataExtra][scope - 1]" :key="tindex" :value="item.value">
                <span>{{ item.title }}</span>
              </a-radio>
            </a-radio-group>
          </div>
        </div>
      </template>
    </a-tree>
    <template #footer>
      <a-button style="margin-right: 10px" type="primary" @click="beforeSubmit">确认</a-button>
      <a-button @click="visible = false">取消</a-button>
    </template>
  </a-drawer>
  <a-modal width="450px" v-model:open="warningrVisible" title="修改角色权限">
    <div>
      <div>已修改该角色的权限，保存后所有使用此角色的用户将被强制退出并需重新登录。</div>
      <div style="margin-top: 12px">请确认无误后再提交修改，是否继续?</div>
    </div>
    <template #footer>
      <a-button type="primary" danger @click="submit">确定</a-button>
      <a-button style="margin-right: 10px" @click="warningrVisible = false">取消</a-button>
    </template>
  </a-modal>
</template>

<script lang="ts" setup>
import TagFilter from '@/components/TagFilter.vue'
import { GetListByRole, SaveAuthRole } from '@/servers/Role'
import { DownOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'

const emits = defineEmits(['query'])

const role_name = ref(null)
const role_id = ref(null)
const scope = ref<any>(null)
const treeDataMaxLevel = ref(0)
const expandedKeys = ref<any[]>([])
const allParentIds = ref<any[]>([])
const needRemoveArr = ref<any[]>([])
const visible = ref(false)
const warningrVisible = ref(false)
const submitArr = ref<any[]>([])
const filterArr = ref<any[]>([])
const activeTag = ref<any[]>([])
const fieldNames = {
  children: 'childrenList',
  title: 'permissions_name',
  key: 'id',
}
const checkedKeys = ref<any[]>([])
const radiodataExtra = ref([
  {
    value: 0,
    title: '本公司',
    scope: false,
  },
  {
    value: 1,
    title: '本部门（含下级）',
    scope: false,
  },
  {
    value: 2,
    title: '仅自己',
    scope: false,
  },
])
const radiodataInner = ref([
  {
    value: 0,
    title: '全部（含外部）',
    scope: false,
  },
  {
    value: 3,
    title: '本公司',
    scope: false,
  },
  {
    value: 1,
    title: '本部门（含下级）',
    scope: true,
  },
  {
    value: 2,
    title: '仅自己',
    scope: false,
  },
])
const treeData = ref<any[]>([])

const checkBoxClick = (data) => {
  if (data.checked) {
    updateParentChecked(data.p_id)
  } else {
    data.radiocheck = null
    updateChildChecked(data)
  }
  update()
}
const update = () => {
  const obj = JSON.parse(JSON.stringify(treeData.value))
  treeData.value = []
  treeData.value = JSON.parse(JSON.stringify(obj))
}
// 递归修改子级状态
const updateChildChecked = (node) => {
  if (node.childrenList && node.childrenList.length > 0) {
    node.childrenList.forEach((child) => {
      child.checked = false
      child.radiocheck = null
      updateChildChecked(child)
    })
  }
}
// 递归修改父级状态
const updateParentChecked = (parentId) => {
  const findParentNode = (nodes, id) => {
    for (const node of nodes) {
      if (node.id === id) {
        return node
      }
      if (node.childrenList && node.childrenList.length > 0) {
        const found = findParentNode(node.childrenList, id)
        if (found) return found
      }
    }
    return null
  }
  const parentNode = findParentNode(treeData.value, parentId)
  if (parentNode) {
    parentNode.checked = true
    updateParentChecked(parentNode.p_id)
  }
}
const addLevelProperty = (nodes: any[], level = 0) => {
  let maxLevel = level
  const updatedNodes = nodes.map((node) => {
    node.checked = false
    checkedKeys.value.forEach((x) => {
      if (x.permission_id === node.id) {
        node.checked = true
        node.radiocheck = x.permissions_range
      }
    })
    const newNode = { ...node, level }
    if (node.childrenList && node.childrenList.length > 0) {
      allParentIds.value.push(node.id)
      const { childrenList, maxLevel: childrenMaxLevel } = addLevelProperty(node.childrenList, level + 1)
      newNode.childrenList = childrenList
      maxLevel = Math.max(maxLevel, childrenMaxLevel)
    } else {
      needRemoveArr.value.push(node.p_id)
    }
    return newNode
  })
  return { childrenList: updatedNodes, maxLevel }
}
const getCheckedNodes = (nodes) => {
  const result: any[] = []
  const traverse = (node) => {
    if (node.checked) {
      result.push({
        permission_id: node.id,
        permissions_range: node.radiocheck,
        has_permissions_range: node.has_permissions_range,
        permissions_name: node.permissions_name,
      })
    }
    if (node.childrenList && node.childrenList.length > 0) {
      node.childrenList.forEach((child) => traverse(child))
    }
  }
  nodes.forEach((node) => traverse(node))
  return result
}
const beforeSubmit = () => {
  let boolean = false
  const arr: any[] = []
  const vilivateArr: any[] = getCheckedNodes(treeData.value)
  vilivateArr.forEach((x) => {
    if (x.has_permissions_range && !x.permissions_range && x.permissions_range != 0) {
      boolean = true
      arr.push(x.permissions_name)
    }
  })
  if (boolean) {
    message.error(`${arr} 未选择数据范围`)
  } else {
    submitArr.value = vilivateArr.map((e) => {
      return {
        permission_id: e.permission_id,
        permissions_range: e.permissions_range,
      }
    })
    warningrVisible.value = true
  }
}
const submit = () => {
  warningrVisible.value = false
  SaveAuthRole({
    role_id: role_id.value,
    permission_param: submitArr.value,
  }).then((res) => {
    if (res.success == true) {
      visible.value = false
      message.success('修改成功')
      emits('query')
    }
  })
}
const opendrawer = (item) => {
  activeTag.value = []
  expandedKeys.value = []
  filterArr.value = []
  treeData.value = []
  role_name.value = item.role_name
  role_id.value = item.id
  scope.value = item.scope
  visible.value = true
  GetListByRole({ role_id: item.id, scope: item.scope }).then((res) => {
    checkedKeys.value = res.data.checkedKeys
    const { childrenList: result, maxLevel } = addLevelProperty(res.data.permissionTreeList)
    treeData.value = JSON.parse(JSON.stringify(result))
    result.forEach((x) => {
      filterArr.value.push({ value: x.id, label: x.permissions_name })
    })
    treeDataMaxLevel.value = maxLevel
    expandedKeys.value = allParentIds.value.filter((item) => ![...new Set(needRemoveArr.value)].includes(item))
    nextTick(() => {
      activeTag.value = [result[0].id]
    })
  })
}
defineExpose({
  opendrawer,
})
</script>

<style lang="scss" scope>
.permissionDrawer {
  * {
    box-sizing: border-box;
  }

  .filterArr {
    padding-left: 10px;
    margin-bottom: 20px;
  }

  .title {
    padding-left: 10px;
    margin-bottom: 20px;
    font-size: 15px;
    font-weight: bold;
    color: #000;
  }

  .description {
    padding-left: 8px;
    font-size: 13px;
    font-weight: normal;
    color: rgb(0 0 0 / 50%);
  }

  .ant-tree-indent-unit {
    width: 40px;
  }

  .ant-tree-show-line .ant-tree-indent-unit::before {
    right: unset;
    left: 12px;
  }

  .textBox {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: 40px;
    padding-right: 40px;
    padding-left: 12px;
    font-size: 15px;
    transition:
      all 0.3s,
      border 0s,
      line-height 0s,
      box-shadow 0s;

    &:hover {
      background-color: rgb(245 245 245);
    }

    .checkBox {
      display: flex;
      flex: 1;
      align-items: center;
      height: 40px;

      .label {
        width: 100%;
        height: 40px;
        font-size: 15px;
        line-height: 40px;
      }
    }

    .permissionBox {
      display: flex;
      align-items: center;
      height: 40px;

      .label {
        margin-right: 12px;
      }
    }
  }

  .ant-tree-show-line .ant-tree-switcher {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .ant-tree .ant-tree-treenode {
    align-items: center;
  }

  .ant-tree-checkbox {
    margin-top: 0;
    margin-left: 12px;
  }

  .ant-tree-switcher-line-icon {
    display: none;
  }

  .treeCheckBox {
    padding-left: 12px;
  }

  .itispage {
    background-color: rgb(245 245 245);
  }

  .ant-checkbox-wrapper {
    flex: 1;
  }

  .ant-radio-wrapper {
    align-items: center;
    height: 40px;
    font-size: 15px;

    .ant-radio {
      top: unset;
    }
  }
}
</style>
