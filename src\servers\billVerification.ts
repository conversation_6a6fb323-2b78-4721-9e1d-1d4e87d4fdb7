import { request } from './request'

// 未对账账单列表
export const GetBillCheckListByUnreconciled = (data) => request({ url: '/api/Bill/GetBillCheckListByUnreconciled', data })

// 未对账单采购明细
export const BuildBillDetailInfoQuery = (data) => request({ url: '/api/Bill/BuildBillDetailInfoQuery', data })

// 未对采购单列表
export const GetPurchaseOrderList = (data) => request({ url: '/api/Bill/GetPurchaseOrderList', data })

// 未对账单列表导出
export const ExportBillCheckListByUnreconciled = (data) => request({ url: '/api/Bill/ExportBillCheckListByUnreconciled', data })

// 生成账单
export const AddBillOrder = (data) => request({ url: '/api/Bill/AddBillOrder', data })

// 编辑生成账单
export const UpdateBillOrder = (data) => request({ url: '/api/Bill/UpdateBillOrder', data })

// 获取已对账账单列表
export const GetBillListResult = (data) => request({ url: '/api/Bill/GetBillListResult', data })

// 已对账单列表导出
export const ExportBillList = (data) => request({ url: '/api/Bill/ExportBillList', data })

// 已对账反审核
export const CounterAuthorization = (data) => request({ url: '/api/Bill/CounterAuthorization', data }, 'GET')

// 已对账删除
export const DeleteBillOrder = (data) => request({ url: '/api/Bill/DeleteBillOrder', data }, 'GET')

// 已对账账单详情
export const GetBillOrder = (data) => request({ url: '/api/Bill/GetBillOrder', data }, 'GET')
