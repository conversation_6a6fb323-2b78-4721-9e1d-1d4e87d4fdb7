<!-- 商品库列表 -->
<template>
  <div class="flex flex-col h-full main">
    <Form ref="formRef" v-model:form="formArr" :page-type="PageTypeEnum.OutboundWarehouseOrder" @search="handleSearch" @setting="tableRef?.showTableSetting()">
      <template #header>
        <StatusTabs v-model:status="status" :options="reservationStatusOption.filter((f) => f.value !== 100)" :count-map="labelStatusCountMap" @change="tableRef?.search()" />
      </template>
    </Form>
    <BaseTable ref="tableRef" :page-type="PageTypeEnum.OutboundWarehouseOrder" v-model:form="formArr" :get-list="GetOutboundWarehouseOrderList" :form-format="formFormat" v-bind="baseTableProps">
      <template #left-btn>
        <a-button v-if="btnPermission[114004]" @click="handleBatchAudit(true)">批量审核通过</a-button>
        <a-button v-if="btnPermission[114004]" @click="handleBatchAudit(false)">批量审核拒绝</a-button>
      </template>
      <template #right-btn>
        <a-button v-if="btnPermission[114001]" @click="handleAddOrder" type="primary">添加委外领料单</a-button>
      </template>

      <template #fix_option="{ row, column }">
        <RightOperate :list="rightOperateList" :row="row" :column="column" />
      </template>
    </BaseTable>
    <AuditConfirm ref="auditConfirmRef" @audit="onAuditConfirm" />
  </div>
</template>
<script lang="ts" setup>
import { message } from 'ant-design-vue'

import { reservationStatusOption } from '@/common/options'

import { GetPurchaseByDeptSelect, GetPageMRPSupplierCompanySelect } from '@/servers/BusinessCommon'
import { GetWarehouses } from '@/servers/Purchaseapplyorder'
import { GetOutboundWarehouseOrderList, BatchAudit } from '@/servers/OutboundWarehouseOrder'

import { PageTypeEnum } from '@/enums/tableEnum'

const { btnPermission } = usePermission()
const { pushPage } = usePage({ refresh: () => tableRef.value.search() })
const { labelStatusCountMap, labelStatusRefresh } = useLabelStatus(PageTypeEnum.OutboundWarehouseOrder)

const formRef = ref()
const tableRef = ref()
const rightOperateList = ref([
  {
    label: '查看',
    show: 114002,
    onClick: ({ row }) => {
      handleRowClick(row, 'view')
    },
  },
  {
    label: '编辑',
    show: ({ row }) => btnPermission.value[114003] && (row.audit_status === AuditStatusEnum.待提审 || row.audit_status === AuditStatusEnum.已拒绝),
    onClick: ({ row }) => {
      handleRowClick(row, 'edit')
    },
  },
  {
    label: '审核',
    show: ({ row }) => row.audit_status === AuditStatusEnum.待审核 && btnPermission.value[114004],
    onClick: ({ row }) => {
      handleRowClick(row, 'review')
    },
  },
])

const formArr = ref([
  { label: '委外领料单编号', value: '', type: 'inputDlg', key: 'number' },
  { label: '采购单编号', value: '', type: 'inputDlg', key: 'purchase_order_number' },
  { label: '聚水潭出库单编号', value: '', type: 'inputDlg', key: 'delivery_number' },
  { label: '商品编码', value: '', type: 'input', key: 'sku_id' },
  { label: '商品名称', value: '', type: 'input', key: 'sku_name' },
  {
    label: '供应商子公司',
    value: null,
    type: 'select-supplier',
    key: 'company_supplier_id',
    mode: 'single',
    api: GetPageMRPSupplierCompanySelect,
    apiParams: { is_contains_srs: true },
  },
  {
    label: '选择仓库',
    type: 'select',
    key: 'warehouse_id',
    api: GetWarehouses,
  },
  {
    label: '选择申请人',
    type: 'select',
    key: 'creator_id',
    api: GetPurchaseByDeptSelect,
  },

  { label: '时间', value: null, type: 'range-picker', key: 'create_at', formKeys: ['start_create_at', 'end_create_at'], placeholder: ['创建开始时间', '创建结束时间'] },
  { label: '采购数量(从)', value: '', type: 'input', key: 'start_purchase_quantity' },
  { label: '采购数量(至)', value: '', type: 'input', key: 'end_purchase_quantity' },
  { label: '本次领料数量(从)', value: '', type: 'input', key: 'start_material_quantity' },
  { label: '本次领料数量(至)', value: '', type: 'input', key: 'end_material_quantity' },
])
useSearchForm(formArr)
const baseTableProps = ref({
  isCheckbox: true,
  checkboxConfig: {
    checkMethod: ({ row }) => {
      return row.audit_status === AuditStatusEnum.待审核
    },
  },
})

const status = ref('')

const formFormat = (data) => {
  labelStatusRefresh()
  return {
    ...data,
    audit_status: status.value || undefined,
    sortField: 'create_at',
  }
}

const handleSearch = () => {
  if (tableRef.value) tableRef.value.search()
}

const handleRowClick = (row, type) => {
  pushPage(`/outboundWarehouseOrderDetails/${type}/${row.id}`, { source: true })
}
const handleAddOrder = () => {
  pushPage(`/outboundWarehouseOrderDetails/new`, { source: true })
}

// 批量审核
const auditConfirmRef = useTemplateRef('auditConfirmRef')
const handleBatchAudit = (type: boolean) => {
  if (tableRef.value?.checkItemsArr?.length === 0) {
    message.warning('请选择要审核的委外领料单')
    return
  }
  auditConfirmRef.value?.open(type)
}
const onAuditConfirm = async (data, close) => {
  const { audit_opinion, is_pass } = data
  const params = {
    is_pass,
    audit_opinion,
    ids: tableRef.value?.checkItemsArr.map((item) => item.id),
  }
  const res = await BatchAudit(params)
  if (res.success) {
    message.success('操作成功')
    close()
    tableRef.value.search()
  } else {
    message.error(res.message)
  }
}
</script>
