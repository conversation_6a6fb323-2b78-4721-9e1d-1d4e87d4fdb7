export const useEasyTable = (columns) => {
  const setWidth = (item) => {
    const title = item.title || item.name
    if (item.width) return typeof item.width === 'string' ? item.width.replace(/[^\d]/g, '') : item.width
    if (/状态|标准装箱数|单位|换算值/.test(title)) return 80
    if (/主图/.test(title)) return 70
    if (/操作/.test(title)) return 100
    if (/数量|金额/.test(title)) return 100
    if (/时间|日期/.test(title)) return 150
    return undefined
  }
  const initColumns = (list) => {
    for (const column of list) {
      const key = column.key || column.field
      const title = column.title || column.name

      column.width = setWidth(column)
      column.minWidth = Math.min(80, column.width || 80)
      column.is_show = typeof column.is_show === 'boolean' ? column.is_show : true

      if (/数量|金额/.test(title)) column.align = 'right'

      if (!column.cellRender) {
        switch (key) {
          case 'image_url':
            column.cellRender = { name: 'image' }
            column.showOverflow = false
            break
          case 'image_urls':
            column.cellRender = { name: 'imageList' }
            column.showOverflow = false
            break

          default:
            break
        }
      }
    }
  }

  onMounted(() => {
    initColumns(columns.value)
  })

  return {
    initColumns,
  }
}
