// 供应商状态
export enum SupplierStatusEnum {
  待确认 = 1,
  已确认 = 2,
}

// 通用审核状态
export enum AuditStatusEnum {
  待提审 = 10,
  待审核 = 20,
  已通过 = 90,
  已拒绝 = 95,
  已完成 = 100,
}

// 推单方式
export enum TakeOrderMethodEnum {
  自动推单 = 0,
  手动关联 = 1,
}

export enum RecommendOrderOperationTypeEnum {
  绑定订单 = 1,
  取消绑定 = 2,
}

export enum TakeOrderTypeEnum {
  默认 = 2,
  订金 = 1,
}

// 商品类型
export enum productTypeEnum {
  成品 = 1,
  半成品 = 2,
  原材料 = 3,
  包材 = 4,
}

// 委外领料单订单状态
export enum OutboundWarehouseOrderStatusEnum {
  进行中 = 1,
  已完成 = 2,
  已取消 = 3,
}

// 操作日志页面类型
export enum OpLogPageTypeEnum {
  系统 = 0,
  登录 = 1,
  角色管理 = 2,
  用户管理 = 3,
  用户管理_外部联系人 = 301,
  用户管理_内部联系人 = 302,
  供应商审核 = 4,
  供应商库 = 5,
  供应商子公司库 = 6,
  采购单管理 = 7,
  采购单变更 = 8,
  应付单 = 9,
  预约入库 = 12,
  账单对账 = 10,
}

// 结算方式
export enum SettlementTypeEnum {
  月结 = 1,
  半月结 = 2,
  周结 = 3,
  预付 = 4,
  线上付款 = 5,
  以销代结 = 6,
}

// 供应商类型
export enum SupplierTypeEnum {
  线下供应商 = 1,
  '1688线上供应商' = 2,
}

// 付款状态
export enum PaymentOrderStatusEnum {
  进行中 = 1,
  已完成 = 2,
}

// 打款状态
export enum PaymentStatusEnum {
  未打款 = 1,
  已打款 = 2,
}

// 商品类型
export enum ProductTypeEnum {
  成品 = 1,
  半成品 = 2,
  原材料 = 3,
  包材 = 4,
}

// 退库原因
export enum ReturnReasonTypeEnum {
  采购退货 = 10,
  质检退货 = 20,
}

// 付款类型
export enum PaymentTypeEnum {
  预付款 = 10,
  应付付款 = 20,
  虚拟付款 = 30,
}

// 付款单来源类型
export enum PaymentOrderSourceTypeEnum {
  新增预付款 = 1,
  应付单 = 2,
}

// 上传模块枚举
export enum UploadFileModuleEnum {
  /** 默认模块 */
  Common = 0,
  /** 付款单 */
  PaymentOrder = 10,
  /** 付款申请单 */
  PaymentApply = 11,
  /** 供应商上传 */
  Supplier = 20,
  /** OA供应商 */
  OaSupplier = 21,
  /** 采购价格 */
  PurchaseOrder = 30,
  /** 预约入库单 */
  ReservationOrder = 40,
  /** 应付单 */
  BillPayable = 12,
}

// 采购单 & 变更单 审核状态
export enum BookingOrderAuditStatusEnum {
  待提审 = 10,
  待一级审核 = 20,
  待二级审核 = 30,
  待三级审核 = 40,
  待四级审核 = 50,
  已通过 = 90,
  已拒绝 = 95,
  已完成 = 100,
  已作废 = 200,
}

// 供应商来源
export enum SupplierSourceTypeEnum {
  SRM = 1,
  OA = 2,
  SRS = 3,
}

export enum InvoiceStatusEnum {
  待上传 = 1,
  待审核 = 2,
  审核通过 = 3,
}

export enum PaymentOrderAuditStatusEnum {
  待提审 = 10,
  部门主管审核 = 20,
  部门经理审核 = 21,
  部门总监审核 = 22,
  会计审核 = 30,
  财务一级审核 = 40,
  财务二级审核 = 41,
  CEO审核 = 50,
  待FMS处理 = 60,
  会计复审 = 70,
  已通过 = 90,
  已拒绝 = 95,
}

export enum AliProductMapMatchStatusEnum {
  未匹配 = 1,
  已匹配 = 2,
}

export enum BillSettlementStatusEnum {
  未对账 = 0,
  未结算 = 1,
  部分结算 = 2,
  已结算 = 3,
}

// 采购申请单来源
export enum FinishedApplyOrderSourceTypeEnum {
  内部生成 = 1,
  常规备货 = 10,
  SRS常规 = 11,
  销售备货 = 20,
  PLM新品 = 30,
  SRS新品 = 31,
  PLM定制 = 40,
  SRS定制 = 41,
}

// 发货状态
export enum ShipmentStatusEnum {
  未发货 = 10,
  部分发货 = 20,
  全部发货 = 30,
}
