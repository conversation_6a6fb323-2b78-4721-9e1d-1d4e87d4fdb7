import { request } from './request'
// 编辑用户
export const SetUserDate = (data) => {
  return request({ url: '/api/UserManager/Update', data })
}
// 获取用户列表
export const GetUserList = (data) => {
  return request({ url: '/api/UserManager/List', data })
}

// 获取内部用户列表
export const GetInnerList = (data) => {
  return request({ url: '/api/UserManager/GetInnerList', data })
}
// 获取外部用户列表
export const GetExtraList = (data) => {
  return request({ url: '/api/UserManager/GetExtraList', data })
}
// 新增用户
export const Add = (data) => {
  return request({ url: '/api/UserManager/Add', data })
}
// 编辑内部用户
export const UpdateInner = (data) => {
  return request({ url: '/api/UserManager/UpdateInner', data })
}
// 编辑外部用户
export const UpdateExtra = (data) => {
  return request({ url: '/api/UserManager/UpdateExtra', data })
}
// 修改密码
export const UpdatePwd = (data) => {
  return request({ url: '/api/UserManager/UpdatePwd', data })
}
// 批量停用/启用内部用户
export const UpdateInnerStatus = (data) => {
  return request({ url: '/api/UserManager/UpdateInnerStatus', data })
}
// 批量停用/启用外部用户
export const UpdateExtraStatus = (data) => {
  return request({ url: '/api/UserManager/UpdateExtraStatus', data })
}
// 删除外部用户
export const Delete = (data) => {
  return request({ url: '/api/UserManager/Delete', data })
}
// 获取部门树状下拉框
export const GetDepartmentTreeList = (data?: any) => {
  return request({ url: '/api/UserManager/GetDepartmentTreeList', data })
}
// 重置密码
export const ResetPwd = (data) => {
  return request({ url: '/api/UserManager/ResetPwd', data })
}

// 获取供应商列表
export const GetSupplierList = (data) => {
  return request({ url: '/api/UserModule/GetSupplierList', data })
}

// 获取用户供应商列表
export const GetUserSupplierList = (data) => {
  return request({ url: '/api/UserModule/GetUserSupplierList', data })
}
// 获取外部用户对应的部门名称
export const GetUserDeptNameList = (data) => {
  return request({ url: '/api/UserModule/GetUserSupplierDepartmentList', data })
}

// 获取内部用户详情
export const DetailsInner = (data) => {
  return request({ url: '/api/UserManager/DetailsInner', data })
}
// 获取外部用户详情
export const DetailsExtra = (data) => {
  return request({ url: '/api/UserManager/DetailsExtra', data })
}

// 获取外部用户详情
export const DetailsExtraByEdit = (data) => {
  return request({ url: '/api/UserModule/GetUserById', data }, 'GET')
}
// 获取客户下拉框
export const GetCustomerSelectOption = () => {
  return request({ url: '/api/UserModule/GetCustomerSelectOption' })
}
// 获取部门下拉框
export const GetDepartmentSelectOption = (data) => {
  return request({ url: '/api/UserModule/GetDeptBySupplier', data }, 'GET')
}
// 获取角色下拉框
export const GetRoleSelectOption = (data) => {
  return request({ url: '/api/UserModule/GetRoleSelectOption', data })
}

// 获取内部用户详情
export const DetailsInnerByEdit = (data) => {
  return request({ url: '/api/UserModule/DetailsInnerByEdit', data })
}

// 获取公司列表
export const GetCompanyList = () => {
  return request({ url: '/api/UserManager/GetSubCompanyOptionsList' })
}

// 获取用户信息
export const GetUserInfo = (data) => {
  return request({ url: '/api/UserModule/GetUserById', data }, 'GET')
}
