<template>
  <a-drawer :maskClosable="false" title="按链接添加映射" width="60vw" :visible="visible" @close="visible = false" :bodyStyle="{ padding: '0' }">
    <div class="flex h-full">
      <div class="w-full p-16 flex flex-col">
        <a-form :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }" :model="form" ref="formRef">
          <a-row>
            <a-col :span="12">
              <a-form-item label="1688商品链接" name="ali_product_url" :rules="[{ required: true }]">
                <a-input v-model:value="form.ali_product_url" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="" name="name">
                <a-button class="ml-10" type="primary" @click="getDetailList">解析链接</a-button>
                <a-button type="primary" class="ml-10px" @click="clearMapBtn">清空映射</a-button>
              </a-form-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="12">
              <a-form-item label="系统供应商子公司" name="company_supplier_id" :rules="[{ required: true, message: '请选择系统供应商子公司' }]">
                <a-select
                  v-model:value="form.company_supplier_id"
                  placeholder="请选择供应商子公司"
                  :filter-option="filterOption"
                  show-search
                  clearable
                  :options="supplierOptions"
                  @change="(e) => changeSupper(e, form)"
                />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
        <div class="drawer-title">商品信息</div>
        <a-space class="mb-12" :size="16">
          <a-input placeholder="颜色规格" v-model:value="queryForm.ali_type_specification" :maxlength="200" />
          <a-button type="primary" @click="filterDetailList(queryForm.ali_type_specification)">查询</a-button>
          <a-button type="primary" @click="filterDetailList()">重置</a-button>
        </a-space>
        <div class="w-full flex-1">
          <SimpleTable ref="SimpleTableRef" :table-key="tableColumns" :data="detailList">
            <template #sku_id="{ row }">
              <a-input :value="row.productList.map((f) => f.sku_id).join(',')" readonly @focus="openSysProductDlg(row)" clearable addon-after="..." :placeholder="'请点击'" />
            </template>
            <!-- <template #ali_image_url="{ row }">
              <a-image :width="50" :src="row.ali_image_url" :preview="{ maskClassName: '预览' }" referrerpolicy="no-referrer" />
            </template> -->
            <template #srm_purchase_quantity="{ row }">
              <a-input-number v-model:value="row.srm_purchase_quantity" :disabled="!row.productList?.length" :precision="0" :min="1" :max="99999" />
            </template>
            <template #ali_purchase_quantity="{ row }">
              <a-input-number v-model:value="row.ali_purchase_quantity" :disabled="!row.productList?.length" :precision="0" :min="1" :max="99999" />
            </template>
          </SimpleTable>
        </div>
      </div>
    </div>
    <template #footer>
      <a-space :size="16">
        <a-button type="primary" @click="updateBtn">确认</a-button>
        <a-button @click="visible = false">取消</a-button>
      </a-space>
    </template>
    <!-- <SysProductDlg ref="SysProductDlgRef" @selectProduct="selectProduct" /> -->
    <SelectProduct ref="selectProductRef" @confirm="selectProduct"></SelectProduct>
  </a-drawer>
</template>

<script setup lang="ts">
import { message } from 'ant-design-vue'

import { filterOption } from '@/utils/index'
import SimpleTable from '@/components/SimpleTable.vue'

// import SysProductDlg from './SysProductDlg.vue'

import { GetAli1688ProductList, AddAliProductMapInfo, GetAliProductMapInfoByUrlAndSupplierCompanyId, UpdateAliProductMapInfo } from '@/servers/ProductMapping'
import { Get1688MRPSupplierCompanySelect } from '@/servers/BusinessCommon'

enum SystemDockingDataStatusEnum {
  新增匹配 = 1,
  修改匹配 = 2,
  删除匹配 = 3,
}

const emit = defineEmits(['search'])

const formRef = ref()
// 表单
const form = ref<any>({
  ali_product_url: null,
  company_supplier_id: null,
  ali_seller_login_id: null,
  style_code: null,
  ali_style_code: null,
  ali_sku_name: null,
  brand: null,
})

const queryForm = ref({
  ali_type_specification: '',
})

const tableColumns = ref([
  // { title: '系统款式编号', field: 'style_code', width: '' },
  { title: '系统商品编码', field: 'sku_id', width: 200 },
  { title: '1688商品编码', field: 'ali_sku_id', width: 110 },
  { title: '1688商品图片', field: 'ali_image_url', width: 100, cellRender: { name: 'image' } },
  { title: '1688商品名称', field: 'ali_sku_name' },
  { title: '1688颜色规格', field: 'ali_type_specification' },
  { title: 'SRM系统数量', field: 'srm_purchase_quantity', width: 110 },
  { title: '1688采购数量', field: 'ali_purchase_quantity', width: 110 },
])

const detailList = ref<any[]>([])
const detailListCache = ref<any[]>([])
const deleteListCache = ref<Record<string, any>>([])
// 显示
const visible = ref(false)

const saveId = ref()

const supplierOptions = ref<any[]>([])
const submitType = ref()
const updateId = ref()
// 获取供应商
const getSupplierList = async () => {
  const res = await Get1688MRPSupplierCompanySelect({ type: 2 })
  supplierOptions.value = res.data.map((i) => ({ label: i.label, value: +i.value }))
}

const changeSupper = (e, item) => {
  item.supplier = supplierOptions.value.find((titem) => titem.value === item.company_supplier_id)?.label
  queryForm.value.ali_type_specification = ''
  detailList.value = []
  detailListCache.value = []
  deleteListCache.value = {}
}

const filterDetailList = (val = '') => {
  if (!val) {
    queryForm.value.ali_type_specification = ''
  }
  detailList.value = detailListCache.value.filter((v) => (v.ali_type_specification || '').indexOf(val) != -1)
}
const aliDataFormatter = (item) => {
  const result = {}
  const keys = ['ali_sku_id', 'ali_type_specification', 'ali_image_url', 'ali_supplier_login_id', 'ali_purchase_quantity', 'ali_start_quantity', 'srm_purchase_quantity', 'ali_sku_name']
  for (const key of keys) {
    if (key in item) {
      result[key] = item[key]
    }
  }
  return result
}
const getDetailList = () => {
  if (!form.value.ali_product_url) {
    message.info('请先输入1688商品链接！')
    return
  }
  if (!form.value.company_supplier_id) {
    message.info('请先选择系统供应商子公司')
    return
  }
  const obj = { ali_product_url: form.value.ali_product_url }
  GetAli1688ProductList(obj).then(async (res) => {
    if (res.success) {
      detailList.value = []
      detailListCache.value = []
      deleteListCache.value = {}
      form.value.ali_sku_name = res.data.ali_sku_name
      form.value.brand = res.data.brand
      form.value.ali_style_code = res.data.ali_style_code
      form.value.style_code = res.data.style_code
      res.data.ali_1688_product_map_detail_list.forEach((item) => {
        const obj = {
          company_supplier_id: form.value.company_supplier_id,
          style_code: res.data.style_code,
          ali_style_code: res.data.ali_style_code,
          ali_sku_name: res.data.ali_sku_name,
          ...aliDataFormatter(item),
          productList: [],
          srm_purchase_quantity: null,
          ali_purchase_quantity: null,
        }
        detailListCache.value.push(obj)
      })

      const { data, success, ...other } = await GetAliProductMapInfoByUrlAndSupplierCompanyId({ ali_product_url: form.value.ali_product_url, company_supplier_id: form.value.company_supplier_id })
      if (!success) return message.error(other.message)
      const count = data?.aliProductMapDetails.length
      submitType.value = count ? 'update' : 'add'
      if (count) {
        updateId.value = data.id
        for (const item of data.aliProductMapDetails) {
          const index = detailListCache.value.findIndex((f) => f.ali_sku_id === item.ali_sku_id)
          if (~index) {
            detailListCache.value[index].srm_purchase_quantity = item.srm_purchase_quantity
            detailListCache.value[index].ali_purchase_quantity = item.ali_purchase_quantity
            detailListCache.value[index].productList.push({
              id: item.id,
              sku_name: item.sku_name,
              sku_id: item.k3_sku_id,
              jst_sku_id: item.jst_sku_id,
              type_specification: item.type_specification,
            })
          }
        }
      }
      await filterDetailList('')
    } else {
      message.error(res.message)
    }
  })
}

const selectProductRef = ref()
const openSysProductDlg = (row) => {
  if (!form.value.company_supplier_id) {
    message.info('请先选择系统供应商子公司')
    return
  }

  // 根据ali_sku_id匹配出不可选商品
  const unSelectSkuIds = detailListCache.value.reduce((acc, cur) => {
    if (cur.ali_sku_id !== row.ali_sku_id) {
      return [...acc, ...cur.productList.map((f) => f.sku_id)]
    }
    return acc
  }, [])
  console.log('unSelectSkuIds', unSelectSkuIds)

  saveId.value = row.ali_sku_id

  selectProductRef.value.open(
    {
      search: ['商品编号/聚水潭编号', '商品名称'],
      left: ['商品编码', '商品名称', '规格型号', '计价单位', '聚水潭编号', '默认供应商'],
      right: ['商品编码', '商品名称'],
      params: { company_supplier_id: form.value.company_supplier_id },
      checkMethod: ({ row }) => {
        return !unSelectSkuIds.includes(row.sku_id)
      },
    },
    // 当前行选中的sku_id
    row.productList,
  )
}

const selectProduct = (arr) => {
  const index = detailListCache.value.findIndex((f) => f.ali_sku_id === saveId.value)
  const list = detailListCache.value[index].productList
  // 将删除的sku_id暂存到delete数组
  const newSkuIds = arr.map((f) => f.sku_id)
  const oldSkuIds = list.filter((f) => f.id).map((f) => f.sku_id)
  const deleteIds = oldSkuIds.filter((id) => !newSkuIds.includes(id))
  if (!deleteListCache.value[saveId.value]) deleteListCache.value[saveId.value] = []
  for (const key of deleteIds) {
    const deleteItem = list.find((f) => f.sku_id === key)
    deleteListCache.value[saveId.value].push(deleteItem)
  }
  // push处理
  detailListCache.value[index].productList = arr.map((item) => {
    let id = list.find((f) => f.sku_id === item.sku_id)?.id
    const foundDeleteIndex = deleteListCache.value[saveId.value].findIndex((f) => f.sku_id === item.sku_id)
    if (~foundDeleteIndex) {
      const [deleteItem] = deleteListCache.value[saveId.value].splice(foundDeleteIndex, 1)
      id = deleteItem.id
    }
    const data_status: SystemDockingDataStatusEnum = id ? 2 : 1
    return {
      ...item,
      id,
      data_status,
    }
  })
  console.log('detailListCache.value.', detailListCache.value)
  console.log('deleteListCache.value.', deleteListCache.value)
}

const clearMapBtn = () => {
  detailListCache.value.forEach((item) => {
    item.productList = []
    item.srm_purchase_quantity = null
    item.ali_purchase_quantity = null
  })
  deleteListCache.value = {}
  form.value.sku_name = null
  filterDetailList()
}

const checkDetailList = () => {
  return detailList.value.some((item, index) => {
    if (item.productList?.length && (!item.srm_purchase_quantity || !item.ali_purchase_quantity)) {
      message.error(`第${index + 1}行的SRM系统数量和1688采购数量不能为空`)
      return true
    }
    if (item.productList?.length && !(item.srm_purchase_quantity == 1 || item.ali_purchase_quantity == 1)) {
      message.error(`第${index + 1}行的SRM系统数量和1688采购数量至少有一个为1`)
      return true
    }
    return false
  })
}

const checkNullList = () => {
  const hasMapArr = detailList.value.some((f) => f.productList.length)
  if (!hasMapArr) {
    message.error(`最少需要映射一个商品`)
    return true
  }
  return false
}

// const addBtn = async () => {
//   filterDetailList()
//   const isErrorValue = checkDetailList()
//   const isErrorNull = checkNullList()
//   if (!isErrorValue && !isErrorNull) {
//     try {
//       await formRef.value?.validateFields()
//       form.value.aliProductMapDetails = detailListCache.value.reduce((acc, cur) => {
//         const productList = cur.productList.map((f) => ({
//           ...aliDataFormatter(cur),
//           ...f,
//         }))
//         return [...acc, ...productList]
//       }, [])
//       const obj = JSON.parse(JSON.stringify(form.value))
//       AddAliProductMapInfo(obj).then(() => {
//         message.success('添加映射成功！')
//         visible.value = false
//         emit('search')
//       })
//     } catch (errorInfo) {
//       console.log('Failed:', errorInfo)
//     }
//   }
// }

const updateBtn = async () => {
  const type = submitType.value
  const isErrorValue = checkDetailList()
  const isErrorNull = checkNullList()
  if (!isErrorValue && !isErrorNull) {
    try {
      await formRef.value?.validateFields()
      const list = detailList.value.reduce((acc, cur) => {
        const productList = cur.productList
          .filter((v) => v.id || (!v.id && v.sku_id))
          .map((f) => ({
            ...aliDataFormatter(cur),
            ...f,
            k3_sku_id: f.sku_id,
            data_status: !f.id ? 1 : 2,
          }))
        const deleteList = (deleteListCache.value[cur.ali_sku_id] || []).map((f) => ({
          ...aliDataFormatter(cur),
          ...f,
          k3_sku_id: f.sku_id,
          data_status: 3,
        }))
        return [...acc, ...productList, ...deleteList]
      }, [])
      const params = {
        aliProductMapDetails: list,
      }
      console.log('params', params)
      // if (params) return
      const api = type === 'update' ? UpdateAliProductMapInfo : AddAliProductMapInfo

      api({
        ...(type !== 'update' ? form.value : {}),
        ...params,
        id: updateId.value,
      }).then(() => {
        message.success('添加映射成功！')
        visible.value = false
        emit('search')
      })
    } catch (errorInfo) {
      console.log('Failed:', errorInfo)
    }
  }
}

// 打开
const open = async () => {
  formRef.value?.resetFields()
  getSupplierList()
  form.value = {
    ali_product_url: null,
    company_supplier_id: null,
    ali_seller_login_id: null,
    style_code: null,
    ali_style_code: null,
    ali_sku_name: null,
    brand: null,
  }
  queryForm.value = {
    ali_type_specification: '',
  }
  detailList.value = []
  detailListCache.value = []
  visible.value = true
}

defineExpose({
  open,
})
</script>

<style scoped lang="scss"></style>
