<template>
  <div class="flex justify-between items-center mb-8" v-if="!props.hideTop">
    <div>
      <a-space>
        <div class="selected-box" v-if="checkItemsArr.length">
          <span class="mr-8">
            已选
            <span class="selected-text">{{ checkItemsArr.length }}</span>
            项数据
          </span>
          <span class="selected-text cursor-pointer" @click="clearCheckbox">清空</span>
        </div>
        <slot name="left-btn"></slot>
      </a-space>
    </div>
    <a-space>
      <slot name="right-btn"></slot>
      <LineHeightSetter v-if="showLineHeightSetter" v-model:type="lineHeightType" @change="setTableConfig(tableKey, props.pageType, lineHeightType)" />
    </a-space>
  </div>
  <div class="tableBox">
    <div class="box" v-show="tableVisble" :class="[...boxCls]">
      <vxe-table
        class="tableBoxwidth"
        :loading="tableLoading"
        :border="true"
        ref="tableRef"
        size="mini"
        :row-config="{
          keyField: keyField,
          isHover: true,
          height: lineHeightMap[lineHeightType],
        }"
        :seq-config="seqConfig"
        :custom-config="{ mode: 'popup' }"
        :data="tableData"
        :show-overflow="true"
        :show-header-overflow="true"
        :show-footer-overflow="true"
        :virtual-x-config="{
          enabled: virtualX,
          gt: 0,
        }"
        height="100%"
        :column-config="{ resizable: true }"
        :checkbox-config="{
          reserve: true,
        }"
        @sort-change="sortChangeEvent"
        @checkbox-all="selectChangeEvent"
        @checkbox-change="selectChangeEvent"
        @resizable-change="columnResizableChangeEvent"
        @toggleRowExpand="onToggleRowExpand"
        :stripe="stripe"
        :expandConfig="expandConfig"
        :footer-data="footerData"
        :sort-config="{ remote: true }"
        :scroll-y="{
          enabled: true,
          mode: 'wheel',
          oSize: 10,
          gt: 11115,
          scrollToTopOnChange: true,
        }"
        v-bind="{
          showFooter: totalField?.length > 0,
          ...$attrs,
        }"
      >
        <slot name="column">
          <vxe-column v-if="isCheckbox" type="checkbox" field="checkbox" width="45" fixed="left" align="center"></vxe-column>
          <vxe-column v-if="isIndex" type="seq" title="序号" field="seq" width="50" align="center" fixed="left"></vxe-column>
          <slot name="append"></slot>

          <template v-for="i in tableKey" :key="i.key">
            <vxe-column
              :visible="i.is_show"
              :field="i.key"
              :title="i.name"
              :sortable="i.is_sort"
              :width="i.width"
              :fixed="i.freeze == 1 ? 'left' : i.freeze == 2 ? 'right' : ''"
              :formatter="i.formatter"
              :header-class-name="i.headerMoreList ? 'has-more' : ''"
              v-bind="i"
            >
              <template #header>
                <i v-if="i?.type === 'expand'" class="cursor-pointer text-[#999999]" :class="isExpand ? 'vxe-icon-caret-down' : 'vxe-icon-caret-right'" @click="() => handleExpand()" />
                {{ i.name || i.title }}
                <a-tooltip v-if="i.tooltip">
                  <template #title>{{ i.tooltip }}</template>
                  <QuestionCircleOutlined class="cursor-pointer text-[#C0C0C0] ml-4 text-13px" />
                </a-tooltip>

                <a-dropdown v-if="i.headerMoreList" :trigger="['click']" destroyPopupOnHide>
                  <MoreOutlined class="link-default text-14px" />
                  <template #overlay>
                    <a-menu>
                      <a-menu-item v-for="menuItem in headerMoreFormat(i.headerMoreList)" :key="menuItem.value" @click="() => menuItem.click({ column: i, data: tableData })">
                        {{ menuItem.label }}
                      </a-menu-item>
                    </a-menu>
                  </template>
                </a-dropdown>
              </template>

              <template v-if="$slots[i.slot] || $slots[i.key]" #default="attr">
                <slot :name="i.slot || i.key" v-bind="attr" :item="i" :rowHeight="lineHeightMap[lineHeightType]" />
              </template>

              <template #content="{ row }" v-if="i.type === 'expand'">
                <DivGrid v-bind="expandTableConfigMap[row[keyField]]"></DivGrid>
              </template>
            </vxe-column>
          </template>
        </slot>
      </vxe-table>
    </div>
  </div>
  <div class="flex items-center justify-between w-full flex-wrap-reverse" v-if="!hidePagination">
    <div class="flex items-center mt-10">
      <a-pagination
        show-quick-jumper
        :total="total"
        :show-total="(total) => `共 ${total} 条`"
        show-size-changer
        :current="page"
        v-model:page-size="pageSize"
        :page-size-options="pageSizeOptions"
        @showSizeChange="onShowSizeChange"
        @change="onChange"
        size="small"
      />
    </div>
    <div class="min-h-20 flex items-center mt-10">
      <slot name="pagination-left"></slot>
    </div>
  </div>

  <TableSetting v-model:columns="tableKey" v-model:visible="visible" v-model:form-arr="form" @save="handleSaveTableSetting" @reset="handleResetTableSetting" :page-type="pageType" />
</template>
<script lang="ts" setup>
import { QuestionCircleOutlined, MoreOutlined } from '@ant-design/icons-vue'
import { VxeTableInstance, VxeToolbarInstance, VxeTablePropTypes } from 'vxe-table'
import { debounce } from 'lodash'

import { checkFormParams, initTable, sumNum, tableWColumnWidthChange, setTableConfig, flattenTableColumn, copyText } from '@/utils/index'
import TableSetting from '@/components/TableSetting.vue'

import LineHeightSetter from './LineHeightSetter.vue'

const props = defineProps({
  title: String,
  getList: {
    type: Function,
  },
  formFormat: Function,
  dataFormat: Function,
  keyField: { default: 'id' },
  pageType: {
    type: Number,
    // required: true,
  },
  tableCb: Function,
  isCheckbox: { default: false },
  isIndex: { default: false },
  stripe: { default: true },
  checkCb: Function,
  totalField: {
    type: Array as PropType<any[]> | null,
    default: null as any,
  },
  autoSearch: {
    type: Boolean,
    default: true,
  },
  virtualX: {
    type: Boolean,
    default: true,
  },
  showLineHeightSetter: {
    type: Boolean,
    default: true,
  },
  hideTop: {
    type: Boolean,
    default: false,
  },
  tableColumns: {
    type: Array as PropType<any[]> | null,
    default: null,
  },
  data: {
    type: Array as PropType<any[]> | null,
    default: null,
  },
  hidePagination: {
    type: Boolean,
    default: false,
  },
  clearCheckboxFlag: {
    type: Boolean,
    default: true,
  },
  boxCls: {
    type: Array as PropType<string[]>,
    default: () => [],
  },
  expandColumns: {
    type: Array as PropType<Record<string, any>[]>,
    default: () => [],
  },
  expandUnHeader: {
    type: Boolean,
    default: false,
  },
  expandData: {
    type: [Function],
  },
  expandRowHeight: {
    type: Number,
    default: 34,
  },
  expandCellMinWidth: {
    type: Number,
    default: 70,
  },
  expandColumnRenderMap: {
    type: Object,
    default: () => ({}),
  },
  pageSizeOptions: {
    default: () => ['20', '50', '100', '250', '500'],
  },
  loading: {
    type: Boolean,
    default: false,
  },
  expandStateKey: String,
})
const emit = defineEmits(['loadData', 'resizableChange'])

const tableLoading = ref(props.loading)
const tableVisble = ref(false)
const toolbarRef = ref<VxeToolbarInstance>()
const tableRef = ref<VxeTableInstance>()
const pageSize = ref(20)
const total = ref(0)
const page = ref(1)
const tableData = ref()

const lineHeightType = ref(1)

const lineHeightMap = {
  1: 36,
  2: 56,
  3: 76,
}

const form = defineModel('form', { type: Array as PropType<any[]>, default: [] })

const tableKey = ref([] as any)

const visibleData = reactive({
  isShow: false,
  isConfirmBtn: true,
  isCancelBtn: true,
  confirmBtnText: '确定',
  title: '',
  okType: 'primary',
  content: '',
  okFn: () => {
    visibleData.isShow = false
  },
})
const ordersort = ref<string | boolean | null>(null)
const orderby = ref<string | null>(null)

watch(
  () => props.tableColumns,
  () => {
    initTable(props.pageType, tableRef.value, tableKey, props.tableColumns, lineHeightType, false).then(() => {
      tableVisble.value = true
    })
  },
)

watch(
  () => props.data,
  debounce((data) => {
    console.log('refreshData')
    tableData.value = data
    tableLoading.value = false
    tableRef.value?.loadData(data)
  }, 200),
  { deep: true },
)

const updateLoading = (bol: boolean) => {
  tableLoading.value = bol
}

onMounted(() => {
  const $table = tableRef.value
  const $toolbar = toolbarRef.value
  if ($table && $toolbar) {
    $table.connect($toolbar)
  }
  initTable(props.pageType, tableRef.value, tableKey, props.tableColumns, lineHeightType, false).then(() => {
    tableVisble.value = true
    if (props.autoSearch) tapQueryForm()
  })
})

const seqConfig = {
  seqMethod({ rowIndex }) {
    return (page.value - 1) * pageSize.value + rowIndex + 1
  },
}

const search = async () => {
  if (props.clearCheckboxFlag) {
    clearCheckbox()
  }
  total.value = 0
  await tapQueryForm()
}
// 查询
const tapQueryForm = (params: { keepPage?: boolean } = {}) => {
  return new Promise((resolve) => {
    const obj = {
      page: params?.keepPage ? page.value : 1,
      pagesize: pageSize.value,
    }
    total.value = 0
    getCustomerList(checkFormParams({ formArr: form.value, obj }), resolve)
  })
}
// 分页数量的变化
const onShowSizeChange = () => {
  // pageChange()
}
// 跳转第几页
const onChange = (i) => {
  pageChange(i)
}

const pageChange = (page) => {
  return new Promise((resolve) => {
    const obj = {
      page,
      pagesize: pageSize.value,
    }
    total.value = 0
    getCustomerList(checkFormParams({ formArr: form.value, obj }), resolve) // 校验完传入回调 -> 回调执行请求接口
  })
}
// table表头排序
const sortChangeEvent = ({ sortList }) => {
  orderby.value = null
  ordersort.value = null
  if (sortList.length != 0) {
    sortList.forEach((x) => {
      orderby.value = x.field
      ordersort.value = x.order === 'asc'
    })
  }

  clearCheckbox()
  tapQueryForm()
}
// 查询表格数据
const getCustomerList = (obj, cb) => {
  obj.sortField = orderby.value
  obj.sortType = ordersort.value ? 'asc' : 'desc'
  if (!props.getList) {
    tableLoading.value = false
    emit('loadData', [], {
      $table: tableRef.value,
    })
    return
  }
  if (props.formFormat) obj = props.formFormat(obj)
  tableLoading.value = true
  props
    .getList(obj)
    .then((res) => {
      const data = res.data.data || res.data.list || res.data.datas.list
      tableData.value = (props.dataFormat ? props.dataFormat(data) : data) || []
      total.value = res.data.total || res.data.datas.total
      page.value = obj.page
      if (props.totalField?.length) statTotal(tableData.value)
      nextTick(() => {
        cb && cb()
        // const expandRecordKeys = tableRef.value?.getRowExpandRecords().map((row) => row[props.keyField]) || []

        // for (const key of expandRecordKeys) {
        //   const row = data.find((f) => f[props.keyField] === key)
        //   expandTableConfigMap.value[row[props.keyField]] = {
        //     ...expandTableConfigMap.value[row[props.keyField]],
        //     data: props?.expandData ? props.expandData({ row }) : [],
        //   }
        // }
        const expandFlag = (props.expandStateKey && localStorage.getItem(props.expandStateKey)) || false

        handleExpand(expandFlag === '1')
        emit('loadData', tableData.value)
      })
      if (props.tableCb) {
        setTimeout(() => {
          props.tableCb && props.tableCb()
        }, 300)
      }
    })
    .finally(() => {
      tableLoading.value = false
    })
}
// 表格设置弹窗显示/隐藏
const visible = ref(false)
// 显示表格设置弹窗
const showTableSetting = () => {
  visible.value = true
}

const checkItemsArr = ref<any[]>([])
const selectChangeEvent = () => {
  const $table = tableRef.value
  // 当前页选中的数据
  const currentSelectedRows = $table?.getCheckboxRecords() || []
  // 其他页选中的数据
  const otherSelectedRows = $table?.getCheckboxReserveRecords() || []
  // 如果有自定义选择回调，优先使用自定义逻辑
  if (props.checkCb) {
    // 调用自定义选择回调，让父组件处理选择逻辑
    props.checkCb(currentSelectedRows, otherSelectedRows)
    return
  }
  // 默认选择逻辑
  checkItemsArr.value = [...currentSelectedRows, ...otherSelectedRows]
  // if (props.checkCb) {
  //   props.checkCb(checkItemsArr.value)
  // }
  if (props.totalField?.length) statTotal(checkItemsArr.value)
}

const columnResizableChangeEvent = async (params) => {
  console.log('columnResizableChangeEvent')
  const { $columnIndex, resizeWidth, column } = params
  if (props.tableColumns?.length) {
    // eslint-disable-next-line vue/no-mutating-props
    props.tableColumns[$columnIndex].resizeWidth = Math.floor(resizeWidth)
  }
  if (props.pageType) {
    tableWColumnWidthChange(column, tableKey.value, props.pageType, tableRef.value)
  }
  if (props.expandColumns?.length || props.expandUnHeader) {
    const columns = await setExpandColumns()
    for (const key in expandTableConfigMap.value) {
      expandTableConfigMap.value[key].columns = columns
    }
  }
}

const clearCheckbox = () => {
  tableRef.value?.clearCheckboxRow()
  tableRef.value?.clearCheckboxReserve()
  selectChangeEvent()
}

const footerData = ref([] as any)
const statTotal = (data: any[] = []) => {
  const $table = tableRef?.value
  if (!$table) return []

  const columns = $table.getColumns()
  const obj = {} as any
  data = data.length ? data : tableData.value || []
  columns.forEach((column, _columnIndex) => {
    if (_columnIndex === 0) {
      obj.checkbox = '合计'
      return
    }

    if (props.totalField?.includes(column.field)) {
      obj[column.field] = sumNum(data, column.field)
    }
  })
  footerData.value = [obj]
}
// 保存表格设置
const handleSaveTableSetting = (arr) => {
  setTableConfig(arr, props.pageType, lineHeightType.value)
  nextTick(() => {
    tableKey.value = flattenTableColumn({ pageType: props.pageType, tableKey: arr })
  })
  orderby.value = null
  ordersort.value = null
  search()
}

const handleResetTableSetting = async () => {
  await setTableConfig([], props.pageType, lineHeightType.value)
  await initTable(props.pageType, tableRef.value, tableKey, undefined, lineHeightType, true)
  orderby.value = null
  ordersort.value = null
  search()
}

// 根据field设置列属性
const setColumnByField = async (field, params) => {
  const $table: any = tableRef.value
  $table.getTableColumn().fullColumn.forEach((item) => {
    if (item.field === field) {
      for (const [key, value] of Object.entries(params)) {
        item[key] = value
      }
    }
  })
  await nextTick()
  $table.refreshColumn()
}

// 展开行
const expandConfig = ref<VxeTablePropTypes.ExpandConfig>({
  showIcon: true,
  iconOpen: 'vxe-icon-caret-down',
  iconClose: 'vxe-icon-caret-right',
  visibleMethod({ row }) {
    const arr = props?.expandData ? props.expandData({ row }) : []
    if (arr?.length > 0) {
      return true
    }
    return false
  },
})
const isExpand = ref(false)
const expandTableConfigMap = ref({})
const formatExpandConfig = ({ rowIndex, columns, row }) => {
  const data = props?.expandData ? props.expandData({ row }) : []
  return {
    columns,
    rowHeight: props.expandRowHeight,
    data,
    index: (page.value - 1) * pageSize.value + rowIndex + 1,
    minHeight: Math.min(data.length * props.expandRowHeight + (!props.expandUnHeader ? 29 : 0) + 5, 6600),
    parentRow: row,
    showHeader: !props.expandUnHeader,
  }
}
const onToggleRowExpand = async (params) => {
  console.log('onToggleRowExpand')
  const { row, expanded } = params
  if (expanded) {
    const columns = await setExpandColumns()
    expandTableConfigMap.value[row[props.keyField]] = formatExpandConfig({
      columns,
      rowIndex: params.rowIndex,
      row,
    })
  }
}
const handleExpand = async (flag?: boolean) => {
  if (!props.expandData) return
  console.log('handleExpand')
  isExpand.value = flag ?? !isExpand.value
  const $table: any = tableRef.value
  const columns = await setExpandColumns()
  tableData.value.forEach((item, index) => {
    expandTableConfigMap.value[item[props.keyField]] = formatExpandConfig({
      columns,
      rowIndex: index,
      row: item,
    })
  })
  $table.setAllRowExpand(isExpand.value)
  if (props.expandStateKey) {
    localStorage.setItem(props.expandStateKey, isExpand.value ? '1' : '0')
  }
}
const setExpandColumns = async () => {
  console.log('setExpandColumns')
  const $table: any = tableRef.value
  const _columns: any[] = $table.getColumns()
  let index = _columns.findIndex((f) => f.type === 'seq' || f.type === 'expand')
  const isSeq = _columns?.[index]?.type === 'seq'

  const result: any[] = [
    ...Array.from({ length: index }, (_, _index) => {
      return { field: `$empty${_index + 1}`, width: _columns[_index].renderWidth, fixed: 'left' }
    }),
    { type: 'seq', width: _columns[index++].renderWidth, fixed: 'left', align: 'center' },
    isSeq ? { field: `$empty-expand`, width: _columns[index++].renderWidth, fixed: 'left' } : undefined,
  ].filter(Boolean)
  let compensateWidth = 0
  const columnState = _columns.filter((f) => f.fixed !== 'right')
  const columnStateByRight = _columns.filter((f) => f.fixed === 'right')
  const expandColumnsValue = (
    props.expandUnHeader
      ? _columns.filter((f) => !f.type).map((f) => ({ field: f.field, title: f.title, align: f.align, formatter: f.formatter, cellRender: f.cellRender, fixed: f.fixed }))
      : props.expandColumns
  ).map((f) => ({
    ...f,
    render: f.render || props.expandColumnRenderMap[f.field],
  }))

  const expandColumnState = expandColumnsValue.filter((f) => f.fixed !== 'right')
  const expandColumnStateByRight = expandColumnsValue.filter((f) => f.fixed === 'right')

  const rightCount = Math.min(expandColumnStateByRight.length, columnStateByRight.length)
  const maxRightCount = Math.max(expandColumnStateByRight.length, columnStateByRight.length)

  const count = Math.max(expandColumnState.length, columnState.length)
  for (const itemIndex of Array.from({ length: count }, (_, i) => i)) {
    const item = expandColumnState[itemIndex] || { field: `$empty${itemIndex}` }
    if (itemIndex !== count - 1) {
      if (!columnState[index]) {
        compensateWidth += 70
      }
      if (columnState[index] && columnState[index].renderWidth < props.expandCellMinWidth && !item.span) {
        result.push({
          ...item,
          width: columnState[index].renderWidth + (columnState?.[index + 1]?.renderWidth ?? 0),
        })
        index += 2
      } else {
        result.push({
          ...item,
          width: columnState[index]?.renderWidth || 70,
        })
        index += 1
      }
    }
    if (index >= count) break
  }
  console.log('compensateWidth', compensateWidth)
  if (compensateWidth) {
    const targetColumn = JSON.parse(JSON.stringify(_columns))
      .reverse()
      .find((f) => !f.fixed)
    const targetItem = tableKey.value.find((f) => f.key === targetColumn.field)
    if (targetItem) {
      targetItem.width = compensateWidth + targetColumn.renderWidth
      await nextTick()
    }
  }
  console.log('maxRightCount', maxRightCount)
  for (const oIndex of Array.from({ length: maxRightCount }, (_, i) => i)) {
    const isFillEmpty = Math.min(oIndex, maxRightCount - rightCount - 1) === oIndex
    // console.log('oIndex', oIndex, isFillEmpty, columnStateByRight)
    // 插入空的列数
    if (isFillEmpty) {
      result.push({
        field: `$empty-right-${oIndex}`,
        width: columnStateByRight[oIndex].renderWidth,
        fixed: 'right',
      })
    } else {
      result.push({
        width: columnStateByRight[oIndex].renderWidth,
        ...expandColumnStateByRight[maxRightCount - oIndex - 1],
        fixed: 'right',
      })
    }
  }

  console.log('setExpandColumns:result: ', result)
  return result
}

const headerMoreFormat = (list) => {
  const result: any[] = []
  const defaultItems = {
    copy: {
      label: '复制列内容',
      value: 'copy',
      click: ({ data, column }) => {
        const text = (data || []).reduce((acc, cur) => {
          acc += `${cur[column.key]}\n`
          return acc
        }, '')
        copyText(text)
      },
    },
  }
  list.forEach((item) => {
    if (typeof item === 'string') {
      defaultItems[item] && result.push(defaultItems[item])
    } else {
      result.push(item)
    }
  })
  return result
}

defineExpose({
  refresh: tapQueryForm,
  tableData,
  checkItemsArr,
  tableRef,
  page,
  pageSize,
  ordersort,
  orderby,
  search,
  showTableSetting,
  tableKey,
  clearCheckbox,
  setColumnByField,
  updateLoading,
  rowHeight: lineHeightMap[lineHeightType.value],
})
</script>

<style lang="scss" scoped>
.tableBox {
  position: relative;
  flex: 1;

  .box {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }
}

.paginationBox {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 0.83rem;

  .totalBox {
    display: flex;
    align-items: center;
    margin-left: 20px;
    color: #000;

    .text {
      margin-right: 8px;
      font-size: 14px;
    }

    .total {
      font-size: 16px;
      color: #1890ff;
    }
  }
}

.selected-box {
  display: flex;
  align-items: center;
  padding: 5px 12px;
  color: #3d3d3d;
  background-color: #f4f9fe;
  border: 1px solid #1890ff;
  border-radius: 4px;

  .selected-text {
    color: #1890ff;
  }
}
</style>
