<template>
  <a-drawer :maskClosable="false" title="采购申请单" :width="drawerWidth" v-model:open="drawerVisible" @close="handleClose" :bodyStyle="{ padding: '0' }" destroyOnClose>
    <template #extra>
      <a-button @click="handleShowAuditRecord" v-if="[DetailTypeEnum.VIEW, DetailTypeEnum.AUDIT].includes(viewType) && btnPermission[83005]">审核记录</a-button>
      <!-- <a-button class="ml-10" @click="handleShowLogRecord" v-if="[DetailTypeEnum.VIEW, DetailTypeEnum.AUDIT].includes(viewType) && btnPermission[83005]">日志</a-button> -->
    </template>
    <div class="flex h-full">
      <div class="w-950 overflow-y-auto p-16">
        <EasyForm ref="formRef" :mode="mode" :formItems="formItems" v-model:form="formData">
          <template #detail>
            <div class="mb-8 flex justify-end" v-if="[DetailTypeEnum.EDIT, DetailTypeEnum.ADD].includes(viewType)">
              <a-button type="primary" @click="handleAdd">新增商品</a-button>
            </div>

            <price-table :table-key="tableColumns" :data="showProductList">
              <template #process_type="{ row }">
                <a-select
                  class="w-full"
                  v-model:value="row.process_type"
                  :disabled="processTypeDisabled"
                  placeholder="选择加工方式"
                  v-if="[DetailTypeEnum.EDIT, DetailTypeEnum.ADD].includes(viewType)"
                  @change="() => (row.process_warehouse_id = undefined)"
                >
                  <a-select-option v-for="item in machiningTypeOption" :key="item.value" :value="item.value">{{ item.label }}</a-select-option>
                </a-select>
                <span v-else>{{ row.process_type_name }}</span>
              </template>
              <template #image_url="{ row }">
                <EasyImage :src="row.image_url"></EasyImage>
              </template>
              <template #quantity="{ row }">
                <a-input-number v-model:value="row.quantity" :min="0" :precision="0" v-if="[DetailTypeEnum.EDIT, DetailTypeEnum.ADD].includes(viewType)" />
                <span v-else>{{ row.quantity }}</span>
              </template>
              <template #predict_delivery_date="{ row }">
                <a-date-picker
                  show-time
                  v-model:value="row.predict_delivery_date"
                  valueFormat="YYYY-MM-DD HH:mm:ss"
                  v-if="[DetailTypeEnum.EDIT, DetailTypeEnum.ADD].includes(viewType)"
                  :disabled-date="disabledDate"
                />
                <span v-else>{{ row.predict_delivery_date }}</span>
              </template>
              <template #warehouse_id="{ row }">
                <a-select
                  v-model:value="row.warehouse_id"
                  :options="warehouseOption"
                  :filter-option="filterOption"
                  show-search
                  clearable
                  class="w-full"
                  v-if="[DetailTypeEnum.EDIT, DetailTypeEnum.ADD].includes(viewType)"
                ></a-select>
                <span v-else>{{ warehouseOption.find((item) => item.value === row.warehouse_id)?.label }}</span>
              </template>
              <template #process_warehouse_id="{ row }">
                <a-select
                  v-model:value="row.process_warehouse_id"
                  :options="warehouseOption"
                  :filter-option="filterOption"
                  show-search
                  clearable
                  class="w-full"
                  v-if="[DetailTypeEnum.EDIT, DetailTypeEnum.ADD].includes(viewType) && row.process_type !== '1'"
                ></a-select>
                <span v-else>{{ warehouseOption.find((item) => item.value === row.process_warehouse_id)?.label }}</span>
              </template>
              <template #action="{ row }">
                <a-button @click="handleDeleteProduct(row)" v-if="[DetailTypeEnum.EDIT, DetailTypeEnum.ADD].includes(viewType)">删除</a-button>
              </template>
            </price-table>
          </template>
        </EasyForm>
      </div>
      <div class="flex-1 overflow-y-auto p-16 bg-#f2f2f2" v-if="showAuditRecord">
        <a-timeline class="ml-16" v-if="auditRecordList.length > 0">
          <a-timeline-item v-for="item in auditRecordList" :key="item.id">
            <div class="timeline-title">
              {{ item.audit_time }}
            </div>
            <div class="timeline-content">
              {{ item.message }}
            </div>
          </a-timeline-item>
        </a-timeline>
        <a-empty v-else class="c-#333" description="暂无审核记录" />
      </div>
      <!-- <OperationLog v-model:visible="logShow" uid="purchaseapplyorder" :is-drawer="false" v-model:width="operationLogWidth"></OperationLog> -->
    </div>
    <template #footer>
      <a-space :size="16" v-if="[DetailTypeEnum.EDIT, DetailTypeEnum.ADD].includes(viewType)">
        <a-button type="primary" :loading="submitLoading" @click="handleSubmitAudit(true)">提交审核</a-button>
        <a-button :loading="submitLoading" @click="handleSubmitAudit(false)">保存暂不提交</a-button>
        <a-button @click="handleClose">取消</a-button>
      </a-space>
      <a-space :size="16" v-else-if="viewType === DetailTypeEnum.AUDIT">
        <a-button type="primary" @click="handleAudit(true)">审核通过</a-button>
        <a-button @click="handleAudit(false)">审核拒绝</a-button>
        <a-button @click="handleClose">取消</a-button>
      </a-space>
    </template>
    <SelectProduct ref="selectProductRef" @confirm="onSelectProduct" />
  </a-drawer>
  <audit-modal ref="auditModalRef" @audit="handleFinishAudit" />
</template>

<script setup lang="ts">
import { message } from 'ant-design-vue'
import { Dayjs } from 'dayjs'
import moment from 'moment'

import { DetailTypeEnum } from '@/common/enum'
import { Enum2Map, Enum2Options, filterOption } from '@/utils'

import AuditModal from '@/views/pageComponents/purchaseManagement/components/AuditModal.vue'

import PriceTable from './PriceTable.vue'

import { AddPurchaseApplyOrder, UpdatePurchaseApplyOrder, GetDeptByUser, GetUsersAndChilrd, GetPurchaseApplyOrder, BatchAuditPurchaseApplyOrder, GetWarehouses } from '@/servers/Purchaseapplyorder'
import { GetPLMMachiningType, GetAuditRecord } from '@/servers/BusinessCommon'

const userInfo = inject('userInfo') as UserInfo
const emits = defineEmits(['refresh'])
const disabledDate = (current: Dayjs) => {
  const today = moment().startOf('day')
  return current && current <= today
}

const { btnPermission } = usePermission()

const auditModalRef = ref()

const drawerWidth = computed(() => {
  const _width = showAuditRecord.value ? 300 : 0
  // const _width = showAuditRecord.value ? 300 : logShow.value ? operationLogWidth.value : 0
  return `${Math.min(_width + 950, window.innerWidth - 180)}px`
})
// const operationLogWidth = ref(640)

const formRef = ref()
const resetFormData = () => {
  return {
    type: 1,
    department_id: `${userInfo?.department_id ?? ''}`,
    applicant_id: `${userInfo?.id ?? ''}`,
  }
}
const formData = ref<Record<string, any>>(resetFormData())
const formItems = ref<EasyFormItemProps[]>([
  { type: 'title', title: '基本信息', span: 24 },
  {
    type: 'input',
    label: '单据编号',
    disabled: true,
    placeholder: ' ',
    name: 'number',
    span: 12,
  },
  {
    type: 'select',
    label: '申请类型',
    required: true,
    name: 'type',
    span: 12,
    options: Enum2Options(PurchaseApplyOrderEnum),
    textFormat: ({ value }) => Enum2Map(PurchaseApplyOrderEnum)[value],
    props: {
      onChange: () => onTypeChange(),
    },
  },
  {
    type: 'select',
    label: '申请部门',
    required: true,
    name: 'department_id',
    alias: 'department_name',
    dataType: 'string',
    span: 12,
    api: async () => {
      return GetDeptByUser(userInfo.id)
    },
  },
  {
    type: 'select',
    label: '申请人',
    required: true,
    name: 'applicant_id',
    alias: 'applicant_name',
    dataType: 'string',
    span: 12,
    api: async () => {
      return GetUsersAndChilrd(userInfo.id)
    },
  },
  {
    type: 'input',
    label: '申请时间',
    disabled: true,
    placeholder: ' ',
    name: 'created',
    span: 12,
  },
  {
    type: 'input',
    label: '审核时间',
    disabled: true,
    placeholder: ' ',
    name: 'audit_time',
    span: 12,
  },
  {
    type: 'text',
    label: 'MRP计划单号',
    name: 'external_id',
    span: 24,
    hide: true,
  },
  {
    type: 'input',
    label: '备注',
    placeholder: ' ',
    name: 'remark',
    span: 24,
    props: {
      maxlength: 1000,
    },
  },
  {
    type: 'input',
    label: '标签',
    placeholder: ' ',
    name: 'tags',
    span: 24,
    textFormat: ({ value }) => {
      return (value || []).map((text) => h('div', { class: 'easy-tag cursor-default mb-5 mr-10 ml-0!' }, text))
    },
    props: {
      maxlength: 300,
    },
  },
  {
    type: 'text',
    label: '状态',
    name: 'audit_status',
    textFormat: ({ value }) => Enum2Map(AuditStatusEnum)[value] || '待提审',
  },
  { type: 'title', title: '明细信息', span: 24 },
  { slot: 'detail', span: 24 },
])

const showAuditRecord = ref(false)
const drawerVisible = ref(false)

const viewType = ref<DetailTypeEnum>(DetailTypeEnum.ADD)
// 审核记录
const auditRecordList = ref<any[]>([])
// 选中的商品
const selectProductList = ref<any[]>([])
// 显示的商品
const showProductList = computed(() => selectProductList.value.filter((item) => !item.is_delete))

// 仓库选项
const warehouseOption = ref<any[]>([])
// 表格列
const tableColumns = computed(() => {
  const action = { title: '操作', field: 'action', width: 100, fixed: 'right' }
  const arr = [
    { title: '加工方式', field: 'process_type', width: 130, required: true },
    { title: '商品主图', field: 'image_url', width: 90 },
    { title: '商品编码', field: 'k3_sku_id', width: 100 },
    { title: '商品名称', field: 'sku_name', width: 100 },
    { title: 'SRS平台商品编码', field: 'srs_platform_prod_code', width: 130 },
    { title: '规格型号', field: 'type_specification', width: 100 },
    { title: '单位', field: 'valuation_unit', width: 100 },
    { title: '供应商子公司', field: 'company_supplier_name', width: 120 },
    { title: '计划采购数量', field: 'quantity', width: 120, required: true },
    { title: '已执行采购数量', field: 'purchase_quantityed', width: 120, required: true },
    { title: '预计交期', field: 'predict_delivery_date', width: 180, required: true },
    { title: '采购收料仓', field: 'warehouse_id', width: 180, required: true },
    { title: '加工领料仓', field: 'process_warehouse_id', width: 180, required: true },
  ]
  if ([DetailTypeEnum.EDIT, DetailTypeEnum.ADD].includes(viewType.value)) {
    arr.push(action)
  }
  if ([DetailTypeEnum.VIEW, DetailTypeEnum.AUDIT].includes(viewType.value)) {
    arr.push({ title: '成品缺货数量', field: 'stockout_quantity', width: 180 })
  }
  return arr.filter((f) => f && (f.title === '已执行采购数量' ? viewType.value != 0 : f))
})
const mode = computed(() => {
  return [DetailTypeEnum.ADD, DetailTypeEnum.EDIT].includes(viewType.value) ? 'new' : 'detail'
})
const handleOpen = async (type: DetailTypeEnum, id?: string) => {
  formRef.value?.resetFields()
  getWarehouseOption()
  getPLMMachiningType()
  viewType.value = type
  drawerVisible.value = true
  await nextTick()
  formRef.value?.changeMode()
  if (id) {
    formData.value.id = id
    const res = await GetPurchaseApplyOrder({ id })
    const { details, ...arg } = res.data
    formRef.value.changeValue({
      ...arg,
      tags: viewType.value === DetailTypeEnum.EDIT ? arg.tags.join(',') : arg.tags,
    })
    selectProductList.value = details.map((item) => ({
      ...item,
      process_type: `${item.process_type || ''}`,
    }))
    formRef.value.changeItem('external_id', {
      hide: !arg.external_id,
    })
  }
  // 显示审核记录
  if ([DetailTypeEnum.VIEW, DetailTypeEnum.AUDIT].includes(viewType.value) && btnPermission.value[83005]) {
    showAuditRecord.value = localStorage.getItem('showAuditRecord') === '1'
    if (showAuditRecord.value) {
      getAuditRecord()
    }
  } else {
    showAuditRecord.value = false
  }
  // 只在新增模式下调用onTypeChange，编辑模式下保持原有值
  if (viewType.value === DetailTypeEnum.ADD) {
    onTypeChange()
  }
}

// 关闭
const handleClose = () => {
  formData.value = resetFormData()
  selectProductList.value = []
  drawerVisible.value = false
  submitLoading.value = false
}

// 新增商品
const selectProductRef = ref()
const handleAdd = () => {
  selectProductRef.value.open(
    {
      width: 1300,
      search: ['商品编号/聚水潭编号', '商品名称', '供应商名称'],
      left: ['商品分类', '商品主图', '商品编码', '商品名称', '规格型号', '款式编码', '材质', '换算值', '换算公式', '采购单位', '标准装箱数', '聚水潭编号', '默认供应商'],
      right: ['商品编码', '商品名称'],
    },
    // selectProductList.value,
  )
}
// 选中商品回调
const onSelectProduct = (list: any[]) => {
  const selectProductMap = selectProductList.value.reduce(
    (acc, cur) => ({
      ...acc,
      [cur.k3_sku_id]: cur,
    }),
    {},
  )
  const newList = list
    .filter((f) => !selectProductMap[f.sku_id])
    .map((item: any) => ({
      ...item,
      k3_sku_id: item.sku_id,
      sku_name: item.sku_name,
      id: undefined,
      company_supplier_name: item.company_supplier_id ? item.company_supplier_name : undefined,
      process_type: formData.value.type !== 2 ? '1' : '',
    }))
  selectProductList.value = [...selectProductList.value, ...newList]
}
// 删除商品
const handleDeleteProduct = (row) => {
  if (row.id) {
    const item = selectProductList.value.find((item) => item.id == row.id)
    item.is_delete = true
  } else {
    selectProductList.value = selectProductList.value.filter((item) => item.k3_sku_id !== row.k3_sku_id)
  }
}

// 提交审核
const submitLoading = ref(false)
const handleSubmitAudit = async (is_pass: boolean) => {
  try {
    const formState = await formRef.value?.validate()
    if (!selectProductList.value.length) {
      message.error('请添加商品')
      return
    }
    const res = selectProductList.value
      .filter((item) => !item.is_delete)
      .some((i, index) => {
        // if (!i.company_supplier_id) {
        //   message.error(`请选择第${index + 1}行供应商子公司`)
        //   return true
        // }
        if (!i.process_type) {
          message.error(`请输入第${index + 1}行加工方式`)
          return true
        }
        if (!i.quantity) {
          message.error(`请输入第${index + 1}行采购数量`)
          return true
        }
        if (!i.predict_delivery_date) {
          message.error(`请选择第${index + 1}行预计交期`)
          return true
        }
        if (!i.warehouse_id) {
          message.error(`请选择第${index + 1}行采购收料仓`)
          return true
        }
        if (!i.process_warehouse_id && i.process_type !== '1') {
          message.error(`请选择第${index + 1}行加工领料仓`)
          return true
        }
        return false
      })
    if (res) return
    selectProductList.value.forEach((item, index) => {
      item.sort = index + 1
    })
    const params = {
      id: formData.value.id,
      is_pass,
      ...formState,
      details: selectProductList.value,
      tags: [
        ...new Set(
          (formState.tags || '').split(',').reduce((acc, cur) => {
            const result = cur.split('，')
            return [...acc, ...result]
          }, []),
        ),
      ].filter(Boolean),
    }
    const fn = formData.value.id ? UpdatePurchaseApplyOrder : AddPurchaseApplyOrder
    await fn(params, { loading: submitLoading })
    message.success('提交成功')
    handleClose()
    emits('refresh')
  } catch (e) {
    console.log(e)
  }
}
// 审核
const handleAudit = (is_pass: boolean) => {
  auditModalRef.value?.open(is_pass)
}
// 审核完成
const handleFinishAudit = async (auditForm) => {
  await BatchAuditPurchaseApplyOrder({ ...auditForm, ids: [formData.value.id] })
  message.success('审核成功')
  auditModalRef.value?.close()
  handleClose()
  emits('refresh')
}

// 显示审核记录
const handleShowAuditRecord = () => {
  // logShow.value = false
  showAuditRecord.value = !showAuditRecord.value
  localStorage.setItem('showAuditRecord', showAuditRecord.value ? '1' : '0')
  if (showAuditRecord.value) {
    getAuditRecord()
  }
}
// 获取审核记录
const getAuditRecord = async () => {
  const res = await GetAuditRecord({ id: formData.value.id, type: 3 })
  auditRecordList.value = res.data
}

// 日志显示
// const logShow = ref(false)
// const handleShowLogRecord = () => {
//   showAuditRecord.value = false
//   logShow.value = !logShow.value
// }

const getWarehouseOption = async () => {
  const res = await GetWarehouses()
  warehouseOption.value = res.data.map((i) => ({ label: i.value, value: i.key }))
}

const machiningTypeOption = ref<any[]>([])
const getPLMMachiningType = async () => {
  const res = await GetPLMMachiningType({})
  machiningTypeOption.value = res.data
}

const processTypeDisabled = computed(() => {
  return formData.value.type !== 2
})
const onTypeChange = () => {
  selectProductList.value = selectProductList.value.map((item) => ({
    ...item,
    process_type: formData.value.type !== 2 ? '1' : item.process_type,
    process_warehouse_id: undefined,
  }))
}

defineExpose({
  open: handleOpen,
})
</script>

<style scoped lang="scss"></style>
