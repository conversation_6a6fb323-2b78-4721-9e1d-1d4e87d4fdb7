<template>
  <div class="flex flex-col h-full">
    <div class="content-area">
      <!-- 基本信息 -->
      <div class="form-section mb-6">
        <EasyForm ref="formRef" :mode="mode" :formItems="formItems" v-model:form="formData">
          <!-- 采购明细插槽 -->
          <template #purchase_details>
            <div class="flex justify-end mb-4" v-if="mode === 'new'">
              <a-button type="primary" @click="handleAddPurchaseOrder" class="mb-4">添加采购单</a-button>
            </div>
            <!-- 采购明细表格 -->
            <vxe-grid ref="purchaseGridRef" v-bind="purchaseGridOptions" @edit-closed="handleEditClosed">
              <template #action="{ row }" v-if="mode === 'new'">
                <a-button type="link" @click="handleDeletePurchaseOrder(row)">删除</a-button>
              </template>
              <template #image="{ row }">
                <EasyImage v-if="row.image_url" :src="row.image_url" :w="40" :h="40" />
                <span v-else>-</span>
              </template>

              <!-- 数字输入插槽 -->
              <template #number="{ row, column }">
                <a-input-number
                  v-if="mode === 'new'"
                  class="w-full"
                  v-model:value="row[column.field]"
                  :precision="column.params.precision"
                  :min="column.params.min ?? undefined"
                  @change="handleNumberChange({ row, column, value: row[column.field] })"
                />
                <span v-else>{{ (row[column.field] ?? 0).roundNext(column?.params?.precision || 0) }}</span>
              </template>
              <template #purchase_number="{ row }">
                <span class="link" @click="handlePurchaseNumberClick(row)">{{ row.purchase_number }}</span>
              </template>

              <!-- 退库申请单号跳转模板 -->
              <template #return_application_number="{ row }">
                <span v-if="row.return_application_number && row.return_application_number.includes(',')">
                  <!-- 多个单号时，每个单号都可以单独点击 -->
                  <span
                    v-for="(number, index) in row.return_application_number.split(',').map((n) => n.trim())"
                    :key="index"
                    class="link"
                    @click="handleReturnApplicationClick(row, number.trim(), index)"
                  >
                    {{ number.trim() }}{{ index < row.return_application_number.split(',').length - 1 ? ', ' : '' }}
                  </span>
                </span>
                <span v-else-if="row.return_application_number" class="link" @click="handleReturnApplicationClick(row, row.return_application_number, 0)">
                  {{ row.return_application_number }}
                </span>
                <span v-else>-</span>
              </template>

              <!-- 应付单号跳转模板 -->
              <template #bill_payable_number="{ row }">
                <span v-if="row.bill_payable_number && row.bill_payable_number.includes(',')">
                  <!-- 多个单号时，每个单号都可以单独点击 -->
                  <span v-for="(number, index) in row.bill_payable_number.split(',').map((n) => n.trim())" :key="index" class="link" @click="handleBillPayableClick(row, number.trim(), index)">
                    {{ number.trim() }}{{ index < row.bill_payable_number.split(',').length - 1 ? ', ' : '' }}
                  </span>
                </span>
                <span v-else-if="row.bill_payable_number" class="link" @click="handleBillPayableClick(row, row.bill_payable_number, 0)">
                  {{ row.bill_payable_number }}
                </span>
                <span v-else-if="row.bill_payable_ids && Array.isArray(row.bill_payable_ids) && row.bill_payable_ids.length > 0" class="link" @click="handleBillPayableClick(row)">
                  {{ row.bill_payable_ids.join(', ') }}
                </span>
                <span v-else>-</span>
              </template>

              <!-- 采购入库数量模板 -->
              <template #purchase_inbound_quantity="{ row }">
                <span v-if="row.is_saled === true">-</span>
                <span v-else>{{ row.purchase_inbound_quantity || '-' }}</span>
              </template>

              <!-- 退库数量模板 -->
              <template #return_quantity="{ row }">
                <span v-if="row.is_saled === true">-</span>
                <span v-else>{{ row.return_quantity || '-' }}</span>
              </template>

              <!-- 未交付数量模板 -->
              <template #undelivered_quantity="{ row }">
                <span v-if="row.is_saled === true">-</span>
                <span v-else>{{ row.undelivered_quantity || '-' }}</span>
              </template>

              <!-- 采购总金额模板 -->
              <template #total_purchase_amount="{ row }">
                <span v-if="row.is_saled === true">-</span>
                <span v-else>{{ row.total_purchase_amount || '-' }}</span>
              </template>
            </vxe-grid>
          </template>
        </EasyForm>
      </div>

      <!-- 编辑模式下的操作按钮 -->
      <div class="submit-row">
        <template v-if="mode === 'new'">
          <a-button type="primary" @click="handleSave(true)" :loading="saving" class="ml mr">提交审核</a-button>
          <a-button @click="handleSave(false)" :loading="saving" class="ml mr">保存暂不提交</a-button>
          <a-button @click="handleCancel" class="ml mr">取消</a-button>
        </template>
        <template v-else-if="mode === 'detail'">
          <a-button @click="handleCancel" class="ml mr">返回</a-button>
        </template>
      </div>
    </div>

    <!-- 采购单选择组件 -->
    <SelectPurchaseOrderForBill ref="selectPurchaseOrderRef" @confirm="handlePurchaseOrderConfirm" />

    <!-- 退库申请单详情抽屉 -->
    <Application ref="applicationRef" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch, onUnmounted } from 'vue'
import { useRoute } from 'vue-router'
import { message, Modal } from 'ant-design-vue'
import dayjs from 'dayjs'

import EasyForm from '@/components/EasyForm'
import EasyImage from '@/components/EasyImage.vue'
import SelectPurchaseOrderForBill from '@/components/business/SelectPurchaseOrderForBill.vue'
import { usePageStore } from '@/store/usePageStore'
import { usePage } from '@/hook/usePage'
import { addFloat } from '@/utils'
import eventBus from '@/utils/eventBus'

import Application from '@/views/pageComponents/PurchaseReturnApplication/components/Application.vue'

import { setFormItems, transformApiData, buildBillDetails, getPurchaseGridOptions } from './GenerateBill.data'

import { BuildBillDetailInfoQuery, AddBillOrder, UpdateBillOrder, GetBillOrder } from '@/servers/billVerification'

// 统一的UUID生成函数
const generateUUID = (item: any): string => {
  // 优先使用 purchase_number+k3_sku_id 格式
  if (item.purchase_number && item.k3_sku_id) {
    return `${item.purchase_number}_${item.k3_sku_id}`
  }

  // 如果没有purchase_number或k3_sku_id，使用detail_id作为备选
  if (item.detail_id) {
    return `${item.detail_id}`
  }

  // 如果都没有，生成一个基于其他字段的UUID
  const fallbackId = item.purchase_order_id || item.purchase_order_detail_id || item.id
  if (fallbackId) {
    return `fallback_${fallbackId}`
  }

  // 最后的备选方案
  return `unknown_${Date.now()}_${Math.random()}`
}

const route = useRoute()

// 表单引用
const formRef = ref()

// 获取页面store实例
const pageStore = usePageStore()

// 使用 usePage hook
const { closePage, setRefreshByPath, pushPage } = usePage({ formRef })
const selectPurchaseOrderRef = ref()
const purchaseGridRef = ref()
const applicationRef = ref()

// 处理采购单编号点击跳转
const handlePurchaseNumberClick = (row: any) => {
  if (row.purchase_order_id) {
    pushPage(`/purchaseOrderLook/${row.purchase_order_id}`, { source: true })
  } else {
    message.warning('无法获取采购单ID')
  }
}

// 打开退库申请详情抽屉
const handleReturnApplicationClick = (row: any, returnNumber?: string, index?: number) => {
  // 如果传入了具体的单号和索引，使用对应的ID
  if (returnNumber !== undefined && index !== undefined && row.return_application_ids && Array.isArray(row.return_application_ids)) {
    // 根据索引获取对应的ID
    const targetId = row.return_application_ids[index]
    if (targetId) {
      // 打开退库申请详情抽屉
      applicationRef.value?.open('view', targetId)
      return
    }
  }

  // 如果传入了具体的单号但没有索引，或者索引无效
  if (returnNumber) {
    // 尝试在ID数组中查找匹配的单号（如果后端返回的数据结构支持）
    if (row.return_application_ids && Array.isArray(row.return_application_ids) && row.return_application_ids.length > 0) {
      // 暂时使用第一个ID
      applicationRef.value?.open('view', row.return_application_ids[0])
      return
    }
  }

  // 如果没有传入具体单号，使用默认逻辑
  if (row.return_application_ids && Array.isArray(row.return_application_ids) && row.return_application_ids.length > 0) {
    // 如果有多个ID，使用第一个
    applicationRef.value?.open('view', row.return_application_ids[0])
  } else if (row.return_application_id) {
    // 兼容单个ID字段
    applicationRef.value?.open('view', row.return_application_id)
  } else {
    message.warning('退库申请信息不存在')
  }
}

// 跳转到应付单详情页面
const handleBillPayableClick = (row: any, billNumber?: string, index?: number) => {
  // 如果传入了具体的单号和索引，使用对应的ID
  if (billNumber !== undefined && index !== undefined && row.bill_payable_ids && Array.isArray(row.bill_payable_ids)) {
    // 根据索引获取对应的ID
    const targetId = row.bill_payable_ids[index]
    if (targetId) {
      pushPage(`/billPayable/view/${targetId}`, { source: true })
      return
    }
  }

  // 如果传入了具体的单号但没有索引，或者索引无效
  if (billNumber) {
    // 尝试在ID数组中查找匹配的单号（如果后端返回的数据结构支持）
    if (row.bill_payable_ids && Array.isArray(row.bill_payable_ids) && row.bill_payable_ids.length > 0) {
      // 暂时使用第一个ID
      pushPage(`/billPayable/view/${row.bill_payable_ids[0]}`, { source: true })
      return
    }
  }

  // 如果没有传入具体单号，使用默认逻辑
  if (row.bill_payable_ids && Array.isArray(row.bill_payable_ids) && row.bill_payable_ids.length > 0) {
    // 如果有多个ID，使用第一个
    pushPage(`/billPayable/view/${row.bill_payable_ids[0]}`, { source: true })
  } else if (row.bill_payable_id) {
    // 兼容单个ID字段
    pushPage(`/billPayable/view/${row.bill_payable_id}`, { source: true })
  } else {
    message.warning('应付单ID不存在')
  }
}

// 采购明细表格配置
const purchaseGridOptions = ref(getPurchaseGridOptions())

const type = ref('new')
const mode = computed(() => {
  // 批量对账模式应该是可编辑的，所以返回 'new'
  if (type.value === 'batch') {
    return 'new'
  }
  // 编辑和新增模式返回 'new'，其他模式返回 'detail'
  const result = ['edit', 'new'].includes(type.value) ? 'new' : 'detail'
  return result
})

// 获取路由参数ID
const routeId = computed(() => {
  return route.params.id as string
})

// 判断是否为新增模式
const isNewMode = computed(() => {
  return routeId.value === 'new'
})

// 判断是否为批量对账模式（通过检查ID格式，包含时间戳）
const isBatchMode = computed(() => {
  return routeId.value && routeId.value.length > 10 && /^\d+$/.test(routeId.value)
})

// 判断是否为查看/编辑模式
const isViewEditMode = computed(() => {
  return routeId.value && routeId.value !== 'new' && !isBatchMode.value
})

// 获取账单ID（用于查看/编辑模式）
const billId = computed(() => {
  return isViewEditMode.value ? routeId.value : null
})

// 加载账单详情数据
const loadBillDetails = async (billId: string) => {
  // 防止重复调用
  if (isLoadingBillDetails.value) {
    return
  }

  // 如果已经加载过相同的账单ID，跳过
  if (loadedBillId.value === billId) {
    return
  }

  try {
    isLoadingBillDetails.value = true
    const response = await GetBillOrder({ id: billId })

    if (response.success && response.data) {
      // 设置表单数据
      formData.value = {
        ...formData.value,
        ...response.data,
        // 根据模式设置不同的账单月份值
        bill_month: (() => {
          if (response.data.bill_month) {
            if (type.value === 'detail') {
              // 查看模式：显示为文本格式
              const textValue = dayjs(response.data.bill_month).format('YYYY-MM')
              return textValue
            }
            if (type.value === 'edit') {
              // 编辑模式：保持为 dayjs 对象，以便编辑
              const dateValue = dayjs(response.data.bill_month)
              return dateValue
            }
            // 其他模式：保持为 dayjs 对象
            const dateValue = dayjs(response.data.bill_month)
            return dateValue
          }
          return null
        })(),
        // 确保费用计入成本字段被设置为固定值
        charge_into_cost: '按金额计入成本',
      }

      // 如果有采购明细数据，设置到表格中
      if (response.data.details && response.data.details.length > 0) {
        const details = response.data.details.map((item: any, index) => {
          // 为每个采购明细生成UUID，确保编辑模式下能正确回显
          let uuid = item.$uuid
          if (!uuid) {
            uuid = generateUUID(item)
          }

          return {
            ...item,
            serial_no: index + 1,
            $uuid: uuid,
          }
        })

        purchaseGridOptions.value = {
          ...purchaseGridOptions.value,
          data: details,
        }

        // 确保编辑模式下正确计算应付总额
        if (type.value === 'edit') {
          // 重新计算应付总额，确保与API数据一致
          calculateTotalAmount()

          // 如果API返回了实际应付金额，使用API的值
          if (response.data.actual_payable_amount !== undefined) {
            formData.value.actual_payable_amount = response.data.actual_payable_amount
          }
        }
      }

      // 记录已加载的账单ID
      loadedBillId.value = billId

      message.success('账单详情加载成功')
    } else {
      message.error(response.message || '加载账单详情失败')
    }
  } catch (_error) {
    message.error('加载账单详情失败')
  } finally {
    isLoadingBillDetails.value = false
  }
}

// 加载采购明细数据
const loadPurchaseDetails = async () => {
  // 防止重复调用
  if (isLoadingPurchaseDetails.value) {
    return
  }

  try {
    isLoadingPurchaseDetails.value = true

    // 如果是批量对账模式，从路由参数获取数据
    if (isBatchMode.value) {
      const batchData = route.query.batchData ? JSON.parse(decodeURIComponent(route.query.batchData as string)) : null

      if (!batchData || !batchData.detailIds || batchData.detailIds.length === 0) {
        return
      }

      // 检查是否是新的批量对账请求，避免重复加载
      const newBatchId = batchData.timestamp?.toString()
      if (newBatchId === currentBatchId.value && purchaseDetailsData.value.length > 0) {
        return
      }

      // 调用接口获取采购明细，直接传递detail_ids数组
      const response = await BuildBillDetailInfoQuery(batchData.detailIds)

      if (response?.success && response?.data) {
        // 使用转换函数处理数据
        const details = transformApiData(response.data)

        // 更新表格数据
        purchaseGridOptions.value = {
          ...purchaseGridOptions.value,
          data: details.map((item, index) => {
            // 为每个采购明细生成UUID，确保批量对账模式下能正确回显
            let uuid = item.$uuid
            if (!uuid) {
              uuid = generateUUID(item)
            }

            return {
              ...item,
              serial_no: index + 1,
              $uuid: uuid,
            }
          }),
        }

        // 检查每行是否都有 actual_unit_price 字段，并确保精度正确
        purchaseGridOptions.value.data.forEach((item, index) => {
          if (!item.actual_unit_price && item.tax_unit_price) {
            purchaseGridOptions.value.data[index].actual_unit_price = parseFloat(item.tax_unit_price)
          } else if (item.actual_unit_price) {
            // 确保现有值也有正确精度
            purchaseGridOptions.value.data[index].actual_unit_price = parseFloat(item.actual_unit_price)
          }

          // 根据需求计算相关金额字段
          const actualUnitPrice = purchaseGridOptions.value.data[index].actual_unit_price || 0
          const purchaseQuantity = item.purchase_quantity || 0
          const returnQuantity = item.return_quantity || 0

          // 1. 采购总金额 = 实际单价 × 采购数量
          if (purchaseQuantity > 0) {
            // 使用银行舍入法避免浮点数精度问题
            const totalAmount = Number((actualUnitPrice * purchaseQuantity).roundNext(2))
            purchaseGridOptions.value.data[index].total_purchase_amount = totalAmount
          }

          // 2. 退款金额：在批量对账模式下，直接使用API返回的 refund_amount 字段值
          // 不要重新计算，因为API返回的退款金额可能包含复杂的业务逻辑计算
          // 只有在没有退款金额时才使用默认计算方式
          if (!item.refund_amount && returnQuantity > 0) {
            // 使用银行舍入法避免浮点数精度问题
            const refundAmount = Number((actualUnitPrice * returnQuantity).roundNext(2))
            purchaseGridOptions.value.data[index].refund_amount = refundAmount
          }

          // 3. 采购单应付金额：优先使用API返回的 inv_amount 字段值
          // 不要重新计算，因为API返回的应付金额可能包含复杂的业务逻辑计算
          // 只有在没有应付金额时才使用默认计算方式
          if (!item.inv_amount) {
            const totalPurchaseAmount = purchaseGridOptions.value.data[index].total_purchase_amount || 0
            const refundAmount = purchaseGridOptions.value.data[index].refund_amount || 0
            // 使用 addFloat 处理浮点数相加，然后用银行舍入法处理精度
            const invAmount = Number(addFloat(totalPurchaseAmount, refundAmount).roundNext(2))
            purchaseGridOptions.value.data[index].inv_amount = invAmount
          }
        })

        // 计算应付总额
        calculateTotalAmount()
      } else {
        // 接口调用失败，显示错误信息并跳转回列表页
        const errorMsg = response?.message || '获取采购明细失败'
        message.error(errorMsg)
        console.error('BuildBillDetailInfoQuery 接口失败:', response)

        // 跳转回账单核对列表页
        pushPage('/billVerification')
      }
    }
  } catch (error) {
    console.error('加载采购明细失败:', error)
    // message.error('加载采购明细失败')
  } finally {
    isLoadingPurchaseDetails.value = false
  }
}

// 表单数据
const formData = ref<Record<string, any>>({})

// 防护函数：确保新增模式下不会设置不必要的数据
// 第二参数仅为兼容旧调用，不使用
// eslint-disable-next-line @typescript-eslint/no-unused-vars
const safeSetFormData = (key: string, _?: any) => {
  if (isNewMode.value && key !== 'charge_into_cost') {
    return false
  }
  return true
}

// 初始化表单数据
const initFormData = () => {
  // 如果是批量对账模式，先完全清理表单数据，然后从路由参数重新设置
  if (isBatchMode.value) {
    // 完全清理表单数据
    formData.value = {}

    // 清理采购明细表格数据
    purchaseGridOptions.value = { ...purchaseGridOptions.value, data: [] }

    // 从路由参数获取批量对账数据
    const batchData = route.query.batchData ? JSON.parse(decodeURIComponent(route.query.batchData as string)) : null

    if (batchData) {
      // 设置供应商信息
      formData.value.supplier_name = batchData.supplierName || ''
      formData.value.company_supplier_name = batchData.companySupplierName || ''
    }

    // 设置费用计入成本默认值
    formData.value.charge_into_cost = '按金额计入成本'
  } else if (['detail', 'edit'].includes(type.value)) {
    formData.value.charge_into_cost = '按金额计入成本'
  } else {
    // 其他模式：设置费用计入成本默认值
    formData.value.charge_into_cost = '按金额计入成本'
  }

  // 如果有采购明细数据，检查是否需要显示"该供应商存在多个子公司"
  if (purchaseDetailsData.value.length > 0) {
    const companySupplierIds = new Set()
    purchaseDetailsData.value.forEach((order) => {
      if (order.company_supplier_id) {
        companySupplierIds.add(order.company_supplier_id)
      }
    })

    if (companySupplierIds.size > 1) {
      formData.value.company_supplier_name = '该供应商存在多个子公司'
    }
  }
}

// 存储多个供应商子公司ID的数组
const multipleCompanySupplierIds = ref<number[]>([])

// 表单配置
// const formItems = ref(generateBillFormData)
const formItems = ref<EasyFormItemProps[]>(setFormItems())

// 采购明细数据 - 现在通过 purchaseGridOptions 管理
const purchaseDetailsData = computed(() => purchaseGridOptions.value.data || [])

// 保存状态
const saving = ref(false)

// 防止重复加载的标志
const isLoadingPurchaseDetails = ref(false)
const isLoadingBillDetails = ref(false) // 防止重复加载账单详情的标志
const loadedBillId = ref<string | null>(null) // 记录已加载的账单ID
const hasInitialized = ref(false) // 防止重复初始化的标志
const currentBatchId = ref<string | null>(null) // 记录当前批量对账ID

// 添加采购单
const handleAddPurchaseOrder = () => {
  // 从路由参数或表单数据获取供应商信息
  let currentSupplierId = null
  let currentSupplierName = null

  if (isBatchMode.value) {
    // 批量对账模式：从路由参数获取
    const batchData = route.query.batchData ? JSON.parse(decodeURIComponent(route.query.batchData as string)) : null
    if (batchData) {
      currentSupplierId = batchData.supplierId
      currentSupplierName = batchData.supplierName
    }
  } else {
    // 其他模式：从表单数据获取
    currentSupplierId = formData.value.supplier_id
    currentSupplierName = formData.value.supplier_name

    // 在新增模式下，如果基本信息中的供应商字段有值，则从表单中获取
    if (type.value === 'new' && formData.value.supplier_name) {
      // 新增模式下，如果用户手动填写了供应商名称，尝试从其他地方获取供应商ID
      if (purchaseDetailsData.value.length > 0) {
        // 从采购明细中查找匹配的供应商ID
        const firstOrder = purchaseDetailsData.value[0]
        if (firstOrder.supplier_id) {
          currentSupplierId = firstOrder.supplier_id
        }
      }

      // 如果还是没有供应商ID，但有供应商名称，至少可以用于显示
      if (!currentSupplierId && formData.value.supplier_name) {
        currentSupplierName = formData.value.supplier_name
      }
    }
  }

  // 构建选择组件的配置
  const formatUUID = (f) => {
    return { ...f, $uuid: generateUUID(f) }
  }
  const selectConfig: any = {
    left: [
      '采购单编号',
      '退库申请单号',
      '采购时间',
      '采购单状态',
      '供应商',
      '供应商子公司',
      '商品名称',
      '商品编号',
      '采购含税单价',
      '采购数量',
      '采购入库数量',
      '未交付数量',
      '退库数量',
      '采购总金额',
      '退款金额',
      '采购单应付金额',
      '赠品数量',
      '赠品单价',
      '赠品应付金额',
    ],
    right: ['采购单编号'],
    search: ['采购单号', '供应商', '供应商子公司', '退库申请号', '应付单号', '商品编码'],
    width: 1200,
    dataFormat: (data) => {
      return data.map(formatUUID)
    },
    keyField: '$uuid',
  }

  // 如果基本信息中有供应商，则添加供应商过滤条件
  if (currentSupplierId && currentSupplierId !== 'multiple') {
    selectConfig.supplierFilter = currentSupplierId // 传递供应商ID用于查询
    selectConfig.supplierId = currentSupplierId // 传递供应商ID用于SelectSupplier组件
    selectConfig.supplierName = currentSupplierName // 传递供应商名称用于显示
    selectConfig.disabledSupplier = true // 禁用供应商选择
  }

  const existingDataWithUUID = purchaseDetailsData.value.map((item) => {
    if (!item.$uuid) {
      const uuid = generateUUID(item)
      return { ...item, $uuid: uuid }
    }
    return item
  })

  // 供应商校验配置
  const supplierValidationConfig = {
    enabled: true, // 启用供应商校验
    message: '供应商不一致，请重新选择', // 校验失败时的提示消息
    validateFunc: (selectedOrders: any[]) => {
      // 获取当前已选中的采购明细数据
      const existingOrders = purchaseDetailsData.value

      // 如果当前没有采购明细，检查新选中的采购单是否来自同一供应商
      if (existingOrders.length === 0) {
        // 检查新选中的采购单是否都来自同一供应商
        const supplierIds = new Set()
        const supplierNames = new Set()

        selectedOrders.forEach((order) => {
          const supplierId = order.company_supplier_id || order.supplier_id
          const supplierName = order.supplier_name

          if (supplierId) {
            supplierIds.add(supplierId)
          }
          if (supplierName) {
            supplierNames.add(supplierName)
          }
        })

        // 优先使用供应商名称进行校验，如果名称相同则认为供应商相同
        const isValid = supplierNames.size <= 1
        return isValid
      }
      // 获取当前已选中采购明细的供应商信息
      const existingSupplierName = existingOrders[0].supplier_name

      // 检查新选中的采购单是否与已选中的供应商一致
      for (const order of selectedOrders) {
        const currentSupplierName = order.supplier_name

        // 优先使用供应商名称进行比较，如果名称相同则认为供应商相同
        if (existingSupplierName && currentSupplierName && currentSupplierName !== existingSupplierName) {
          return false
        }
      }
      return true
    },
  }

  // 打开采购单选择组件，传入供应商校验配置
  selectPurchaseOrderRef.value?.open(selectConfig, existingDataWithUUID, supplierValidationConfig)
}

// 处理采购单选择确认
const handlePurchaseOrderConfirm = (selectedOrders: any[]) => {
  // 检测是否有不同的供应商子公司
  const companySupplierIds = new Set()
  const companySupplierNames = new Set()

  selectedOrders.forEach((order) => {
    if (order.company_supplier_id) {
      companySupplierIds.add(order.company_supplier_id)
    }
    if (order.company_supplier_name) {
      companySupplierNames.add(order.company_supplier_name)
    }
  })

  // 更新表单中的供应商子公司显示
  if (companySupplierIds.size > 1) {
    // 如果有多个不同的供应商子公司，显示特殊信息
    if (safeSetFormData('company_supplier_name', '当前供应商存在多个子公司')) {
      formData.value.company_supplier_name = '当前供应商存在多个子公司'
    }
    // 保存多个供应商子公司ID数组
    multipleCompanySupplierIds.value = Array.from(companySupplierIds).map((id: any) => parseInt(id.toString()))
  } else if (companySupplierIds.size === 1) {
    // 如果只有一个供应商子公司，显示具体名称
    const companySupplierName = Array.from(companySupplierNames)[0]
    if (safeSetFormData('company_supplier_name', companySupplierName)) {
      formData.value.company_supplier_name = companySupplierName
    }
    // 清空多个供应商子公司ID数组
    multipleCompanySupplierIds.value = []
  }

  // 完全替换表格数据，而不是追加
  purchaseGridOptions.value = {
    ...purchaseGridOptions.value,
    data: selectedOrders.map((item, index) => ({
      ...item,
      serial_no: index + 1,
    })),
  }

  message.success(`已更新采购明细，共 ${selectedOrders.length} 个采购单`)

  // 计算应付总额
  calculateTotalAmount()

  // 自动更新供应商信息（仅在非批量对账模式下）
  if (selectedOrders.length > 0 && !isBatchMode.value) {
    updateSupplierInfo()
  } else if (selectedOrders.length === 0) {
    clearSupplierInfo()
  }
}

// 保存
const handleSave = async (isSubmit: boolean = true) => {
  try {
    saving.value = true

    if (purchaseDetailsData.value.length === 0) {
      message.warning('请先添加采购明细')
      return
    }
    // 获取表单验证结果
    const formState = await formRef.value.validate()

    // 使用表单中显示的应付总额，而不是重新计算
    const totalInvAmount = formData.value.inv_amount || 0

    // 从采购明细数据中获取供应商信息
    const firstPurchaseDetail = purchaseDetailsData.value[0]
    if (!firstPurchaseDetail) {
      message.error('没有采购明细数据')
      return
    }

    // 获取供应商ID，优先从采购明细中获取
    const supplierId = firstPurchaseDetail.supplier_id || firstPurchaseDetail.company_supplier_id
    if (!supplierId) {
      message.error('无法获取供应商ID')
      return
    }

    // 新增模式下根据供应商子公司数量设置子公司参数
    let companySupplierId: any = null
    let companySupplierName: any = null

    if (type.value === 'new') {
      // 新增模式
      if (multipleCompanySupplierIds.value.length > 1) {
        // 存在多个供应商子公司：传 company_supplier_name: null 和 company_supplier_id: 0
        companySupplierId = 0
        companySupplierName = null
      } else {
        // 如果只有单个供应商子公司，就正常传
        companySupplierId = firstPurchaseDetail.company_supplier_id
        companySupplierName = firstPurchaseDetail.company_supplier_name
      }
    } else {
      // 编辑/查看模式：保持原有逻辑
      companySupplierId = firstPurchaseDetail.company_supplier_id
      companySupplierName = firstPurchaseDetail.company_supplier_name
    }

    // 构建账单详情数据
    const details = buildBillDetails(purchaseDetailsData.value)
    // 新增模式下删除id
    if (type.value === 'new' || type.value === 'batch') {
      details.forEach((item) => {
        delete item.id
      })
    }

    // 使用银行舍入法计算实际应付金额，避免精度溢出
    // 使用表单中显示的值进行计算，确保与UI一致
    const actualPayableAmount = Number(addFloat(addFloat(totalInvAmount, -(formState.optimize_amount || 0)), addFloat(-(formState.deduct_amount || 0), formState.other_fees || 0)).roundNext(2))

    const params = {
      is_pass: isSubmit, // 根据是否提交审核设置
      ...formState,
      supplier_id: supplierId, // 使用从采购明细中获取的供应商ID
      company_supplier_id: companySupplierId, // 根据模式设置的子公司ID
      company_supplier_name: companySupplierName, // 根据模式设置的子公司名称
      inv_amount: Number(totalInvAmount.roundNext(2)), // 使用银行舍入法处理应付总额
      actual_payable_amount: actualPayableAmount, // 使用银行舍入法处理实际应付金额
      bill_month: dayjs(formState.bill_month).format('YYYY-MM'),
      details,
    }

    let response
    if (['new', 'batch'].includes(type.value)) {
      // 新增模式或批量对账模式：调用添加接口
      if (isSubmit) {
        // 提交审核：is_pass: true
        response = await AddBillOrder(params)
      } else {
        // 保存暂不提交：is_pass: false
        response = await AddBillOrder(params)
      }
    } else if (type.value === 'edit') {
      // 编辑模式：调用更新接口
      if (billId.value) {
        params.id = billId.value
      }
      if (isSubmit) {
        // 提交审核：is_pass: true
        response = await UpdateBillOrder(params)
      } else {
        // 保存暂不提交：is_pass: false
        response = await UpdateBillOrder(params)
      }
    } else {
      message.error(`不支持的编辑模式: ${type.value}`)
      return
    }

    if (response?.success) {
      const actionText = isSubmit ? '提交审核' : '保存'
      message.success(`${actionText}成功`)

      // 触发列表刷新事件，确保新创建的账单能显示在列表中
      // 批量对账模式下不触发列表刷新，避免死循环
      if (type.value !== 'batch') {
        eventBus.emit('refreshBillVerificationList')
      }

      // 设置刷新路径，确保返回【已对账待请款】页面
      setRefreshByPath('/billVerification')

      // 关闭当前页面
      closePage()
    } else {
      const actionText = isSubmit ? '提交审核' : '保存'
      message.error(response?.message || `${actionText}失败`)
    }
  } catch (error) {
    const actionText = isSubmit ? '提交审核' : '保存'
    console.error(`${actionText}失败:`, error)
    message.error(`${actionText}失败`)
  } finally {
    saving.value = false
  }
}

// 取消
const handleCancel = () => {
  // 取消时不清空store数据，保持账单状态

  // 触发列表刷新事件，确保列表数据是最新的
  // 批量对账模式下不触发列表刷新，避免死循环
  if (type.value !== 'batch') {
    eventBus.emit('refreshBillVerificationList')
  }

  closePage()
}

// 删除采购单
const handleDeletePurchaseOrder = (row: any) => {
  const currentData = purchaseDetailsData.value

  // 检查是否要按采购单分组删除
  if (row.purchase_number) {
    console.log('检测到删除采购单:', row.purchase_number)

    // 找到所有具有相同采购单号的行
    const samePurchaseRows = currentData.filter((item) => item.purchase_number === row.purchase_number)
    console.log(`采购单 ${row.purchase_number} 下的明细行数:`, samePurchaseRows.length)

    if (samePurchaseRows.length > 1) {
      // 如果有多个相同采购单的行，显示确认对话框
      Modal.confirm({
        title: '确认删除',
        content: `检测到采购单 ${row.purchase_number} 下有 ${samePurchaseRows.length} 个明细行，是否全部删除？`,
        okText: '全部删除',
        onOk() {
          // 用户选择全部删除
          deletePurchaseOrderRows(samePurchaseRows, row.purchase_number)
        },
      })
      return
    }
  }

  // 如果没有采购单号或只有一个明细行，直接删除
  deletePurchaseOrderRows([row], null)
}

// 执行删除操作
const deletePurchaseOrderRows = (rowsToDelete: any[], purchaseNumber: string | null) => {
  const currentData = purchaseDetailsData.value

  if (purchaseNumber) {
    console.log(`删除采购单 ${purchaseNumber} 下的所有明细行，共 ${rowsToDelete.length} 行`)
  } else {
    console.log('删除单个明细行')
  }

  // 从当前数据中移除要删除的行
  const newData = currentData.filter((item) => {
    return !rowsToDelete.some((deleteRow) => {
      // 优先使用id字段匹配
      if (item.id && deleteRow.id && item.id === deleteRow.id) {
        return true
      }
      // 如果id不匹配，使用detail_id
      if (item.detail_id && deleteRow.detail_id && item.detail_id === deleteRow.detail_id) {
        return true
      }
      // 如果detail_id也不匹配，使用purchase_number
      if (item.purchase_number && deleteRow.purchase_number && item.purchase_number === deleteRow.purchase_number) {
        return true
      }
      return false
    })
  })

  // 重新计算序号
  newData.forEach((item, idx) => {
    item.serial_no = idx + 1
  })

  // 更新表格数据
  purchaseGridOptions.value = {
    ...purchaseGridOptions.value,
    data: newData,
  }

  // 如果删除后采购明细列表长度为0，清空供应商信息
  if (newData.length === 0) {
    // 如果是批量对账模式，不要清空供应商信息，保持路由参数中的信息
    if (!isBatchMode.value) {
      clearSupplierInfo()
    }
  } else {
    // 如果还有数据，更新供应商信息
    // 如果是批量对账模式，不要更新供应商信息，保持路由参数中的信息
    if (!isBatchMode.value) {
      updateSupplierInfo()
    }
  }

  // 显示删除成功消息
  if (purchaseNumber) {
    message.success(`已删除采购单 ${purchaseNumber} 下的所有明细行，共 ${rowsToDelete.length} 行`)
  } else {
    message.success('删除成功')
  }

  // 重新计算应付总额
  calculateTotalAmount()
}

// 数字输入变化处理
const handleNumberChange = ({ row, column, value }: any) => {
  if (column.field === 'actual_unit_price') {
    // 实际单价变化时，重新计算相关金额
    const actualUnitPrice = Number(value || 0)
    const purchaseQuantity = Number(row.purchase_quantity || 0)
    const returnQuantity = Number(row.return_quantity || 0)

    // 1. 采购总金额 = 实际单价 × 采购数量
    if (purchaseQuantity > 0) {
      row.total_purchase_amount = Number((actualUnitPrice * purchaseQuantity).roundNext(2))
    }

    // 2. 退款金额：优先使用API返回的 refund_amount 字段值
    if (!row.refund_amount && returnQuantity > 0) {
      row.refund_amount = Number((actualUnitPrice * returnQuantity).roundNext(2))
    }

    // 3. 采购单应付金额：修改实际单价后，强制重新计算
    const invAmount = Number(addFloat(row.total_purchase_amount || 0, row.refund_amount || 0).roundNext(2))
    row.inv_amount = invAmount

    // 重新计算应付总额
    calculateTotalAmount()
  }
}

// 编辑关闭事件 - 处理实际单价修改
const handleEditClosed = ({ row, column }: any) => {
  // 如果是实际单价列被编辑
  if (column.field === 'actual_unit_price') {
    // 确保数值有效并设置正确精度
    const newValue = parseFloat(row.actual_unit_price) || 0
    const roundedValue = newValue

    // 更新表格数据
    const currentData = purchaseDetailsData.value
    const index = currentData.findIndex((item) => {
      // 优先使用id字段
      if (item.id && row.id && item.id === row.id) {
        return true
      }
      // 如果id不匹配，使用detail_id
      if (item.detail_id && row.detail_id && item.detail_id === row.detail_id) {
        return true
      }
      // 如果detail_id也不匹配，使用purchase_number
      if (item.purchase_number && row.purchase_number && item.purchase_number === row.purchase_number) {
        return true
      }
      return false
    })

    if (index > -1) {
      const newData = [...currentData]
      const updatedItem = {
        ...newData[index],
        actual_unit_price: roundedValue,
      }

      // 根据需求重新计算相关金额
      // 1. 采购总金额 = 实际单价 × 采购数量
      if (updatedItem.purchase_quantity) {
        // 使用银行舍入法避免浮点数精度问题
        const totalAmount = Number((roundedValue * updatedItem.purchase_quantity).roundNext(2))
        updatedItem.total_purchase_amount = totalAmount
      }

      // 2. 退款金额：优先使用API返回的 refund_amount 字段值
      // 只有在没有退款金额时才重新计算
      if (!updatedItem.refund_amount && updatedItem.return_quantity && updatedItem.return_quantity > 0) {
        // 使用银行舍入法避免浮点数精度问题
        const refundAmount = Number((roundedValue * updatedItem.return_quantity).roundNext(2))
        updatedItem.refund_amount = refundAmount
      }

      // 3. 采购单应付金额：修改实际单价后，强制重新计算
      // 因为实际单价变化会影响采购总金额，进而影响应付金额
      const invAmount = Number(addFloat(updatedItem.total_purchase_amount, updatedItem.refund_amount).roundNext(2))
      updatedItem.inv_amount = invAmount

      newData[index] = updatedItem

      purchaseGridOptions.value = {
        ...purchaseGridOptions.value,
        data: newData,
      }

      message.success('实际单价修改成功，相关金额已重新计算')

      // 重新计算应付总额
      calculateTotalAmount()
    } else {
      console.error('未找到要更新的项目:', row)
      message.error('更新失败，未找到对应项目')
    }
  }
}

// 计算应付总额
const calculateTotalAmount = () => {
  const totalInvAmount = purchaseDetailsData.value.reduce((sum, item) => {
    return sum + (item.inv_amount || 0)
  }, 0)

  const totalGiftAmount = purchaseDetailsData.value.reduce((sum, item) => {
    return sum + (item.gift_amount_due || 0)
  }, 0)

  // 使用银行舍入法处理合计精度问题
  const totalAmount = Number(addFloat(totalInvAmount, totalGiftAmount).roundNext(2))

  // 更新表单中的应付总额
  formData.value.inv_amount = totalAmount

  // 计算实际应付金额
  calculateActualPayableAmount()
}

// 计算实际应付金额
const calculateActualPayableAmount = () => {
  const invAmount = formData.value.inv_amount || 0
  const optimizeAmount = formData.value.optimize_amount || 0
  const deductAmount = formData.value.deduct_amount || 0
  const otherFees = formData.value.other_fees || 0

  // 使用银行舍入法处理浮点数计算精度问题
  // 先计算 invAmount - deductAmount - optimizeAmount
  let tempAmount = Number(addFloat(invAmount, -deductAmount).roundNext(2))
  tempAmount = Number(addFloat(tempAmount, -optimizeAmount).roundNext(2))
  // 最后加上 otherFees
  const actualPayableAmount = Number(addFloat(tempAmount, otherFees).roundNext(2))

  // 更新表单中的实际应付金额
  formData.value.actual_payable_amount = actualPayableAmount
}

// 初始化方法
const initMethod = async () => {
  const {
    params: { id },
    meta: { code },
  } = route

  // 根据路由 meta.code 判断当前模式
  if (code === 'billVerificationView') {
    type.value = 'detail'
  } else if (code === 'billVerificationEdit') {
    type.value = 'edit'
  } else if (code === 'billVerificationNew') {
    type.value = 'new'
  } else {
    // 如果没有明确的 code，根据路径判断
    if (id === 'new') {
      type.value = 'new'
    } else if (id && id.length > 10 && /^\d+$/.test(id as string)) {
      type.value = 'batch' // 批量对账模式
    } else if (id) {
      type.value = 'detail' // 默认查看模式
    } else {
      type.value = 'new'
    }
  }

  if (type.value === 'new') {
    // 新增模式：直接设置默认值
    formData.value = {
      charge_into_cost: '按金额计入成本',
    }
    purchaseGridOptions.value = { ...purchaseGridOptions.value, data: [] }
  }
  if (type.value === 'batch') {
    initFormData()

    // 设置默认的账单月份为当前月份（在initFormData清理数据之后）
    formData.value.bill_month = dayjs()

    // 检查是否是新的批量对账请求
    const batchData = route.query.batchData ? JSON.parse(decodeURIComponent(route.query.batchData as string)) : null
    const newBatchId = batchData?.timestamp?.toString()

    if (newBatchId && newBatchId !== currentBatchId.value) {
      currentBatchId.value = newBatchId
      // 只在初始化时加载一次，避免重复调用
      if (!isLoadingPurchaseDetails.value) {
        await loadPurchaseDetails()
      }
    }

    // 批量对账模式下不触发列表刷新，避免死循环
    // eventBus.emit('refreshBillVerificationList')
  } else if (['detail', 'edit'].includes(type.value)) {
    initFormData()

    if (id && !loadedBillId.value) {
      await loadBillDetails(id as string)
      // 无论 details 是否为空，都保留接口返回的供应商信息
      // 因此此处不再根据明细清空或覆盖 supplier_name/company_supplier_name
    }
  }

  // 初始化计算
  calculateActualPayableAmount()

  // 仅在【新增】模式下根据明细同步供应商信息；
  // 【查看】/【编辑】模式保留接口返回的供应商信息，不做覆盖或清空
  if (type.value === 'new') {
    if (purchaseDetailsData.value.length === 0) {
      clearSupplierInfo()
    } else {
      updateSupplierInfo()
    }
  }
  // 批量模式与查看/编辑模式在此不做供应商信息处理

  // 标记已初始化
  hasInitialized.value = true
}

// 组件挂载时初始化数据
onMounted(async () => {
  await initMethod()
})

// 监听路由变化
watch(
  () => [route.query, route.params],
  ([newQuery, newParams], [oldQuery, oldParams]) => {
    // 只在路由真正变化时才执行，避免初始化时的重复调用
    if (JSON.stringify([newQuery, newParams]) === JSON.stringify([oldQuery, oldParams])) {
      return
    }

    const currentRouteId = newParams.id as string
    const currentRouteCode = route.meta?.code

    // 重置初始化标志，允许重新初始化
    hasInitialized.value = false

    // 根据路由信息重新设置type
    if (currentRouteCode === 'billVerificationView') {
      type.value = 'detail'
    } else if (currentRouteCode === 'billVerificationEdit') {
      type.value = 'edit'
    } else if (currentRouteCode === 'billVerificationNew') {
      type.value = 'new'
    } else if (currentRouteId === 'new') {
      type.value = 'new'
    } else if (currentRouteId && currentRouteId.length > 10 && /^\d+$/.test(currentRouteId)) {
      // 批量对账模式：ID是长数字（时间戳）
      type.value = 'batch'
    } else if (currentRouteId && currentRouteId !== 'new' && !(currentRouteId.length > 10 && /^\d+$/.test(currentRouteId))) {
      // 其他模式：ID是普通数字（账单ID）
      type.value = 'detail'
    } else {
      // 没有ID的情况
      type.value = 'new'
    }
  },
  { immediate: false }, // 避免初始化时立即执行
)

// 监听采购明细数据变化，自动计算应付总额
watch(
  () => purchaseDetailsData.value,
  () => {
    // 如果是新增模式，不执行任何数据设置逻辑
    if (isNewMode.value) {
      return
    }

    if (purchaseDetailsData.value.length > 0) {
      calculateTotalAmount()
      // 自动更新供应商信息（仅在非批量对账模式下）
      if (!isBatchMode.value) {
        updateSupplierInfo()
      }
    } else {
      // 清空供应商信息（仅在非批量对账模式下）
      if (!isBatchMode.value) {
        clearSupplierInfo()
      }
      // 清空所有金额字段（仅在非新增模式下）
      if (!isNewMode.value) {
        formData.value.inv_amount = 0
        formData.value.actual_payable_amount = 0
        formData.value.optimize_amount = 0
        formData.value.deduct_amount = 0
        formData.value.other_fees = 0
      }
    }
  },
  { deep: true },
)

// 更新供应商信息（仅用于【新增】场景的明细驱动）
const updateSupplierInfo = () => {
  if (purchaseDetailsData.value.length === 0) return
  const firstOrder = purchaseDetailsData.value[0]
  const supplierName = firstOrder.supplier_name
  const companySupplierName = firstOrder.company_supplier_name

  // 检查是否有多个供应商子公司
  const companySupplierIds = new Set()
  purchaseDetailsData.value.forEach((order) => {
    if (order.company_supplier_id) {
      companySupplierIds.add(order.company_supplier_id)
    }
  })

  // 仅当 type 为 new 时由明细写回基本信息
  if (type.value !== 'new') return

  if (safeSetFormData('supplier_name', supplierName || '')) {
    formData.value.supplier_name = supplierName || ''
  }

  if (companySupplierIds.size > 1) {
    if (safeSetFormData('company_supplier_name', '该供应商存在多个子公司')) {
      formData.value.company_supplier_name = '该供应商存在多个子公司'
    }
  } else {
    if (safeSetFormData('company_supplier_name', companySupplierName || '')) {
      formData.value.company_supplier_name = companySupplierName || ''
    }
  }
}

// 清空供应商信息
const clearSupplierInfo = () => {
  // 清空表单中的供应商信息
  formData.value.supplier_name = ''
  formData.value.company_supplier_name = ''
}

// 监听表单字段变化，自动计算实际应付金额
watch(
  () => [formData.value.inv_amount, formData.value.optimize_amount, formData.value.deduct_amount, formData.value.other_fees],
  () => {
    calculateActualPayableAmount()
  },
  { deep: true },
)

// 组件卸载时重新启用KeepAlive缓存
onUnmounted(() => {
  pageStore.removeByExclude('/billVerification/generateBill')

  // 注意：不要在组件卸载时清空store状态，保持账单在【已对账待请款】状态

  // 如果是在批量对账模式下，确保列表数据是最新的
  // 批量对账模式下不触发列表刷新，避免死循环
  if (isBatchMode.value && type.value !== 'batch') {
    eventBus.emit('refreshBillVerificationList')
  }
})
</script>

<style scoped>
.content-area {
  flex: 1;
  padding: 16px;
  padding-bottom: 80px; /* 为底部固定按钮留出空间 */
  overflow-y: auto;
}

.form-section {
  padding: 16px;
  margin-bottom: 16px;
  background: #fff;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
}
</style>
