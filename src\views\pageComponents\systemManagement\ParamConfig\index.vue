<template>
  <div class="main">
    <div class="title">系统配置</div>
    <template v-for="item in settingList" :key="item.type">
      <div class="main-box">
        <div class="labelbox">{{ item.name }}</div>
        <a-button type="primary" @click="handleSetting(item)" v-if="btnPermission[item.permission]">设置</a-button>
      </div>
    </template>
    <a-drawer :maskClosable="false" v-model:open="visible" :title="dlgTitle" placement="right" :footer-style="{ textAlign: 'left' }" width="700">
      <template v-for="item in configList" :key="item.key">
        <div class="mb-16 flex items-center">
          <span class="c-#333 mr-16 w-200 text-right">{{ item.label }}</span>
          <!-- 开关 -->
          <a-switch v-if="'switch_case' in item" v-model:checked="item.switch_case" :checked-value="true" :un-checked-value="false">
            <template #checkedChildren>
              <span class="iconfont icon-zhengquede_correct"></span>
            </template>
            <template #unCheckedChildren>
              <span class="iconfont icon-guanbi_close"></span>
            </template>
          </a-switch>
          <!-- 输入框 -->
          <template v-if="dlgType === 2">
            <a-input-number class="ml-10 mr" v-model:value="item.val" />
          </template>
          <span class="text-black">{{ item.unit }}</span>
          <span class="text-black ml-20px">{{ item.toolText }}</span>
        </div>
      </template>
      <template #footer>
        <a-space :size="12">
          <a-button type="primary" @click="handleSaveConfig">保存</a-button>
          <a-button @click="handleCancel">取消</a-button>
        </a-space>
      </template>
    </a-drawer>
  </div>
</template>

<script setup lang="ts">
import { message } from 'ant-design-vue'

import { GetSystemParamConfigList, AddSystemParamConfig, UpdateSystemParamConfig, RefreshByKey } from '@/servers/System'

const { btnPermission } = usePermission()
const settingList = ref([
  {
    name: '采购流程参数配置',
    type: 1,
    keys: [
      { key: 'allowOverApply', switch_case: false, isAdd: true, label: '允许超申请数量采购', toolText: '' },
      { key: 'AllowAutoCreatePurchaseOrder', switch_case: false, isAdd: true, label: '加工申请单审核自动创建采购单', toolText: '' },
      { key: 'AllowAutoCreateBooking', switch_case: false, isAdd: true, label: '采购订单自动生成预约入库', toolText: '' },
      { key: 'AllowNotOutboundInBooking', switch_case: false, isAdd: true, label: '委外订单未领料是否允许预约入库', toolText: '' },
    ],
    permission: 32002,
  },
  {
    name: '同步1688设置',
    type: 2,
    keys: [
      {
        key: 'orderSync',
        switch_case: false,
        val: null,
        isAdd: true,
        label: '同步1688订单同步',
        unit: '分钟',
        toolText: '(提示：未设置时同步时间默认为60分钟)',
      },
      {
        key: 'logisticsCompany',
        switch_case: false,
        val: null,
        isAdd: true,
        label: '物流公司同步间隔',
        unit: '小时',
        toolText: '(提示：未设置时同步时间默认为12小时）',
      },
      {
        key: 'SyncProductSkuInfo',
        switch_case: false,
        val: null,
        isAdd: true,
        label: '同步金蝶商品库',
        unit: '分钟',
        toolText: '(提示：未设置时同步时间默认为60分钟）',
      },
      {
        key: 'SyncProductSkuInventory',
        switch_case: false,
        val: null,
        isAdd: true,
        label: '同步聚水潭商品库存',
        unit: '分钟',
        toolText: '(提示：未设置时同步时间默认为15分钟）',
      },
    ],
    permission: 32002,
  },
])
const visible = ref(false)
const dlgType = ref()
const dlgTitle = ref()
const configList = ref<any>([])

const handleSetting = (row) => {
  dlgTitle.value = row.name
  dlgType.value = row.type
  configList.value = settingList.value.find((v) => v.type == dlgType.value)?.keys
  configList.value.forEach(async (item) => {
    const obj = { type: dlgType.value, key: item.key }
    await getSystemConfig(obj)
  })
  visible.value = true
}

// 获取系统配置
const getSystemConfig = (obj) => {
  GetSystemParamConfigList(obj).then((res) => {
    const arr = res.data.list
    configList.value.map((item) => {
      if (item.key == obj.key) {
        if (arr.length > 0) {
          item.val = arr[0].val ? arr[0].val : undefined
          item.switch_case = arr[0].switch_case
          item.isAdd = false
        } else {
          item.isAdd = true
        }
      }
      return item
    })
  })
}

// 保存系统配置
const handleSaveConfig = async () => {
  configList.value.forEach(async (item) => {
    await saveConfig(item)
  })
  handleCancel()
}

const saveConfig = (obj) => {
  const api = obj.isAdd ? AddSystemParamConfig : UpdateSystemParamConfig
  api({
    type: dlgType.value,
    key: obj.key,
    val: obj.val,
    switch_case: obj.switch_case,
  }).then((res) => {
    if (res.success) {
      message.success(`设置${obj.label}成功`)
      if (['小时', '分钟'].includes(obj.unit)) {
        refreshByKey(obj)
      }
    } else {
      message.error(res.message)
    }
  })
}

const refreshByKey = (row) => {
  console.log(row)

  RefreshByKey({ type: dlgType.value, key: row.key, is_hours: row.unit == '小时' }).then((res) => {
    if (res.success) {
      console.log(`${row.key}刷新间隔`)
    }
  })
}

// 取消
const handleCancel = () => {
  visible.value = false
  configList.value = []
}

onMounted(() => {})
</script>

<style scoped lang="scss">
.title {
  margin-top: 20px;
  margin-bottom: 16px;
  font-size: 20px;
  font-weight: 600;
  color: #333;
}

.main-box {
  padding: 16px;
  margin-bottom: 10px;
  color: #666;
  border: 1px solid #ccc;

  .labelbox {
    display: inline-block;
    width: 100px;
    margin-right: 16px;
  }
}
</style>
