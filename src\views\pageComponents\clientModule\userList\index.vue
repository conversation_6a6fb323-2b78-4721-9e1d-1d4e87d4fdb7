<template>
  <div class="main">
    <Form ref="formRef" v-model:form="formArr" @search="search" @setting="tableRef?.showTableSetting()" :page-type="PageTypeEnum.USER_MANAGE">
      <template #header>
        <StatusTabs
          v-model:status="scope"
          :options="
            [
              { label: '外部联系人', value: 2, show: btnPermission[21100] },
              { label: '内部联系人', value: 1, show: btnPermission[21200] },
            ].filter((item) => item.show)
          "
        />
      </template>
    </Form>

    <BaseTable ref="tableRef" :page-type="PageTypeEnum.USER_MANAGE" v-model:form="formArr" :get-list="GetList" :auto-search="false" :data-format="dataFormat">
      <template #left-btn>
        <a-button @click="tapBatch(1)" v-if="(scope == 1 && btnPermission[21202]) || (scope == 2 && btnPermission[21104])">批量停用</a-button>
        <a-button @click="tapBatch(0)" v-if="(scope == 1 && btnPermission[21202]) || (scope == 2 && btnPermission[21104])">批量启用</a-button>
      </template>
      <template #right-btn>
        <a-button v-if="scope == 2" type="primary" @click="tapManipulate('add')">新建用户</a-button>
      </template>
      <template #column>
        <vxe-column type="checkbox" width="40"></vxe-column>
        <template v-for="item in tableKey" :key="item.key">
          <vxe-column
            v-if="(scope == 1 && item.key != 'customer_name') || (scope == 2 && item.key != 'sub_company_name')"
            :sortable="item.is_sort"
            :field="item.key"
            :title="item.name"
            :width="item.width"
            :fixed="item.freeze == 1 ? 'left' : item.freeze == 2 ? 'right' : ''"
            :tree-node="item.treeNode"
          >
            <template v-if="item.key == 'fix_option'" #default="{ row, column }">
              <RightOperate :list="rightOperateList" :row="row" :column="column" />
            </template>

            <template v-if="item.key == 'customer_name'" #default="{ row }">
              {{ supplierNameMap[row.customer_id] }}
            </template>

            <template v-if="item.key == 'scope'" #default="{ row }">
              {{ row['scope'] == 1 ? '内部联系人' : '外部联系人' }}
            </template>
            <template v-if="item.key == 'status'" #default="{ row }">
              <div class="btnBox" v-if="(scope == 1 && btnPermission[21202]) || (scope == 2 && btnPermission[21104])">
                <!-- <a-switch class="btn" @click="tapSwitch(row.status, [row.id])" v-model:checked="[false, true][row.status]" checked-children="启用" un-checked-children="停用" /> -->

                <a-switch v-model:checked="[false, true][row.status]" @click="tapSwitch(row.status, [row.id])">
                  <template #checkedChildren>
                    <span class="iconfont icon-zhengquede_correct"></span>
                  </template>
                  <template #unCheckedChildren>
                    <span class="iconfont icon-guanbi_close"></span>
                  </template>
                </a-switch>
              </div>
              <div class="btnBox" v-else>
                {{ row.status ? '启用' : '停用' }}
              </div>
            </template>
            <!-- <template v-if="item.key == 'role_names'" #default="{ row }">
              <a-tag v-for="item2 in row['role_names']" :key="item2">{{ item2 }}</a-tag>
            </template> -->
            <template v-if="item.key == 'source_type'" #default="{ row }">
              <span>{{ formatOptionLabel(row.source_type, sourceOption) }}</span>
            </template>
          </vxe-column>
        </template>
      </template>
    </BaseTable>

    <a-drawer
      v-model:open="isAddUser"
      width="31.25vw"
      :title="userModuleType == 'add' ? '新建用户' : '编辑用户'"
      placement="right"
      :maskClosable="false"
      :closable="false"
      :footer-style="{ textAlign: 'left' }"
    >
      <a-form ref="updateFormRef" :model="formData" :rules="rules" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
        <a-form-item label="所属供应商" name="customer" v-if="userModuleType == 'add'">
          <a-button type="link" size="small" @click="supplierOpen">{{ formData.customer ? formData.customer.supplier_name : '请选择供应商' }}</a-button>
        </a-form-item>
        <a-form-item label="帐号" name="user_name">
          <a-input autocomplete="off" v-model:value="formData.user_name" placeholder="请输入帐号" :disabled="userModuleType == 'compiler'" />
        </a-form-item>
        <a-form-item label="密码" name="password" v-if="userModuleType == 'add'">
          <a-input-password autocomplete="off" v-model:value="formData.password" placeholder="请输入密码" />
        </a-form-item>
        <a-form-item label="确认密码" name="passwordStr" v-if="userModuleType == 'add'">
          <a-input-password autocomplete="off" v-model:value="formData.passwordStr" placeholder="请输入确认密码" />
        </a-form-item>
        <a-form-item label="用户名称" name="real_name">
          <a-input v-model:value="formData.real_name" placeholder="请输入用户名称" :disabled="scope == 1 && userModuleType == 'compiler'" />
        </a-form-item>

        <a-form-item label="所属部门" name="department_id" v-if="scope == 1">
          <a-tree-select
            class="treeSelect"
            :disabled="userModuleType == 'compiler'"
            v-model:value="formData.department_id"
            placeholder="选择部门"
            allow-clear
            tree-default-expand-all
            :tree-data="department_Arr"
            :fieldNames="{
              children: 'childrenList',
              label: 'departmentname',
              value: 'departmentid',
            }"
            :maxTagCount="1"
          />
        </a-form-item>
        <a-form-item label="角色" name="role_ids">
          <a-select
            class="select"
            v-model:value="formData.role_ids"
            style="width: 100%"
            placeholder="选择角色"
            :maxTagCount="1"
            :options="role_Arr"
            :field-names="{ label: 'role_name', value: 'role_id' }"
          ></a-select>
        </a-form-item>
        <a-form-item label="状态" name="status" v-if="userModuleType == 'add'">
          <a-radio-group v-model:value="formData.status" name="radioGroup">
            <a-radio value="1">启用</a-radio>
            <a-radio value="0">停用</a-radio>
          </a-radio-group>
        </a-form-item>
      </a-form>

      <template #footer>
        <a-button style="margin-right: 10px" type="primary" @click="tapaddUserSubmit">确认</a-button>
        <a-button @click="isAddUser = false">取消</a-button>
      </template>
    </a-drawer>

    <a-modal v-model:open="visibleData.isShow" :title="visibleData.title">
      <div class="modalContent">{{ visibleData.content }}</div>
      <template #footer>
        <a-button v-if="visibleData.isConfirmBtn" style="margin-right: 10px" type="primary" @click="visibleData.okFn">{{ visibleData.confirmBtnText }}</a-button>
        <a-button v-if="visibleData.isCancelBtn" @click="visibleData.isShow = false">取消</a-button>
      </template>
    </a-modal>

    <Supplier ref="supplierRef" @change="(val) => (formData.customer = val)"></Supplier>

    <ExtraFormDrawer ref="extraFormDrawerRef" @query="search" />
    <InnerFormDrawer ref="innerFormDrawerRef" @query="search" />
    <UserInfoDrawer ref="userInfoDrawerRef" />
  </div>
</template>
<script lang="ts" setup>
import { message } from 'ant-design-vue'
import { onMounted, ref } from 'vue'

import { sourceOption } from '@/common/options'
import { formatOptionLabel, filterOption } from '@/utils'

import Supplier from './components/SupplierRadio.vue'
import ExtraFormDrawer from './components/ExtraFormDrawer.vue'
import InnerFormDrawer from './components/InnerFormDrawer.vue'
import UserInfoDrawer from './components/UserInfoDrawer.vue'

import {
  Add,
  Delete,
  GetDepartmentTreeList,
  GetExtraList,
  GetInnerList,
  GetUserSupplierList,
  GetUserDeptNameList,
  ResetPwd,
  UpdateExtra,
  UpdateExtraStatus,
  UpdateInner,
  UpdateInnerStatus,
  GetCompanyList,
  GetSupplierList,
} from '@/servers/UserManager'
import { GetRoleSelectOption } from '@/servers/Role'
import { GetEnum } from '@/servers/Common'

import { PageTypeEnum } from '@/enums/tableEnum'

const tableKey = computed(() => {
  if (!tableRef.value?.tableKey) return []
  return tableRef.value.tableKey
})

const rightOperateList = computed(() => [
  {
    label: '查看',
    onClick: ({ row }) => tapManipulate('view', row),
  },
  {
    label: '编辑',
    show: () => (scope.value == 2 && btnPermission.value[21102]) || (scope.value == 1 && btnPermission.value[21201]),
    onClick: ({ row }) => tapManipulate('compiler', row),
  },
  {
    label: '重置密码',
    show: () => scope.value == 2 && btnPermission.value[21103],
    onClick: ({ row }) => tapManipulate('resetPassword', row),
  },
])

const isAddUser = ref(false)

const extraFormDrawerRef = ref()
const innerFormDrawerRef = ref()
const userInfoDrawerRef = ref()

const { btnPermission } = usePermission()

const scope = ref(2)
const userModuleType = ref('add')

const GetList = ref(GetExtraList)

const tableRecords = ref([])
const visibleDataStr = () => ({
  isShow: false,
  isConfirmBtn: true,
  isCancelBtn: true,
  confirmBtnText: '确定',
  title: '',
  content: '',
  okFn: () => {
    visibleData.value.isShow = false
  },
})
const visibleData: any = ref(visibleDataStr())

const tableRef = ref()
const formRef = ref()
const search = () => tableRef.value.search()

const supplierNameMap = ref({} as any)
const departmentMap = ref<any>({})

const dataFormat = (data) => {
  const supplierIds = [...new Set(data.map((v) => v.customer_id).filter((v) => v))] as any[]
  if (supplierIds.length) {
    GetUserSupplierList({
      supplier_id: supplierIds,
    }).then((res) => {
      res.data.forEach((v) => {
        supplierNameMap.value[v.supplier_id] = v.supplier_name
      })
    })
  }
  const department_id = [...new Set(data.map((v) => v.department_id).filter((v) => v))]
  if (department_id.length) {
    GetUserDeptNameList({
      department_id,
    }).then((res) => {
      res.data.forEach((v) => {
        departmentMap.value[v.department_id] = v.department_name
      })
    })
  }
  return data
}

const formArr: any = ref([
  {
    label: '搜索用户名称',
    value: null,
    type: 'input',
    selectArr: [],
    key: 'real_name',
  },
  {
    label: '搜索帐号',
    value: null,
    type: 'input',
    selectArr: [],
    key: 'user_name',
  },
  {
    label: '所属供应商',
    value: null,
    type: 'select',
    selectArr: [],
    key: 'customer_id',
    showSearch: true,
    filterOption,
  },
  {
    label: '所属公司',
    value: null,
    type: '',
    selectArr: [],
    key: 'sub_company_id',
    onChange: (id) => {
      if (id) {
        GetDepartmentTreeList({ id }).then((res) => {
          department_Arr.value = res.data
          formArr.value.forEach((item) => {
            if (item.key == 'departmentid') {
              item.selectArr = res.data
              item.value = null
            }
          })
        })
      } else {
        GetDepartmentTreeList().then((res) => {
          department_Arr.value = res.data
          formArr.value.forEach((item) => {
            if (item.key == 'departmentid') {
              item.selectArr = res.data
              item.value = null
            }
          })
        })
      }
    },
  },
  {
    label: '所在部门',
    value: null,
    type: '',
    fieldNames: {
      children: 'childrenList',
      label: 'departmentname',
      value: 'departmentid',
    },
    selectArr: [],
    key: 'departmentid',
  },
  {
    label: '角色',
    value: null,
    type: 'select',
    fieldNames: {
      label: 'role_name',
      value: 'role_id',
    },
    selectArr: [],
    key: 'role_ids',
  },
  {
    label: '状态',
    value: null,
    type: 'select',
    selectArr: [],
    key: 'status',
  },
  {
    label: '来源',
    value: null,
    type: 'select',
    selectArr: sourceOption,
    key: 'source_type',
  },
  {
    label: '创建时间',
    value: null,
    type: 'range-picker',
    key: 'create_at',
    formKeys: ['start_time', 'end_time'],
    placeholder: ['创建开始时间', '创建结束时间'],
  },
  {
    label: '修改时间',
    value: null,
    type: 'range-picker',
    key: 'update_at',
    formKeys: ['update_start_time', 'update_end_time'],
    placeholder: ['修改开始时间', '修改结束时间'],
  },
])
const role_Arr = ref([])
const department_Arr = ref([])
const updateFormRef = ref()

const formDataStr = () => ({
  user_id: null,
  user_name: null,
  password: null,
  passwordStr: null,
  real_name: null, // 用户名称
  role_ids: null, // 角色ids
  // customer_id: null, // 客户id
  status: '1',
  department_id: null,
  id: null,
  old_password: null,
  new_password: null,
})
const formData: any = ref(formDataStr())
const supplierRef = ref()
const supplierOpen = () => {
  supplierRef.value.open(formData.value.customer)
}
const rules = ref({
  customer: [{ required: true, message: '请选择供应商' }],
  user_name: [{ required: true, message: '请输入帐号' }],
  password: [{ required: true, message: '请输入密码' }],
  passwordStr: [
    { required: true, message: '请输入确认密码' },
    {
      validator: (_rule, value) => {
        if (value != formData.value.password) {
          return Promise.reject('两次密码不一致')
        }
        return Promise.resolve()
      },
    },
  ],
  real_name: [{ required: true, message: '请输入用户名称' }],
  department_id: [{ required: true, message: '请选择部门' }],
})
const enumData: any = ref([])

const checkScope = () => {
  formArr.value.forEach((item) => {
    if (item.key == 'departmentid') {
      item.selectArr = []
      item.value = null
      item.type = scope.value == 1 ? '' : 'select_Tree'
      if (scope.value == 2) {
        item.type = ''
      } else {
        item.type = 'select_Tree'
      }
    }
    if (item.key == 'customer_id') {
      item.type = scope.value == 1 ? '' : 'select'
    }
    if (item.key == 'sub_company_id') {
      item.type = scope.value == 1 ? 'select' : ''
    }
  })
}
watch(scope, () => {
  checkScope()
  checkGetList()
  getRoleSelectOption()
  getDepartmentTreeList()

  nextTick(() => search())
})

onMounted(() => {
  getEnum()

  checkedBtnShow()
})

onActivated(() => {
  getSupplierOption()
  getCompanyList()
})
// 按钮权限
const checkedBtnShow = () => {
  if (btnPermission.value[21200] && btnPermission.value[21100]) {
    scope.value = 2
  } else if (btnPermission.value[21200]) {
    scope.value = 1
  } else if (btnPermission.value[21100]) {
    scope.value = 2
  }
  getRoleSelectOption()
  checkGetList()
  nextTick(() => {
    search()
  })
}

const checkGetList = () => {
  if (scope.value == 2) {
    GetList.value = GetExtraList
  } else {
    GetList.value = GetInnerList
  }
}

// 点击批量备货按钮
const tapBatch = (status) => {
  if (tableRef.value.checkItemsArr?.length) {
    const arr = [] as any[]
    tableRef.value.checkItemsArr.forEach((item) => {
      arr.push(item.id)
    })
    tapSwitch(status, arr)
  } else {
    message.info('请勾选对应项~')
  }
}
const tapaddUserSubmit = () => {
  switch (userModuleType.value) {
    case 'add':
      updateFormRef.value.validate().then(() => {
        addUser()
      })
      break
    case 'compiler':
      updateFormRef.value.validate().then(() => {
        setUserDate()
      })
      break
    default:
      break
  }
}
const tapManipulate = (type: string, row: any = '') => {
  formData.value = formDataStr()
  visibleData.value = visibleDataStr()
  switch (type) {
    case 'view':
      row.deptName = departmentMap.value[row.department_id]
      userInfoDrawerRef.value.open(row, scope.value, supplierNameMap.value)
      break
    case 'add':
      extraFormDrawerRef.value.open(1)
      // isAddUser.value = true
      // userModuleType.value = 'add'
      break
    case 'compiler':
      if (scope.value == 1) {
        innerFormDrawerRef.value.open(2, row)
      } else {
        extraFormDrawerRef.value.open(2, row.id)
      }
      break
    case 'removes':
      visibleData.value.isShow = true
      visibleData.value.title = '删除用户：'
      visibleData.value.content = `
      即将删除该用户的帐号，删除后：

          * 该用户将无法访问系统的任何功能或数据。
          * 用户的操作历史记录和相关数据将保留在系统中。

      请在执行此操作前确认：

          * 该用户的相关工作已妥善移交或完成。

      此操作不可恢复，确定要删除该用户的帐号吗？`
      visibleData.value.confirmBtnText = '确定'
      visibleData.value.isCancelBtn = true
      visibleData.value.okFn = () => {
        deleteRole(row.id)
      }
      break
    case 'resetPassword':
      visibleData.value.isShow = true
      visibleData.value.title = '重置密码'
      visibleData.value.content = `
      即将为该用户重置密码，重置后：
        * 用户的密码将被重置为系统的初始默认密码。
        * 用户需使用默认密码重新登录并更新为新的个人密码。
      确定要重置该用户的密码吗？`
      visibleData.value.confirmBtnText = '确定'
      visibleData.value.isCancelBtn = true
      visibleData.value.okFn = () => {
        resetPwd(row.id)
      }
      break
    default:
      break
  }
}
const tapSwitch = (status, arr) => {
  if (!status) {
    updateExtraStatus({ user_ids: arr, status: 1 })
  } else {
    visibleData.value.isShow = true
    visibleData.value.title = '停用用户：'
    visibleData.value.content = `
    停用后，该用户将无法访问系统的任何功能或数据。
    确定要停用该用户的帐号吗？`
    visibleData.value.confirmBtnText = '停用'
    visibleData.value.isCancelBtn = true
    visibleData.value.okFn = () => {
      updateExtraStatus({ user_ids: arr, status: 0 })
    }
  }
}

// 批量停用/启用
const updateExtraStatus = (obj) => {
  const api = scope.value == 1 ? UpdateInnerStatus : UpdateExtraStatus
  api(obj).then(() => {
    visibleData.value.isShow = false
    message.success('设置成功~')
    tableRecords.value = []
    tableRef.value.refresh()
  })
}
// 获取所有枚举选项
const getEnum = () => {
  GetEnum().then((res) => {
    enumData.value = res.data
    formArr.value.forEach((item) => {
      if (item.key == 'status') {
        item.selectArr = enumData.value.common.status
      }
      if (item.key == 'scope') {
        item.selectArr = enumData.value.role.scope
      }
    })
  })
}
// 新增用户
const addUser = () => {
  console.log(formData.value)

  Add({
    ...formData.value,
    customer_id: formData.value.customer?.supplier_id,
  }).then(() => {
    message.success('新增成功')
    isAddUser.value = false
    tableRef.value.refresh()
  })
}
// 编辑用户
const setUserDate = () => {
  const api = scope.value == 1 ? UpdateInner : UpdateExtra
  api({
    user_id: formData.value.user_id,
    real_name: formData.value.real_name,

    role_ids: formData.value.role_ids,
    ...(scope.value == 1 ? { department_id: formData.value.department_id } : { customer_id: formData.value.customer?.supplier_id }),
  }).then(() => {
    message.success('修改成功')
    isAddUser.value = false
    tableRef.value.refresh()
  })
}
// 删除外部用户
const deleteRole = (id) => {
  Delete({ id }).then(() => {
    visibleData.value.isShow = false
    message.success('删除成功')
    tableRef.value.refresh()
  })
}

// 修改密码
// const setUpdatePwd = () => {
//   UpdatePwd({
//     id: formData.value.id,
//     old_password: formData.value.old_password,
//     new_password: formData.value.new_password,
//   }).then(res => {
//     message.success('修改成功');
//     isAddUser.value = false;
//     tapQueryForm();
//   });
// };

// 重置密码
const resetPwd = (id) => {
  ResetPwd({ id }).then((res) => {
    visibleData.value = visibleDataStr()
    setTimeout(() => {
      visibleData.value.isShow = true
      visibleData.value.title = '重置密码'
      visibleData.value.content = `
    已成功重置该用户的密码，重置后的密码为：

            ${res.data.newPwd}

    请及时将密码发给该用户，并通知其登录后修改密码。`
      visibleData.value.isCancelBtn = false
      visibleData.value.confirmBtnText = '关闭'
    }, 300)
  })
}
// // 获取客户下拉框
// const getCustomerSelectOption = () => {
//   GetCustomerSelectOption().then((res) => {
//     res.data.forEach((item) => {
//       item.customer_id += ''
//     })
//     real_Arr.value = res.data
//     formArr.value.forEach((item) => {
//       if (item.key == 'customer_id') {
//         item.selectArr = res.data
//       }
//     })
//   })
// }
// 获取角色下拉
const getRoleSelectOption = () => {
  GetRoleSelectOption({
    scope: scope.value,
  }).then((res) => {
    res.data.forEach((item) => {
      item.role_id += ''
    })
    role_Arr.value = res.data
    formArr.value.forEach((item) => {
      if (item.key == 'role_ids') {
        item.selectArr = res.data
      }
    })
  })
}

// 获取供应商下拉框
const getSupplierOption = () => {
  GetSupplierList({ page: 1, pageSize: 9999 }).then((res) => {
    const item = formArr.value.find((i) => i.key === 'customer_id')
    item.selectArr = res.data.list.map((i) => ({
      label: i.supplier_name,
      value: i.supplier_id,
    }))
  })
}

const getCompanyList = () => {
  GetCompanyList().then((res) => {
    const item = formArr.value.find((i) => i.key === 'sub_company_id')
    item.selectArr = res.data.map((i) => ({
      label: i.company_name,
      value: i.id,
    }))
  })
}

// 获取部门树状下拉框
const getDepartmentTreeList = () => {
  GetDepartmentTreeList().then((res) => {
    department_Arr.value = res.data
    formArr.value.forEach((item) => {
      if (item.key == 'departmentid') {
        item.selectArr = res.data
      }
    })
  })
}
</script>
<style lang="scss" scoped>
.userRoleBox {
  padding-right: 40px;

  .li {
    display: flex;
    align-items: center;
    margin-top: 24px;
    font-size: 16px;
    color: #000;

    .label {
      width: 120px;
      margin-right: 30px;
      text-align: right;

      .text {
        position: relative;

        .icon_i {
          position: absolute;
          top: 50%;
          left: -15px;
          padding-top: 7px;
          font-size: 12px;
          color: red;
          transform: translateY(-50%);
        }
      }
    }

    .input {
      flex: 1;
    }

    .checkedBox {
      flex: 1;
    }

    .select {
      flex: 1;
    }

    .treeSelect {
      flex: 1;
    }
  }
}

.modalContent {
  font-size: 14px;
  word-break: break-word;
  white-space: pre-wrap;
}
</style>
