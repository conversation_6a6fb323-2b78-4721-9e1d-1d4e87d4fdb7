.log-wrapper {
  position: relative;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-shadow: 3px 0 10px 1px rgb(0 0 0 / 20%);

  &.move-in {
    cursor: default;
  }

  .move-box {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 20;
    display: block;
    width: 10px;
    cursor: ew-resize;
    content: '';
    background: transparent;
  }
}

.scroller {
  @apply flex-1 relative;

  &-item {
    &::before {
      position: absolute;
      top: 0;
      bottom: 0;
      left: 30px;
      display: block;
      width: 1px;
      content: '';
      background-color: #ddd;
    }

    @apply pl-50 pr-20 pt-10 leading-21 relative;
  }

  .log-dot {
    position: absolute;
    top: 10px;
    left: calc(30px - 10px);
    z-index: 10;
    width: 21px;
    height: 21px;
    background-color: #1890ff;
    border: 7px #fff solid;
    border-radius: 50%;

    &.small {
      top: 8px;
      left: 15px;
      width: 5px;
      height: 5px;
      background-color: #aaa;
      border: none;
    }
  }
}
