<template>
  <div class="flex flex-col h-full">
    <Form ref="formRef" v-model:form="formArr" :page-type="PageTypeEnum.SupplierAudit" :is-conmmence="isConmmence" @search="search" :clear-cb="clearForm">
      <template #btnlist>
        <div>
          <a-button v-if="btnPermission[41301]" class="btn" style="margin-left: auto" size="small" type="primary" @click="handleAdd">新增供应商</a-button>
        </div>
      </template>
    </Form>
    <div class="commenceBox">
      <div class="lineBox" v-if="formArr.length">
        <div class="lin1"></div>
        <a-button class="centerBtn" size="small" type="primary" @click="isConmmence = !isConmmence">{{ isConmmence ? '展开' : '收起' }}</a-button>
        <div class="lin2"></div>
      </div>
      <a-button class="!size-32px ml-auto flex-shrink-0 !center" @click="showTableSetting" shape="circle" size="small">
        <i class="vxe-button--icon vxe-icon-custom-column"></i>
      </a-button>
    </div>

    <BaseTable ref="tableRef" :page-type="PageTypeEnum.SupplierAudit" v-model:form="formArr" :get-list="GetSupplierAuditList">
      <template #settlement_type="{ row }">
        {{ settlementTypeMap[row.settlement_type] }}
      </template>
      <template #supplier_type="{ row }">
        {{ supplierTypeMap[row.supplier_type] }}
      </template>
      <template #audit_status="{ row }">
        <a-tag :color="auditStatusColorMap[row.audit_status]">
          {{ auditStatusMap[row.audit_status] }}
        </a-tag>
      </template>

      <template #audit_type="{ row }">
        {{ row.type === 1 ? '准入申请' : '变更申请' }}
      </template>

      <template #fix_option="{ row, column }">
        <RightOperate :list="rightOperateList" :row="row" :column="column" />
      </template>
    </BaseTable>

    <Detail ref="detailRef" @refresh="search" />
    <Update ref="updateRef" @refresh="search" />
  </div>
</template>

<script lang="ts" setup>
// import { GetPurchaseByDeptSelect } from '@/servers/BusinessCommon'
import { filterOption } from '@/utils'
import { settleAccountOption } from '@/common/options'
import { auditStatusColorMap, auditStatusMap, settlementTypeMap, supplierTypeMap } from '@/common/map'

import Detail from './SupplierDetail.vue'
import Update from './SupplierUpdate.vue'

import { GetMRPSupplierGroupSelect, GetPurchaseByDept, GetSupplierAuditList } from '@/servers/Supplier'
import { GetDeptSelect } from '@/servers/BusinessCommon'

import { PageTypeEnum } from '@/enums/tableEnum'

const btnPermission = inject('btnPermission') as any

const isConmmence = ref(false)
const tableRef = ref()
const rightOperateList = ref([
  {
    label: '查看',
    show: 41302,
    onClick: ({ row }) => {
      handleDetail(row)
    },
  },
  {
    label: '编辑',
    show: ({ row }) => btnPermission.value[41304] && [10, 95].includes(row.audit_status),
    onClick: ({ row }) => {
      handleEdit(row)
    },
  },
  {
    label: '审核',
    show: ({ row }) => (btnPermission.value[41303] && [20, 21].includes(row.audit_status)) || (btnPermission.value[41305] && [30].includes(row.audit_status)),
    onClick: ({ row }) => {
      handleAudit(row)
    },
  },
])

const formRef = ref()
const search = () => tableRef.value.search()
const showTableSetting = () => tableRef.value.showTableSetting()

const clearForm = () => {
  formArr.value.find((v) => v.key === 'buyer_id')!.selectArr = []
}

const formArr = ref([
  { label: '供应商编码', value: '', type: 'inputNumber', key: 'supplier_id', precision: 0 },
  { label: '供应商名称', value: '', type: 'input', key: 'supplier_name' },
  { label: '供应商分组', value: null, type: 'select', key: 'supplier_group_id', selectArr: [], showSearch: true, filterOption },
  {
    label: '结算方式',
    value: null,
    type: 'select',
    key: 'settlement_type',
    selectArr: settleAccountOption,
  },
  {
    label: '供应商类型',
    value: null,
    type: 'select',
    key: 'supplier_type',
    selectArr: [
      { label: '线下供应商', value: 1 },
      { label: '1688线上供应商', value: 2 },
    ],
  },
  {
    label: '对接部门',
    value: null,
    type: 'select',
    key: 'dept_id',
    selectArr: [],
    showSearch: true,
    filterOption,
    onChange: async (val) => {
      const res = await GetPurchaseByDept({ dept_id: val })
      formArr.value.find((v) => v.key === 'buyer_id')!.selectArr = res.data
    },
  },
  { label: '对接采购员', value: null, type: 'select', key: 'buyer_id', selectArr: [], showSearch: true, filterOption },
  {
    label: '审核类型',
    value: null,
    type: 'select',
    key: 'type',
    selectArr: [
      { label: '准入申请', value: 1 },
      { label: '变更申请', value: 2 },
    ],
  },
  {
    label: '审核状态',
    value: null,
    type: 'select',
    key: 'audit_status',
    selectArr: [
      { label: '待提审', value: 10 },
      { label: '待审核', value: 20 },
      { label: '待变更审核', value: 21 },
      { label: '待财务审核', value: 30 },
      { label: '已通过', value: 90 },
      { label: '已拒绝', value: 95 },
    ],
  },
  { label: '创建时间', value: null, type: 'range-picker', key: 'create_at', formKeys: ['start_time', 'end_time'], placeholder: ['创建开始时间', '创建结束时间'] },
])

onMounted(() => {
  getSeletOptions()
})

const getSeletOptions = async () => {
  const res1 = await GetMRPSupplierGroupSelect()
  const res2 = await GetDeptSelect({})

  formArr.value.forEach((item) => {
    if (item.key === 'supplier_group_id') {
      item.selectArr = res1.data
    } else if (item.key === 'dept_id') {
      item.selectArr = res2.data
    }
  })
}
const updateRef = ref()
const detailRef = ref()
const handleAdd = () => {
  updateRef.value.open()
}

const handleDetail = (row) => {
  detailRef.value.open(row.audit_id, true)
}

const handleAudit = (row) => {
  detailRef.value.open(row.audit_id, true, true)
}

const handleEdit = async (row) => {
  updateRef.value.open(row.audit_id, true)
}

defineExpose({
  search,
})
</script>

<style lang="scss" scoped>
//
</style>
