import { request } from './request'
// 查询列表
export const List = (data) => {
  return request({ url: '/api/BookingOrder/GetBookingOrderList', data })
}
// 获取详情
export const GetBookingOrderDetail = (data) => {
  return request({ url: `/api/BookingOrder/GetBookingOrderDetail`, data }, 'POST')
}
// 获取预约入库商品详情列表
export const GetBookingOrderDetailList = (data) => {
  return request({ url: '/api/BookingOrder/GetBookingOrderDetailList', data })
}

// 新增
export const AddBookingOrder = (data) => {
  return request({ url: '/api/BookingOrder/AddBookingOrder', data })
}
// 编辑
export const Edit = (data) => {
  return request({ url: '/api/BookingOrder/UpdateBookingOrder', data })
}
// 审核
export const BatchAudit = (data) => {
  return request({ url: '/api/BookingOrder/BatchAudit', data })
}
// 获取审核记录
export const GetAuditRecord = (data) => {
  return request({ url: `/api/BookingOrder/GetAuditRecord?id=${data.id}`, data }, 'GET')
}
// 获取申请人下拉列表
export const GetUserOptions = () => {
  return request({ url: `/api/BookingOrder/GetBuyers` }, 'GET')
}
// 添加采购单的商品
export const GetBookingPurchaseOrderDetail = (data) => {
  return request({ url: '/api/BookingOrder/GetBookingPurchaseOrderDetail', data })
}
// 添加采购单的商品
export const ChangeModel = (data) => {
  return request({ url: '/api/BookingOrder/ChangeModel', data })
}
// 更新物流信息
export const UpdateLogisticsInfo = (data) => {
  return request({ url: '/api/BookingOrder/UpdateLogisticsInfo', data })
}

// 采购单详情获取明细
export const GetPurchaseDetailBookingList = (data) => {
  return request({ url: `/api/BookingOrder/GetPurchaseDetailBookingList?id=${data.id}`, data }, 'GET')
}

export const GetPurchaseOrderList = (data) => {
  return request({ url: '/api/BookingOrder/GetPurchaseOrderList', data })
}

// 获取仓库下拉列表
export const GetWarehouses = () => {
  return request({ url: `/api/BookingOrder/GetWarehouses` }, 'GET')
}

// export const GetMRPSupplierCompanySelect = () => {
//   return request({ url: '/api/BookingOrder/GetMRPSupplierCompanySelect' }, 'GET')
// }

export const CheckPurchaseStatus = (data) => {
  return request({ url: '/api/BookingOrder/CheckPurchaseStatus', data })
}

// 确认到货
export const ConfirmTheArrival = (data) => {
  return request({ url: '/api/BookingOrder/ConfirmTheArrival', data })
}

// 检查预约入库单完成状态
export const CheckBookingOrderStatus = (data) => {
  return request({ url: '/api/BookingOrder/CheckBookingOrderStatus', data })
}

// 作废预约入库单
export const CancelBookingOrder = (data) => {
  return request({ url: '/api/BookingOrder/CancelBookingOrder', data })
}

// 导入预约入库单商品信息
export const ImportProductInfo = (data) => {
  return request({ url: '/api/BookingOrder/ImportProductInfo', data, isFormData: true, timeout: 0 })
}

// 导入模板下载
export const DownloadImportTemplate = () => {
  return request({ url: `/api/BookingOrder/DownloadImportTemplate`, responseType: 'blob' }, 'GET')
}

export const BatchCompletedBookingOrder = (data) => {
  return request({ url: '/api/BookingOrder/BatchCompletedBookingOrder', data })
}
