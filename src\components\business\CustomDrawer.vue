<template>
  <a-drawer v-model:open="openFlag" v-bind="options?.drawerOptions">
    <EasyForm class="mb-5" mode="search" v-if="formArr.length" ref="formRef" :formItems="formArr" v-model:form="formDataState" @search="onSearch" @reset="onSearch" />

    <div class="flex-1">
      <vxe-grid ref="gridRef" v-if="options?.gridOptions?.columns?.length" v-bind="options.gridOptions" @checkbox-all="selectChangeEvent" @checkbox-change="selectChangeEvent" />
    </div>

    <template #footer>
      <a-space>
        <a-button type="primary" :loading="confirmLoading" @click="handleConfirm">{{ options?.confirmText }}</a-button>
        <a-button @click="handleClose">取消</a-button>
      </a-space>
    </template>
  </a-drawer>
</template>

<script lang="ts" setup>
import { DrawerProps } from 'ant-design-vue'
import { merge } from 'lodash'

interface CustomDrawerType {
  drawerOptions: DrawerProps
  gridOptions: VxeGridProps
  formItems: EasyFormItemProps[]
  formData: Record<string, any>
  confirmText: string
  onConfirm: (any) => void | Promise<boolean>
  getList?: (formData: Record<string, any>, options) => void
}

const openFlag = ref(false)
const options = ref<CustomDrawerType>()

const formArr = ref<EasyFormItemProps[]>([])
const formDataState = ref({})
const onSearch = (data = {}) => {
  options.value?.getList && options.value.getList(data, { gridRef })
}

const open = async (params: CustomDrawerType) => {
  const defaultOptions = {
    drawerOptions: {
      bodyStyle: { display: 'flex', padding: '16px', flexDirection: 'column' },
      destroyOnClose: true,
    },
    gridOptions: {
      maxHeight: '100%',
      minHeight: 100,
      border: true,
      size: 'mini',
      columnConfig: { resizable: true },
      style: {
        '--vxe-ui-table-cell-padding-mini': '4px 6px',
        '--vxe-ui-table-header-font-color': 'rgba(0,0,0,0.8)',
      },
      virtualXConfig: {
        enabled: true,
        gt: 20,
      },
      virtualYConfig: {
        enabled: true,
        gt: 20,
      },
      rowConfig: {
        height: 34,
        isHover: true,
        // keyField: 'id',
      },
    },
    confirmText: '确定',
    onConfirm: () => {},
  }
  const { formItems, formData, ...other } = params
  formDataState.value = formData
  formArr.value = formItems || []
  options.value = merge(defaultOptions, other)
  confirmLoading.value = false
  openFlag.value = true
  await nextTick()
  onSearch()
}

const keyField = computed(() => options.value?.gridOptions?.rowConfig?.keyField ?? '')

const gridRef = ref()

const checkItems = ref<any[]>([])
const selectChangeEvent = () => {
  const $table = gridRef.value
  // 当前页选中的数据
  const currentSelectedRows = $table?.getCheckboxRecords() || []
  // 其他页选中的数据
  const otherSelectedRows = $table?.getCheckboxReserveRecords() || []
  checkItems.value = [...currentSelectedRows, ...otherSelectedRows]
}

const confirmLoading = ref(false)
const handleConfirm = async () => {
  confirmLoading.value = true
  const fn = options.value?.onConfirm({
    list: checkItems.value,
    ids: checkItems.value.map((item) => item[keyField.value]),
    data: gridRef.value.getTableData().fullData,
  })
  if (fn instanceof Promise) {
    const flag = await fn
    if (flag) {
      handleClose()
    } else {
      confirmLoading.value = false
    }
  } else {
    handleClose()
  }
}

const handleClose = () => {
  openFlag.value = false
  confirmLoading.value = false
}

defineExpose({
  open,
  close: handleClose,
})
</script>

<style lang="scss" scoped>
//
</style>
