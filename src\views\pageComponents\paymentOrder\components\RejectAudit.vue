<template>
  <a-modal v-model:open="visible" :footer="null" title="审核拒绝">
    <a-form :label-col="{ span: 7 }" :wrapper-col="{ span: 17 }" :model="form" :rules="rules" ref="formRef">
      <a-row>
        <a-col :span="20">
          <a-form-item label="退回节点" name="rejected_audit_status">
            <a-select v-model:value="form.rejected_audit_status" :options="review_progress" placeholder="请选择" allowClear>
              <!-- <a-select-option v-for="item in accountType" :key="item.value" :value="item.key">{{ item.value }}</a-select-option> -->
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>
      <a-row>
        <a-col :span="20">
          <a-form-item label="重新提交审批方式" name="rejected_type_status">
            <a-radio-group v-model:value="form.rejected_type_status" name="radioGroup">
              <a-radio :value="1">逐层审批</a-radio>
              <a-radio :value="2">直达审核</a-radio>
            </a-radio-group>
          </a-form-item>
        </a-col>
      </a-row>
      <a-row>
        <a-col :span="20">
          <a-form-item label="审核意见" name="audit_opinion">
            <a-textarea v-model:value="form.audit_opinion" :rows="4" placeholder="请输入审核意见" />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>

    <a-flex justify="center" class="mt-16">
      <a-space :size="16">
        <a-button type="primary" @click="handleOk">确定拒绝</a-button>
        <a-button @click="handleCancel">取消</a-button>
      </a-space>
    </a-flex>
  </a-modal>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue'
import type { SelectValue } from 'ant-design-vue/es/select'

import { Enum2Options } from '@/utils'

interface FormState {
  rejected_audit_status: SelectValue
  rejected_type_status: 1 | 2
  audit_opinion: string
  is_pass: boolean
}

const visible = ref(false)
const formRef = ref()

const emits = defineEmits(['audit'])

const form = ref<FormState>({
  rejected_audit_status: undefined,
  rejected_type_status: 1,
  audit_opinion: '',
  is_pass: false,
})

const rules = {
  rejected_audit_status: [{ required: true, message: '请选择审核节点' }],
  rejected_type_status: [{ required: true, message: '请选择审核方式' }],
  audit_opinion: [
    { required: true, message: '请输入审核意见' },
    { max: 500, message: '输入内容不能超过500个字' },
  ],
}

interface ReviewProgressItem {
  label: string
  value: number
}

const review_progress_list: ReviewProgressItem[] = Enum2Options(PaymentOrderAuditStatusEnum)

const audit_status = ref<number>()
const actual_amount = ref<number | undefined>()

const review_progress = computed(() => {
  // 如果金额<=10000，过滤掉41节点
  if (actual_amount.value !== undefined && Number(actual_amount.value) <= 10000) {
    return review_progress_list.filter((item) => item.value !== 41 && item.value < (audit_status.value ?? Infinity))
  }
  return review_progress_list.filter((item) => item.value < (audit_status.value ?? Infinity))
})

// 确定
const handleOk = async () => {
  try {
    await formRef.value?.validate()
    emits('audit', form.value)
    visible.value = false
    // 重置表单内容
    form.value.audit_opinion = ''
    form.value.rejected_audit_status = undefined
    form.value.rejected_type_status = 1
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

// 取消
const handleCancel = () => {
  visible.value = false
  form.value.audit_opinion = ''
  form.value.rejected_audit_status = undefined
  form.value.rejected_type_status = 1
}

// 打开
const open = async (temp_audit_status: number, temp_actual_amount?: number) => {
  await formRef.value?.clearValidate()
  audit_status.value = temp_audit_status
  actual_amount.value = temp_actual_amount
  visible.value = true
}

defineExpose({
  open,
  close: handleCancel,
})
</script>
<style scoped lang="scss"></style>
