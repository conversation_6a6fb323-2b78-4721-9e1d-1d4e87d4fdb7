<template>
  <div class="main">
    <div class="flex justify-end mb-12">
      <div class="easy-tabs-single">
        <div class="easy-tabs-single-btn big" v-for="(item, index) in dateList" :key="item.label" :class="{ current: index === dateIndex }" @click="onDateChange(index)">
          {{ item.label }}
        </div>
      </div>
    </div>
    <Form ref="formRef" v-model:form="form" @search="tableRef?.search()" @setting="tableRef?.showTableSetting()" :page-type="PageTypeEnum.PaymentStatistics"></Form>
    <!-- 付款概览 -->
    <div class="cur-page-box">
      <div class="title">
        付款概览
        <span class="title-desc">默认显示当月数据</span>
      </div>
      <div class="flex">
        <div class="statistic-item">
          <div class="statistic-item-title">付款总金额</div>
          <div class="statistic-item-content">￥{{ overview.total_actual_amount.roundNext(2) }}</div>
        </div>
        <div class="statistic-item">
          <div class="statistic-item-title">付款供应商数量</div>
          <div class="statistic-item-content">
            <span>{{ overview.supplier_count.roundNext(0) }}</span>
            <span class="sm">个</span>
          </div>
        </div>
        <div class="statistic-item">
          <div class="statistic-item-title">付款订单数量</div>
          <div class="statistic-item-content">
            <span>{{ overview.pay_order_count.roundNext(0) }}</span>
            <span class="sm">单</span>
          </div>
        </div>
        <div class="statistic-item">
          <div class="statistic-item-title">
            待付款总金额
            <a-tooltip>
              <template #title>待审核+待出纳付款申请单的待付款总金额</template>
              <InfoCircleOutlined></InfoCircleOutlined>
            </a-tooltip>
          </div>
          <div class="statistic-item-content">
            <span>￥{{ overview.total_payable_amount.roundNext(2) }}</span>
          </div>
        </div>
      </div>
    </div>
    <!-- 付款金额表 -->
    <div class="cur-page-box mt-12 mb-28">
      <div class="title">
        付款金额表
        <span class="title-desc">默认显示当月数据</span>

        <div class="ml-auto">
          <a-radio-group v-model:value="type" button-style="solid" @change="tableRef?.search()">
            <a-radio-button :value="item.value" v-for="item in types" :key="item.value">{{ item.label }}</a-radio-button>
          </a-radio-group>
        </div>
      </div>
      <div class="chart" id="chart"></div>
    </div>

    <div class="title">
      付款明细表
      <span class="title-desc">默认显示当月数据</span>
    </div>

    <BaseTable
      ref="tableRef"
      v-model:form="form"
      :get-list="PaymentDetailStatistics"
      :isIndex="true"
      :form-format="formatData"
      :tableColumns="tableColumns"
      keyField="id"
      :boxCls="['relative!']"
      :height="40 * 15"
      :dataFormat="dataFormat"
      :showLineHeightSetter="false"
    />
  </div>
</template>

<script lang="ts" setup>
import { InfoCircleOutlined } from '@ant-design/icons-vue'
import { Chart } from '@antv/g2'
import dayjs from 'dayjs'
import { onMounted, ref } from 'vue'

import Form from '@/components/Form.vue'
import { cloneDeep } from '@/utils'

import { ApplicantFillterOptions, PaymentAccountFillterOptions } from '@/servers/PaymentApply'
import { PaymentDetailStatistics, PaymentProfileStatistics, PaymentScheduleStatistics } from '@/servers/PaymentStatistics'

import { PageTypeEnum } from '@/enums/tableEnum'

const types = [
  { label: '当月', value: '1' },
  { label: '按月', value: '2' },
  { label: '按季', value: '3' },
  { label: '按年', value: '4' },
]
const type = ref('1')

const dateList = ref([
  { label: '当月', value: [dayjs().startOf('month').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')] },
  { label: '近半年', value: [dayjs().subtract(6, 'month').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')] },
  { label: '近一年', value: [dayjs().subtract(1, 'year').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')] },
  { label: '近两年', value: [dayjs().subtract(2, 'year').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')] },
])
const dateIndex = ref(0)
const onDateChange = (value) => {
  dateIndex.value = value
  type.value = '2'
  tableRef.value?.search()
}
const formRef = ref()
const form: any = ref([
  // {
  //   label: '选择时间',
  //   value: null,
  //   type: 'range-picker',
  //   key: 'date',
  //   formKeys: ['start_date', 'end_date'],
  //   placeholder: ['开始时间', '结束时间'],
  //   valueFormat: 'YYYY-MM-DD',
  //   isShow: true,
  //   isQuicks: true,
  //   quickIndex: 1,
  //   showTime: false,
  //   quickNotFull: true,
  //   quickLabel: '选择时间(按月份)',
  //   quicks: [
  //     { label: '当月', value: [dayjs().startOf('month').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')] },
  //     { label: '近半年', value: [dayjs().subtract(6, 'month').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')] },
  //     { label: '近一年', value: [dayjs().subtract(1, 'year').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')] },
  //     { label: '近两年', value: [dayjs().subtract(2, 'year').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')] },
  //   ],
  //   line: true,
  //   change: () => {
  //     type.value = '2'
  //   },
  // },
  {
    label: '结算方式',
    value: [],
    type: 'select',
    multiple: true,
    selectArr: [
      {
        label: '线上账期-月结',
        value: '线上账期-月结',
      },
      {
        label: '线下账期-月结',
        value: '线下账期-月结',
      },
      {
        label: '线下-预付款',
        value: '线下-预付款',
      },
      {
        label: '线下-分期预付款',
        value: '线下-分期预付款',
      },
    ],
    key: 'settlement_method',
    isShow: true,
    isQuicks: true,
    quickIndex: 2,
  },
  {
    label: '付款账户名称',
    value: [],
    type: 'select',
    key: 'payment_account_name',
    selectArr: [],
    multiple: true,
    isShow: true,
  },
  {
    label: '供应商名称',
    value: [],
    type: 'supplier',
    selectArr: [],
    key: 'supplier_id',
    isShow: true,
  },
  {
    label: '申请部门',
    value: [],
    type: 'select',
    key: 'applicant_department_name',
    selectArr: [],
    multiple: true,
    isShow: true,
  },
  {
    label: '申请人',
    value: [],
    type: 'select',
    key: 'applicant_id',
    selectArr: [],
    multiple: true,
    isShow: true,
  },
])

const tableRef = ref()
const tableKey = ref([
  { key: 'supplier_name', width: 200, title: '供应商名称', fixed: 'left' },
  { key: 'total_actual_amount', width: 110, title: '付款总金额', is_sort: true, fixed: 'left', formatter: 'price', align: 'right' },
  { key: 'total_payable_amount', width: 110, title: '待付款总金额', is_sort: true, fixed: 'left' },
  { key: 'total_pay_count', width: 110, title: '交易总数量', is_sort: true, fixed: 'left' },
] as any)
const tableColumns = ref<any[]>([])

const formatData = (data: any) => {
  console.log('data', data)
  data.start_date = dateList.value[dateIndex.value]?.value[0]
  data.end_date = dateList.value[dateIndex.value]?.value[1]
  // statistics
  PaymentProfileStatistics(data).then((res) => {
    overview.value = res.data
  })
  // chat
  const obj = cloneDeep(data)
  obj.statistics_date_type = type.value
  if (type.value === '1') {
    obj.start_date = dayjs().startOf('month').format('YYYY-MM-DD')
    obj.end_date = dayjs().format('YYYY-MM-DD')
  }
  PaymentScheduleStatistics(obj).then((res) => {
    chart.data(
      res.data.map((v) => ({
        日期: v.pay_date,
        金额: v.pay_amount,
      })),
    )
    chart.render()
  })

  return obj
}
const dataFormat = (data) => {
  const date = [...new Set(data.map((v) => (v.payment_date_detail ? Object.keys(v.payment_date_detail) : [])).flat())].sort((a, b) => {
    // 用正则提取数字
    const numA = Number(String(a).match(/\d+/)?.join('') || '0')
    const numB = Number(String(b).match(/\d+/)?.join('') || '0')
    return numA - numB
  }) as any
  tableColumns.value = [
    ...tableKey.value,
    ...date.map((v) => ({
      key: v,
      title: v,
      is_sort: true,
      minWidth: 120,
      align: 'right',
      formatter: 'price',
    })),
  ]
  return data.map((v) => ({
    ...v,
    ...date.reduce((r, d) => ({ ...r, [d]: v.payment_date_detail[d] || 0 }), {}),
  }))
}

const windowScreenWidth = ref(window.innerWidth)
const updateWindowWidth = () => {
  windowScreenWidth.value = window.innerWidth
}
onMounted(() => {
  // const date = form.value.find((v) => v.key === 'date')
  // date.value = date.quicks[0].value

  initApi()

  window.addEventListener('resize', updateWindowWidth)
})
onBeforeUnmount(() => {
  window.removeEventListener('resize', updateWindowWidth)
})

let chart = null as any
const initApi = () => {
  ApplicantFillterOptions(1).then((res) => {
    form.value.find((v) => v.key === 'applicant_id').selectArr = res.data.map((v) => ({ label: v.value, value: v.key }))
  })
  ApplicantFillterOptions(2).then((res) => {
    form.value.find((v) => v.key === 'applicant_department_name').selectArr = res.data
  })
  PaymentAccountFillterOptions({ paymentAccountOptionType: 3 }).then((res) => {
    form.value.find((v) => v.key === 'payment_account_name').selectArr = res.data.map((v) => ({ label: v.value, value: v.key }))
  })
  chart = new Chart({
    container: 'chart',
    autoFit: true,
    inset: 20,
  })

  chart.interval().encode('x', '日期').encode('y', '金额').style('maxWidth', 40).label({
    text: '金额',
    fill: '#000',
    position: 'top',
    dy: -20,
  })
}
const overview = ref({
  total_actual_amount: 0,
  supplier_count: 0,
  pay_order_count: 0,
  total_payable_amount: 0,
})
</script>

<style lang="scss" scoped>
.main {
  flex: none;
  overflow: auto;
}

.title {
  display: inline-flex;
  align-items: center;
  height: 16px;
  padding-left: 4px;
  margin-bottom: 20px;
  font-size: 14px;
  line-height: 1;
  color: #333;
  border-left: 2px $color solid;

  &-desc {
    margin-left: 8px;
    font-size: 12px;
    color: #999;
  }
}

:deep(.ant-card) {
  .ant-card-body {
    padding: 20px;
  }
}

.chart {
  flex-shrink: 0;
  height: 400px;
}

.cur-page-box {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  padding: 20px 16px 16px;
  border: 1px solid #eaecee;
  border-radius: 4px;
}

.statistic-item {
  flex: 1;
  padding: 20px;
  margin-right: 16px;
  color: #666;
  border: 1px solid #eaecee;
  border-radius: 4px;

  &-content {
    margin-top: 12px;
    font-size: 24px;
    font-weight: bold;
    color: #333;

    .sm {
      margin-left: 4px;
      font-size: 12px;
      font-weight: normal;
    }
  }

  &:last-child {
    margin-right: 0;
  }
}
</style>
