import { request } from './request'

// 获取采购调价列表
export const GetPurchaseAdjustPriceList = (data: any) => {
  return request({ url: '/api/PurchaseAdjustPrice/GetPurchaseAdjustPriceList', data })
}

// 添加采购调价表
export const AddPurchaseAdjustPrice = (data: any) => {
  return request({ url: '/api/PurchaseAdjustPrice/AddPurchaseAdjustPrice', data })
}

// 编辑采购调价表
export const UpdatePurchaseAdjustPrice = (data: any) => {
  return request({ url: '/api/PurchaseAdjustPrice/UpdatePurchaseAdjustPrice', data })
}

// 审核采购调价表
export const AuditPurchaseAdjustPrice = (data: any) => {
  return request({ url: '/api/PurchaseAdjustPrice/Audit', data })
}

// 批量审核通过 / 拒绝
export const BatchAuditPurchaseAdjustPrice = (data: any) => {
  return request({ url: '/api/PurchaseAdjustPrice/BatchAuditPurchaseAdjustPrice', data })
}

// 获取采购调价表详情
export const GetPurchaseAdjustPrice = (data: any) => {
  return request({ url: '/api/PurchaseAdjustPrice/GetPurchaseAdjustPrice', data }, 'GET')
}

// 获取采购调价表详情
export const GetPurchaseAdjustPriceDetail = (data: any) => {
  return request({ url: '/api/PurchaseAdjustPrice/GetPurchaseAdjustPriceDetail', data })
}

// 导入金蝶商品明细
export const PurchaseAdjustImportK3SkuInfo = (data: any, id: string) => {
  return request({ url: `/api/PurchaseAdjustPrice/PurchaseAdjustImportK3SkuInfo?id=${id}`, data, isFormData: true })
}

// 导入金蝶新商品明细
export const PurchaseAdjustImportNewK3SkuInfo = (data: any, id: string) => {
  return request({ url: `/api/PurchaseAdjustPrice/PurchaseAdjustImportNewK3SkuInfo?id=${id}`, data, isFormData: true })
}

export const PurchaseAdjustImportMaterialInfo = (data: any, id: string) => {
  return request({ url: `/api/PurchaseAdjustPrice/PurchaseAdjustImportMaterialInfo?id=${id}`, data, isFormData: true })
}

// 下载调价表导入模板
export const DownloadImportTemplate = (bol) => {
  return request({ url: `/api/PurchaseAdjustPrice/DownloadImportTemplate?isMaterial=${bol}`, responseType: 'blob' }, 'GET')
}
// 获取价目表选择
export const GetPurchasePriceSelects = () => {
  return request({ url: '/api/PurchasePrice/GetPurchasePriceSelects' }, 'GET')
}

// 获取采购调价表材质明细详情
export const GetPurchaseAdjustPriceMaterialDetail = (data) => {
  return request({ url: '/api/PurchaseAdjustPrice/GetPurchaseAdjustPriceMaterialDetail', data })
}
