<template>
  <div class="flex flex-col h-full">
    <Form ref="formRef" v-model:form="formArr" :page-type="PageTypeEnum.SupplierLiquidation" @search="onSearch" @resetForm="onReset" @setting="tableRef?.showTableSetting()" />
    <BaseTable
      ref="tableRef"
      v-model:form="formArr"
      :page-type="PageTypeEnum.SupplierLiquidation"
      :get-list="getListWithExportFlag"
      :isCheckbox="true"
      :totalField="['inv_amount']"
      :pageSizeOptions="['20', '50', '100', '250', '500', '1000', '2000', '3000']"
      :auto-search="false"
      v-bind="{ emptyText: '请选择「供应商」查看数据' }"
    >
      <template #left-btn>
        <div class="flex items-center gap-4">
          <a-button type="default" @click="handleGeneratePayableOrder()">生成应付单</a-button>
          <span class="ml-24">
            入库未清算余额：
            <span style="font-weight: bold; color: #ff4d4f">{{ notReconciledAmount }}</span>
          </span>
          <span class="ml-24">
            预付款余额：
            <span style="font-weight: bold; color: #ff4d4f">{{ advanceAmount }}</span>
          </span>
        </div>
      </template>
      <template #purchase_order_number="{ row }">
        <span class="link" @click="pushPage(`/purchaseOrderLook/${row.purchase_order_id}`, { source: true })">
          {{ row.purchase_order_number }}
        </span>
      </template>

      <!-- 税率列自定义显示 -->
      <template #tax_rate="{ row }">
        <span v-if="row.order_type === 1">{{ row.tax_rate }}%</span>
        <span v-else-if="row.order_type === 2">-</span>
        <span v-else>{{ row.tax_rate }}%</span>
      </template>

      <template #right-btn>
        <a-select v-model:value="exportType" :dropdownMatchSelectWidth="false" style="width: 120px" @change="onExportTypeChange" :disabled="exportLoading" placeholder="导出">
          <a-select-option v-for="item in exportOptions" :key="item.value" :value="item.value">{{ item.label }}</a-select-option>
        </a-select>
      </template>
    </BaseTable>
  </div>
</template>

<script setup lang="ts">
import { message } from 'ant-design-vue'

import eventBus from '@/utils/eventBus'
import { Enum2Options, checkFormParams } from '@/utils'

import { GetSupplierLiquidateList, ExportSupplierLiquidateList } from '@/servers/SupplierLiquidate'
import { GetPageSuppliersSelect, GetPageMRPSupplierCompanySelect } from '@/servers/BusinessCommon'

import { PageTypeEnum } from '@/enums/tableEnum'

const { pushPage } = usePage()

// 监听页面激活，每次进入页面时刷新列表
onActivated(() => {
  if (tableRef.value) {
    const hasSupplier = formArr.value.find((item) => item.key === 'supplier_id')?.value || formArr.value.find((item) => item.key === 'company_supplier_id')?.value
    if (hasSupplier) {
      // 延迟一点时间确保页面完全加载后再刷新
      setTimeout(() => {
        tableRef.value.search()
      }, 100)
    }
  }
})

const formRef = ref()
const tableRef = ref()
const exportLoading = ref(false)
const exportType = ref()

const advanceAmount = ref('0') // 预付款余额
const notReconciledAmount = ref('0') // 入库未对账余额

// 导出类型枚举
const exportOptions = [
  { label: '根据勾选导出', value: 1 },
  { label: '根据筛选结果导出', value: 2 },
  // { label: '全部导出', value: 3 },
]

const formArr = ref([
  { label: '进出仓单号', value: '', type: 'inputDlg', key: 'io_id' },
  { label: '采购单号', value: '', type: 'inputDlg', key: 'purcharse_order_number' },
  { label: '预约进出仓单号', value: '', type: 'inputDlg', key: 'booking_number' },
  {
    label: '供应商',
    value: null,
    type: 'select-supplier',
    key: 'supplier_id',
    mode: 'single',
    api: GetPageSuppliersSelect,
  },
  {
    label: '供应商子公司',
    value: null,
    type: 'select-supplier',
    key: 'company_supplier_id',
    mode: 'single',
    api: GetPageMRPSupplierCompanySelect,
  },
  { label: '商品编号', value: '', type: 'inputDlg', key: 'k3_sku_id' },
  { label: '商品名称', value: '', type: 'inputDlg', key: 'sku_name' },
  { label: '商品类型', value: null, type: 'select', key: 'product_type', selectArr: Enum2Options(ProductTypeEnum) },
  { label: '应付状态', value: 1, type: 'select', key: 'bill_payable_status', selectArr: Enum2Options(BillPayableOrderStatusEnum) },
  { label: '单据类型', value: null, type: 'select', key: 'order_type', selectArr: Enum2Options(SupplierLiquidateOrderTypeEnum) },
  { label: '入库日期', value: null, type: 'range-picker', key: 'io_date', formKeys: ['io_start_time', 'io_end_time'], placeholder: ['入库开始日期', '入库结束日期'] },
  { label: '采购日期', value: null, type: 'range-picker', key: 'purchase_time', formKeys: ['po_start_time', 'po_end_time'], placeholder: ['采购开始日期', '采购结束日期'] },
])

// 生成应付单
const handleGeneratePayableOrder = () => {
  const ids = tableRef.value?.checkItemsArr?.map((i) => i.unique_value) || []
  if (!ids.length) {
    message.warning('请先勾选要生成的数据')
    return
  }

  const [first] = tableRef.value.checkItemsArr

  // 检查是否所有勾选的数据都是"已生成"状态
  const allGenerated = tableRef.value.checkItemsArr.every((item) => item.bill_payable_status_string === '已生成')
  if (allGenerated) {
    message.warning('已生成应付单的单据不能再生成应付单')
    return
  }

  // 存在混选：包含已生成的单据
  const hasGenerated = tableRef.value.checkItemsArr.some((item) => item.bill_payable_status_string === '已生成')
  if (hasGenerated) {
    message.warning('存在已生成应付单，请重新选择')
    return
  }

  for (const item of tableRef.value.checkItemsArr) {
    if (item.reconciliation_status === 2) {
      message.warning(`所选单据包含已对账`)
      return
    }
    if (item.supplier_name !== first.supplier_name) {
      message.warning('相同供应商主公司的单据才可生成应付单')
      return
    }
  }
  pushPage(`/billPayable/new?ids=${ids.join(',')}`, { source: true })
}

const onSearch = () => {
  // 检查是否选择了供应商
  const hasSupplier = formArr.value.find((item) => item.key === 'supplier_id')?.value || formArr.value.find((item) => item.key === 'company_supplier_id')?.value

  if (!hasSupplier) {
    message.warning('请选择供应商')
    return
  }

  tableRef.value?.search && tableRef.value.search()
}

const onReset = () => {
  tableRef.value?.search && tableRef.value.search()
}

// 监听供应商字段变化，当清空时同步清空列表数据
watch(
  () => [formArr.value.find((item) => item.key === 'supplier_id')?.value, formArr.value.find((item) => item.key === 'company_supplier_id')?.value],
  ([supplierId, companySupplierId]) => {
    // 如果供应商字段被清空，清空列表数据
    if (!supplierId && !companySupplierId) {
      // 重置金额统计
      advanceAmount.value = '0'
      notReconciledAmount.value = '0'
      // 触发表格重新渲染，显示空数据状态
      if (tableRef.value?.search) {
        tableRef.value.search()
      }
    }
  },
  { deep: true },
)

const modal = inject('modal') as any
const onExportTypeChange = (type) => {
  if (!type) return
  let ids = []
  if (type === 1) {
    ids = tableRef.value?.checkItemsArr?.map((i) => i.unique_value) || []
    if (!ids.length) {
      message.warning('请先勾选要导出的数据')
      exportType.value = undefined
      return
    }
  }
  modal.open({
    title: '是否确定导出供应商清算数据?',
    onOk() {
      return new Promise((resolve) => {
        const params = {
          exportType: type,
          liquidateChecks: ids,
          searchListParam: checkFormParams({ formArr: formArr.value }),
        }
        ExportSupplierLiquidateList(params).then(({ data }) => {
          eventBus.emit('downLoadId', {
            id: data as number,
            resolve,
            download: true,
          })
        })
      })
    },
  })
}

// 获取列表数据并同时更新金额统计
const getListWithExportFlag = async (params) => {
  // 检查是否选择了供应商
  const hasSupplier = params.supplier_id || params.company_supplier_id

  if (!hasSupplier) {
    // 如果没有选择供应商，返回空数据状态
    return {
      success: true,
      data: [],
      total: 0,
      message: '请选择供应商',
    }
  }

  try {
    const res = await GetSupplierLiquidateList({
      ...params,
      ListOrExport: true,
    })

    // 同时更新金额统计
    if (res?.data) {
      advanceAmount.value = res.data.payable_order_amount ?? '0'
      notReconciledAmount.value = res.data.amounts_iventory_liquidate_off ?? '0'
    }

    // 确保返回正确的数据结构
    if (res?.data?.datas?.list) {
      // 对数据进行预处理，修复应付金额的精度问题
      const processedList = res.data.datas.list.map((item) => ({
        ...item,
        inv_amount: item.inv_amount != null && !Number.isNaN(Number(item.inv_amount)) ? Number(Number(item.inv_amount).toFixed(2)) : item.inv_amount,
      }))

      return {
        ...res,
        data: {
          ...res.data,
          datas: {
            ...res.data.datas,
            list: processedList,
          },
        },
      }
    }

    return res
  } catch (error) {
    console.error('获取供应商清算列表失败:', error)
    // 出错时设置默认金额
    advanceAmount.value = '0'
    notReconciledAmount.value = '0'
    throw error
  }
}
</script>

<style lang="scss" scoped>
.custom-empty {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;

  :deep(.ant-empty-description) {
    font-size: 14px;
    color: #666;
  }
}

// 覆盖 VXE Table 的空数据文本
:deep(.vxe-table--empty-block) {
  .vxe-table--empty-content {
    font-size: 14px !important;
    color: #666 !important;

    &::before {
      content: '请选择供应商' !important;
    }
  }
}
</style>
