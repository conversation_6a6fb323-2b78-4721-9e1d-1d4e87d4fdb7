import { reactive, ref } from 'vue'

import { Enum2Options } from '@/utils'
import { PurchaseOrderStatusEnum } from '@/types/Purchase'
import { SettlementTypeEnum } from '@/types/enums'
import { BillPayableOrderSettlementEnum } from '@/types/BillPayable'

import { GetPageSuppliersSelect, GetPageMRPSupplierCompanySelect } from '@/servers/BusinessCommon'
// 状态选项
export const statusOptions = ref([
  { label: '未对账', value: 'unverified', code: 'unverified' },
  { label: '已对账待请款', value: 'verified_pending', code: 'verified_pending' },
  { label: '等待打款', value: 'awaiting_payment', code: 'awaiting_payment' },
  { label: '已打款', value: 'paid', code: 'paid' },
  { label: '全部', value: 'all', code: 'all' },
])

// 状态数量统计（写死的数据）
export const statusCounts = reactive({
  unverified: 1,
  verified_pending: 3,
  awaiting_payment: 3,
  paid: 0,
  all: 7,
})

// 未对账列表表单配置
export const unBillFormArr = ref([
  { label: '采购单号', value: '', type: 'inputDlg', key: 'purchase_number' },
  { label: '退库申请单号', value: '', type: 'inputDlg', key: 'return_application_number' },
  { label: '供应商', value: null, type: 'select-supplier', key: 'supplier_id', mode: 'single', api: GetPageSuppliersSelect },
  { label: '供应商子公司', value: null, type: 'select-supplier', key: 'company_supplier_id', mode: 'single', api: GetPageMRPSupplierCompanySelect },
  { label: '采购单状态', value: null, type: 'select', key: 'purchase_order_status', options: Enum2Options(PurchaseOrderStatusEnum) },
  { label: '结算方式', value: null, type: 'select', key: 'settlement_type', options: Enum2Options(SettlementTypeEnum) },
  { label: '商品编号', value: null, type: 'inputDlg', key: 'k3_sku_id' },
  { label: '商品名称', value: null, type: 'inputDlg', key: 'sku_name' },
  { label: '采购日期', value: null, type: 'range-picker', key: 'purchase_date', formKeys: ['purchase_time_start', 'purchase_time_end'], placeholder: ['采购开始日期', '采购结束日期'] },
])

// 账单列表表单配置
export const billFormArr = ref([
  { label: '账单单号', value: null, type: 'inputDlg', key: 'number' },
  { label: '应付单号', value: null, type: 'inputDlg', key: 'pinvid' },
  { label: '供应商', value: null, type: 'select-supplier', key: 'supplier_id', mode: 'single', api: GetPageSuppliersSelect },
  { label: '供应商子公司', value: null, type: 'select-supplier', key: 'company_supplier_id', mode: 'single', api: GetPageMRPSupplierCompanySelect },
  { label: '结算状态', value: null, type: 'select', key: 'settlement_status', options: Enum2Options(BillPayableOrderSettlementEnum).filter((f) => f.value !== 0) },
  { label: '备注', value: null, type: 'inputDlg', key: 'remark' },
  { label: '创建人', value: null, type: 'inputDlg', key: 'creator_name' },
  {
    label: '账单月份',
    value: null,
    type: 'range-picker',
    key: 'bill_month',
    formKeys: ['bill_time_start', 'bill_time_end'],
    placeholder: ['账单开始月份', '账单结束月份'],
    format: 'YYYY-MM',
    valueFormat: 'YYYY-MM',
    picker: 'month',
  },
])

// 导出类型枚举
export enum BillVerificationExportTypeEnum {
  根据勾选导出 = 1,
  根据筛选结果导出 = 2,
  全部导出 = 3,
}
export const exportOptions = ref([
  ...Enum2Options(BillVerificationExportTypeEnum),
  ...Enum2Options(BillVerificationExportTypeEnum).map((f) => ({
    label: `${f.label} (带图)`,
    value: f.value + 10,
  })),
])

// 默认状态
export const defaultUnBillStatus = 'unverified'
export const defaultBillStatus = 'verified_pending'

// 待核对金额
export const pendingAmount = ''
