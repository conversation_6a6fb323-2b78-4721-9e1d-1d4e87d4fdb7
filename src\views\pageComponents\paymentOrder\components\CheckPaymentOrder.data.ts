// CheckPaymentOrder 组件配置数据
export interface PaymentTypeConfig {
  value: number
  label: string
  detailTitle: string // 明细标题
  orderField: string // 订单号字段
  orderTitle: string // 订单号列标题
  detailFields?: string[] // 明细字段
  detailColumns?: Array<{
    field: string
    title: string
    width?: number
  }>
  // 主表格列配置
  mainColumns: Array<{
    field: string
    title: string
    width?: number
    formatter?: string // 格式化函数名称
    condition?: boolean // 是否显示的条件
  }>
  defaultSettlementMethod?: number // 默认结算方式
  apiConfig: {
    listApi: string // 列表接口
    detailApi?: string // 详情接口（可选）
    component: string // 选择组件
  }
  // 账单明细配置（仅应付类型）
  billDetailConfig?: {
    title: string
    orderField: string
    orderTitle: string
    mainColumns: Array<{
      field: string
      title: string
      width?: number
      formatter?: string
    }>
    apiConfig: {
      listApi: string
      detailApi?: string
      component: string
    }
  }
}

export const PAYMENT_TYPE_CONFIG: Record<number, PaymentTypeConfig> = {
  10: {
    value: 10,
    label: '预付款',
    detailTitle: '采购明细',
    orderField: 'number',
    orderTitle: '采购单号',
    detailFields: ['purchase_order_details'],
    detailColumns: [
      { field: 'sku_name', title: '商品名称' },
      { field: 'k3_sku_id', title: '商品编号' },
      { field: 'total_purchase_quantity', title: '采购总数' },
      { field: 'cost_num', title: '成本数量' },
      { field: 'tax_unit_price', title: '含税单价' },
      { field: 'total_purchase_amount', title: '采购总金额' },
      { field: 'total_scheduled_quantity', title: '已预约入库数量' },
      { field: 'total_actual_inbound', title: '实际入库总数量' },
    ],
    mainColumns: [
      { field: 'purchase_type_string', title: '采购类型', width: 120 },
      { field: 'total_purchase_amount', title: '采购单总金额', width: 120, formatter: 'number' },
      { field: 'executed_amount', title: '已执行金额', width: 100, formatter: 'number' },
      { field: 'prepayment_ratio', title: '预付比例', width: 100, formatter: 'percentage' },
    ],
    defaultSettlementMethod: 3,
    apiConfig: {
      listApi: 'GetPurchaseOrderList',
      detailApi: 'GetPurchaseOrderInfo',
      component: 'PurchaseOrderList',
    },
  },
  20: {
    value: 20,
    label: '应付款',
    detailTitle: '应付单明细',
    orderField: 'pinvid',
    orderTitle: '应付单号',
    // detailFields: ['payable_order_details'],
    // detailColumns: [],
    mainColumns: [
      { field: 'inv_amount', title: '应付总额', width: 120, formatter: 'number' },
      { field: 'remaining_amount', title: '剩余金额', width: 120, formatter: 'number' },
    ],
    apiConfig: {
      listApi: 'GetJstPayPayableList',
      component: 'PayableOrderList',
    },
    // 账单明细配置
    billDetailConfig: {
      title: '账单明细',
      orderField: 'number',
      orderTitle: '账单单号',
      mainColumns: [
        { field: 'number', title: '账单单号', width: 120 },
        { field: 'bill_month', title: '账单月份', width: 120 },
        { field: 'actual_payable_amount', title: '应付金额', width: 120, formatter: 'number' },
        { field: 'optimize_amount', title: '优化金额', width: 120, formatter: 'number' },
        { field: 'deduct_amount', title: '扣款金额', width: 120, formatter: 'number' },
        { field: 'other_fee', title: '其他费用', width: 120, formatter: 'number' },
        { field: 'paid_amount', title: '已付金额', width: 120, formatter: 'number' },
        { field: 'remaining_amount', title: '剩余金额', width: 120, formatter: 'number' },
      ],
      apiConfig: {
        listApi: 'GetBillListResultByPayOrder',
        component: 'SelectBillDrawer',
      },
    },
  },
}

// 获取付款类型配置
export const getPaymentTypeConfig = (type: number): PaymentTypeConfig | undefined => {
  return PAYMENT_TYPE_CONFIG[type]
}

// 获取所有付款类型选项
export const getPaymentTypeOptions = () => {
  return Object.values(PAYMENT_TYPE_CONFIG).map((config) => ({
    key: config.value,
    value: config.value,
    label: config.label,
  }))
}

// 获取付款类型文本
export const getPaymentTypeText = (type: number): string => {
  const config = getPaymentTypeConfig(type)
  return config?.label || '未知类型'
}

// 结算方式配置
export const SETTLEMENT_METHOD_CONFIG = [
  { value: '月结', key: 1 },
  { value: '半月结', key: 2 },
  { value: '周结', key: 3 },
  { value: '预付', key: 4 },
  { value: '线上付款', key: 5 },
  // 月结、半月结、周结、预付、线上预付
]

// 收款方式配置
export const PAYMENT_TERM_CONFIG = [
  { value: '银行', key: 1 },
  { value: '支付宝', key: 2 },
  { value: '微信', key: 3 },
  { value: '现金', key: 4 },
]

// 账户类型配置
export const ACCOUNT_TYPE_CONFIG = [
  { key: 1, value: '公户' },
  { key: 2, value: '私户' },
]

// 格式化函数
export const formatters = {
  number: (value: number | string | null | undefined) => {
    const num = Number(value || 0)
    return Number.isNaN(num) ? '0.00' : (num as any).roundNext(2)
  },
  percentage: (value: number | string | null | undefined) => {
    const num = Number(value || 0)
    return Number.isNaN(num) ? '0%' : `${(num as any).roundNext(0)}`
  },
}
