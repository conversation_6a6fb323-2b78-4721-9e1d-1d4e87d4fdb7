<template>
  <a-drawer v-model:open="visible" :title="title" :width="1200" :mask-closable="false" :destroyOnClose="true" placement="right" @close="handleClose">
    <div class="payment-order-detail-drawer">
      <!-- 搜索表单 -->
      <!-- <div class="search-form mb-4">
        <a-form layout="inline" :model="searchForm">
          <a-form-item label="采购单编号">
            <a-input v-model:value="searchForm.purchaseOrderNumber" placeholder="请输入采购单编号" allowClear />
          </a-form-item>
          <a-form-item label="商品名称">
            <a-input v-model:value="searchForm.productName" placeholder="请输入商品名称" allowClear />
          </a-form-item>
          <a-form-item label="采购时间">
            <a-range-picker v-model:value="searchForm.purchaseTimeRange" :placeholder="['开始时间', '结束时间']" format="YYYY-MM-DD" />
          </a-form-item>
          <a-form-item>
            <a-button type="primary" @click="handleSearch">搜索</a-button>
            <a-button class="ml-2" @click="handleReset">重置</a-button>
          </a-form-item>
        </a-form>
      </div> -->

      <!-- 表格 -->
      <div class="table-container">
        <vxe-table
          ref="tableRef"
          :data="tableData"
          :loading="loading"
          :border="true"
          size="mini"
          height="100%"
          :show-overflow="true"
          :show-header-overflow="true"
          :sort-config="{ remote: true }"
          :merge-cells="mergeCells"
          @sort-change="handleSortChange"
        >
          <vxe-column type="seq" title="序号" width="60" align="center" />

          <!-- 动态生成列 -->
          <template v-for="column in tableColumns" :key="column.key">
            <vxe-column :field="column.key" :title="column.name" :width="column.width" :align="getColumnAlign(column.key)">
              <template #default="{ row, rowIndex }">
                <!-- 账单单号特殊处理 -->
                <template v-if="column.key === 'bill_number'">
                  {{ getBillNumberDisplay(row, rowIndex) }}
                </template>

                <!-- 采购单编号特殊处理 -->
                <template v-else-if="column.key === 'purchase_number'">
                  <a @click="handlePurchaseOrderClick(row)" class="link">{{ row[column.key] }}</a>
                </template>

                <!-- 退库申请单号特殊处理 -->
                <template v-else-if="column.key === 'return_application_numbers'">
                  <span v-if="row[column.key] && Array.isArray(row[column.key]) && row[column.key].length > 0">
                    {{ row[column.key].join(', ') }}
                  </span>
                  <span v-else>-</span>
                </template>

                <!-- 采购时间特殊处理 -->
                <template v-else-if="column.key === 'purchase_time'">
                  {{ formatTime(row[column.key]) }}
                </template>

                <!-- 应付单号特殊处理 -->
                <template v-else-if="column.key === 'bill_payable_numbers'">
                  <span v-if="row[column.key] && Array.isArray(row[column.key]) && row[column.key].length > 0">
                    {{ row[column.key].join(', ') }}
                  </span>
                  <span v-else>-</span>
                </template>

                <!-- 商品图片特殊处理 -->
                <template v-else-if="column.key === 'image_url'">
                  <div v-if="row[column.key]" class="image-cell">
                    <a-image
                      :src="row[column.key]"
                      :preview="{
                        src: row[column.key],
                      }"
                      width="50%"
                      class="product-image"
                    />
                  </div>
                  <span v-else class="no-image">-</span>
                </template>

                <!-- 数字类型字段 -->
                <template v-else-if="isNumberField(column.key)">
                  {{ formatNumber(row[column.key]) }}
                </template>

                <!-- 价格类型字段 -->
                <template v-else-if="isPriceField(column.key)">
                  {{ formatPrice(row[column.key]) }}
                </template>

                <!-- 默认显示 -->
                <template v-else>
                  {{ row[column.key] }}
                </template>
              </template>
            </vxe-column>
          </template>
        </vxe-table>
      </div>

      <!-- 分页 -->
      <div class="pagination-container">
        <a-pagination
          v-model:current="pagination.current"
          v-model:pageSize="pagination.pageSize"
          :total="pagination.total"
          :show-size-changer="true"
          :show-quick-jumper="true"
          :show-total="(total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`"
          :page-size-options="['10', '20', '50', '100']"
          @change="handlePageChange"
          @showSizeChange="handlePageSizeChange"
        />
      </div>
    </div>
  </a-drawer>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { message } from 'ant-design-vue'
import dayjs from 'dayjs'

import { calculateMergeCells } from '@/utils'
import { usePage } from '@/hook/usePage'

import { GetBillDrawerList } from '@/servers/paymentOrder'

interface PaymentOrderDetail {
  id: string
  bill_number: string
  purchase_number: string
  return_application_numbers: string[]
  purchase_time: string
  image_url: string
  sku_name: string
  k3_sku_id: string
  purchase_quantity: number
  tax_unit_price: number
  actual_tax_unit_price: number
  purchase_inbound_quantity: number
  undelivered_quantity: number
  return_quantity: number
  total_purchase_amount: number
  refund_amount: number
  prepayment_amount: number
  inv_amount: number
  bill_payable_numbers: string[]
  purchase_id: number
}

// 搜索表单接口（暂时注释，因为搜索表单被注释了）
// interface SearchForm {
//   purchaseOrderNumber: string
//   productName: string
//   purchaseTimeRange: [string, string] | undefined
// }

interface Pagination {
  current: number
  pageSize: number
  total: number
}

defineProps<{
  title?: string
}>()

const emit = defineEmits(['close', 'purchase-order-click'])

const visible = ref(false)
const loading = ref(false)
const tableRef = ref()
const mergeCells = ref<any[]>([])

// 搜索表单（暂时注释，因为搜索表单被注释了）
// const searchForm = reactive<SearchForm>({
//   purchaseOrderNumber: '',
//   productName: '',
//   purchaseTimeRange: undefined,
// })

// 当前付款单数据（用于保存付款单ID和相关参数）
const currentPaymentOrderData = reactive<{
  bill_purchase_numbers?: string[]
  payment_type?: number
}>({})

// 分页配置
const pagination = reactive<Pagination>({
  current: 1,
  pageSize: 20,
  total: 0,
})

// 表格数据
const tableData = ref<PaymentOrderDetail[]>([])

// 表格列配置（从tableEnum移到这里）
const tableColumns = ref([
  { key: 'bill_number', name: '账单单号', is_show: true, width: 150 },
  { key: 'purchase_number', name: '采购单编号', is_show: true, width: 150 },
  { key: 'return_application_numbers', name: '退库申请单号', is_show: true, width: 150 },
  { key: 'purchase_time', name: '采购时间', is_show: true, width: 120 },
  { key: 'image_url', name: '商品图片', is_show: true, width: 80 },
  { key: 'sku_name', name: '商品名称', is_show: true, width: 200 },
  { key: 'k3_sku_id', name: '商品编号', is_show: true, width: 120 },
  { key: 'purchase_quantity', name: '采购数量', is_show: true, width: 100 },
  { key: 'tax_unit_price', name: '采购含税单价', is_show: true, width: 120 },
  { key: 'actual_tax_unit_price', name: '实际含税单价', is_show: true, width: 120 },
  { key: 'purchase_inbound_quantity', name: '采购入库数量', is_show: true, width: 120 },
  { key: 'undelivered_quantity', name: '未入库数量', is_show: true, width: 100 },
  { key: 'return_quantity', name: '退库数量', is_show: true, width: 100 },
  { key: 'total_purchase_amount', name: '采购总金额', is_show: true, width: 120 },
  { key: 'refund_amount', name: '退款金额', is_show: true, width: 100 },
  { key: 'prepayment_amount', name: '预付金额', is_show: true, width: 100 },
  { key: 'inv_amount', name: '采购单应付金额', is_show: true, width: 140 },
  { key: 'bill_payable_numbers', name: '应付单号', is_show: true, width: 150 },
])

// 当前付款单ID
const currentPaymentOrderId = ref<string | undefined>(undefined)

// 获取列表数据
const getList = async () => {
  try {
    loading.value = true

    // 调用GetBillDrawerList接口获取数据
    const params: any = {
      page: pagination.current,
      pageSize: pagination.pageSize,
    }

    // 如果有付款单ID，添加必要的参数
    if (currentPaymentOrderId.value) {
      // 使用已保存的付款单数据
      const paymentOrderData = currentPaymentOrderData

      if (paymentOrderData) {
        // 添加账单/采购单号数组参数，使用numbers作为参数名
        if (paymentOrderData.bill_purchase_numbers && Array.isArray(paymentOrderData.bill_purchase_numbers)) {
          params.numbers = paymentOrderData.bill_purchase_numbers
        }

        // 添加付款类型参数
        if (paymentOrderData.payment_type !== undefined) {
          params.payment_type = paymentOrderData.payment_type
        }
      }
    }

    console.log('调用GetBillDrawerList接口，参数:', params)
    const res = await GetBillDrawerList(params)
    console.log('GetBillDrawerList接口响应:', res)

    if (res.code === 0) {
      tableData.value = res.data.list || []
      pagination.total = res.data.total || 0
      console.log('获取到的数据:', tableData.value)

      // 调试图片数据
      tableData.value.forEach((item, index) => {
        console.log(`第${index + 1}行图片数据:`, {
          image_url: item.image_url,
          hasImage: !!item.image_url,
          imageType: typeof item.image_url,
        })
      })
    } else {
      message.error(res.message || '获取数据失败')
      tableData.value = []
      pagination.total = 0
    }

    // 计算合并单元格
    calculateMergeCellsForTable()
  } catch (error) {
    console.error('获取数据失败:', error)
    message.error('获取数据失败')
    tableData.value = []
    pagination.total = 0
  } finally {
    loading.value = false
  }
}

// 计算合并单元格
const calculateMergeCellsForTable = () => {
  mergeCells.value = calculateMergeCells(tableData.value, 'bill_number', 1)
}

// 获取账单单号显示文本
const getBillNumberDisplay = (row: PaymentOrderDetail, rowIndex: number) => {
  // 检查当前行是否在合并单元格中
  const mergeCell = mergeCells.value.find((cell) => cell.row === rowIndex && cell.col === 1)

  if (mergeCell && mergeCell.rowspan > 1) {
    // 如果是合并单元格的第一行，显示账单单号 + 多单标识
    return `${row.bill_number}【多单】`
  }
  // 检查当前行是否属于某个合并单元格
  const isInMergeCell = mergeCells.value.some((cell) => cell.row <= rowIndex && cell.row + cell.rowspan > rowIndex && cell.col === 1)

  if (isInMergeCell) {
    // 如果是合并单元格的非第一行，不显示内容（会被合并）
    return ''
  }
  // 普通行，只显示账单单号
  return row.bill_number
}

// 搜索功能（暂时注释，因为搜索表单被注释了）
// const handleSearch = () => {
//   pagination.current = 1
//   getList()
// }

// 重置功能（暂时注释，因为搜索表单被注释了）
// const handleReset = () => {
//   Object.assign(searchForm, {
//     purchaseOrderNumber: '',
//     productName: '',
//     purchaseTimeRange: [],
//   })
//   pagination.current = 1
//   getList()
// }

// 分页变化
const handlePageChange = (page: number) => {
  pagination.current = page
  getList()
}

// 分页大小变化
const handlePageSizeChange = (current: number, size: number) => {
  pagination.current = 1
  pagination.pageSize = size
  getList()
}

// 排序变化
const handleSortChange = ({ sortList }: any) => {
  console.log('排序变化:', sortList)
  // TODO: 处理排序逻辑
}

// 格式化时间
const formatTime = (time: string) => {
  if (!time) return '-'
  return dayjs(time).format('YYYY-MM-DD HH:mm:ss')
}

// 格式化数字
const formatNumber = (num: number) => {
  if (num === null || num === undefined) return '-'
  return num.toLocaleString()
}

// 格式化价格
const formatPrice = (price: number) => {
  if (price === null || price === undefined) return '-'
  return `${price}`
}

// 使用 usePage hook
const { pushPage } = usePage()

// 点击采购单编号
const handlePurchaseOrderClick = (row: PaymentOrderDetail) => {
  // emit('purchase-order-click', row.purchase_id)

  // 使用 usePage 跳转到采购单详情页面
  if (row.purchase_id) {
    pushPage(`/purchaseOrderLook/${row.purchase_id}`)
    // 跳转后关闭抽屉
    handleClose()
  } else {
    message.warning('采购单ID不存在，无法跳转')
  }
}

// 关闭抽屉
const handleClose = () => {
  visible.value = false
  emit('close')
}

// 打开抽屉
const open = (paymentOrderId?: string, paymentOrderData?: any) => {
  visible.value = true
  currentPaymentOrderId.value = paymentOrderId

  if (paymentOrderId) {
    console.log('打开付款单详情:', paymentOrderId)

    // 如果有付款单数据，保存到组件中
    if (paymentOrderData) {
      console.log('付款单数据:', paymentOrderData)
      // 可以直接使用传入的数据，而不需要再次调用接口
      if (paymentOrderData.bill_purchase_numbers && paymentOrderData.payment_type !== undefined) {
        // 保存数据用于后续接口调用
        currentPaymentOrderData.bill_purchase_numbers = paymentOrderData.bill_purchase_numbers
        currentPaymentOrderData.payment_type = paymentOrderData.payment_type
      }
    }
  }

  getList()
}

// 获取列对齐方式
const getColumnAlign = (key: string) => {
  const numberFields = ['purchase_quantity', 'purchase_inbound_quantity', 'undelivered_quantity', 'return_quantity']
  const priceFields = ['tax_unit_price', 'actual_tax_unit_price', 'total_purchase_amount', 'refund_amount', 'prepayment_amount', 'inv_amount']

  if (numberFields.includes(key) || priceFields.includes(key)) {
    return 'right'
  }
  if (key === 'image_url') {
    return 'center'
  }
  return 'left'
}

// 判断是否为数字字段
const isNumberField = (key: string) => {
  const numberFields = ['purchase_quantity', 'purchase_inbound_quantity', 'undelivered_quantity', 'return_quantity']
  return numberFields.includes(key)
}

// 判断是否为价格字段
const isPriceField = (key: string) => {
  const priceFields = ['tax_unit_price', 'actual_tax_unit_price', 'total_purchase_amount', 'refund_amount', 'prepayment_amount', 'inv_amount']
  return priceFields.includes(key)
}

// 暴露方法给父组件
defineExpose({
  open,
})
</script>

<style scoped lang="scss">
.payment-order-detail-drawer {
  display: flex;
  flex-direction: column;
  height: 100%;

  .search-form {
    padding: 16px;
    margin-bottom: 16px;
    background-color: #fafafa;
    border-radius: 6px;
  }

  .table-container {
    flex: 1;
    overflow: hidden;

    :deep(.vxe-table) {
      height: 100%;
    }
  }

  .pagination-container {
    padding: 16px 0;
    text-align: right;
    border-top: 1px solid #f0f0f0;
  }

  .image-cell {
    display: flex;
    align-items: center;
    justify-content: center;

    // width: 100%;
    // height: 100%;
  }

  .product-image {
    max-height: 30px;
    border-radius: 4px;
  }
}

:deep(.vxe-table--header-wrapper) {
  background-color: #f5f5f5;
}

:deep(.vxe-table--body-wrapper) {
  background-color: #fff;
}

:deep(.vxe-cell) {
  padding: 8px 12px;
}
</style>
