<template>
  <a-drawer :maskClosable="false" :title="getDrawerTitle" :width="showAuditRecord ? '1250px' : '950px'" :visible="drawerVisible" @close="handleClose" :bodyStyle="{ padding: '0' }">
    <template #extra>
      <a-button @click="handleShowAuditRecord" v-if="[DetailTypeEnum.VIEW, DetailTypeEnum.AUDIT].includes(viewType)">审核记录</a-button>
      <!-- && btnPermission[131006] -->
    </template>
    <div class="flex h-full">
      <div class="w-950 overflow-y-auto p-16">
        <div class="drawer-title">收款信息</div>
        <a-form :label-col="{ span: 7 }" :wrapper-col="{ span: 17 }" :model="form" :rules="rules" ref="formRef">
          <a-row>
            <a-col :span="12">
              <a-form-item label="付款单编号" name="number">
                <span v-if="[DetailTypeEnum.VIEW, DetailTypeEnum.AUDIT].includes(viewType)">{{ form.payment_order_number }}</span>
                <a-input v-else v-model:value="form.payment_order_number" disabled />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="付款类型" name="payment_type">
                <span v-if="[DetailTypeEnum.VIEW, DetailTypeEnum.AUDIT, DetailTypeEnum.EDIT].includes(viewType)">{{ getPaymentTypeText(form.payment_type) }}</span>
                <a-select v-model:value="form.payment_type" placeholder="请选择" @change="handlePaymentTypeChange" :disabled="props.orderType === 'PREPAY' || isBatchPaymentMode" v-else>
                  <a-select-option v-for="option in paymentTypeOptions" :key="option.value" :value="option.value">
                    {{ option.label }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col v-if="form.payment_type === 20" :span="12">
              <a-form-item label="供应商" name="supplier_id">
                <span v-if="[DetailTypeEnum.VIEW, DetailTypeEnum.AUDIT].includes(viewType)">
                  {{ form.supplier_name }}
                  <span v-if="form.company_supplier_name && form.company_supplier_name !== form.supplier_name" class="text-gray-500 ml-2">({{ form.company_supplier_name }})</span>
                </span>
                <SelectSupplier
                  v-else
                  v-model:value="form.supplier_id"
                  v-model:label="form.supplier_name"
                  :title="'请选择供应商'"
                  :api="GetPageSuppliersSelect"
                  :mode="'single'"
                  :labelInValue="false"
                  @update:value="handleSupplierIdChange"
                />
              </a-form-item>
            </a-col>
            <a-col v-if="form.payment_type === 20" :span="12">
              <a-form-item label="账单月份" name="bill_month">
                <span v-if="[DetailTypeEnum.VIEW, DetailTypeEnum.AUDIT].includes(viewType)">{{ form.bill_month ? dayjs(form.bill_month).format('YYYY-MM') : '-' }}</span>
                <a-date-picker v-model:value="form.bill_month" picker="month" format="YYYY-MM" valueFormat="YYYY-MM" v-else />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="供应商子公司" name="company_supplier_id">
                <div v-if="isSupplierSubsidiaryDisabled" class="flex items-center">
                  <span :class="{ 'text-red-500': hasMultipleSupplierSubsidiaries }">{{ supplierSubsidiaryDisplayText }}</span>
                  <a-tooltip v-if="hasMultipleSupplierSubsidiaries" title="该供应商的应付单包含多个子公司，将跟供应商主公司进行结算" placement="top">
                    <ExclamationCircleOutlined class="ml-2 text-red-500 cursor-pointer" />
                  </a-tooltip>
                </div>
                <a-input v-else-if="form.payment_type === 20 && !form.supplier_id" :disabled="true" placeholder="请选择供应商">{{ form.company_supplier_id }}</a-input>
                <SelectSupplier
                  v-else
                  v-model:value="form.company_supplier_id"
                  v-model:label="form.company_supplier_name"
                  :title="'请选择供应商子公司'"
                  :api="GetPageMRPSupplierCompanySelect"
                  :apiParams="supplierSubsidiaryApiParams"
                  :mode="'single'"
                  :labelInValue="false"
                  @update:value="handleSupplierChange"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="结算方式" name="settlement_method">
                <span v-if="[DetailTypeEnum.VIEW, DetailTypeEnum.AUDIT].includes(viewType)">
                  {{ settlement_method.find((item) => item.key === form.settlement_method)?.value }}
                </span>
                <a-select v-model:value="form.settlement_method" placeholder="请选择" :disabled="form.payment_type === 10" v-else>
                  <a-select-option v-for="item in settlement_method" :key="item.key" :value="item.key">{{ item.value }}</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="收款方式" name="receipt_method">
                <span v-if="[DetailTypeEnum.VIEW, DetailTypeEnum.AUDIT].includes(viewType)">{{ paymentTerm.find((i) => i.key === form.receipt_method)?.value }}</span>
                <a-select v-model:value="form.receipt_method" placeholder="请选择" @change="handleReceiptMethodChange" v-else>
                  <a-select-option v-for="item in paymentTerm" :key="item.key" :value="item.key">{{ item.value }}</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="账户类型" name="account_type">
                <span v-if="[DetailTypeEnum.VIEW, DetailTypeEnum.AUDIT].includes(viewType)">{{ accountType.find((i) => i.key === form.account_type)?.value }}</span>
                <a-select v-model:value="form.account_type" placeholder="请选择" @change="handleAccountTypeChangeForReceipt" v-else>
                  <a-select-option v-for="item in accountType" :key="item.key" :value="item.key">{{ item.value }}</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="收款银行" name="receipt_bank">
                <span v-if="[DetailTypeEnum.VIEW, DetailTypeEnum.AUDIT].includes(viewType)">{{ form.receipt_bank }}</span>
                <a-input v-model:value="form.receipt_bank" v-else />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="收款账户名称" name="receipt_account_name">
                <span v-if="[DetailTypeEnum.VIEW, DetailTypeEnum.AUDIT].includes(viewType)">{{ form.receipt_account_name }}</span>
                <a-input v-model:value="form.receipt_account_name" v-else />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="收款账号" name="receipt_card_number">
                <span v-if="[DetailTypeEnum.VIEW, DetailTypeEnum.AUDIT].includes(viewType)">{{ form.receipt_card_number }}</span>
                <a-input v-model:value="form.receipt_card_number" v-else />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="备注" name="remark">
                <span v-if="[DetailTypeEnum.VIEW, DetailTypeEnum.AUDIT].includes(viewType)">{{ form.remark }}</span>
                <a-textarea v-model:value="form.remark" placeholder="请输入描述说明" v-else />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="上传发票" name="Invoice_attachments">
                <div class="flex items-center">
                  <Upload
                    v-model:value="form.Invoice_attachments"
                    v-if="[DetailTypeEnum.EDIT, DetailTypeEnum.ADD].includes(viewType)"
                    :module="UploadFileModuleEnum.PaymentOrder"
                    :accept="'.jpg,.png,.pdf,.docx,.xlsx,.xls,.csv'"
                    @change="() => formRef.validateFields(['Invoice_attachments'])"
                  />
                  <FileModal v-else :list="form.Invoice_attachments || []" />
                </div>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="附件" name="attachments">
                <div class="flex items-center">
                  <Upload
                    v-model:value="form.attachments"
                    v-if="[DetailTypeEnum.EDIT, DetailTypeEnum.ADD].includes(viewType)"
                    :module="UploadFileModuleEnum.PaymentOrder"
                    @change="() => formRef.validateFields(['attachments'])"
                  />
                  <FileModal v-else :list="form.attachments || []" />
                </div>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
        <!-- 应付类型下使用tabs -->
        <template v-if="form.payment_type === 20">
          <div class="drawer-title">明细信息</div>
          <a-tabs v-model:activeKey="activeTab" class="mb-8">
            <a-tab-pane key="bill" tab="账单明细">
              <a-flex justify="flex-end" class="mb-8" v-if="[DetailTypeEnum.EDIT, DetailTypeEnum.ADD].includes(viewType) && selectProductList.length === 0">
                <a-space>
                  <a-button
                    v-if="currentPaymentTypeConfig?.billDetailConfig?.apiConfig.component === 'SelectBillDrawer'"
                    type="primary"
                    @click="selectBillDrawerRef?.open(selectBillList, form.company_supplier_id, form.company_supplier_name)"
                  >
                    添加账单
                  </a-button>
                </a-space>
              </a-flex>
            </a-tab-pane>
            <a-tab-pane key="payable" tab="应付单明细" v-if="!isBatchPaymentMode">
              <a-flex justify="flex-end" class="mb-8" v-if="[DetailTypeEnum.EDIT, DetailTypeEnum.ADD].includes(viewType) && selectBillList.length === 0">
                <a-space>
                  <a-button
                    v-if="currentPaymentTypeConfig?.apiConfig.component === 'PayableOrderList'"
                    type="primary"
                    @click="payableOrderListRef?.open(selectProductList, form.company_supplier_id, form.company_supplier_name)"
                  >
                    添加应付单
                  </a-button>
                </a-space>
              </a-flex>
            </a-tab-pane>
          </a-tabs>
        </template>
        <!-- 预付类型下不使用tabs -->
        <template v-else>
          <div class="drawer-title">{{ currentPaymentTypeConfig?.detailTitle || '明细' }}</div>
          <a-flex justify="flex-end" class="mb-8" v-if="[DetailTypeEnum.EDIT, DetailTypeEnum.ADD].includes(viewType) && (!props.purchaseOrderIds || props.purchaseOrderIds.length === 0)">
            <a-space>
              <a-button
                v-if="currentPaymentTypeConfig?.apiConfig.component === 'PurchaseOrderList'"
                type="primary"
                @click="purchaseOrderListRef?.open(selectProductList, form.company_supplier_id, form.company_supplier_name)"
              >
                添加采购单
              </a-button>
            </a-space>
          </a-flex>
        </template>
        <vxe-table
          ref="tableRef"
          :data="showProductList"
          :row-config="{ keyField: form.payment_type === 20 ? 'pinvid' : 'number', isHover: true, useKey: true }"
          :expand-config="expandConfig"
          show-footer
          :border="true"
          size="mini"
          :footer-data="footerData"
          :column-config="{ resizable: true }"
          :virtual-y-config="{ enabled: false }"
          class="tableBoxwidth"
        >
          <vxe-column type="expand" width="50" fixed="left">
            <template #header v-if="currentPaymentTypeConfig?.detailColumns">
              <i :class="isExpandAll ? 'vxe-icon-square-minus' : 'vxe-icon-square-plus'" style="cursor: pointer" @click="toggleExpandAll"></i>
            </template>
            <template #content="{ row }" v-if="currentPaymentTypeConfig?.detailColumns">
              <vxe-table :data="getDetailData(row)" :row-config="{ keyField: 'id', isHover: true }" :column-config="{ resizable: true }">
                <vxe-column v-for="col in currentPaymentTypeConfig?.detailColumns" :key="col.field" :field="col.field" :title="col.title" :width="col.width" />
              </vxe-table>
            </template>
          </vxe-column>
          <vxe-column :field="currentPaymentTypeConfig?.orderField" :title="currentPaymentTypeConfig?.orderTitle" width="140" v-if="!(form.payment_type === 20 && activeTab === 'bill')">
            <template #default="{ row }">
              <span :class="!(viewType === DetailTypeEnum.ADD) && form.payment_type === 10 ? 'purchase-link' : ''" @click="handleOrderClick(row)">
                {{ row[currentPaymentTypeConfig?.orderField || ''] }}
              </span>
            </template>
          </vxe-column>
          <!-- 主表格列配置：应付类型下，根据当前tab显示不同的列 -->
          <template v-if="form.payment_type === 20 && activeTab === 'payable'">
            <vxe-column field="inv_amount" title="应付总额" width="120" :formatter="({ row }) => formatters.number(row.inv_amount)" />
            <vxe-column field="remaining_amount" title="剩余金额" width="120" :formatter="({ row }) => formatters.number(row.remaining_amount)" />
            <vxe-column field="current_payment" title="本次付款" width="120">
              <template #default="{ row }">
                <template v-if="[DetailTypeEnum.ADD, DetailTypeEnum.EDIT].includes(viewType)">
                  <a-input-number
                    v-model:value="row.current_payment"
                    :min="0"
                    :precision="2"
                    :parser="(value) => Number(value) || 0"
                    :formatter="(value) => String(Number(value) || 0)"
                    @change="() => handlePrepaymentAmountChange(row)"
                    style="width: 100px"
                  />
                </template>
                <template v-else>
                  {{ row.current_payment }}
                </template>
              </template>
            </vxe-column>
          </template>
          <!-- 账单明细列配置 -->
          <template v-if="form.payment_type === 20 && activeTab === 'bill'">
            <!-- 账单单号列 -->
            <vxe-column field="number" title="账单单号" width="120" />
            <!-- 账单月份列 - 单独处理格式 -->
            <vxe-column field="bill_month" title="账单月份" width="120">
              <template #default="{ row }">
                {{ row.bill_month ? dayjs(row.bill_month).format('YYYY-MM') : '-' }}
              </template>
            </vxe-column>
            <!-- 其他列 -->
            <vxe-column
              v-for="col in currentPaymentTypeConfig?.billDetailConfig?.mainColumns?.filter((c) => c.field !== 'number' && c.field !== 'bill_month')"
              :key="col.field"
              :field="col.field"
              :title="col.title"
              :width="col.width"
              :formatter="col.formatter ? ({ row }) => formatters[col.formatter as keyof typeof formatters](row[col.field]) : undefined"
            />
            <vxe-column field="current_payment" title="本次付款" width="120">
              <template #default="{ row }">
                <template v-if="[DetailTypeEnum.ADD, DetailTypeEnum.EDIT].includes(viewType)">
                  <a-input-number
                    v-model:value="row.current_payment"
                    :min="0"
                    :precision="2"
                    :parser="(value) => Number(value) || 0"
                    :formatter="(value) => String(Number(value) || 0)"
                    @change="() => handlePrepaymentAmountChange(row)"
                    style="width: 100px"
                  />
                </template>
                <template v-else>
                  {{ row.current_payment }}
                </template>
              </template>
            </vxe-column>
          </template>
          <!-- 预付类型下，按 mainColumns 渲染 -->
          <template v-else-if="form.payment_type === 10">
            <vxe-column
              v-for="col in currentPaymentTypeConfig?.mainColumns"
              :key="col.field"
              :field="col.field"
              :title="col.title"
              :width="col.width"
              :formatter="col.formatter ? ({ row }) => formatters[col.formatter as keyof typeof formatters](row[col.field]) : undefined"
            />
            <vxe-column field="prepayment_amount" title="本次付款" width="120">
              <template #default="{ row }">
                <template v-if="[DetailTypeEnum.ADD, DetailTypeEnum.EDIT].includes(viewType)">
                  <a-input-number v-model:value="row.prepayment_amount" :min="0" :precision="2" @change="() => handlePrepaymentAmountChange(row)" style="width: 100px" />
                </template>
                <template v-else>
                  {{ row.prepayment_amount }}
                </template>
              </template>
            </vxe-column>
          </template>
          <vxe-column field="fix_option" title="操作" width="100" fixed="right">
            <template #default="{ row }">
              <a-button v-if="[DetailTypeEnum.ADD, DetailTypeEnum.EDIT].includes(viewType)" @click="deleteProduct(row)">删除</a-button>
            </template>
          </vxe-column>
        </vxe-table>

        <!-- 金额计算字段 -->
        <a-form :label-col="{ span: 7 }" :wrapper-col="{ span: 17 }" :model="form" :rules="rules" class="mt-16">
          <a-row>
            <!-- 预付明细专用字段 -->
            <template v-if="form.payment_type === 10">
              <a-col :span="12">
                <a-form-item label="本次付款合计金额">
                  <a-input-number v-model:value="totalPaymentAmount" :min="0" :precision="2" readonly :bordered="false" class="no-padding-left" />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="其他费用" name="other_fees">
                  <span v-if="[DetailTypeEnum.VIEW, DetailTypeEnum.AUDIT].includes(viewType)">{{ form.other_fees?.roundNext(2) }}</span>
                  <a-input-number v-model:value="form.other_fees" :min="0" :precision="2" @change="handleOtherFeesChange" v-else />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="扣款金额" name="deduct_amount">
                  <span v-if="[DetailTypeEnum.VIEW, DetailTypeEnum.AUDIT].includes(viewType)">{{ form.deduct_amount?.roundNext(2) }}</span>
                  <a-input-number v-model:value="form.deduct_amount" @change="changeDeductAmount" v-else :precision="2" />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="优化金额" name="optimize_amount">
                  <span v-if="[DetailTypeEnum.VIEW, DetailTypeEnum.AUDIT].includes(viewType)">{{ form.optimize_amount?.roundNext(2) }}</span>
                  <a-input-number v-model:value="form.optimize_amount" :min="0" :precision="2" @change="handleOptimizeAmountChange" v-else />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="备注" name="deduction_remark">
                  <span v-if="[DetailTypeEnum.VIEW, DetailTypeEnum.AUDIT].includes(viewType)">{{ form.deduction_remark }}</span>
                  <a-textarea v-model:value="form.deduction_remark" placeholder="请输入明细备注" v-else />
                </a-form-item>
              </a-col>
            </template>
            <!-- 应付明细专用字段 -->
            <template v-if="form.payment_type === 20 && activeTab === 'payable'">
              <a-col :span="12">
                <a-form-item label="关联预付单" name="pre_payment_order_ids">
                  <span v-if="[DetailTypeEnum.VIEW, DetailTypeEnum.AUDIT].includes(viewType)">{{ getPrePaymentOrderDisplayText() }}</span>
                  <a-select
                    v-model:value="form.pre_payment_order_ids"
                    mode="multiple"
                    placeholder="请选择关联预付单"
                    :options="prePaymentOrderOptions"
                    :loading="prePaymentOrderLoading"
                    show-search
                    :filter-option="filterPrePaymentOrderOption"
                    @dropdown-visible-change="handlePrePaymentOrderDropdownVisibleChange"
                    @change="handlePrePaymentOrderChange"
                    v-else
                  />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="优化金额" name="optimize_amount">
                  <span v-if="[DetailTypeEnum.VIEW, DetailTypeEnum.AUDIT].includes(viewType)">{{ form.optimize_amount?.roundNext(2) }}</span>
                  <a-input-number v-model:value="form.optimize_amount" :min="0" :precision="2" @change="handleOptimizeAmountChange" v-else />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="其他费用" name="other_fees">
                  <span v-if="[DetailTypeEnum.VIEW, DetailTypeEnum.AUDIT].includes(viewType)">{{ form.other_fees?.roundNext(2) }}</span>
                  <a-input-number v-model:value="form.other_fees" :min="0" :precision="2" @change="handleOtherFeesChange" v-else />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="扣款金额" name="deduct_amount">
                  <span v-if="[DetailTypeEnum.VIEW, DetailTypeEnum.AUDIT].includes(viewType)">{{ form.deduct_amount?.roundNext(2) }}</span>
                  <a-input-number v-model:value="form.deduct_amount" @change="changeDeductAmount" v-else :precision="2" />
                </a-form-item>
              </a-col>
            </template>
            <!-- 通用字段：本次实付金额 -->
            <a-col :span="12">
              <a-form-item label="本次实付金额" name="the_actual_amount">
                <div class="flex items-center">
                  <a-tooltip :title="form.payment_type === 10 ? '本次付款合计金额-优化金额-扣款金额+其他费用' : '账单明细本次付款合计'" placement="top">
                    <ExclamationCircleOutlined class="ml-2 text-blue-500 cursor-pointer" />
                  </a-tooltip>
                  <a-input-number v-model:value="form.the_actual_amount" :min="0" :precision="2" readonly :bordered="false" class="no-padding-left ml-2" />
                </div>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>

        <div class="drawer-title mt-16">打款信息</div>
        <a-form :label-col="{ span: 7 }" :wrapper-col="{ span: 17 }" :model="form" :rules="rules" ref="receiptFormRef">
          <a-row>
            <a-col :span="12">
              <a-form-item label="付款账户类型" name="common_private_account" :required="isFieldRequired">
                <span v-if="[DetailTypeEnum.VIEW].includes(viewType)">{{ form.common_private_account ? accountType.find((i) => i.key === form.common_private_account)?.value : '-' }}</span>
                <a-select v-else v-model:value="form.common_private_account" placeholder="请选择" :disabled="isFieldDisabled" @change="handleAccountTypeChange">
                  <a-select-option v-for="item in accountType" :key="item.key" :value="item.key">{{ item.value }}</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="付款账号名称" name="payment_account_name">
                <span v-if="[DetailTypeEnum.VIEW].includes(viewType)">{{ form.payment_account_name }}</span>
                <a-select
                  v-else
                  v-model:value="form.payment_account_name"
                  :key="`account-select-${form.common_private_account}-${form.payment_account_name}`"
                  placeholder="请选择"
                  show-search
                  :filter-option="filterOption"
                  :disabled="isFieldDisabled"
                  :default-active-first-option="false"
                  :show-arrow="true"
                  :not-found-content="null"
                >
                  <a-select-option v-for="item in accountList" :key="item.value" :value="item.label" @click="handleAccountSelect(item)">
                    <a-tooltip :title="`${item.label}(${item.accout})`">{{ item.label }}({{ item.accout }})</a-tooltip>
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="12">
              <a-form-item label="付款公司" name="payment_company">
                <span v-if="[DetailTypeEnum.VIEW, DetailTypeEnum.AUDIT].includes(viewType)">{{ form.payment_company }}</span>
                <a-select v-else v-model:value="form.payment_company" placeholder="请选择付款公司" show-search :filter-option="filterOption">
                  <a-select-option v-for="company in paymentCompanyOptions" :key="company" :value="company">
                    {{ company }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="付款账号" name="payment_account">
                <span v-if="[DetailTypeEnum.VIEW].includes(viewType)">{{ form.payment_account }}</span>
                <a-input v-model:value="form.payment_account" readonly v-else :disabled="isFieldDisabled" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="12">
              <a-form-item label="期望付款日期" name="expected_payment_date">
                <span v-if="[DetailTypeEnum.VIEW, DetailTypeEnum.AUDIT].includes(viewType)">{{ form.expected_payment_date ? dayjs(form.expected_payment_date).format('YYYY-MM-DD') : '-' }}</span>
                <a-date-picker v-model:value="form.expected_payment_date" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" v-else />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="打款操作人" name="payment_operator_id" :required="isFieldRequired">
                <span v-if="[DetailTypeEnum.VIEW].includes(viewType)">{{ form.payment_operator_name }}</span>
                <a-select
                  v-else
                  v-model:value="form.payment_operator_id"
                  placeholder="请选择打款操作人"
                  :disabled="isFieldDisabled"
                  show-search
                  option-filter-prop="label"
                  :options="userList"
                  :default-active-first-option="false"
                  :not-found-content="null"
                  @select="handlePaymentOperatorSelect"
                ></a-select>
              </a-form-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="12">
              <a-form-item label="财资云付款流水号" name="treasury_payment_serial_number">
                <span>{{ form.treasury_payment_serial_number }}</span>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="出纳实际付款金额" name="actual_payment_amount">
                <span>{{ form.actual_payment_amount ? Number(form.actual_payment_amount).roundNext(2) : '-' }}</span>
              </a-form-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="12">
              <a-form-item label="付款时间" name="payment_time">
                <span>{{ form.payment_time ? dayjs(form.payment_time).format('YYYY-MM-DD') : '-' }}</span>
              </a-form-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="12">
              <a-form-item label="出纳附件" name="cashier_attachment">
                <!-- <Upload v-model:value="form.cashier_attachment" /> -->
                <FileModal :list="form.cashier_attachments" customApi="/api/PaymentOrder/GetCashierAttachment" customParam="id" />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </div>
      <div class="flex-1 overflow-y-auto p-16 bg-#f2f2f2" v-if="showAuditRecord">
        <a-timeline class="ml-16" v-if="auditRecordList.length > 0">
          <a-timeline-item v-for="item in auditRecordList" :key="item.id">
            <div class="timeline-title">
              {{ item.audit_time }}
            </div>
            <div class="timeline-content">
              {{ item.message }}
            </div>
          </a-timeline-item>
        </a-timeline>
        <a-empty v-else class="c-#333" description="暂无审核记录" />
      </div>
    </div>
    <template #footer>
      <a-space :size="16" v-if="[DetailTypeEnum.EDIT, DetailTypeEnum.ADD].includes(viewType)">
        <a-button type="primary" @click="handleSubmitAudit(true)">提交审核</a-button>
        <a-button @click="handleSubmitAudit(false)">保存暂不提交</a-button>
        <a-button @click="handleClose">取消</a-button>
      </a-space>
      <a-space :size="16" v-else-if="viewType === DetailTypeEnum.AUDIT">
        <a-button type="primary" @click="handleAudit(true)">审核通过</a-button>
        <a-button @click="handleAudit(false)">审核拒绝</a-button>
        <a-button @click="handleClose">取消</a-button>
      </a-space>
      <a-space :size="16" v-else-if="viewType === DetailTypeEnum.VIEW">
        <a-button @click="handleDelete(form.id!)" v-if="form.audit_status && [10, 95].includes(form.audit_status)">删除</a-button>
        <a-button @click="handleClose">关闭</a-button>
      </a-space>
    </template>
    <purchase-order-list
      ref="purchaseOrderListRef"
      :supplier-id="form.company_supplier_id"
      :supplier-name="form.company_supplier_name"
      :selected-products="selectProductList"
      @select-product="handleSelectProduct"
      @update-product="handleUpdateProduct"
    />
    <payable-order-list
      ref="payableOrderListRef"
      :supplier-subsidiary-id="form.company_supplier_id"
      :supplier-subsidiary-name="form.company_supplier_name && !hasMultipleSupplierSubsidiaries ? form.company_supplier_name : undefined"
      :selected-products="selectProductList"
      :payment-order-id="form.id"
      @select-product="handleSelectPayableProduct"
      @update-product="handleUpdatePayableProduct"
    />
    <select-bill-drawer
      ref="selectBillDrawerRef"
      :supplier-id="form.company_supplier_id"
      :supplier-name="form.company_supplier_name"
      :selected-bills="selectBillList"
      :payment-order-id="form.id"
      @select-product="handleSelectBillProduct"
      @updateProduct="handleUpdateBillProduct"
    />
  </a-drawer>
  <Purchase ref="PurchaseRef" @confirm="purchaseConfirm"></Purchase>
  <audit-modal ref="auditModalRef" @audit="handleFinishAudit" />
  <reject-audit ref="rejectAuditRef" @audit="handleRejectAudit" />
</template>

<script setup lang="ts">
// Third-party imports
import { computed, ref, onMounted, nextTick, watch } from 'vue'
import { message, Modal } from 'ant-design-vue'
import { ExclamationCircleOutlined } from '@ant-design/icons-vue'
import dayjs, { Dayjs } from 'dayjs'
import type { RuleObject } from 'ant-design-vue/es/form'

// Local imports
import { DetailTypeEnum } from '@/common/enum'
import SelectSupplier from '@/components/business/SelectSupplier'
import FileModal from '@/components/FileModal.vue'
import '@/core' // 导入银行家舍入法

import AuditModal from '@/views/pageComponents/purchaseManagement/components/AuditModal.vue'
import Purchase from '@/views/pageComponents/supplierManagement/components/PurchaseModal.vue'

import { getPaymentTypeConfig, getPaymentTypeOptions, SETTLEMENT_METHOD_CONFIG, PAYMENT_TERM_CONFIG, ACCOUNT_TYPE_CONFIG, formatters } from './CheckPaymentOrder.data'
import RejectAudit from './RejectAudit.vue'
import PurchaseOrderList from './PurchaseOrderList.vue'
import PayableOrderList from './PayableOrderList.vue'
import SelectBillDrawer from './SelectBillDrawer.vue'

import { GetWarehouses } from '@/servers/Purchaseapplyorder'
import { GetPageMRPSupplierCompanySelect, GetPageSuppliersSelect } from '@/servers/BusinessCommon'
import {
  GetSupplierFinanceList,
  Get1688MRPSupplierCompanySelect,
  AddPaymentOrder,
  GetPaymentAccount,
  GetPaymentOrderDetail,
  BatchAudit,
  AuditRejected,
  GetAuditRecord,
  UpdatePaymentOrder,
  DeletePayment,
  UpdatePaymentFinanceOrder,
  GetPurchaseOrderInfo,
  // GetPayableOrderInfo,
  GetInnerUsers,
  GetPrePaymentOrderSelect,
  GetPrePaymentOrderDetailThisDeductionAmountTotal,
} from '@/servers/paymentOrder'
import { GetSupplierCompanyMessage } from '@/servers/Supplier'

// Type definitions
interface FileItem {
  id: string
  name: string
  url: string
  status: string
}

interface SupplierItem {
  label: string
  value: number
}

interface AccountItem {
  label: string
  value: number
  accout: string
}

interface PaymentOrderDetail {
  purcharse_order_id: number
  current_payment: number
  total_purchase_amount: number
  executed_amount: number
  payment_order_detail?: PaymentOrderDetail[]
}

interface FieldConfig {
  disableSupplier?: boolean
  // 可以继续添加其他字段的配置
}

interface FormState {
  id: number | null
  payment_order_number: string
  payment_type: number | undefined
  settlement_method: number | undefined
  expected_payment_date: Dayjs | undefined
  payment_company: string
  common_private_account: number | undefined
  payment_account_id: number | null
  receipt_account_name: string
  account_type: number | undefined
  receipt_bank: string
  receipt_method: number | undefined
  receipt_card_number: string
  the_actual_amount: number
  remark: string
  deduction_remark: string
  company_supplier_name: string
  company_supplier_id: any
  supplier_id: number | undefined
  supplier_name: string
  supplier_type: number | undefined
  payment_account_name: string
  payment_account: string
  deduct_amount: number
  payment_operator_id: number | undefined
  payment_operator_name: string
  treasury_payment_serial_number: string
  payment_order_detail: PaymentOrderDetail[]
  files: FileItem[]
  cashier_attachments: FileItem[]
  Invoice_attachments?: any[]
  invoice_files?: FileItem[]
  audit_status: number | null
  actual_payment_amount: string | number | undefined
  payment_time: string | Dayjs | undefined
  other_fees: number
  optimize_amount: number
  pre_payment_order_ids: number[]
  bill_month: string | Dayjs | undefined
  attachments?: FileItem[]
}

interface ApiResponse {
  data: {
    payment_order_detail: PaymentOrderDetail[]
    paymentJstBillDetail?: any[] // 应付类型的明细数据
    payment_bill_details?: any[] // 账单明细数据
    files: FileItem[]
    invoice_files?: FileItem[] // 发票附件
    invoice_attachment_array?: any[] // 发票附件数组
    cashier_attachments: FileItem[]
    payment_order_number: string | null
    payment_type: number | null
    settlement_method: string | null // 修改为字符串类型，因为接口返回的是字符串
    expected_payment_date: string | null
    common_private_account: number | null
    account_type: number | null
    receipt_method: number | null
    company_supplier_id: number | null
    company_supplier_name: string | null
    supplier_id: number | null
    supplier_name: string | null
    payment_account_id: number | null
    payment_account_name: string | null
    payment_account: string | null
    payment_company: string | null
    receipt_account_name: string | null
    receipt_bank: string | null
    receipt_card_number: string | null
    the_actual_amount: number | null
    deduct_amount: number | null
    remark: string | null
    payment_operator_id: number | null
    payment_operator_name: string | null
    treasury_payment_serial_number: string | null
    audit_status: number | null
    actual_payment_amount: string | number | null
    payment_time: string | null
    other_fees: number | null
    optimize_amount: number | null
    pre_payment_order_ids: number[] | null
    bill_month: string | null
    deduction_remark?: string | null // 新增：明细备注字段
  }
}

const emits = defineEmits(['query', 'close'])
const { btnPermission } = usePermission()

// 定义供应商信息接口
interface SupplierInfo {
  supplier_name?: string
  company_supplier_id?: number
}

// 添加props定义，接收采购单ids、字段配置和供应商信息
const props = defineProps<{
  purchaseOrderIds?: number[]
  fieldConfig?: FieldConfig
  supplierInfo?: SupplierInfo
  orderType?: string
  billIds?: number[] // 批量请款的账单ID列表
}>()

const tableRef = ref()
const showAuditRecord = ref(false)
const drawerVisible = ref(false)
const footerData = ref<VxeTablePropTypes.FooterData>([{}])
const viewType = ref<DetailTypeEnum>(DetailTypeEnum.ADD)
const auditRecordList = ref<any[]>([])
const selectProductList = ref<any[]>([])
// 判断是否为批量请款模式
const isBatchPaymentMode = computed(() => {
  const result = props.orderType === 'BATCH_PAYMENT' && props.billIds && props.billIds.length > 0
  return result
})

const showProductList = computed(() => {
  // 应付类型下，根据当前选中的tab显示不同的数据
  if (form.value.payment_type === 20) {
    if (activeTab.value === 'payable') {
      return selectProductList.value
    }
    if (activeTab.value === 'bill') {
      return selectBillList.value
    }
  }
  return selectProductList.value
})
const warehouseOption = ref<any[]>([])

const supperList = ref<SupplierItem[]>([])
const accountList = ref<AccountItem[]>([])
const attachment = ref<FileItem[]>([])

const userList = ref<{ label: string; value: number }[]>([])

// 关联预付单相关变量
const prePaymentOrderOptions = ref<{ label: string; value: number }[]>([])
const prePaymentOrderLoading = ref(false)
const prePaymentOrderTotalAmount = ref(0) // 预付单的本次付款合计

// 付款公司选项
const paymentCompanyOptions = ref([
  '汕头市西月电子商务有限公司',
  '广东云月仓供应链管理有限公司',
  '广东省西之月科技有限公司',
  '汕头市皓物科技有限公司',
  '广东依欧好生物科技有限公司',
  '广东省简素净电子商务有限公司',
  '汕头市欧浩科技有限公司',
  '汕头市优家电子商务有限公司',
  '汕头市古格尔科技有限公司',
  '广东省简素净生物科技有限公司',
  '广东省欧皓生物技术有限公司',
  '广东云跨易网络科技有限公司',
  '广东采云月供应链管理有限公司',
  '深圳维克采国际贸易有限公司',
  '广东西之月跨境电商有限公司',
  '广东德吉清生物技术有限公司',
])

// 付款类型选项
const paymentTypeOptions = computed(() => {
  // 如果 orderType 为 PREPAY，只返回预付款选项
  if (props.orderType === 'PREPAY') {
    return [{ label: '预付款', value: 10 }]
  }
  // 否则返回所有选项
  return getPaymentTypeOptions()
})

// 当前付款类型配置
const currentPaymentTypeConfig = computed(() => getPaymentTypeConfig(form.value.payment_type || 10))

// 供应商子公司API参数
const supplierSubsidiaryApiParams = computed(() => {
  if (form.value.payment_type === 20 && form.value.supplier_id) {
    // 应付类型且有选择供应商时，按供应商ID过滤
    return {
      supplier_id: form.value.supplier_id,
    }
  }
  return {}
})

const form = ref<FormState>({
  id: null,
  payment_order_number: '',
  payment_type: undefined,
  settlement_method: undefined,
  expected_payment_date: dayjs(),
  payment_company: '',
  common_private_account: undefined,
  payment_account_id: null,
  receipt_account_name: '',
  account_type: undefined,
  receipt_bank: '',
  receipt_method: undefined,
  receipt_card_number: '',
  the_actual_amount: 0,
  remark: '',
  deduction_remark: '',
  company_supplier_name: '',
  company_supplier_id: null,
  supplier_id: undefined,
  supplier_name: '',
  supplier_type: undefined,
  payment_account_name: '',
  payment_account: '',
  deduct_amount: 0,
  payment_operator_id: undefined,
  payment_operator_name: '',
  treasury_payment_serial_number: '',
  payment_order_detail: [],
  files: [],
  cashier_attachments: [],
  Invoice_attachments: [],
  invoice_files: [],
  audit_status: null,
  actual_payment_amount: undefined,
  payment_time: undefined,
  other_fees: 0,
  optimize_amount: 0,
  pre_payment_order_ids: [],
  bill_month: undefined,
})

const router = useRouter()

// 修改 isFieldRequired 计算属性
const isFieldRequired = computed(() => {
  // 如果是审核模式且状态是财务一级或二级审核
  if (viewType.value === DetailTypeEnum.AUDIT && [40, 41].includes(temp_audit_status.value || 0)) {
    return true
  }
  // 如果是编辑模式且状态是财务一级或二级审核
  if (viewType.value === DetailTypeEnum.EDIT && [40, 41].includes(form.value.audit_status || 0)) {
    return true
  }
  // 其他所有情况都是非必填
  return false
})

// 添加实付金额的验证函数
const validateActualAmount = async (_rule: RuleObject, value: number) => {
  if (value < 0) {
    return Promise.reject('本次付款不能小于0')
  }
  return Promise.resolve()
}

// 动态验证规则
const getDynamicRules = (): { [key: string]: RuleObject[] } => {
  const baseRules: { [key: string]: RuleObject[] } = {
    payment_type: [{ required: true, message: '请选择付款类型' }],
    settlement_method: [{ required: true, message: '请选择结算方式' }],
    the_actual_amount: [
      { required: true, message: '请填写本次实付金额' },
      { validator: validateActualAmount, message: '本次付款不能小于0' },
    ],
    common_private_account: [{ required: false, message: '请选择付款账户类型' }],
    receipt_bank: [
      {
        validator: (_, value) => {
          if (form.value.receipt_method === 1) {
            if (!value || value.trim() === '') {
              return Promise.reject('请填写收款银行')
            }
          }
          return Promise.resolve()
        },
      },
    ],
    payment_operator_id: [
      {
        required: isFieldRequired.value,
        message: '请选择打款操作人',
      },
    ],
    attachments: [
      {
        trigger: ['change'],
        required: true,
        validator: (_, value) => {
          if (!value?.length) {
            return Promise.reject('请上传附件')
          }
          return Promise.resolve()
        },
      },
    ],
  }

  // 根据供应商类型动态设置字段必填状态
  const isIndividualSupplier = form.value.supplier_type === 2

  const rules: { [key: string]: RuleObject[] } = {
    ...baseRules,
    receipt_method: [{ required: !isIndividualSupplier, message: '请选择收款方式' }],
    account_type: [{ required: !isIndividualSupplier, message: '请选择收款账户类型' }],
    receipt_account_name: [{ required: !isIndividualSupplier, message: '请填写收款账户' }],
  }

  // 根据付款类型设置不同的验证规则
  if (form.value.payment_type === 20) {
    // 应付类型：供应商子公司非必填，账单月份必填，供应商必填
    rules.company_supplier_id = [{ required: false, message: '请选择供应商子公司' }]
    rules.bill_month = [{ required: true, message: '请选择账单月份' }]
    rules.supplier_id = [{ required: true, message: '请选择供应商' }]

    // 关联预付单字段验证（非必填）
    rules.pre_payment_order_ids = [{ required: false, message: '请选择关联预付单' }]
  } else {
    // 其他类型：供应商子公司必填
    rules.company_supplier_id = [{ required: true, message: '请选择供应商' }]
  }

  return rules
}

const rules = computed(() => getDynamicRules())

const expandConfig = reactive<VxeTablePropTypes.ExpandConfig>({
  showIcon: true,
  iconOpen: 'vxe-icon-square-minus',
  iconClose: 'vxe-icon-square-plus',
  visibleMethod({ row }) {
    const config = currentPaymentTypeConfig.value
    if (!config) return false
    const detailField = config.detailFields?.[0] || 'payable_order_details'
    return row[detailField]?.length > 0
  },
})

const purchaseOrderListRef = ref()
const payableOrderListRef = ref()
const selectBillDrawerRef = ref()
const auditModalRef = ref()
const rejectAuditRef = ref()
const formRef = ref()
const receiptFormRef = ref()

// 添加tabs相关变量
const activeTab = ref('bill')
const selectBillList = ref<any[]>([])

// 添加一个变量来存储初始表单值
const initialFormValues = ref<any>(null)

onMounted(async () => {
  const res = await Get1688MRPSupplierCompanySelect({})
  supperList.value = res.data.map((i) => ({
    label: i.label,
    value: Number(i.value),
  }))

  const paymentAccountRes = await GetPaymentAccount({})
  accountList.value = paymentAccountRes.data.list.map((i) => ({
    label: i.payment_account_name,
    value: Number(i.payment_account_id),
    accout: i.payment_account,
  }))

  // 获取内部用户列表
  const usersRes = await GetInnerUsers({})
  userList.value = usersRes.data.map((user) => ({
    label: user.value,
    value: user.key,
  }))
})

const deleteProduct = (row) => {
  // 应付类型下，根据当前tab删除对应的数据
  if (form.value.payment_type === 20) {
    if (activeTab.value === 'payable') {
      const index = selectProductList.value.findIndex((item) => {
        const match = item.pinvid === row.pinvid
        return match
      })

      if (index !== -1) {
        selectProductList.value.splice(index, 1)
      } else {
        const refIndex = selectProductList.value.indexOf(row)
        if (refIndex !== -1) {
          selectProductList.value.splice(refIndex, 1)
        }
      }
    } else if (activeTab.value === 'bill') {
      const index = selectBillList.value.findIndex((item) => {
        const match = item.number === row.number
        return match
      })

      if (index !== -1) {
        selectBillList.value.splice(index, 1)
      } else {
        const refIndex = selectBillList.value.indexOf(row)
        if (refIndex !== -1) {
          selectBillList.value.splice(refIndex, 1)
        }
      }
    }
  } else {
    // 预付类型：使用 purcharse_order_id
    const index = selectProductList.value.findIndex((item) => {
      const match = item.purcharse_order_id === row.purcharse_order_id
      return match
    })

    if (index !== -1) {
      selectProductList.value.splice(index, 1)
    } else {
      const refIndex = selectProductList.value.indexOf(row)
      if (refIndex !== -1) {
        selectProductList.value.splice(refIndex, 1)
      }
    }
  }

  // 如果删除后没有明细了，清空供应商相关信息
  const currentList = form.value.payment_type === 20 && activeTab.value === 'bill' ? selectBillList.value : selectProductList.value
  if (currentList.length === 0) {
    form.value.the_actual_amount = 0
    form.value.other_fees = 0
    form.value.optimize_amount = 0

    // 清空供应商子公司信息
    form.value.company_supplier_id = null
    form.value.company_supplier_name = ''

    form.value.receipt_account_name = ''
    form.value.receipt_bank = ''
    form.value.receipt_card_number = ''
    form.value.account_type = undefined
    form.value.receipt_method = undefined
    form.value.supplier_id = undefined
    form.value.supplier_name = ''
  } else {
    // 如果还有其他明细，重新计算实付金额
    setActualAmount()
  }
  calculateFooterData()
}

const handlePaymentOperatorSelect: SelectProps['onSelect'] = (value, option) => {
  form.value.payment_operator_id = typeof value === 'number' ? value : undefined
  form.value.payment_operator_name = option?.label || ''
}

const handleAccountSelect = (item: any) => {
  form.value.payment_account_id = item.value
  form.value.payment_account = item.accout
  // 拼接账户名称和账号，格式：账户名称(账号)
  form.value.payment_account_name = `${item.label}(${item.accout})`
}

const setActualAmount = () => {
  let totalPrepaymentAmount = 0

  // 根据当前tab计算金额
  const currentList = form.value.payment_type === 20 && activeTab.value === 'bill' ? selectBillList.value : selectProductList.value

  currentList.forEach((item) => {
    if (form.value.payment_type === 20) {
      // 应付类型使用 current_payment
      totalPrepaymentAmount += Number(item.current_payment || 0)
    } else {
      // 预付类型使用 prepayment_amount
      totalPrepaymentAmount += Number(item.prepayment_amount || 0)
    }
  })

  const deductAmount = Number(form.value.deduct_amount || 0)
  const otherFees = Number(form.value.other_fees || 0)
  const optimizeAmount = Number(form.value.optimize_amount || 0)

  if (form.value.payment_type === 20) {
    // 应付付款：本次实付金额 = 应付明细的本次付款合计 - 预付单的本次付款合计 + 其他费用 - 优惠金额 - 扣款金额
    const prePaymentOrderAmount = Number(form.value.pre_payment_order_ids && form.value.pre_payment_order_ids.length > 0 ? prePaymentOrderTotalAmount.value : 0)
    form.value.the_actual_amount = Number(Math.max(0, totalPrepaymentAmount - prePaymentOrderAmount + otherFees - optimizeAmount - deductAmount).roundNext(2))
  } else {
    // 预付款：本次实付金额 = 本次付款合计 + 其他费用 - 优惠金额 - 扣款金额
    form.value.the_actual_amount = Number(Math.max(0, totalPrepaymentAmount + otherFees - optimizeAmount - deductAmount).roundNext(2))
  }
}

const changeDeductAmount = () => {
  if (form.value.deduct_amount !== null && form.value.deduct_amount !== undefined) {
    form.value.deduct_amount = Number(form.value.deduct_amount.roundNext(2))
  }
  // 重新计算本次实付金额
  setActualAmount()
}

// 处理供应商子公司变化
const handleSupplierChange = async (val: any) => {
  // 应付类型时，检查是否已选择供应商
  if (form.value.payment_type === 20 && !form.value.supplier_id) {
    message.warning('请先选择供应商')
    return
  }

  // 清空缓存
  clearSupplierFinanceCache()

  // 检查应付明细列表中是否有多个供应商子公司，如果有则清空应付明细列表
  const hasMultipleSubsidiaries =
    selectProductList.value.length > 0 &&
    form.value.payment_type === 20 &&
    selectProductList.value.some((item, index) => {
      if (index === 0) return false
      return item.company_supplier_id !== selectProductList.value[0].company_supplier_id
    })

  if (hasMultipleSubsidiaries) {
    // 如果有多个供应商子公司，清空应付明细列表
    selectProductList.value = []
    form.value.the_actual_amount = 0
    form.value.deduct_amount = 0
    form.value.other_fees = 0
    form.value.optimize_amount = 0
    form.value.bill_month = undefined
    calculateFooterData()
  } else {
    // 如果没有多个供应商子公司，正常清空采购单列表
    form.value.payment_type === 10 && (selectProductList.value = [])
    // 重置实付金额
    form.value.the_actual_amount = 0
    // 重置扣款金额
    form.value.deduct_amount = 0
    // 重置其他费用和优化金额
    form.value.other_fees = 0
    form.value.optimize_amount = 0
    // 重置账单月份
    form.value.bill_month = undefined
    // 重新计算表格合计
    calculateFooterData()
  }

  if (!val) {
    // 清空供应商相关信息
    selectProductList.value = []
    form.value.company_supplier_id = null
    form.value.company_supplier_name = ''
    form.value.supplier_id = undefined
    form.value.supplier_name = ''
    form.value.receipt_account_name = ''
    form.value.receipt_bank = ''
    form.value.receipt_card_number = ''
    form.value.account_type = undefined
    form.value.receipt_method = undefined
    form.value.supplier_type = undefined
    // 重置表单验证状态
    formRef.value?.resetFields(['company_supplier_id', 'receipt_account_name', 'receipt_bank', 'receipt_card_number', 'account_type', 'receipt_method'])
  } else {
    // 先清空所有供应商相关字段
    form.value.company_supplier_name = ''
    form.value.receipt_account_name = ''
    form.value.receipt_bank = ''
    form.value.receipt_card_number = ''
    form.value.account_type = undefined
    form.value.receipt_method = undefined
    form.value.supplier_type = undefined

    // 等待下一个 tick，确保清空操作完成
    await nextTick()

    // 设置新的供应商ID
    form.value.company_supplier_id = val

    // 等待下一个 tick，确保值已经更新
    await nextTick()

    // 从供应商列表中查找供应商名称
    const selectedSupplier = supperList.value.find((item) => item.value === val)
    if (selectedSupplier) {
      form.value.company_supplier_name = selectedSupplier.label
    }
    // 获取供应商信息（包括supplier_type）
    await getSupplierInfo(val)

    // 获取供应商财务信息
    try {
      const financeInfo = await getSupplierFinanceInfo(val, form.value.receipt_method, form.value.account_type)
      if (financeInfo) {
        // 重新设置供应商财务信息
        // form.value.company_supplier_name = financeInfo.supplier_name || ''
        form.value.receipt_account_name = financeInfo.receipt_account_name || ''
        form.value.receipt_bank = financeInfo.receipt_bank || ''
        form.value.receipt_card_number = financeInfo.receipt_card_number || ''
        form.value.account_type = financeInfo.account_type
        form.value.receipt_method = financeInfo.receipt_method
      }
    } catch (error) {
      console.error('获取供应商财务信息失败:', error)
      // 静默处理错误，不显示提示
    }
  }

  // 检查应付明细列表中是否有多个供应商子公司，如果有则清空银行信息
  if (hasMultipleSupplierSubsidiaries.value) {
    form.value.receipt_account_name = ''
    form.value.receipt_bank = ''
    form.value.receipt_card_number = ''
    form.value.account_type = undefined
    form.value.receipt_method = undefined
  }
}

// 处理供应商变化
const handleSupplierIdChange = async (val: any) => {
  if (!val) {
    // 清空供应商相关信息
    form.value.supplier_id = undefined
    form.value.supplier_name = ''
  } else {
    // 设置新的供应商ID
    form.value.supplier_id = val
    // 从供应商列表中查找供应商名称
    const selectedSupplier = supperList.value.find((item) => item.value === val)
    if (selectedSupplier) {
      form.value.supplier_name = selectedSupplier.label
    }
  }

  // 清空缓存
  clearSupplierFinanceCache()

  // 清空供应商子公司信息（因为供应商变化了）
  form.value.company_supplier_id = null
  form.value.company_supplier_name = ''

  // 清空银行信息
  form.value.receipt_account_name = ''
  form.value.receipt_bank = ''
  form.value.receipt_card_number = ''
  form.value.account_type = undefined
  form.value.receipt_method = undefined

  // 检查应付明细列表中是否有多个供应商子公司，如果有则清空应付明细列表
  const hasMultipleSubsidiaries =
    selectProductList.value.length > 0 &&
    form.value.payment_type === 20 &&
    selectProductList.value.some((item, index) => {
      if (index === 0) return false
      return item.company_supplier_id !== selectProductList.value[0].company_supplier_id
    })

  if (hasMultipleSubsidiaries) {
    // 如果有多个供应商子公司，清空应付明细列表
    selectProductList.value = []
    form.value.the_actual_amount = 0
    form.value.deduct_amount = 0
    form.value.other_fees = 0
    form.value.optimize_amount = 0
    form.value.bill_month = undefined
    calculateFooterData()
  } else {
    // 如果没有多个供应商子公司，正常清空应付单列表和相关金额
    selectProductList.value = []
    form.value.the_actual_amount = 0
    form.value.deduct_amount = 0
    form.value.other_fees = 0
    form.value.optimize_amount = 0
    form.value.bill_month = undefined
    calculateFooterData()
  }

  // 检查应付明细列表中是否有多个供应商子公司，如果有则清空银行信息
  if (hasMultipleSupplierSubsidiaries.value) {
    form.value.receipt_account_name = ''
    form.value.receipt_bank = ''
    form.value.receipt_card_number = ''
    form.value.account_type = undefined
    form.value.receipt_method = undefined
  }
}

const resetFormData = () => {
  form.value.receipt_account_name = ''
  form.value.receipt_bank = ''
  form.value.receipt_card_number = ''
  form.value.account_type = undefined
  form.value.receipt_method = undefined
  form.value.company_supplier_name = ''
  form.value.company_supplier_id = null
  form.value.supplier_id = undefined
  form.value.supplier_name = ''
  form.value.supplier_type = undefined
  form.value.other_fees = 0
  form.value.optimize_amount = 0
  form.value.pre_payment_order_ids = []
  form.value.bill_month = undefined
  selectBillList.value = []
  selectProductList.value = []
  form.value.the_actual_amount = 0
  form.value.deduct_amount = 0
}

// 结算方式
const settlement_method = SETTLEMENT_METHOD_CONFIG

// 收款方式
const paymentTerm = PAYMENT_TERM_CONFIG

// 账户类型
const accountType = ACCOUNT_TYPE_CONFIG
const temp_audit_status = ref()

// eslint-disable-next-line @typescript-eslint/no-unused-expressions
const handleOpen = async (type: DetailTypeEnum, pid?: string, audit_status?: number) => {
  if (type === DetailTypeEnum.AUDIT) {
    temp_audit_status.value = audit_status
  }

  // 清空附件缓存，确保新增模式下附件不会缓存
  attachment.value = []

  form.value = {
    id: pid ? Number(pid) : null,
    payment_order_number: '',
    payment_type: (() => {
      // 批量请款模式固定为应付款
      if (props.orderType === 'BATCH_PAYMENT') {
        return 20
      }
      // 其他情况按原来的逻辑
      return type === DetailTypeEnum.ADD ? 10 : undefined
    })(),
    settlement_method: (() => {
      // 批量请款模式默认月结
      if (props.orderType === 'BATCH_PAYMENT') {
        return 1
      }
      // 其他情况按原来的逻辑
      return type === DetailTypeEnum.ADD ? 4 : undefined
    })(), // 4 对应预付款
    expected_payment_date: dayjs(),
    payment_company: '',
    common_private_account: undefined,
    payment_account_id: null,
    receipt_account_name: '',
    account_type: undefined,
    receipt_bank: '',
    receipt_method: undefined,
    receipt_card_number: '',
    the_actual_amount: 0,
    remark: '',
    deduction_remark: '',
    company_supplier_name: '',
    company_supplier_id: null,
    supplier_id: undefined,
    supplier_name: '',
    supplier_type: undefined,
    payment_account_name: '',
    payment_account: '',
    deduct_amount: 0,
    payment_operator_id: undefined,
    payment_operator_name: '',
    treasury_payment_serial_number: '',
    payment_order_detail: [],
    files: [],
    cashier_attachments: [],
    Invoice_attachments: [],
    invoice_files: [],
    audit_status: null,
    actual_payment_amount: undefined,
    payment_time: undefined,
    other_fees: 0,
    optimize_amount: 0,
    pre_payment_order_ids: [],
    bill_month: undefined,
  }

  formRef.value?.resetFields()
  getWarehouseOption()
  viewType.value = type
  drawerVisible.value = true
  selectProductList.value = []
  selectBillList.value = []
  activeTab.value = 'bill' // 重置为默认tab

  if (pid) {
    const res = (await GetPaymentOrderDetail({ id: pid })) as ApiResponse
    const { payment_order_detail, paymentJstBillDetail, payment_bill_details, files, cashier_attachments, ...rest } = res.data

    const newFormState: FormState = {
      id: pid ? Number(pid) : null,
      payment_order_number: rest.payment_order_number || '',
      payment_type: props.orderType === 'PREPAY' ? 10 : rest.payment_type || undefined,
      settlement_method: (() => {
        // 预付款类型：默认设置为预付（4）
        if (rest.payment_type === 10) {
          return 4
        }
        // 应付款类型：根据接口返回的值设置，如果没有则设置为月结（1）
        if (rest.payment_type === 20) {
          if (rest.settlement_method) {
            // 如果是字符串，根据字符串找到对应的数字键值
            if (typeof rest.settlement_method === 'string') {
              const config = SETTLEMENT_METHOD_CONFIG.find((item) => item.value === rest.settlement_method)
              return config ? config.key : 1 // 默认月结
            }
            // 如果是数字，直接使用
            return Number(rest.settlement_method)
          }
          return 1 // 应付款默认月结
        }
        // 其他情况：根据接口返回的值设置
        if (rest.settlement_method) {
          if (typeof rest.settlement_method === 'string') {
            const config = SETTLEMENT_METHOD_CONFIG.find((item) => item.value === rest.settlement_method)
            return config ? config.key : undefined
          }
          return Number(rest.settlement_method)
        }
        return undefined
      })(),
      expected_payment_date: rest.expected_payment_date ? dayjs(rest.expected_payment_date) : undefined,
      payment_company: rest.payment_company || '',
      common_private_account: Number(rest.common_private_account) || undefined,
      payment_account_id: rest.payment_account_id || null,
      receipt_account_name: rest.receipt_account_name || '',
      account_type: rest.account_type || undefined,
      receipt_bank: rest.receipt_bank || '',
      receipt_method: rest.receipt_method || undefined,
      receipt_card_number: rest.receipt_card_number || '',
      the_actual_amount: rest.the_actual_amount || 0,
      remark: rest.remark || '',
      deduction_remark: rest.deduction_remark || '',
      company_supplier_name: rest.company_supplier_name || '',
      company_supplier_id: rest.company_supplier_id || null,
      supplier_id: typeof rest.supplier_id === 'number' ? rest.supplier_id : undefined,
      supplier_name: rest.supplier_name || '',
      supplier_type: undefined, // 将在下面通过接口获取
      payment_account_name: (() => {
        // 如果后端返回的数据没有拼接账号，且同时有账户名称和账号，则进行拼接
        if (rest.payment_account_name && rest.payment_account && !rest.payment_account_name.includes('(')) {
          return `${rest.payment_account_name}(${rest.payment_account})`
        }
        return rest.payment_account_name || ''
      })(),
      payment_account: rest.payment_account || '',
      deduct_amount: rest.deduct_amount || 0,
      payment_operator_id: rest.payment_operator_id || undefined,
      payment_operator_name: rest.payment_operator_name || '',
      treasury_payment_serial_number: rest.treasury_payment_serial_number || '',
      payment_order_detail: payment_order_detail || [],
      files: files || [],
      cashier_attachments: transformCashierAttachments(cashier_attachments) || [],
      Invoice_attachments: rest.invoice_attachment_array || [],
      invoice_files: rest.invoice_files || [],
      audit_status: rest.audit_status || null,
      actual_payment_amount: rest.actual_payment_amount || undefined,
      payment_time: rest.payment_time ? dayjs(rest.payment_time) : undefined,
      other_fees: Number(rest.other_fees || 0),
      optimize_amount: Number(rest.optimize_amount || 0),
      pre_payment_order_ids: rest.pre_payment_order_ids || [],
      bill_month: rest.bill_month ? dayjs(rest.bill_month) : undefined,
      attachments: files || [],
    }

    form.value = newFormState

    // 根据付款类型设置不同的明细数据
    if (rest.payment_type === 20) {
      // 应付类型：优先使用 payment_bill_details，如果没有则使用 paymentJstBillDetail
      if (payment_bill_details && payment_bill_details.length > 0) {
        // 如果有账单明细数据，设置到 selectBillList
        selectBillList.value = payment_bill_details.map((item: any) => {
          const actual_payable_amount = Number(item.actual_payable_amount || 0)
          const paid_amount = Number(item.paid_amount || 0)
          const remaining_amount = Math.max(0, actual_payable_amount - paid_amount)

          return {
            id: item.id,
            number: item.number,
            bill_month: item.bill_month,
            supplier_name: item.supplier_name,
            company_supplier_name: item.company_supplier_name,
            actual_payable_amount,
            paid_amount,
            remaining_amount,
            current_payment: item.current_payment || actual_payable_amount,
            supplier_id: item.supplier_id,
            company_supplier_id: item.company_supplier_id,
            // 添加缺失的字段，确保数值类型正确
            inv_amount: actual_payable_amount, // 应付总额
            optimize_amount: Number(item.optimize_amount || 0), // 优化金额
            deduct_amount: Number(item.deduct_amount || 0), // 扣款金额
            other_fee: Number(item.other_fee || item.other_fees || 0), // 其他费用，兼容不同字段名
            payable_amount: paid_amount, // 已付金额
          }
        })

        // 设置默认tab为账单明细
        activeTab.value = 'bill'

        // 从账单明细中提取供应商信息
        if (selectBillList.value.length > 0) {
          const firstBill = selectBillList.value[0]
          // 如果账单明细中有供应商信息，设置到表单中
          if (firstBill.supplier_id && !form.value.supplier_id) {
            form.value.supplier_id = firstBill.supplier_id
            form.value.supplier_name = firstBill.supplier_name || ''
          }
          // 如果账单明细中有供应商子公司信息，设置到表单中
          if (firstBill.company_supplier_id && !form.value.company_supplier_id) {
            form.value.company_supplier_id = firstBill.company_supplier_id
            form.value.company_supplier_name = firstBill.company_supplier_name || ''
          }
        }

        // 重新计算金额和表格合计
        setActualAmount()
        calculateFooterData()
      } else if (paymentJstBillDetail && paymentJstBillDetail.length > 0) {
        // 如果没有账单明细但有应付单明细，使用 paymentJstBillDetail
        let payableData = paymentJstBillDetail

        // 检查是否有重复的pinvid并进行去重处理
        const pinvidCounts = {}
        payableData.forEach((item) => {
          pinvidCounts[item.pinvid] = (pinvidCounts[item.pinvid] || 0) + 1
        })

        const duplicatePinvids = Object.keys(pinvidCounts).filter((pinvid) => pinvidCounts[pinvid] > 1)
        if (duplicatePinvids.length > 0) {
          console.warn('发现重复的pinvid:', duplicatePinvids)
          // 去重处理：保留第一个出现的记录
          const uniqueData: any[] = []
          const seenPinvids = new Set()
          payableData.forEach((item) => {
            if (!seenPinvids.has(item.pinvid)) {
              seenPinvids.add(item.pinvid)
              uniqueData.push(item)
            }
          })
          payableData = uniqueData
        }

        selectProductList.value = payableData.map((item: any) => {
          // 确保只保留必要的字段，避免传递额外数据
          return {
            id: item.id,
            pinvid: item.pinvid,
            inv_amount: item.inv_amount,
            payable_amount: item.payable_amount,
            remaining_amount: item.remaining_amount,
            current_payment: item.current_payment,
            supplier_id: item.supplier_id,
            supplier_name: item.supplier_name,
            company_supplier_id: item.company_supplier_id,
            company_supplier_name: item.company_supplier_name,
          }
        })

        // 设置默认tab为应付单明细
        activeTab.value = 'payable'
      }
    } else {
      // 预付款类型：使用 payment_order_detail
      selectProductList.value = payment_order_detail || []
    }

    // 如果有供应商ID，获取供应商信息
    if (form.value.company_supplier_id) {
      await getSupplierInfo(form.value.company_supplier_id)

      // 只在新增模式下获取供应商财务信息，避免覆盖已有数据
      if (viewType.value === DetailTypeEnum.ADD) {
        try {
          const financeInfo = await getSupplierFinanceInfo(form.value.company_supplier_id, form.value.receipt_method, form.value.account_type)
          if (financeInfo) {
            // 重新设置供应商财务信息（不修改供应商名称）
            form.value.receipt_account_name = financeInfo.receipt_account_name || ''
            form.value.receipt_bank = financeInfo.receipt_bank || ''
            form.value.receipt_card_number = financeInfo.receipt_card_number || ''
            form.value.account_type = financeInfo.account_type
            form.value.receipt_method = financeInfo.receipt_method

            // 确保不覆盖之前设置的供应商名称
            // 如果接口返回了供应商名称，但我们已经有从数据获取的名称，则不覆盖
            if (!form.value.company_supplier_name && financeInfo.supplier_name) {
              form.value.company_supplier_name = financeInfo.supplier_name
            }
          }
        } catch (error) {
          console.error('获取供应商财务信息失败:', error)
          // 静默处理错误，不显示提示
        }
      }
    }

    // 检查应付明细列表中是否有多个供应商子公司，如果有则清空银行信息
    // 注意：在查看、编辑和审核模式下，如果接口已经返回了银行信息，不应该清空
    if (hasMultipleSupplierSubsidiaries.value && ![DetailTypeEnum.VIEW, DetailTypeEnum.EDIT, DetailTypeEnum.AUDIT].includes(viewType.value)) {
      form.value.receipt_account_name = ''
      form.value.receipt_bank = ''
      form.value.receipt_card_number = ''
      form.value.account_type = undefined
      form.value.receipt_method = undefined
    }
  }

  // 保存初始表单值
  initialFormValues.value = {
    ...form.value,
    payment_order_detail: JSON.parse(JSON.stringify(selectProductList.value)),
    files: JSON.parse(JSON.stringify(attachment.value || [])),
    other_fees: form.value.other_fees || 0,
    optimize_amount: form.value.optimize_amount || 0,
    bill_month: form.value.bill_month,
  }

  if ([DetailTypeEnum.VIEW, DetailTypeEnum.AUDIT].includes(viewType.value) && btnPermission.value[131006]) {
    showAuditRecord.value = localStorage.getItem('showAuditRecord') === '1'
    if (showAuditRecord.value) {
      getAuditRecord()
    }
  } else {
    showAuditRecord.value = false
  }

  // 新增模式下，如果有传入采购单ids，则自动加载采购单信息
  if (type === DetailTypeEnum.ADD && props.purchaseOrderIds && props.purchaseOrderIds.length > 0) {
    try {
      const res = await GetPurchaseOrderInfo({ ids: props.purchaseOrderIds })
      if (res.success && res.data && res.data.length > 0) {
        const newList = res.data.map((item: any) => {
          const prepaymentRatio = Number(item.prepayment_ratio) / 100 // 除以100，因为prepayment_ratio是百分比形式
          const totalAmount = Number(item.total_purchase_amount)
          const executedAmount = Number(item.executed_amount)
          const prepaymentAmount = Math.max(0, Number((totalAmount * prepaymentRatio - executedAmount).roundNext(2)))
          return {
            ...item,
            current_payment: prepaymentAmount,
          }
        })

        selectProductList.value = newList
        setActualAmount()
        calculateFooterData()

        // 设置供应商信息
        if (newList.length > 0 && newList[0].company_supplier_id) {
          // 先清空所有供应商相关字段
          form.value.company_supplier_id = null
          form.value.company_supplier_name = ''
          form.value.receipt_account_name = ''
          form.value.receipt_bank = ''
          form.value.receipt_card_number = ''
          form.value.account_type = undefined
          form.value.receipt_method = undefined
          form.value.other_fees = 0
          form.value.optimize_amount = 0

          // 等待下一个 tick，确保清空操作完成
          await nextTick()

          // 重新设置供应商ID和名称
          form.value.company_supplier_id = newList[0].company_supplier_id

          // 从采购单数据中获取供应商名称
          if (newList[0].company_supplier_name) {
            form.value.company_supplier_name = newList[0].company_supplier_name
          } else if (newList[0].supplier_name) {
            form.value.company_supplier_name = newList[0].supplier_name
          } else if (props.supplierInfo && props.supplierInfo.supplier_name) {
            form.value.company_supplier_name = props.supplierInfo.supplier_name
          }

          await nextTick()

          // 从供应商列表中查找供应商名称
          const selectedSupplier = supperList.value.find((item) => item.value === newList[0].company_supplier_id)
          if (selectedSupplier) {
            form.value.company_supplier_name = selectedSupplier.label
          }

          // 获取供应商信息（包括supplier_type）
          await getSupplierInfo(newList[0].company_supplier_id)

          // 获取供应商财务信息
          try {
            const financeInfo = await getSupplierFinanceInfo(newList[0].company_supplier_id, form.value.receipt_method, form.value.account_type)
            if (financeInfo) {
              // 重新设置供应商财务信息（不修改供应商名称）
              form.value.receipt_account_name = financeInfo.receipt_account_name || ''
              form.value.receipt_bank = financeInfo.receipt_bank || ''
              form.value.receipt_card_number = financeInfo.receipt_card_number || ''
              form.value.account_type = financeInfo.account_type
              form.value.receipt_method = financeInfo.receipt_method

              // 确保不覆盖之前设置的供应商名称
              // 如果接口返回了供应商名称，但我们已经有从数据获取的名称，则不覆盖
              if (!form.value.company_supplier_name && financeInfo.supplier_name) {
                form.value.company_supplier_name = financeInfo.supplier_name
              }
            }
          } catch (error) {
            console.error('获取供应商财务信息失败:', error)
            // 静默处理错误，不显示提示
          }
        }

        // 更新初始表单值
        initialFormValues.value = {
          ...form.value,
          payment_order_detail: JSON.parse(JSON.stringify(selectProductList.value)),
          files: JSON.parse(JSON.stringify(attachment.value || [])),
          other_fees: form.value.other_fees || 0,
          optimize_amount: form.value.optimize_amount || 0,
          bill_month: form.value.bill_month,
        }
      }
    } catch (error) {
      console.error('获取采购单信息失败:', error)
      // message.error('获取采购单信息失败')
    }
  }
}

// 修改 hasFormChanges 函数
const hasFormChanges = () => {
  // 如果不是新增或编辑模式，直接返回 false
  if (![DetailTypeEnum.ADD, DetailTypeEnum.EDIT].includes(viewType.value)) {
    return false
  }

  // 如果没有初始值，返回 false
  if (!initialFormValues.value) {
    return false
  }

  // 检查附件是否有变化
  const currentAttachment = attachment.value || []
  const initialAttachment = initialFormValues.value.files || []
  if (currentAttachment.length !== initialAttachment.length) {
    return true
  }

  // 检查采购单列表是否有变化
  const currentProducts = selectProductList.value
  const initialProducts = initialFormValues.value.payment_order_detail
  if (currentProducts.length !== initialProducts.length) {
    return true
  }

  // 检查采购单的预付金额是否有变化
  const hasProductChanges = currentProducts.some((current, index) => {
    const initial = initialProducts[index]
    return current.current_payment !== initial.current_payment
  })
  if (hasProductChanges) {
    return true
  }

  // 检查基本表单字段是否有变化
  const formFields = [
    'payment_type',
    'company_supplier_id',
    'settlement_method',
    'receipt_method',
    'account_type',
    'receipt_bank',
    'receipt_account_name',
    'receipt_card_number',
    'remark',
    'deduct_amount',
    'the_actual_amount',
    'payment_operator_name',
    'other_fees',
    'optimize_amount',
    'pre_payment_order_ids',
    'bill_month',
  ]

  return formFields.some((field) => {
    const currentValue = form.value[field]
    const initialValue = initialFormValues.value[field]

    // 处理数字类型
    if (typeof currentValue === 'number' && typeof initialValue === 'number') {
      return currentValue !== initialValue
    }

    // 处理其他类型
    return currentValue !== initialValue
  })
}

// 修改 handleClose 函数
const handleClose = (fromSubmitOrEvent: boolean | Event = false) => {
  // 判断是否是从事件处理器调用的
  const fromSubmit = typeof fromSubmitOrEvent === 'boolean' ? fromSubmitOrEvent : false

  // 如果是从提交按钮调用的，直接关闭
  if (fromSubmit) {
    showAuditRecord.value = false
    localStorage.setItem('showAuditRecord', '0')
    resetFormData()
    selectProductList.value = []
    selectBillList.value = []
    attachment.value = []
    drawerVisible.value = false
    return
  }

  // 检查是否有表单更改
  if (hasFormChanges()) {
    Modal.confirm({
      title: '确认关闭',
      content: '您有未保存的更改，确定要关闭吗？关闭后所有更改将丢失。',
      okText: '确定',
      cancelText: '取消',
      onOk() {
        // 清空表单数据
        form.value = {
          id: null,
          payment_order_number: '',
          payment_type: undefined,
          settlement_method: undefined,
          expected_payment_date: dayjs(),
          payment_company: '',
          common_private_account: undefined,
          payment_account_id: null,
          receipt_account_name: '',
          account_type: undefined,
          receipt_bank: '',
          receipt_method: undefined,
          receipt_card_number: '',
          the_actual_amount: 0,
          remark: '',
          deduction_remark: '',
          company_supplier_name: '',
          company_supplier_id: null,
          supplier_id: undefined,
          supplier_name: '',
          supplier_type: undefined,
          payment_account_name: '',
          payment_account: '',
          deduct_amount: 0,
          payment_operator_id: undefined,
          payment_operator_name: '',
          treasury_payment_serial_number: '',
          payment_order_detail: [],
          files: [],
          cashier_attachments: [],
          Invoice_attachments: [],
          invoice_files: [],
          audit_status: null,
          actual_payment_amount: undefined,
          payment_time: undefined,
          other_fees: 0,
          optimize_amount: 0,
          pre_payment_order_ids: [],
          bill_month: undefined,
        }

        // 清空附件
        attachment.value = []

        // 清空选中的采购单列表
        selectProductList.value = []
        selectBillList.value = []

        // 重置表单验证状态
        formRef.value?.resetFields()

        // 关闭审核记录显示
        showAuditRecord.value = false
        localStorage.setItem('showAuditRecord', '0')

        // 关闭抽屉
        drawerVisible.value = false
      },
    })
  } else {
    // 如果没有更改，直接关闭
    showAuditRecord.value = false
    localStorage.setItem('showAuditRecord', '0')
    resetFormData()
    selectProductList.value = []
    drawerVisible.value = false
  }
}

// 修改 handleSelectProduct 函数
const handleSelectProduct = async (selectedProducts: any[]) => {
  const res = await handleUpdateProduct(selectedProducts)
  if (res && res.success) {
    purchaseOrderListRef.value?.close()
  }
}

// 处理应付单选择
const handleSelectPayableProduct = async (selectedProducts: any[]) => {
  const res = await handleUpdatePayableProduct(selectedProducts)
  if (res && res.success) {
    payableOrderListRef.value?.close()
  }
}

// 处理账单选择
const handleSelectBillProduct = async (selectedBills: any[], supplierId: string, supplierName: string) => {
  // 处理账单数据，确保数据类型正确
  const processedBills = selectedBills.map((item: any) => {
    const actual_payable_amount = Number(item.actual_payable_amount || 0)
    const paid_amount = Number(item.paid_amount || 0)
    const remaining_amount = Math.max(0, actual_payable_amount - paid_amount)

    return {
      ...item,
      actual_payable_amount,
      paid_amount,
      remaining_amount,
      current_payment: actual_payable_amount, // 本次付款初始化为实际应付金额（可修改）
      // 确保其他费用字段正确初始化
      other_fee: Number(item.other_fee || item.other_fees || 0), // 其他费用，兼容不同字段名
      optimize_amount: Number(item.optimize_amount || 0), // 优化金额
      deduct_amount: Number(item.deduct_amount || 0), // 扣款金额
    }
  })

  selectBillList.value = processedBills

  // 更新供应商信息
  if (supplierId && supplierName) {
    form.value.company_supplier_id = supplierId
    form.value.company_supplier_name = supplierName

    // 如果账单数据中有供应商信息，也设置到表单中
    if (processedBills.length > 0) {
      const firstBill = processedBills[0]
      if (firstBill.supplier_id && !form.value.supplier_id) {
        form.value.supplier_id = firstBill.supplier_id
        form.value.supplier_name = firstBill.supplier_name || ''
      }
    }
  }
  selectBillDrawerRef.value?.close()
}

// 处理账单更新
const handleUpdateBillProduct = async (updatedBills: any[]) => {
  // 如果没有选择任何账单，清空列表并重置相关数据
  if (!updatedBills || updatedBills.length === 0) {
    selectBillList.value = []
    form.value.the_actual_amount = 0
    form.value.other_fees = 0
    form.value.optimize_amount = 0
    form.value.bill_month = undefined
    // 清空供应商相关信息
    form.value.company_supplier_id = null
    form.value.company_supplier_name = ''
    form.value.receipt_account_name = ''
    form.value.receipt_bank = ''
    form.value.receipt_card_number = ''
    form.value.account_type = undefined
    form.value.receipt_method = undefined
    form.value.supplier_id = undefined
    form.value.supplier_name = ''
    calculateFooterData()
    return { success: true } // 返回成功响应
  }

  try {
    // 检查是否有重复的账单号
    const numberCounts: { [key: string]: number } = {}
    updatedBills.forEach((item) => {
      numberCounts[item.number] = (numberCounts[item.number] || 0) + 1
    })

    const duplicateNumbers = Object.keys(numberCounts).filter((number) => numberCounts[number] > 1)
    let billsToProcess = updatedBills
    if (duplicateNumbers.length > 0) {
      console.warn('发现重复的账单号:', duplicateNumbers)
      // 去重处理，保留第一个出现的
      const uniqueBills: any[] = []
      const seenNumbers = new Set()
      updatedBills.forEach((item) => {
        if (!seenNumbers.has(item.number)) {
          seenNumbers.add(item.number)
          uniqueBills.push(item)
        }
      })
      // 使用去重后的数据
      billsToProcess = uniqueBills
    }

    const newList = billsToProcess.map((item: any) => {
      const actual_payable_amount = Number(item.actual_payable_amount || 0)
      const paid_amount = Number(item.paid_amount || 0)
      const remaining_amount = Math.max(0, actual_payable_amount - paid_amount)

      // 确保只保留有效的数字ID，过滤掉表格组件自动生成的临时ID
      const validId = item.id && typeof item.id === 'number' && item.id > 0 ? item.id : undefined

      // 只保留必要的字段，避免传递额外数据
      return {
        id: validId, // 只保留有效的数字ID
        bill_id: item.id, // 使用接口返回的id作为bill_id
        number: item.number,
        bill_month: item.bill_month,
        supplier_name: item.supplier_name,
        company_supplier_name: item.company_supplier_name,
        actual_payable_amount, // 确保是数字类型
        paid_amount, // 确保是数字类型
        remaining_amount, // 计算剩余金额，确保是数字类型
        current_payment: actual_payable_amount, // 本次付款初始化为实际应付金额（可修改）
        supplier_id: item.supplier_id,
        company_supplier_id: item.company_supplier_id,
        // 添加缺失的字段，确保数值类型正确
        inv_amount: actual_payable_amount, // 应付总额
        optimize_amount: Number(item.optimize_amount || 0), // 优化金额
        deduct_amount: Number(item.deduct_amount || 0), // 扣款金额
        other_fee: Number(item.other_fee || item.other_fees || 0), // 其他费用，兼容不同字段名
        payable_amount: paid_amount, // 已付金额
      }
    })

    selectBillList.value = newList
    setActualAmount()
    calculateFooterData()

    // 检查是否所有账单都是同一个供应商子公司
    const firstSupplierSubsidiaryId = newList[0]?.company_supplier_id
    const allSameSupplierSubsidiary = newList.every((item) => item.company_supplier_id === firstSupplierSubsidiaryId)

    // 检查是否所有账单都是同一个供应商
    const firstSupplierId = newList[0]?.supplier_id
    const allSameSupplier = newList.every((item) => item.supplier_id === firstSupplierId)

    // 如果当前已经有供应商子公司，检查新选择的账单是否与当前供应商子公司一致
    const currentSupplierSubsidiaryId = form.value.company_supplier_id
    if (currentSupplierSubsidiaryId && newList.length > 0) {
      const hasDifferentSupplierSubsidiary = newList.some((item) => item.company_supplier_id !== currentSupplierSubsidiaryId)
      if (hasDifferentSupplierSubsidiary) {
        // 如果新选择的账单与当前供应商子公司不一致，清空供应商子公司信息
        form.value.company_supplier_id = null
        form.value.company_supplier_name = ''
        form.value.supplier_id = undefined
        form.value.supplier_name = ''
        form.value.receipt_account_name = ''
        form.value.receipt_bank = ''
        form.value.receipt_card_number = ''
        form.value.account_type = undefined
        form.value.receipt_method = undefined
      }
    }

    // 如果所有账单都是同一个供应商子公司，自动填充供应商子公司信息
    if (allSameSupplierSubsidiary && firstSupplierSubsidiaryId) {
      form.value.company_supplier_id = firstSupplierSubsidiaryId
      form.value.company_supplier_name = newList[0].company_supplier_name || ''
    }

    // 如果所有账单都是同一个供应商，自动填充供应商信息
    if (allSameSupplier && firstSupplierId) {
      form.value.supplier_id = firstSupplierId
      form.value.supplier_name = newList[0].supplier_name || ''
    }

    // 如果只有一个供应商子公司，获取财务信息
    if (allSameSupplierSubsidiary && firstSupplierSubsidiaryId) {
      try {
        const financeInfo = await getSupplierFinanceInfo(firstSupplierSubsidiaryId, form.value.receipt_method, form.value.account_type)
        if (financeInfo) {
          form.value.receipt_bank = financeInfo.receipt_bank || ''
          form.value.receipt_card_number = financeInfo.receipt_card_number || ''
          form.value.receipt_account_name = financeInfo.receipt_account_name || ''
          form.value.account_type = financeInfo.account_type || undefined
        }
      } catch (error) {
        console.error('获取供应商财务信息失败:', error)
      }
    }

    return { success: true }
  } catch (error) {
    console.error('处理账单更新失败:', error)
    return { success: false, error }
  }
}

const handleOrderClick = (row: any) => {
  const config = currentPaymentTypeConfig.value
  const isAdd = viewType.value === DetailTypeEnum.ADD
  if (!config) return

  if (config.value === 10) {
    // 采购单
    if (!isAdd) {
      router.push(`/purchaseOrderLook/${row.purcharse_order_id}`)
    } else {
      return false
    }
  } else if (config.value === 20) {
    // 应付单 - 暂时不跳转，只关闭抽屉
    // router.push(`/payableOrderLook/${row.payable_order_id}`)
    return false
  }
  handleClose()
}

// 获取明细数据
const getDetailData = (row: any) => {
  const config = currentPaymentTypeConfig.value
  if (!config) return []
  const detailField = config.detailFields?.[0] || 'payable_order_details'
  return row[detailField] || []
}

const PurchaseRef = ref()
const purchaseConfirm = ref<any>(() => {})

// 提交审核
const handleSubmitAudit = async (is_pass: boolean) => {
  try {
    // 验证表单
    await formRef.value?.validate()

    // 验证是否添加了明细
    if ([DetailTypeEnum.EDIT, DetailTypeEnum.ADD].includes(viewType.value)) {
      if (form.value.payment_type === 20) {
        // 应付类型：检查应付明细或账单明细
        if (activeTab.value === 'payable' && !selectProductList.value.length) {
          message.error('请添加应付单')
          return
        }
        if (activeTab.value === 'bill' && !selectBillList.value.length) {
          message.error('请添加账单')
          return
        }
      } else {
        // 预付类型：检查采购单
        if (!selectProductList.value.length) {
          message.error('请添加采购单')
          return
        }
      }
    }

    // 根据当前tab获取数据源
    const currentList = form.value.payment_type === 20 && activeTab.value === 'bill' ? selectBillList.value : selectProductList.value

    // Debug log
    console.log(
      'Payment amounts:',
      currentList.map((item) => ({
        number: item.number,
        current_payment: item.current_payment,
        prepayment_amount_type: typeof item.current_payment,
      })),
    )

    // 验证预付金额
    const invalidPayment = currentList.find((item) => Number(item.current_payment) <= 0)
    if (invalidPayment) {
      message.error(`存在本次付款金额小于或等于0`)
      return
    }

    // 计算每个明细的扣款金额
    const totalPrepaymentAmount = currentList.reduce((sum, item) => sum + Number(item.current_payment || 0), 0)
    const totalDeductAmount = Number(form.value.deduct_amount || 0)

    // 计算每个明细的扣款金额
    const paymentOrderDetail = currentList.map((item, index) => {
      const prepaymentAmount = Number(item.current_payment || 0)

      let thisDeductionAmount = 0
      if (totalPrepaymentAmount > 0) {
        if (index === currentList.length - 1) {
          // 最后一笔明细的扣款金额 = 付款单扣款金额 - 前N笔明细扣款金额
          const previousDeductionAmount = currentList.slice(0, index).reduce((sum, prevItem) => {
            const prevPrepaymentAmount = Number(prevItem.current_payment || 0)
            return sum + Number(((prevPrepaymentAmount / totalPrepaymentAmount) * totalDeductAmount).roundNext(2))
          }, 0)
          thisDeductionAmount = Number((totalDeductAmount - previousDeductionAmount).roundNext(2))
        } else {
          // 其他明细的扣款金额 = 本次付款金额 / 合计 * 付款单的扣款金额
          thisDeductionAmount = Number(((prepaymentAmount / totalPrepaymentAmount) * totalDeductAmount).roundNext(2))
        }
      }

      // 计算本次实付金额
      const actualPaymentAmount = Number((prepaymentAmount - thisDeductionAmount).roundNext(2))

      // 根据付款类型构建不同的数据结构
      if (form.value.payment_type === 20) {
        if (activeTab.value === 'payable') {
          // 应付单类型：编辑时需要传递id，新增时不传递
          const baseFields = {
            pinvid: item.pinvid,
            bill_month: form.value.bill_month ? dayjs(form.value.bill_month).format('YYYY-MM') : undefined, // 添加账单月份
            inv_amount: Number((item.inv_amount || 0).roundNext(2)),
            current_payment: Number((item.current_payment || 0).roundNext(2)),
            remaining_amount: Number((item.remaining_amount || 0).roundNext(2)),
            // 新增缺少的字段
            bill_id: item.bill_id || item.id || 0,
            optimize_amount: Number((item.optimize_amount || 0).roundNext(2)), // 优化金额
            deduct_amount: Number((item.deduct_amount || 0).roundNext(2)), // 扣款金额
            other_fee: Number((item.other_fee || item.other_fees || 0).roundNext(2)), // 其他费用，兼容不同字段名
            payable_amount: Number((item.payable_amount || 0).roundNext(2)), // 已付金额
            actual_payable_amount: Number((item.inv_amount || 0).roundNext(2)), // 实际应付金额，使用应付总额
            paid_amount: Number((item.payable_amount || 0).roundNext(2)), // 已付金额
          }

          // 如果是编辑模式且有有效的数字id，则包含id字段
          if (form.value.id && item.id && typeof item.id === 'number' && item.id > 0) {
            return {
              ...baseFields,
              id: item.id,
            }
          }

          return baseFields
        }
        if (activeTab.value === 'bill') {
          // 账单类型
          const baseFields = {
            number: item.number,
            bill_month: item.bill_month,
            supplier_name: item.supplier_name,
            company_supplier_name: item.company_supplier_name,
            actual_payable_amount: Number((item.actual_payable_amount || 0).roundNext(2)),
            paid_amount: Number((item.paid_amount || 0).roundNext(2)),
            remaining_amount: Number((item.remaining_amount || 0).roundNext(2)),
            current_payment: Number((item.current_payment || 0).roundNext(2)),
            // 新增缺少的字段
            bill_id: item.bill_id || item.id || 0,
            inv_amount: Number((item.actual_payable_amount || 0).roundNext(2)), // 应付总额，使用实际应付金额
            optimize_amount: Number((item.optimize_amount || 0).roundNext(2)), // 优化金额
            deduct_amount: Number((item.deduct_amount || 0).roundNext(2)), // 扣款金额
            other_fee: Number((item.other_fee || item.other_fees || 0).roundNext(2)), // 其他费用，兼容不同字段名
            payable_amount: Number((item.paid_amount || 0).roundNext(2)), // 已付金额
            // 添加采购状态字段，确保查看时有回显
            purchase_order_status_string: item.purchase_order_status_string || '',
          }

          // 如果是编辑模式且有有效的数字id，则包含id字段
          if (form.value.id && item.id && typeof item.id === 'number' && item.id > 0) {
            return {
              ...baseFields,
              id: item.id,
            }
          }

          return baseFields
        }
      }
      // 预付款类型：包含所有字段
      return {
        ...item,
        current_payment: Number((item.current_payment || 0).roundNext(2)),
        this_deduction_amount: Number(thisDeductionAmount.roundNext(2)),
        actual_payment_amount: Number(actualPaymentAmount.roundNext(2)),
        // 确保包含必要的字段
        inv_amount: Number((item.total_purchase_amount || 0).roundNext(2)), // 应付总额，使用采购单总金额
        actual_payable_amount: Number((item.total_purchase_amount || 0).roundNext(2)), // 实际应付金额
        paid_amount: Number((item.executed_amount || 0).roundNext(2)), // 已付金额
        remaining_amount: Number((item.total_purchase_amount - item.executed_amount || 0).roundNext(2)), // 剩余金额
        bill_id: item.bill_id || item.id || 0,
        optimize_amount: Number((item.optimize_amount || 0).roundNext(2)), // 优化金额
        deduct_amount: Number((item.deduct_amount || 0).roundNext(2)), // 扣款金额
        other_fee: Number((item.other_fee || 0).roundNext(2)), // 其他费用
      }
    })

    // 验证数据完整性，确保没有多余的数据
    if (paymentOrderDetail.length !== currentList.length) {
      console.warn('数据长度不匹配！paymentOrderDetail:', paymentOrderDetail.length, 'currentList:', currentList.length)
    }

    // 解构 form.value，去掉 company_supplier_id 和 company_supplier_name
    const { company_supplier_id, company_supplier_name, attachments, Invoice_attachments, ...restForm } = form.value
    const baseParams: any = {
      ...restForm,
      is_pass,
      current_total_amount: Number(totalPaymentAmount.value.roundNext(2)),
      other_fees: Number((form.value.other_fees || 0).roundNext(2)),
      optimize_amount: Number((form.value.optimize_amount || 0).roundNext(2)),
      bill_month: form.value.bill_month ? dayjs(form.value.bill_month).format('YYYY-MM') : undefined,
      attachments: (attachments || [])?.map((i) => i.id),
      Invoice_attachments: (Invoice_attachments || [])?.map((i) => i.id),
    }

    // 删除不需要传递给后端的字段
    delete baseParams.supplier_type
    delete baseParams.files
    delete baseParams.invoice_files
    delete baseParams.audit_status

    if (!hasMultipleSupplierSubsidiaries.value) {
      baseParams.company_supplier_id = company_supplier_id
      baseParams.company_supplier_name = company_supplier_name
    }

    // 如果有多个子公司，删除这两个字段
    if (hasMultipleSupplierSubsidiaries.value) {
      baseParams.company_supplier_id = 0
      baseParams.company_supplier_name = ''
    }

    // 根据当前tab和付款类型决定使用哪个字段名
    let params
    if (form.value.payment_type === 20) {
      // 应付类型
      // 新增模式下根据供应商子公司数量设置参数
      if (!form.value.id) {
        // 新增模式
        if (hasMultipleSupplierSubsidiaries.value) {
          // 存在多个供应商子公司：传 company_supplier_name: null 和 company_supplier_id: 0
          baseParams.company_supplier_name = null
          baseParams.company_supplier_id = 0
        }
        // 如果只有单个供应商子公司，就正常传（不需要额外处理）
      }

      if (activeTab.value === 'bill') {
        // 账单明细
        params = { ...baseParams, payment_bill_details: paymentOrderDetail }
      } else {
        // 应付单明细
        params = { ...baseParams, paymentJstBillDetail: paymentOrderDetail }
      }
    } else {
      // 预付类型
      // 根据新增/编辑模式设置供应商参数
      if (form.value.id) {
        // 编辑模式：传详情接口返回的（原封不动，照传回去）
        // 不需要额外处理，直接使用 baseParams 中的值
      } else {
        // 新增模式：传 supplier_id: 0 和 supplier_name: null
        baseParams.supplier_id = 0
        baseParams.supplier_name = null
      }
      params = { ...baseParams, payment_order_detail: paymentOrderDetail }
    }

    const fn = form.value.id ? UpdatePaymentOrder : AddPaymentOrder
    const res = await fn(params)
    if (res.success) {
      handleClose(true) // 传入 true 表示是从提交按钮调用的
      emits('query')
    } else {
      message.error(res.message || '操作失败')
    }
  } catch (_e) {
    // 处理错误
  }
}

// 审核
const handleAudit = async (is_pass: boolean) => {
  if (is_pass) {
    // 如果是财务一级或二级审核，先校验表单再调用UpdatePaymentFinanceOrder
    if (form.value.audit_status === 40 || form.value.audit_status === 41) {
      try {
        // 先校验付款账户类型和打款操作人
        await receiptFormRef.value?.validateFields(['common_private_account', 'payment_operator_id', 'payment_operator_id'])
        const financeParams = {
          id: form.value.id,
          common_private_account: form.value.common_private_account,
          payment_account_name: form.value.payment_account_name,
          payment_account_id: form.value.payment_account_id,
          payment_account: form.value.payment_account,
          payment_operator_id: form.value.payment_operator_id,
          payment_operator_name: form.value.payment_operator_name,
        }
        const financeRes = await UpdatePaymentFinanceOrder(financeParams)
        if (!financeRes.success) {
          message.error(financeRes.message || '更新财务信息失败')
          return
        }
      } catch (_error) {
        // 表单校验失败
        // message.error('请完善付款账户类型和打款操作人')
        return
      }
    }
    auditModalRef.value?.open(is_pass)
  } else {
    rejectAuditRef.value?.open(temp_audit_status.value, form.value.the_actual_amount)
  }
}

const handleRejectAudit = async (auditForm) => {
  try {
    await AuditRejected({ ...auditForm, ids: [form.value.id] })
    message.success('审核拒绝成功')
    handleClose(true) // 传入 true 表示是从提交按钮调用的
    emits('query')
  } catch (_error) {
    message.error('审核拒绝失败')
  }
}
// 审核完成
const handleFinishAudit = async (auditForm) => {
  try {
    auditModalRef.value.setLoading(true)

    // 调用审核通过接口
    const auditRes = await BatchAudit({ ...auditForm, ids: [form.value.id] })
    if (auditRes.success) {
      message.success('审核成功')
      auditModalRef.value?.close()
      handleClose(true) // 传入 true 表示是从提交按钮调用的
      emits('query')
    } else {
      message.error(auditRes.message || '审核失败')
    }
  } catch (error) {
    message.error('审核失败')
    console.error(error)
  } finally {
    auditModalRef.value.setLoading(false)
  }
}
// 获取用户信息
// const getUserInfo = async () => {
//   const res = await GetUserInfo()
//   const applyDeptListRes = await GetDeptByUser(res.data.id)
//   // const applyerListRes = await GetUsersAndChilrd(res.data.id)
//   if (viewType.value === DetailTypeEnum.ADD) {
//     form.value.department_id = res.data.department_id
//     form.data.applicant_id = res.data.id
//   }
//   applyDeptList.value = applyDeptListRes.data
//   // applyerList.value = applyerListRes.data
// }

// 显示审核记录
const handleShowAuditRecord = () => {
  showAuditRecord.value = !showAuditRecord.value
  localStorage.setItem('showAuditRecord', showAuditRecord.value ? '1' : '0')
  if (showAuditRecord.value) {
    getAuditRecord()
  }
}
// 获取审核记录
const getAuditRecord = async () => {
  const res = await GetAuditRecord({ id: form.value.id })
  auditRecordList.value = res.data
}
// 删除商品
// const handleDeleteProduct = (row) => {
//   if (row.id) {
//     const item = selectProductList.value.find((item) => item.id == row.id)
//     item.is_delete = true
//   } else {
//     selectProductList.value = selectProductList.value.filter((item) => item.k3_sku_id !== row.k3_sku_id)
//   }
// }

const getWarehouseOption = async () => {
  const res = await GetWarehouses()
  warehouseOption.value = res.data.map((i) => ({ label: i.value, value: i.key }))
}

// 添加 watch 监听付款类型的变化
watch(
  () => form.value.payment_type,
  (newVal) => {
    if (newVal === 10) {
      // 预付款
      form.value.settlement_method = 4 // 4 对应预付款
      // 清除结算方式字段的校验错误
      nextTick(() => {
        formRef.value?.clearValidate(['settlement_method'])
      })
    }
  },
)

// 计算表格合计数据
const calculateFooterData = () => {
  if (!showProductList.value.length) {
    footerData.value = []
    return
  }

  let totalPrepaymentAmount = 0
  showProductList.value.forEach((item) => {
    if (form.value.payment_type === 20) {
      // 应付类型使用 current_payment
      totalPrepaymentAmount += Number(item.current_payment || 0)
    } else {
      // 预付类型使用 prepayment_amount
      totalPrepaymentAmount += Number(item.prepayment_amount || 0)
    }
  })
  // 根据当前tab设置不同的合计字段
  const footerItem: any = {
    number: '合计',
  }

  if (form.value.payment_type === 20) {
    if (activeTab.value === 'payable') {
      footerItem.pinvid = '合计'
      footerItem.current_payment = totalPrepaymentAmount.roundNext(2)
    } else if (activeTab.value === 'bill') {
      footerItem.current_payment = totalPrepaymentAmount.roundNext(2)
    }
  } else {
    footerItem.prepayment_amount = totalPrepaymentAmount.roundNext(2)
  }

  footerData.value = [footerItem]
}

// 监听列表变化，更新合计
watch(
  () => showProductList.value,
  () => {
    calculateFooterData()
  },
  { deep: true },
)

// 修改预付金额变更事件
const handlePrepaymentAmountChange = (row: any) => {
  if (form.value.payment_type === 10) {
    // 预付款：检查最大可付金额
    // 确保输入值是有效的数字
    const inputAmount = Number(row.prepayment_amount || 0)
    if (isNaN(inputAmount)) {
      // 如果输入值无效，重置为0
      row.prepayment_amount = 0
      return
    }

    const maxAmount = Number((Number(row.total_purchase_amount || 0) - Number(row.executed_amount || 0)).roundNext(2))
    if (inputAmount > maxAmount) {
      row.prepayment_amount = maxAmount
      message.warning(`本次付款不能大于剩余金额 ${maxAmount}`)
    } else {
      // 确保值是有效的数字，然后进行四舍五入
      row.prepayment_amount = Number(inputAmount.roundNext(2))
    }
  } else if (form.value.payment_type === 20) {
    // 应付付款：检查不能大于剩余金额
    // 确保输入值是有效的数字
    const inputAmount = Number(row.current_payment || 0)
    if (isNaN(inputAmount)) {
      // 如果输入值无效，重置为0
      row.current_payment = 0
      return
    }

    const remainingAmount = Number(row.remaining_amount || 0)
    if (inputAmount > remainingAmount) {
      row.current_payment = remainingAmount
      message.warning(`本次付款不能大于剩余金额 ${remainingAmount}`)
    } else {
      // 确保值是有效的数字，然后进行四舍五入
      row.current_payment = Number(inputAmount.roundNext(2))
    }
  }
  setActualAmount()
  calculateFooterData()
}

// 修改 isFieldDisabled 计算属性
const isFieldDisabled = computed(() => {
  // 如果是编辑模式
  const isEdit = viewType.value === DetailTypeEnum.EDIT

  // 如果是新增模式或编辑模式，字段可编辑
  if (viewType.value === DetailTypeEnum.ADD || isEdit) return false

  // 如果是审核模式且是财务一级或二级审核，这些字段可编辑
  if (viewType.value === DetailTypeEnum.AUDIT && [40, 41].includes(temp_audit_status.value || 0)) return false

  // 如果是编辑模式且是财务一级或二级审核，这些字段可编辑
  if (isEdit && [40, 41].includes(form.value.audit_status || 0)) return false

  // 其他所有情况都禁用
  return true
})

// 修改 filterOption 函数实现
const filterOption = (input: string, option: any) => {
  return option.value?.toLowerCase().includes(input.toLowerCase())
}

// 添加计算属性获取抽屉标题
const getDrawerTitle = computed(() => {
  // const paymentTypeText = form.value.payment_type === 20 ? '应付单' : '付款单'
  const paymentTypeText = '付款单'

  // 检查是否为批量请款场景
  if (props.orderType === 'BATCH_PAYMENT') {
    return '新增付款申请单'
  }

  switch (viewType.value) {
    case DetailTypeEnum.ADD:
      return `新增${paymentTypeText}`
    case DetailTypeEnum.EDIT:
      return `编辑${paymentTypeText}`
    case DetailTypeEnum.AUDIT:
      return `审核${paymentTypeText}`
    case DetailTypeEnum.VIEW:
      return `查看${paymentTypeText}`
    default:
      return paymentTypeText
  }
})

// 修改删除按钮的处理函数
const handleDelete = (id: number) => {
  Modal.confirm({
    title: '删除确认',
    content: '确定要删除该付款单吗？',
    okText: '确定',
    cancelText: '取消',
    async onOk() {
      try {
        await DeletePayment({ id })
        message.success('删除成功')
        emits('query') // 使用emit触发父组件的查询
        handleClose() // 关闭抽屉
      } catch (_err) {
        message.error('删除失败')
      }
    },
  })
}

// 添加付款账户类型变化的处理函数
const handleAccountTypeChange = (value: any) => {
  // 如果选择私户（key为2），清空公户相关字段
  if (Number(value) === 2) {
    form.value.payment_account_name = ''
    form.value.payment_account = ''
    form.value.payment_account_id = null
  }
}

// 获取付款类型文本
const getPaymentTypeText = (paymentType: number | undefined) => {
  switch (paymentType) {
    case 10:
      return '预付款'
    case 20:
      return '应付款'
    default:
      return '预付款'
  }
}

// 处理付款类型变化
const handlePaymentTypeChange = (value: any) => {
  const paymentType = Number(value)
  const config = getPaymentTypeConfig(paymentType)

  // 清空已选择的明细
  selectProductList.value = []
  selectBillList.value = [] // 清空账单明细列表
  form.value.the_actual_amount = 0
  form.value.deduct_amount = 0
  form.value.other_fees = 0
  form.value.optimize_amount = 0
  form.value.bill_month = undefined

  // 重置tab为默认状态
  activeTab.value = 'bill'

  // 清空供应商和供应商子公司信息
  form.value.company_supplier_id = null
  form.value.company_supplier_name = ''
  form.value.supplier_id = undefined
  form.value.supplier_name = ''
  form.value.receipt_account_name = ''
  form.value.receipt_bank = ''
  form.value.receipt_card_number = ''
  form.value.account_type = undefined
  form.value.receipt_method = undefined
  form.value.supplier_type = undefined

  calculateFooterData()

  // 根据配置设置默认值
  if (config?.defaultSettlementMethod) {
    form.value.settlement_method = config.defaultSettlementMethod
  } else {
    form.value.settlement_method = undefined
  }
}

// 处理收款方式改变
const handleReceiptMethodChange = async () => {
  // 当收款方式改变时，重新验证收款银行字段
  nextTick(() => {
    formRef.value?.validateFields(['receipt_bank'])
  })

  // 如果有供应商ID，从缓存中获取供应商财务信息
  if (form.value.company_supplier_id) {
    const financeInfo = getFinanceInfoFromCache(form.value.company_supplier_id, form.value.receipt_method, form.value.account_type)

    if (financeInfo) {
      form.value.receipt_account_name = financeInfo.receipt_account_name || ''
      form.value.receipt_bank = financeInfo.receipt_bank || ''
      form.value.receipt_card_number = financeInfo.receipt_card_number || ''
      // 不重新设置 account_type 和 receipt_method，保持用户的选择
    }
  }
}

// 处理其他费用变化
const handleOtherFeesChange = () => {
  if (form.value.other_fees !== null && form.value.other_fees !== undefined) {
    form.value.other_fees = Number(form.value.other_fees.roundNext(2))
  }
  // 重新计算本次实付金额
  setActualAmount()
}

// 处理优化金额变化
const handleOptimizeAmountChange = () => {
  if (form.value.optimize_amount !== null && form.value.optimize_amount !== undefined) {
    form.value.optimize_amount = Number(form.value.optimize_amount.roundNext(2))
  }
  // 重新计算本次实付金额
  setActualAmount()
}

// 添加转换出纳上传附件的方法
const transformCashierAttachments = (attachments: any[]) => {
  if (!attachments?.length) return []
  return attachments.map((item) => ({
    ...item,
    name: item.name || item.org_name,
    status: 'done',
    id: item.id,
  }))
}

// 获取供应商信息并更新验证规则
const getSupplierInfo = async (supplierId: number | string | null) => {
  if (!supplierId) {
    form.value.supplier_type = undefined
    return
  }

  try {
    const res = await GetSupplierCompanyMessage({ id: supplierId })
    if (res?.success && res?.data) {
      form.value.supplier_type = res.data.supplier_type
      // 不在这里进行验证，只在提交时验证
    }
  } catch (error) {
    console.error('获取供应商信息失败:', error)
    // 静默处理错误，不显示提示
  }
}

// 修改 handleUpdateProduct 函数
const handleUpdateProduct = async (updatedProducts: any[]) => {
  // 如果没有选择任何采购单，清空列表并重置相关数据
  if (!updatedProducts || updatedProducts.length === 0) {
    selectProductList.value = []
    form.value.the_actual_amount = 0
    form.value.other_fees = 0
    form.value.optimize_amount = 0
    form.value.bill_month = undefined
    // 清空供应商相关信息
    form.value.company_supplier_id = null
    form.value.company_supplier_name = ''
    form.value.receipt_account_name = ''
    form.value.receipt_bank = ''
    form.value.receipt_card_number = ''
    form.value.account_type = undefined
    form.value.receipt_method = undefined
    calculateFooterData()
    return { success: true } // 返回成功响应
  }

  try {
    // 过滤掉无效的 ID
    const validIds = updatedProducts.map((item) => item.id || item.purcharse_order_id).filter((id) => id != null)
    if (validIds.length === 0) {
      selectProductList.value = []
      form.value.the_actual_amount = 0
      form.value.other_fees = 0
      form.value.optimize_amount = 0
      form.value.bill_month = undefined
      // 清空供应商相关信息
      form.value.company_supplier_id = null
      form.value.company_supplier_name = ''
      form.value.receipt_account_name = ''
      form.value.receipt_bank = ''
      form.value.receipt_card_number = ''
      form.value.account_type = undefined
      form.value.receipt_method = undefined
      calculateFooterData()
      return { success: true } // 返回成功响应
    }

    const res = await GetPurchaseOrderInfo({ ids: validIds })
    if (res.success && res.data) {
      const newList = res.data.map((item: any) => {
        const prepaymentRatio = Number(item.prepayment_ratio) / 100 // 除以100，因为prepayment_ratio是百分比形式
        const totalAmount = Number(item.total_purchase_amount)
        const executedAmount = Number(item.executed_amount)
        const prepaymentAmount = Math.max(0, Number((totalAmount * prepaymentRatio - executedAmount).roundNext(2)))

        // 根据付款类型设置不同的字段
        const result = {
          ...item,
        }

        if (form.value.payment_type === 10) {
          // 预付类型：使用 prepayment_amount 字段
          result.prepayment_amount = prepaymentAmount
        } else if (form.value.payment_type === 20) {
          // 应付类型：使用 current_payment 字段
          result.current_payment = prepaymentAmount
        }

        return result
      })

      selectProductList.value = newList
      setActualAmount()
      calculateFooterData()

      // 检查是否所有采购单都是同一个供应商
      const firstSupplierId = newList[0]?.company_supplier_id
      const allSameSupplier = newList.every((item) => item.company_supplier_id === firstSupplierId)

      // 如果当前已经有供应商子公司，检查新选择的采购单是否与当前供应商一致
      const currentSupplierId = form.value.company_supplier_id
      if (currentSupplierId && newList.length > 0) {
        const hasDifferentSupplier = newList.some((item) => item.company_supplier_id !== currentSupplierId)
        if (hasDifferentSupplier) {
          // 如果新选择的采购单与当前供应商不一致，清空供应商信息
          form.value.company_supplier_id = null
          form.value.company_supplier_name = ''
          form.value.receipt_account_name = ''
          form.value.receipt_bank = ''
          form.value.receipt_card_number = ''
          form.value.account_type = undefined
          form.value.receipt_method = undefined
          form.value.other_fees = 0
          form.value.optimize_amount = 0
          form.value.bill_month = undefined
        }
        // 如果所有采购单都与当前供应商一致，保持当前供应商信息不变
      } else if (newList.length > 0 && firstSupplierId && allSameSupplier) {
        // 如果当前没有供应商，且所有采购单都是同一个供应商，更新供应商信息
        // 先清空所有供应商相关字段
        form.value.company_supplier_id = null
        form.value.company_supplier_name = ''
        form.value.receipt_account_name = ''
        form.value.receipt_bank = ''
        form.value.receipt_card_number = ''
        form.value.account_type = undefined
        form.value.receipt_method = undefined
        form.value.other_fees = 0
        form.value.optimize_amount = 0
        form.value.bill_month = undefined

        // 等待下一个 tick，确保清空操作完成
        await nextTick()

        // 重新设置供应商ID和名称
        form.value.company_supplier_id = firstSupplierId

        // 从采购单数据中获取供应商名称
        if (newList[0].company_supplier_name) {
          form.value.company_supplier_name = newList[0].company_supplier_name
        } else if (newList[0].supplier_name) {
          form.value.company_supplier_name = newList[0].supplier_name
        } else if (props.supplierInfo && props.supplierInfo.supplier_name) {
          form.value.company_supplier_name = props.supplierInfo.supplier_name
        }

        await nextTick()

        // 从供应商列表中查找供应商名称
        const selectedSupplier = supperList.value.find((item) => item.value === firstSupplierId)
        if (selectedSupplier) {
          form.value.company_supplier_name = selectedSupplier.label
        }

        // 获取供应商信息（包括supplier_type）
        await getSupplierInfo(firstSupplierId)

        // 获取供应商财务信息
        try {
          const financeInfo = await getSupplierFinanceInfo(firstSupplierId, form.value.receipt_method, form.value.account_type)
          if (financeInfo) {
            // 重新设置供应商财务信息（不修改供应商名称）
            form.value.receipt_account_name = financeInfo.receipt_account_name || ''
            form.value.receipt_bank = financeInfo.receipt_bank || ''
            form.value.receipt_card_number = financeInfo.receipt_card_number || ''
            form.value.account_type = financeInfo.account_type
            form.value.receipt_method = financeInfo.receipt_method

            // 确保不覆盖之前设置的供应商名称
            // 如果接口返回了供应商名称，但我们已经有从数据获取的名称，则不覆盖
            if (!form.value.company_supplier_name && financeInfo.supplier_name) {
              form.value.company_supplier_name = financeInfo.supplier_name
            }
          }
        } catch (error) {
          console.error('获取供应商财务信息失败:', error)
          // 静默处理错误，不显示提示
        }
      } else if (newList.length > 0 && !allSameSupplier) {
        // 如果采购单来自不同供应商，清空供应商信息
        form.value.company_supplier_id = null
        form.value.company_supplier_name = ''
        form.value.receipt_account_name = ''
        form.value.receipt_bank = ''
        form.value.receipt_card_number = ''
        form.value.account_type = undefined
        form.value.receipt_method = undefined
        form.value.other_fees = 0
        form.value.optimize_amount = 0
        form.value.bill_month = undefined
      }

      // 确保采购明细有数据时，供应商子公司信息能够显示
      if (newList.length > 0 && firstSupplierId && !form.value.company_supplier_id) {
        form.value.company_supplier_id = firstSupplierId

        // 从采购单数据中获取供应商名称
        if (newList[0].company_supplier_name) {
          form.value.company_supplier_name = newList[0].company_supplier_name
        } else if (newList[0].supplier_name) {
          form.value.company_supplier_name = newList[0].supplier_name
        } else if (props.supplierInfo && props.supplierInfo.supplier_name) {
          form.value.company_supplier_name = props.supplierInfo.supplier_name
        }

        // 从供应商列表中查找供应商名称
        const selectedSupplier = supperList.value.find((item) => item.value === firstSupplierId)
        if (selectedSupplier) {
          form.value.company_supplier_name = selectedSupplier.label
        }

        // 获取供应商信息（包括supplier_type）
        await getSupplierInfo(firstSupplierId)

        // 获取供应商财务信息
        try {
          const financeInfo = await getSupplierFinanceInfo(firstSupplierId, form.value.receipt_method, form.value.account_type)
          if (financeInfo) {
            // 重新设置供应商财务信息（不修改供应商名称）
            form.value.receipt_account_name = financeInfo.receipt_account_name || ''
            form.value.receipt_bank = financeInfo.receipt_bank || ''
            form.value.receipt_card_number = financeInfo.receipt_card_number || ''
            form.value.account_type = financeInfo.account_type
            form.value.receipt_method = financeInfo.receipt_method

            // 确保不覆盖之前设置的供应商名称
            // 如果接口返回了供应商名称，但我们已经有从数据获取的名称，则不覆盖
            if (!form.value.company_supplier_name && financeInfo.supplier_name) {
              form.value.company_supplier_name = financeInfo.supplier_name
            }
          }
        } catch (error) {
          console.error('获取供应商财务信息失败:', error)
          // 静默处理错误，不显示提示
        }
      }
    }

    // 检查应付明细列表中是否有多个供应商子公司，如果有则清空银行信息
    if (hasMultipleSupplierSubsidiaries.value) {
      form.value.receipt_account_name = ''
      form.value.receipt_bank = ''
      form.value.receipt_card_number = ''
      form.value.account_type = undefined
      form.value.receipt_method = undefined
    }

    return res // 返回接口响应
  } catch (error) {
    console.error('获取采购单详情失败:', error)
    return { success: false, message: '获取采购单详情失败' }
  }
}

// 处理应付单更新
const handleUpdatePayableProduct = async (updatedProducts: any[]) => {
  // 如果没有选择任何应付单，清空列表并重置相关数据
  if (!updatedProducts || updatedProducts.length === 0) {
    selectProductList.value = []
    form.value.the_actual_amount = 0
    form.value.other_fees = 0
    form.value.optimize_amount = 0
    form.value.bill_month = undefined
    // 清空供应商相关信息
    form.value.company_supplier_id = null
    form.value.company_supplier_name = ''
    form.value.receipt_account_name = ''
    form.value.receipt_bank = ''
    form.value.receipt_card_number = ''
    form.value.account_type = undefined
    form.value.receipt_method = undefined
    form.value.supplier_id = undefined
    form.value.supplier_name = ''
    calculateFooterData()
    return { success: true } // 返回成功响应
  }

  try {
    // 直接使用从 PayableOrderList 获取的数据，不需要额外的详情接口

    // 检查是否有重复的pinvid
    const pinvidCounts: { [key: string]: number } = {}
    updatedProducts.forEach((item) => {
      pinvidCounts[item.pinvid] = (pinvidCounts[item.pinvid] || 0) + 1
    })

    const duplicatePinvids = Object.keys(pinvidCounts).filter((pinvid) => pinvidCounts[pinvid] > 1)
    let productsToProcess = updatedProducts
    if (duplicatePinvids.length > 0) {
      console.warn('发现重复的pinvid:', duplicatePinvids)
      // 去重处理，保留第一个出现的
      const uniqueProducts: any[] = []
      const seenPinvids = new Set()
      updatedProducts.forEach((item) => {
        if (!seenPinvids.has(item.pinvid)) {
          seenPinvids.add(item.pinvid)
          uniqueProducts.push(item)
        }
      })
      // 使用去重后的数据
      productsToProcess = uniqueProducts
    }

    const newList = productsToProcess.map((item: any) => {
      const inv_amount = Number(item.inv_amount || 0)
      const payable_amount = Number(item.payable_amount || 0)
      const remaining_amount = Math.max(0, inv_amount - payable_amount)

      // 确保只保留有效的数字ID，过滤掉表格组件自动生成的临时ID
      const validId = item.id && typeof item.id === 'number' && item.id > 0 ? item.id : undefined

      // 只保留必要的字段，避免传递额外数据
      return {
        id: validId, // 只保留有效的数字ID
        pinvid: item.pinvid,
        inv_amount: item.inv_amount,
        payable_amount: item.payable_amount,
        remaining_amount, // 计算剩余金额
        current_payment: remaining_amount, // 应付单的剩余金额作为本次付款金额
        supplier_id: item.supplier_id,
        supplier_name: item.supplier_name,
        company_supplier_id: item.company_supplier_id,
        company_supplier_name: item.company_supplier_name,
      }
    })

    selectProductList.value = newList
    setActualAmount()
    calculateFooterData()

    // 检查是否所有应付单都是同一个供应商子公司
    const firstSupplierSubsidiaryId = newList[0]?.company_supplier_id
    const allSameSupplierSubsidiary = newList.every((item) => item.company_supplier_id === firstSupplierSubsidiaryId)

    // 检查是否所有应付单都是同一个供应商
    const firstSupplierId = newList[0]?.supplier_id
    const allSameSupplier = newList.every((item) => item.supplier_id === firstSupplierId)

    // 如果当前已经有供应商子公司，检查新选择的应付单是否与当前供应商子公司一致
    const currentSupplierSubsidiaryId = form.value.company_supplier_id
    if (currentSupplierSubsidiaryId && newList.length > 0) {
      const hasDifferentSupplierSubsidiary = newList.some((item) => item.company_supplier_id !== currentSupplierSubsidiaryId)
      if (hasDifferentSupplierSubsidiary) {
        // 如果新选择的应付单与当前供应商子公司不一致，清空供应商子公司信息
        form.value.company_supplier_id = null
        form.value.company_supplier_name = ''
        form.value.supplier_id = undefined
        form.value.supplier_name = ''
        form.value.receipt_account_name = ''
        form.value.receipt_bank = ''
        form.value.receipt_card_number = ''
        form.value.account_type = undefined
        form.value.receipt_method = undefined
        form.value.other_fees = 0
        form.value.optimize_amount = 0
      }
      // else {
      //   // 如果所有应付单都与当前供应商子公司一致，更新供应商子公司信息以确保数据一致性
      //   if (firstSupplierSubsidiaryId && allSameSupplierSubsidiary) {
      //     form.value.company_supplier_id = firstSupplierSubsidiaryId
      //     if (newList[0].company_supplier_name) {
      //       form.value.company_supplier_name = newList[0].company_supplier_name
      //     }
      //   }
      // }
    } else if (newList.length > 0 && firstSupplierSubsidiaryId && allSameSupplierSubsidiary) {
      // 如果当前没有供应商子公司，且所有应付单都是同一个供应商子公司，更新供应商子公司信息
      // 先清空所有供应商子公司相关字段
      form.value.company_supplier_id = null
      form.value.company_supplier_name = ''
      form.value.receipt_account_name = ''
      form.value.receipt_bank = ''
      form.value.receipt_card_number = ''
      form.value.account_type = undefined
      form.value.receipt_method = undefined
      form.value.other_fees = 0
      form.value.optimize_amount = 0

      // 等待下一个 tick，确保清空操作完成
      await nextTick()

      // 重新设置供应商子公司ID和名称
      form.value.company_supplier_id = firstSupplierSubsidiaryId

      // 从应付单数据中获取供应商子公司名称
      if (newList[0].company_supplier_name) {
        form.value.company_supplier_name = newList[0].company_supplier_name
      } else if (props.supplierInfo && props.supplierInfo.supplier_name) {
        form.value.company_supplier_name = props.supplierInfo.supplier_name
      }

      await nextTick()

      // 从供应商列表中查找供应商子公司名称
      const selectedSupplier = supperList.value.find((item) => item.value === firstSupplierSubsidiaryId)
      if (selectedSupplier) {
        form.value.company_supplier_name = selectedSupplier.label
      }

      // 获取供应商子公司信息（包括supplier_type）
      await getSupplierInfo(firstSupplierSubsidiaryId)

      // 获取供应商子公司财务信息
      try {
        const financeInfo = await getSupplierFinanceInfo(firstSupplierSubsidiaryId, form.value.receipt_method, form.value.account_type)
        if (financeInfo) {
          // 重新设置供应商子公司财务信息（不修改供应商子公司名称）
          form.value.receipt_account_name = financeInfo.receipt_account_name || ''
          form.value.receipt_bank = financeInfo.receipt_bank || ''
          form.value.receipt_card_number = financeInfo.receipt_card_number || ''
          form.value.account_type = financeInfo.account_type
          form.value.receipt_method = financeInfo.receipt_method

          // 确保不覆盖之前设置的供应商子公司名称
          // 如果接口返回了供应商子公司名称，但我们已经有从应付单获取的名称，则不覆盖
          if (!form.value.company_supplier_name && financeInfo.supplier_name) {
            form.value.company_supplier_name = financeInfo.supplier_name
          }
        }
      } catch (error) {
        console.error('获取供应商子公司财务信息失败:', error)
        // 静默处理错误，不显示提示
      }

      // 更新供应商信息
      if (firstSupplierId && allSameSupplier) {
        // 如果所有应付单都有相同的供应商ID，更新供应商信息
        form.value.supplier_id = firstSupplierId
        // 从应付单数据中获取供应商名称
        if (newList[0].supplier_name) {
          form.value.supplier_name = newList[0].supplier_name
        }
      } else {
        // 如果应付单没有供应商ID或供应商不一致，清空供应商信息
        form.value.supplier_id = undefined
        form.value.supplier_name = ''
      }
    } else if (newList.length > 0 && !allSameSupplierSubsidiary) {
      // 如果应付单来自不同供应商子公司，清空供应商子公司信息
      form.value.company_supplier_id = null
      form.value.company_supplier_name = ''
      form.value.supplier_id = undefined
      form.value.supplier_name = ''
      form.value.receipt_account_name = ''
      form.value.receipt_bank = ''
      form.value.receipt_card_number = ''
      form.value.account_type = undefined
      form.value.receipt_method = undefined
      form.value.other_fees = 0
      form.value.optimize_amount = 0
    }

    // 确保应付单明细有数据时，供应商子公司信息能够显示
    if (newList.length > 0 && firstSupplierSubsidiaryId && !form.value.company_supplier_id) {
      form.value.company_supplier_id = firstSupplierSubsidiaryId

      // 从应付单数据中获取供应商子公司名称
      if (newList[0].company_supplier_name) {
        form.value.company_supplier_name = newList[0].company_supplier_name
      } else if (props.supplierInfo && props.supplierInfo.supplier_name) {
        form.value.company_supplier_name = props.supplierInfo.supplier_name
      }

      // 从供应商列表中查找供应商子公司名称
      const selectedSupplier = supperList.value.find((item) => item.value === firstSupplierSubsidiaryId)
      if (selectedSupplier) {
        form.value.company_supplier_name = selectedSupplier.label
      }

      // 获取供应商子公司信息（包括supplier_type）
      await getSupplierInfo(firstSupplierSubsidiaryId)

      // 获取供应商子公司财务信息
      try {
        const financeInfo = await getSupplierFinanceInfo(firstSupplierSubsidiaryId, form.value.receipt_method, form.value.account_type)
        if (financeInfo) {
          // 重新设置供应商子公司财务信息（不修改供应商子公司名称）
          form.value.receipt_account_name = financeInfo.receipt_account_name || ''
          form.value.receipt_bank = financeInfo.receipt_bank || ''
          form.value.receipt_card_number = financeInfo.receipt_card_number || ''
          form.value.account_type = financeInfo.account_type
          form.value.receipt_method = financeInfo.receipt_method

          // 确保不覆盖之前设置的供应商子公司名称
          // 如果接口返回了供应商子公司名称，但我们已经有从应付单获取的名称，则不覆盖
          if (!form.value.company_supplier_name && financeInfo.supplier_name) {
            form.value.company_supplier_name = financeInfo.supplier_name
          }
        }
      } catch (error) {
        console.error('获取供应商子公司财务信息失败:', error)
        // 静默处理错误，不显示提示
      }
    }

    // 更新供应商信息
    if (newList.length > 0 && firstSupplierId && allSameSupplier) {
      // 如果所有应付单都有相同的供应商ID，更新供应商信息
      form.value.supplier_id = firstSupplierId
      // 从应付单数据中获取供应商名称
      if (newList[0].supplier_name) {
        form.value.supplier_name = newList[0].supplier_name
      }
    } else if (newList.length > 0) {
      // 如果应付单没有供应商ID或供应商不一致，清空供应商信息
      form.value.supplier_id = undefined
      form.value.supplier_name = ''
    }

    // 检查是否有多個供应商子公司，如果有则清空银行信息
    if (hasMultipleSupplierSubsidiaries.value) {
      form.value.receipt_account_name = ''
      form.value.receipt_bank = ''
      form.value.receipt_card_number = ''
      form.value.account_type = undefined
      form.value.receipt_method = undefined
    }

    return { success: true } // 返回成功响应
  } catch (error) {
    console.error('处理应付单数据失败:', error)
    return { success: false, message: '处理应付单数据失败' }
  }
}

const isExpandAll = ref(false)

const toggleExpandAll = () => {
  const $table = tableRef.value
  if (!$table) return
  isExpandAll.value = !isExpandAll.value
  const records = $table.getTableData().fullData
  const config = currentPaymentTypeConfig.value
  if (!config) return

  const detailField = config.detailFields?.[0] || 'payable_order_details'
  records.forEach((record) => {
    if (record[detailField] && record[detailField].length > 0) {
      $table.setRowExpand(record, isExpandAll.value)
    }
  })
}

// 设置批量请款数据的方法
const setBatchPaymentData = async (billData: any) => {
  if (!isBatchPaymentMode.value) {
    return
  }

  // 设置账单明细数据
  if (billData.payment_bill_details) {
    selectBillList.value = billData.payment_bill_details.map((item: any) => {
      const actual_payable_amount = Number(item.actual_payable_amount || 0)
      const paid_amount = Number(item.paid_amount || 0)
      const remaining_amount = Math.max(0, actual_payable_amount - paid_amount)

      return {
        ...item,
        actual_payable_amount,
        paid_amount,
        remaining_amount,
        current_payment: actual_payable_amount, // 本次付款初始化为实际应付金额（可修改）
        // 确保包含所有必要的字段，数值类型正确
        bill_id: Number(item.bill_id || item.id || 0),
        inv_amount: actual_payable_amount, // 应付总额，使用实际应付金额
        optimize_amount: Number(item.optimize_amount || 0), // 优化金额
        deduct_amount: Number(item.deduct_amount || 0), // 扣款金额
        other_fee: Number(item.other_fee || item.other_fees || 0), // 其他费用，兼容不同字段名
        payable_amount: paid_amount, // 已付金额
        // 添加采购状态字段，确保查看时有回显
        purchase_order_status_string: item.purchase_order_status_string || '',
      }
    })
  }

  // 设置表单数据
  if (billData.company_supplier_id) {
    form.value.company_supplier_id = billData.company_supplier_id
    form.value.company_supplier_name = billData.company_supplier_name || ''
  }

  if (billData.supplier_id) {
    form.value.supplier_id = billData.supplier_id
    // 如果接口没有返回供应商名称，尝试从供应商列表中查找
    let supplierName = billData.supplier_name
    if (!supplierName && supperList.value.length > 0) {
      const supplier = supperList.value.find((item) => item.value === billData.supplier_id)
      supplierName = supplier?.label || ''
    }
    form.value.supplier_name = supplierName
  }

  // 设置财务信息
  if (billData.receipt_bank) {
    form.value.receipt_bank = billData.receipt_bank
  }
  if (billData.receipt_card_number) {
    form.value.receipt_card_number = billData.receipt_card_number
  }
  if (billData.receipt_account_name) {
    form.value.receipt_account_name = billData.receipt_account_name
  }
  if (billData.account_type) {
    form.value.account_type = billData.account_type
  }

  // 设置收款方式
  if (billData.receipt_method) {
    form.value.receipt_method = billData.receipt_method
  }

  // 设置结算方式
  if (billData.settlement_method) {
    form.value.settlement_method = billData.settlement_method
  }

  // 设置账单月份
  if (billData.bill_month) {
    form.value.bill_month = dayjs(billData.bill_month)
  }

  // 设置备注
  if (billData.remark) {
    form.value.remark = billData.remark
  }

  // 设置明细备注
  if (billData.deduction_remark) {
    form.value.deduction_remark = billData.deduction_remark
  }

  // 强制设置为账单明细tab
  activeTab.value = 'bill'

  // 重新计算金额
  setActualAmount()
  calculateFooterData()
}

defineExpose({
  open: handleOpen,
  handleDelete,
  setBatchPaymentData,
})

// 添加计算属性来检查应付明细列表中供应商子公司是否一致
const hasMultipleSupplierSubsidiaries = computed(() => {
  if (form.value.payment_type !== 20) {
    return false
  }
  // 在查看、编辑和审核模式下，如果接口返回的 company_supplier_id 为 0 或空，表示存在多个子公司
  if ([DetailTypeEnum.VIEW, DetailTypeEnum.EDIT, DetailTypeEnum.AUDIT].includes(viewType.value)) {
    if (!form.value.company_supplier_id || form.value.company_supplier_id === 0) {
      return true
    }
  }

  // 如果没有明细数据，返回 false
  if (!selectProductList.value.length) {
    return false
  }

  // 获取所有有效的供应商子公司ID
  const supplierSubsidiaryIds = selectProductList.value.map((item) => item.company_supplier_id).filter((id) => id != null && id !== undefined)

  if (supplierSubsidiaryIds.length === 0) {
    return false
  }

  // 检查是否所有供应商子公司ID都相同
  const firstId = supplierSubsidiaryIds[0]
  return !supplierSubsidiaryIds.every((id) => id === firstId)
  // const allSame = supplierSubsidiaryIds.every((id) => id === firstId)

  // // 添加调试信息
  // console.log('hasMultipleSupplierSubsidiaries debug:', {
  //   viewType: viewType.value,
  //   supplierSubsidiaryIds,
  //   firstId,
  //   allSame,
  //   formSupplierSubsidiaryId: form.value.company_supplier_id,
  //   selectProductListLength: selectProductList.value.length,
  //   result: !allSame,
  // })

  // return !allSame
})

// 添加计算属性来判断供应商子公司是否应该显示为文本
const isSupplierSubsidiaryDisabled = computed(() => {
  // 如果是查看或审核模式，或者字段配置禁用，或者存在多个供应商子公司
  return [DetailTypeEnum.VIEW, DetailTypeEnum.AUDIT, DetailTypeEnum.EDIT].includes(viewType.value) || props.fieldConfig?.disableSupplier || hasMultipleSupplierSubsidiaries.value
})

// 添加计算属性来获取供应商子公司的显示文本
const supplierSubsidiaryDisplayText = computed(() => {
  if (hasMultipleSupplierSubsidiaries.value) {
    return '存在多个子公司'
  }
  return form.value.company_supplier_name
})

// 添加计算属性来获取本次付款合计金额
const totalPaymentAmount = computed(() => {
  let total = 0
  showProductList.value.forEach((item) => {
    if (form.value.payment_type === 20) {
      // 应付类型使用 current_payment
      total += Number(item.current_payment || 0)
    } else {
      // 预付类型使用 prepayment_amount
      total += Number(item.prepayment_amount || 0)
    }
  })
  return Number(total.roundNext(2))
})

// 添加缓存变量来存储供应商财务信息
const supplierFinanceCache = ref<{ [key: string]: any[] }>({})

// 添加一个辅助函数来处理供应商财务信息
const getSupplierFinanceInfo = async (supplierId: number | string | null, receiptMethod?: number, accountType?: number) => {
  if (!supplierId) return null

  try {
    // 检查缓存中是否已有该供应商的数据
    const cacheKey = supplierId.toString()
    let cachedData = supplierFinanceCache.value[cacheKey]

    // 如果缓存中没有数据，则调用接口获取
    if (!cachedData) {
      const supplierRes = await GetSupplierFinanceList({ id: supplierId })
      if (supplierRes?.success && supplierRes?.data && Array.isArray(supplierRes.data)) {
        cachedData = supplierRes.data
        // 将数据存入缓存
        supplierFinanceCache.value[cacheKey] = cachedData
      } else {
        return null
      }
    }

    // 从缓存数据中筛选匹配的记录
    let filteredData = cachedData

    // 如果指定了收款方式，进行筛选
    if (receiptMethod !== undefined && receiptMethod !== null) {
      filteredData = filteredData.filter((item) => item.receipt_method === receiptMethod)
    }

    // 如果指定了账户类型，进行筛选
    if (accountType !== undefined && accountType !== null) {
      filteredData = filteredData.filter((item) => item.account_type === accountType)
    }

    // 返回第一个匹配的记录，如果没有匹配的记录则返回第一个
    return filteredData.length > 0 ? filteredData[0] : cachedData[0] || null
  } catch (error) {
    console.error('获取供应商财务信息失败:', error)
    return null
  }
}

// 添加一个函数来从缓存数据中筛选财务信息（不调用接口）
const getFinanceInfoFromCache = (supplierId: number | string | null, receiptMethod?: number, accountType?: number) => {
  if (!supplierId) return null

  const cacheKey = supplierId.toString()
  const cachedData = supplierFinanceCache.value[cacheKey]

  if (!cachedData || !Array.isArray(cachedData)) {
    return null
  }

  // 从缓存数据中筛选匹配的记录
  let filteredData = cachedData

  // 如果指定了收款方式，进行筛选
  if (receiptMethod !== undefined && receiptMethod !== null) {
    filteredData = filteredData.filter((item) => item.receipt_method === receiptMethod)
  }

  // 如果指定了账户类型，进行筛选
  if (accountType !== undefined && accountType !== null) {
    filteredData = filteredData.filter((item) => item.account_type === accountType)
  }

  // 返回第一个匹配的记录，如果没有匹配的记录则返回第一个
  return filteredData.length > 0 ? filteredData[0] : cachedData[0] || null
}

// 获取关联预付单显示文本
const getPrePaymentOrderDisplayText = () => {
  if (!form.value.pre_payment_order_ids || form.value.pre_payment_order_ids.length === 0) {
    return ''
  }

  const selectedLabels = form.value.pre_payment_order_ids.map((id) => {
    const option = prePaymentOrderOptions.value.find((opt) => opt.value === id)
    return option ? option.label : `ID: ${id}`
  })

  return selectedLabels.join(', ')
}

// 过滤关联预付单选项
const filterPrePaymentOrderOption = (input: string, option: any) => {
  return option.label.toLowerCase().includes(input.toLowerCase())
}

// 处理关联预付单变化
const handlePrePaymentOrderChange = async (selectedValues: any) => {
  if (selectedValues && selectedValues.length > 0) {
    try {
      // 调用接口获取预付单的本次付款合计
      const res = await GetPrePaymentOrderDetailThisDeductionAmountTotal(selectedValues)
      if (res.success && res.data !== undefined) {
        prePaymentOrderTotalAmount.value = res.data

        // 重新计算本次实付金额
        setActualAmount()
      } else {
        console.error('获取预付单本次付款合计失败:', res.message)
        prePaymentOrderTotalAmount.value = 0
      }
    } catch (error) {
      console.error('调用预付单接口失败:', error)
      prePaymentOrderTotalAmount.value = 0
    }
  } else {
    // 没有选择关联预付单时，重置为0
    prePaymentOrderTotalAmount.value = 0
    // 重新计算本次实付金额
    setActualAmount()
  }
}

// 处理关联预付单下拉框显示状态变化
const handlePrePaymentOrderDropdownVisibleChange = async (open: boolean) => {
  if (open && prePaymentOrderOptions.value.length === 0) {
    await loadPrePaymentOrderOptions()
  }
}

// 加载关联预付单选项
const loadPrePaymentOrderOptions = async () => {
  try {
    prePaymentOrderLoading.value = true
    const res = await GetPrePaymentOrderSelect({})
    if (res.success && res.data) {
      prePaymentOrderOptions.value = res.data.map((item: any) => ({
        label: item.label || item.value,
        value: item.value,
      }))
    }
  } catch (error) {
    console.error('获取关联预付单选项失败:', error)
  } finally {
    prePaymentOrderLoading.value = false
  }
}

// 添加 handleAccountTypeChangeForReceipt 函数
const handleAccountTypeChangeForReceipt = async (value: any) => {
  // 如果有供应商ID，从缓存中获取供应商财务信息
  if (form.value.company_supplier_id) {
    const financeInfo = getFinanceInfoFromCache(form.value.company_supplier_id, form.value.receipt_method, value)

    if (financeInfo) {
      form.value.receipt_account_name = financeInfo.receipt_account_name || ''
      form.value.receipt_bank = financeInfo.receipt_bank || ''
      form.value.receipt_card_number = financeInfo.receipt_card_number || ''
      // 不重新设置 account_type 和 receipt_method，保持用户的选择
    }
  }
}

// 添加一个函数来清空缓存
const clearSupplierFinanceCache = () => {
  supplierFinanceCache.value = {}
}

// 添加一个函数来从缓存数据中筛选财务信息（不调用接口）

// 监听所有明细数据变化，自动计算本次实付金额
watch(
  () => [selectProductList.value, selectBillList.value, activeTab.value],
  () => {
    // 所有模式下都重新计算本次实付金额
    setActualAmount()
  },
  { deep: true },
)
</script>

<style scoped lang="scss">
:deep(.vxe-table) {
  // 调整表格行高
  .vxe-body--row {
    height: 50px; // 设置行高

    .vxe-cell {
      // 单元格内容垂直居中
      display: flex;
      align-items: center;
    }
  }

  // 表头样式
  .vxe-header--row {
    height: 45px; // 表头高度
    background-color: #f5f7fa;

    .vxe-cell {
      font-weight: 500;
      color: #606266;
    }
  }

  // 确保展开图标垂直居中
  .vxe-table--expand-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
  }
}

// 调整操作列按钮样式
.operateBox {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  height: 100%;

  .ant-btn {
    margin: 0 4px;
  }
}

// 调整输入框在单元格中的位置
:deep(.ant-input-number) {
  width: 120px; // 设置输入框宽度
}

:deep(:where(.css-dev-only-do-not-override-14rd88r).ant-modal .ant-modal-content) {
  min-height: 800px;
}

// 输入框样式
:deep(.ant-input) {
  background-color: white !important;

  &:hover,
  &:focus {
    background-color: white !important;
  }

  // 覆盖自动填充的背景色
  &:-webkit-autofill,
  &:-webkit-autofill:hover,
  &:-webkit-autofill:focus {
    box-shadow: 0 0 0 1000px white inset !important;
    -webkit-text-fill-color: rgb(0 0 0 / 88%) !important;
    transition: background-color 5000s ease-in-out 0s !important;
  }
}

.purchase-link {
  color: #1890ff;
  cursor: pointer;
}

// 设置输入框左边距为0
:deep(.no-padding-left) {
  .ant-input-number-input {
    padding-left: 0 !important;
  }
}
</style>
