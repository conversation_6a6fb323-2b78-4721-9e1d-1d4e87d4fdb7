import { request } from './request'

// 获取付款申请列表
export function GetBillPayableOrderList(data: any) {
  return request({ url: '/api/BillPayable/GetBillPayableOrderList', data }, 'POST')
}

// 添加应付单
export function AddBillPayableOrder(data: any, config?) {
  return request({ url: '/api/BillPayable/AddBillPayableOrder', data, config }, 'POST')
}
// 编辑应付单
export function EditBillPayableOrder(data: any, config?) {
  return request({ url: '/api/BillPayable/EditBillPayableOrder', data, config }, 'POST')
}

// 查询预付可抵明细（生成应付单用）
export function BillPayablePrepaidDeductibleDetails(data: any) {
  return request({ url: '/api/BillPayable/BillPayablePrepaidDeductibleDetails', data }, 'POST')
}

// 仅退款可抵明细（生成应付单用）
export function BillPayableRefundOnlyWithDeductibleDetails(data: any) {
  return request({ url: '/api/BillPayable/BillPayableRefundOnlyWithDeductibleDetails', data }, 'POST')
}

// 开模明细（生成应付单用）
export function BillPayableMouldDetail(data: any) {
  return request({ url: '/api/BillPayable/BillPayableMouldDetail', data }, 'POST')
}

// 应付单详情
export function GetBillPayableOrderDetail(data: any) {
  return request({ url: '/api/BillPayable/GetBillPayableOrderDetail', data }, 'GET')
}

// 删除应付单
export function DeleteBillPayableOrder(data: any) {
  return request({ url: '/api/BillPayable/DeleteBillPayableOrder', data }, 'POST')
}

// 反审核应付单
export function AntiAuditBillPayableOrder(data: any) {
  return request({ url: '/api/BillPayable/AntiAuditBillPayableOrder', data }, 'GET')
}

// 导出应付单
export function ExportBillPayableOrders(data: any) {
  return request({ url: '/api/BillPayable/ExportBillPayableOrders', data }, 'POST')
}

// 获取应付单创建人列表
export function GetBillPayableCreatorNameList(data: any) {
  return request({ url: '/api/BillPayable/GetBillPayableCreatorNameList', data }, 'GET')
}
