<template>
  <div class="flex flex-col h-full main">
    <Form ref="formRef" v-model:form="formArr" :page-type="PageTypeEnum.SupplierStore" @search="search" @setting="tableRef?.showTableSetting()" />

    <BaseTable ref="tableRef" :page-type="PageTypeEnum.SupplierStore" v-model:form="formArr" :get-list="QuerySupplier" :auto-search="false">
      <template #right-btn>
        <a-button v-if="btnPermission[42103]" type="primary" @click="handleAdd">新增供应商</a-button>
      </template>
      <template #settlement_type="{ row }">
        {{ settlementTypeMap[row.settlement_type] }}
      </template>
      <template #supplier_type="{ row }">
        {{ supplierTypeMap[row.supplier_type] }}
      </template>
      <template #audit_status="{ row }">
        <a-tag :color="auditStatusColorMap[row.audit_status]">
          {{ auditStatusMap[row.audit_status] }}
        </a-tag>
      </template>
      <template #source_type="{ row }">
        {{ formatOptionLabel(row.source_type, sourceOption) }}
      </template>
      <template #fix_option="{ row, column }">
        <RightOperate :list="rightOperateList" :row="row" :column="column" />
      </template>
    </BaseTable>

    <Detail ref="detailRef" @refresh="search" />
    <Update ref="updateRef" @refresh="updateRefresh" />
  </div>
</template>

<script lang="ts" setup>
import { message } from 'ant-design-vue'

import { Enum2Options, filterOption, formatOptionLabel } from '@/utils'
import { settleAccountOption } from '@/common/options'
import { auditStatusColorMap, auditStatusMap, settlementTypeMap, supplierTypeMap } from '@/common/map'

import Detail from '../components/SupplierDetail.vue'
import Update from '../components/SupplierUpdate.vue'

import { GetMRPSupplierGroupSelectByParent, GetDeptSelect } from '@/servers/BusinessCommon'
import { IsCheckProcess, QuerySupplier } from '@/servers/Supplier'

import { PageTypeEnum } from '@/enums/tableEnum'

const { btnPermission } = usePermission()

const tableRef = ref()
const formRef = ref()
const search = () => tableRef.value.search()

const emit = defineEmits(['auditRefresh'])
const updateRefresh = () => {
  emit('auditRefresh')
}

const sourceOption = Enum2Options(SupplierSourceTypeEnum)

const formArr = ref([
  { label: '供应商编码', value: '', type: 'inputNumber', key: 'supplier_id', precision: 0 },
  { label: '供应商名称', value: '', type: 'input', key: 'supplier_name' },
  { label: '供应商分组', value: null, type: 'select', key: 'supplier_group_id', selectArr: [], showSearch: true, filterOption },
  {
    label: '结算方式',
    value: null,
    type: 'select',
    key: 'settlement_type',
    selectArr: settleAccountOption,
  },
  {
    label: '供应商类型',
    value: null,
    type: 'select',
    key: 'supplier_type',
    selectArr: [
      { label: '线下供应商', value: 1 },
      { label: '1688线上供应商', value: 2 },
    ],
  },
  { label: '来源', value: null, type: 'select', key: 'source_type', selectArr: sourceOption, showSearch: true, filterOption },
  { label: '审核时间', value: null, type: 'range-picker', key: 'create_at', formKeys: ['start_time', 'end_time'], placeholder: ['审核开始时间', '审核结束时间'] },
])

onMounted(() => {
  getSeletOptions()
  search()
})

const getSeletOptions = async () => {
  const res1 = await GetMRPSupplierGroupSelectByParent({})
  const res2 = await GetDeptSelect({})

  formArr.value.forEach((item) => {
    if (item.key === 'supplier_group_id') {
      item.selectArr = res1.data.map((v) => ({
        label: v.name,
        options: v.options.map((item) => ({ label: item.label, value: item.value })),
      }))
    } else if (item.key === 'dept_id') {
      item.selectArr = res2.data
    }
  })
}
const updateRef = ref()
const detailRef = ref()

const handleDetail = (row) => {
  detailRef.value.open(row.supplier_id)
}

const handleAdd = () => {
  updateRef.value.open()
}

const handleEdit = async (row) => {
  const isCheck = (await IsCheckProcess({ id: row.supplier_id })).data
  if (isCheck) return message.warning('变更审核中，请审核后再进行变更')
  updateRef.value.open(row.supplier_id)
}

const rightOperateList = ref([
  {
    label: '查看',
    show: 42101,
    onClick: ({ row }) => {
      handleDetail(row)
    },
  },
  {
    label: '提交变更申请',
    show: 42102,
    onClick: ({ row }) => {
      handleEdit(row)
    },
  },
])
</script>

<style lang="scss" scoped>
//
</style>
