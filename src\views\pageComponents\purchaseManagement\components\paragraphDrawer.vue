<template>
  <CheckPaymentOrder
    ref="checkPaymentOrderEl"
    :purchaseOrderIds="props.purchaseOrderIds"
    :fieldConfig="props.fieldConfig"
    :supplierInfo="props.supplierInfo"
    :orderType="props.orderType"
    @query="handleSuccess"
  />
</template>

<script setup lang="ts">
import { ref } from 'vue'

import { DetailTypeEnum } from '@/common/enum'

import CheckPaymentOrder from '@/views/pageComponents/paymentOrder/components/CheckPaymentOrder.vue'

// 定义字段配置接口
interface FieldConfig {
  disableSupplier?: boolean
  // 可以继续添加其他字段的配置
}

// 定义供应商信息接口
interface SupplierInfo {
  supplier_name?: string
  company_supplier_id?: number
}

// 定义props接收采购单ids、字段配置和供应商信息
const props = defineProps<{
  purchaseOrderIds?: number[]
  fieldConfig?: FieldConfig
  supplierInfo?: SupplierInfo
  orderType?: string
}>()

// 定义emit事件
const emit = defineEmits<{
  success: []
}>()

const checkPaymentOrderEl = ref()

// 处理成功回调
const handleSuccess = () => {
  emit('success')
}

// 暴露open方法
const open = () => {
  // 直接打开CheckPaymentOrder组件
  if (checkPaymentOrderEl.value) {
    checkPaymentOrderEl.value.open(DetailTypeEnum.ADD)
  }
}

// 暴露方法给父组件
defineExpose({
  open,
})
</script>
