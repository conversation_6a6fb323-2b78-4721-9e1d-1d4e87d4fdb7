import dayjs from 'dayjs'

import { Enum2Map } from '@/utils'
/* eslint-disable no-unused-vars */
export enum PageTypeEnum {
  ROLE_MANAGE = 2, // 角色管理
  USER_MANAGE = 3, // 用户管理
  PaymentApply = 4, // 付款申请单
  PaymentStatistics = 6, // 付款统计
  NoticeManage = 5, // 通知管理

  SupplierSubsidiariesStore = 10, // 供应商子公司库
  SupplierStore = 11, // 供应商
  SupplierAudit = 12, // 供应商审核
  PurchasePriceList = 13, // 采购价目表
  PurchaseAdjustPrice = 14, // 采购调价表
  PurchaseApply = 15, // 采购申请单
  PurchaseManagement = 16, // 采购管理
  PurchaseChange = 17, // 采购变更
  PurchaseMyOrder = 18, // 我的采购订单
  ProductMapping = 19, // 1688商品映射
  PurchaseAccount = 20, // 1688采购账号管理
  ReceivingAddress = 21, // 收货地址
  WarehouseManagement = 22, // 仓库管理
  LogisticsList = 23, // 物流管理
  ReservationList = 24, // 预约入库单
  SupplierOrganization = 99, // 供应商组织架构
  ProductSku = 25, // 商品库
  paymentOrder = 26, // 付款单
  ProductInventory = 27, // 商品库存管理
  OutboundWarehouseOrder = 28, // 委外领料单
  PurchaseReturnApplication = 29, // 退库申请单
  PurchaseOut = 30, // 采购退库单
  PurchaseOther = 31, // 其他出入库单
  PurchaseInto = 32, // 采购入库单

  SupplierLiquidation = 33, // 供应商清算
  BillPayable = 34, // 应付单
  billVerificationDoneBillList = 35, // 已对账单
  billVerificationUnDoneBillList = 36, // 未对账单
}

export const pageTableKeyEnum = {
  2: [
    { key: 'role_name', name: '角色名称', freeze: 0, is_show: true, is_sort: true },
    { key: 'scope', name: '适用范围', freeze: 0, is_show: true, is_sort: true },
    { key: 'status', name: '状态', freeze: 0, is_show: true, is_sort: true, cellRender: { name: 'status' } },
    { key: 'create_at', name: '创建时间', freeze: 0, is_show: true, is_sort: true },
    { key: 'update_at', name: '最后修改时间', freeze: 0, is_show: true, is_sort: true },
    { key: 'fix_option', name: '操作', width: 150, freeze: 2, is_show: true },
  ],
  3: [
    { key: 'real_name', name: '用户名称', is_sort: true, is_show: true },
    { key: 'user_name', name: '帐号', is_show: true },
    { key: 'customer_name', name: '所属供应商', is_sort: true, is_show: true },
    { key: 'sub_company_name', name: '所属公司', is_sort: true, is_show: true },
    { key: 'departmentname', name: '所在部门', is_sort: true, is_show: true },
    { key: 'job_title_name', name: '岗位', is_show: true },
    { key: 'email', name: '电子邮箱', is_show: true },
    { key: 'role_names', name: '角色', is_sort: true, is_show: true },
    { key: 'status', name: '状态', is_show: true, cellRender: { name: 'status' } },
    { key: 'create_at', name: '创建时间', is_sort: true, is_show: true },
    { key: 'update_at', name: '最后修改时间', is_sort: true, is_show: true },
    { key: 'source_type', name: '来源', is_sort: true, is_show: true },
    { width: 150, key: 'fix_option', name: '操作', is_show: true },
  ],
  4: [
    { key: 'apply_time', name: '申请时间', index: 1, is_show: true, freeze: 0, is_sort: true, is_total: false, width: 94 },
    {
      key: 'oa_running_number',
      name: 'OA流水号',
      index: 2,
      is_show: true,
      freeze: 0,
      is_sort: false,
      is_total: false,
      width: 204,

      cellRender: {
        name: 'copy',
      },
    },
    {
      key: 'prepay_order_number',
      name: '应付/预付单号',
      index: 3,
      is_show: true,
      freeze: 0,
      is_sort: true,
      is_total: false,
      width: 114,
    },
    { key: 'settlement_method', name: '结算方式', index: 4, is_show: true, freeze: 0, is_sort: true, is_total: false, width: 90 },
    { key: 'common_private_account', name: '公户/私户', index: 5, is_show: true, freeze: 0, is_sort: false, is_total: false, width: 74 },
    { key: 'payment_company', name: '付款公司', index: 6, is_show: true, freeze: 0, is_sort: false, is_total: false, width: 220 },
    { key: 'applicant_department_name', name: '申请部门', index: 7, is_show: true, freeze: 0, is_sort: false, is_total: false, width: 109 },
    { key: 'applicant_name', name: '申请人', index: 8, is_show: true, freeze: 0, is_sort: false, is_total: false, width: 71 },
    { key: 'bill_month', name: '账单月份', index: 9, is_show: true, freeze: 0, is_sort: true, is_total: false, width: 115 },
    { key: 'supplier_name', name: '供应商名称', index: 10, is_show: true, freeze: 0, is_sort: true, is_total: false, width: 208 },
    {
      key: 'supplier_id',
      name: '供应商编码',
      index: 11,
      is_show: true,
      freeze: 0,
      is_sort: false,
      is_total: false,
      width: 87,

      cellRender: {
        name: 'copy',
      },
    },
    {
      key: 'purchase_order_number',
      name: '采购单号',
      index: 12,
      is_show: true,
      freeze: 0,
      is_sort: false,
      is_total: false,
      width: 82,

      cellRender: {
        name: 'copy',
      },
    },
    {
      key: 'purchase_order_amount',
      name: '采购单金额',
      index: 13,
      is_show: true,
      freeze: 0,
      is_sort: true,
      is_total: true,
      width: 120,
      tooltip: '列表存在多单，金额排序会按照多单包含的所有单合计金额',
      formatter: 'price',
      align: 'right',
    },
    {
      key: 'payable_order_amount',
      name: '应付单金额/预付款金额',
      index: 14,
      is_show: true,
      freeze: 0,
      is_sort: true,
      is_total: true,
      width: 179,
      tooltip: '列表存在多单，金额排序会按照多单包含的所有单合计金额',
      formatter: 'price',
      align: 'right',
    },
    {
      key: 'other_fee',
      name: '其他费用',
      index: 15,
      is_show: true,
      freeze: 0,
      is_sort: true,
      is_total: true,
      width: 110,

      tooltip: '列表存在多单，金额排序会按照多单包含的所有单合计金额',
      formatter: 'price',
      align: 'right',
    },
    {
      key: 'optimize_amount',
      name: '优化金额',
      index: 16,
      is_show: true,
      freeze: 0,
      is_sort: true,
      is_total: true,
      width: 110,

      tooltip: '列表存在多单，金额排序会按照多单包含的所有单合计金额',
      formatter: 'price',
      align: 'right',
    },
    {
      key: 'deduct_amount',
      name: '扣款金额',
      index: 17,
      is_show: true,
      freeze: 0,
      is_sort: true,
      is_total: true,
      width: 110,

      tooltip: '列表存在多单，金额排序会按照多单包含的所有单合计金额',
      formatter: 'price',
      align: 'right',
    },
    {
      key: 'the_payable_amount',
      name: '本次应付金额',
      index: 18,
      is_show: true,
      freeze: 0,
      is_sort: true,
      is_total: true,
      width: 140,

      tooltip: '列表存在多单，金额排序会按照多单包含的所有单合计金额',
      formatter: 'price',
      align: 'right',
    },
    {
      key: 'the_actual_amount',
      name: '本次实际付款金额',
      index: 19,
      is_show: true,
      freeze: 0,
      is_sort: true,
      is_total: true,
      width: 160,

      tooltip: '列表存在多单，金额排序会按照多单包含的所有单合计金额',
      formatter: 'price',
      align: 'right',
    },
    { key: 'remark', name: '备注', index: 20, is_show: true, freeze: 0, is_sort: false, is_total: false, width: 84 },
    { key: 'receipt_account_info', name: '收款账户信息', index: 21, is_show: true, freeze: 0, is_sort: false, is_total: false, width: 98 },
    { key: 'supplier_type', name: '供应商类型', index: 22, is_show: true, freeze: 0, is_sort: false, is_total: false, width: 100 },
    {
      key: 'receipt_supplier_id',
      name: '收款供应商编码',
      index: 23,
      is_show: true,
      freeze: 0,
      is_sort: false,
      is_total: false,
      width: 108,

      cellRender: {
        name: 'copy',
      },
    },
    { key: 'receipt_card_number', name: '收款卡号', index: 24, is_show: true, freeze: 0, is_sort: false, is_total: false, width: 154 },
    { key: 'receipt_bank', name: '收款银行', index: 25, is_show: true, freeze: 0, is_sort: false, is_total: false, width: 84 },
    { key: 'receipt_account_seal_voucher', name: '收款账号盖章凭证', index: 26, is_show: true, freeze: 0, is_sort: false, is_total: false, width: 151 },
    { key: 'expense_apply_attachment', name: '费用申请单附件', index: 27, is_show: true, freeze: 0, is_sort: false, is_total: false, width: 141 },
    { key: 'contract_attachment', name: '合同附件', index: 28, is_show: true, freeze: 0, is_sort: false, is_total: false, width: 113 },
    { key: 'payment_method', name: '支付方式', index: 29, is_show: true, freeze: 0, is_sort: false, is_total: false, width: 75 },
    { key: 'payment_account_number', name: '付款账号', index: 30, is_show: true, freeze: 0, is_sort: false, is_total: false, width: 75 },
    { key: 'account_name', name: '付款账户名称', index: 31, is_show: true, freeze: 0, is_sort: false, is_total: false, width: 165 },
    { key: 'payment_time', name: '付款时间', index: 32, is_show: true, freeze: 0, is_sort: true, is_total: false, width: 150 },
    { key: 'bank_receipt', name: '银行回单', index: 33, is_show: true, freeze: 0, is_sort: false, is_total: false, width: 94 },
    { key: 'audit_status', name: '审核状态', index: 34, is_show: true, freeze: 0, is_sort: false, is_total: false, width: 160, cellRender: { name: 'status' } },
    { key: 'flow_id', name: '后台流程ID', index: 35, is_show: true, freeze: 0, is_sort: true, is_total: false, width: 183 },
    { key: 'is_already_reject', name: '是否有驳回', index: 36, is_show: true, freeze: 0, is_sort: false, is_total: false, width: 86 },
    { key: 'already_reject_remark', name: '驳回原因', index: 37, is_show: true, freeze: 0, is_sort: true, is_total: false, width: 133 },
    { key: 'fix_option', name: '操作', index: 38, is_show: true, freeze: 2, is_sort: false, is_total: false, width: 180 },
  ],
  5: [
    {
      key: 'no',
      name: '通知编码',
      index: 10,
      is_show: true,
      freeze: 0,
      is_sort: false,
      width: 130,
      cellRender: {
        name: 'copy',
      },
    },
    { key: 'title', name: '通知标题', index: 20, is_show: true, freeze: 0, is_sort: false, width: 150 },
    { key: 'content', name: '通知内容', index: 30, is_show: true, freeze: 0, is_sort: false, width: 300 },
    { key: 'scope', name: '通知范围', index: 40, is_show: true, freeze: 0, is_sort: true, width: 150 },
    { key: 'plan_publish_time', name: '计划发布时间', index: 50, is_show: true, freeze: 0, is_sort: true, width: 160 },
    { key: 'status', name: '状态', index: 60, is_show: true, freeze: 0, is_sort: true, width: 105, cellRender: { name: 'status' } },
    { key: 'publish_time', name: '发布时间', index: 70, is_show: true, freeze: 0, is_sort: true, width: 160 },
    { key: 'created', name: '创建时间', index: 80, is_show: true, freeze: 0, is_sort: true, width: 150 },
    { key: 'modified', name: '最后修改时间', index: 90, is_show: true, freeze: 0, is_sort: true, width: 150 },
    { key: 'fix_option', name: '操作', is_must: true, index: 91, is_show: true, freeze: 2, is_sort: false, width: 340 },
  ],
  10: [
    {
      key: 'supplier_id',
      name: '供应商编码',
      is_show: true,
      width: 100,
      cellRender: {
        name: 'copy',
      },
    },
    {
      key: 'company_supplier_id',
      name: '子公司编码',
      is_show: true,
      width: 200,
      cellRender: {
        name: 'copy',
      },
    },
    {
      key: 'jst_supplier_id',
      name: '聚水潭编码',
      is_show: true,
      width: 100,
      cellRender: {
        name: 'copy',
      },
    },
    { key: 'company_supplier_name', name: '供应商子公司', is_show: true, width: 200 },
    { key: 'supplier_name', name: '供应商名称', is_show: true, width: 200 },
    { key: 'supplier_category', name: '供应商分类', is_show: true, width: 120 },
    { key: 'supplier_group_name', name: '供应商分组(金蝶)', is_show: true, width: 100 },
    { key: 'purchase_nature_string', name: '采购性质', is_show: true, width: 100 },
    {
      key: 'settlement_type',
      name: '结算方式',
      is_show: true,
      width: 100,
      formatter: ({ cellValue }) => Enum2Map(SettlementTypeEnum)[cellValue],
    },
    {
      key: 'supplier_type',
      name: '供应商类型',
      is_show: true,
      width: 100,
      formatter: ({ cellValue }) => Enum2Map(SupplierTypeEnum)[cellValue],
    },
    { key: 'purchase_price_list_name', name: '价目表', is_show: true, width: 100 },
    { key: 'name', name: '联系人姓名', is_show: true, width: 100 },
    { key: 'mobile_phone_number', name: '联系人手机', is_show: true, width: 140 },
    {
      key: 'address',
      name: '供应商地址',
      is_show: true,
      width: 100,
      formatter: ({ row }) => {
        return [...new Set([row.province, row.city, row.area])].reduce((acc, cur) => acc + (cur || ''), '') + (row.address_detail || '')
      },
    },
    { key: 'audit_status_time', name: '审核时间', is_show: true, width: 160 },
    { key: 'dept', name: '对接部门', is_show: true, width: 100 },
    { key: 'buyer', name: '对接采购员', is_show: true, width: 120 },
    { key: 'source_type', name: '来源', is_sort: true, is_show: true, width: 120 },
    { key: 'fix_option', name: '操作', is_show: true, freeze: 2, width: 200 },
  ],
  11: [
    {
      key: 'supplier_id',
      name: '供应商编码',
      is_show: true,

      cellRender: {
        name: 'copy',
      },
    },
    { key: 'supplier_name', name: '供应商名称', is_show: true, width: 0 },
    { key: 'supplier_group_name', name: '供应商分组(金蝶)', is_show: true, width: 0 },
    { key: 'purchase_nature_string', name: '采购性质', is_show: true, width: 0 },
    { key: 'settlement_type', name: '结算方式', is_show: true, width: 0 },
    { key: 'create_at', name: '创建时间', is_show: true, width: 0 },
    { key: 'audit_status_time', name: '审核时间', is_show: true, width: 0 },
    { key: 'source_type', name: '来源', is_sort: true, is_show: true, width: 0 },
    { key: 'fix_option', name: '操作', is_show: true, freeze: 2, width: 150 },
  ],
  12: [
    {
      key: 'supplier_audit_number',
      name: '编号',
      is_show: true,
      width: 125,
      cellRender: {
        name: 'copy',
      },
    },
    {
      key: 'supplier_id',
      name: '供应商编码',
      is_show: true,
      cellRender: {
        name: 'copy',
      },
    },
    { key: 'supplier_name', name: '供应商名称', is_show: true, width: 135 },
    { key: 'supplier_group_name', name: '供应商分组(金蝶)', is_show: true, width: 110 },
    { key: 'purchase_nature_string', name: '采购性质', is_show: true },
    { key: 'settlement_type', name: '结算方式', is_show: true, width: 95 },
    { key: 'create_at', name: '创建时间', is_show: true },
    { key: 'audit_status_time', name: '审核时间', is_show: true },
    { key: 'audit_type', name: '审核类型', is_show: true },
    {
      key: 'audit_status',
      name: '状态',
      is_show: true,
      formatter: ({ cellValue }) => Enum2Map(PurchaseOrderAuditStatusEnum)[cellValue],
      cellRender: {
        name: 'status',
      },
    },
    { key: 'source_type', name: '来源', is_sort: true, is_show: true, width: 80 },
    { key: 'sync_jst_status', name: '同步聚水潭状态', is_show: true },
    { key: 'sync_jst_remark', name: '同步聚水潭备注', is_show: true },
    { key: 'sync_k3_status', name: '同步金蝶状态', is_show: true },
    { key: 'sync_k3_remark', name: '同步金蝶备注', is_show: true },
    { key: 'fix_option', name: '操作', is_show: true, freeze: 2, width: 120 },
  ],
  13: [
    {
      key: 'number',
      name: '编号',
      is_show: true,

      cellRender: {
        name: 'copy',
      },
    },
    { key: 'name', name: '价目表名称', is_show: true, width: 0 },
    { key: 'is_default', name: '是否默认', is_show: true, width: 0 },
    { key: 'price_type', name: '价目类型', is_show: true, width: 0 },
    { key: 'supplier_name', name: '供应商', is_show: true, width: 0 },
    { key: 'company_supplier_name', name: '供应商子公司', is_show: true, width: 0 },
    { key: 'created', name: '发布时间', is_show: true, width: 0 },
    { key: 'audit_time', name: '审核时间', is_show: true, width: 0 },
    {
      key: 'audit_status',
      name: '状态',
      is_show: true,
      formatter: ({ cellValue }) => Enum2Map(PurchaseOrderAuditStatusEnum)[cellValue],
      cellRender: {
        name: 'status',
      },
    },
    { key: 'fix_option', name: '操作', is_show: true, width: 0 },
  ],
  14: [
    {
      key: 'number',
      name: '编号',
      is_show: true,

      cellRender: {
        name: 'copy',
      },
    },
    {
      key: 'price_list_number',
      name: '对应价目表编号',
      is_show: true,

      cellRender: {
        name: 'copy',
      },
    },
    { key: 'name', name: '调价表名称', is_show: true, width: 0 },
    { key: 'remark', name: '备注', is_show: true, width: 0 },
    { key: 'created', name: '创建时间', is_show: true, width: 0 },
    { key: 'audit_time', name: '审核时间', is_show: true, width: 0 },
    {
      key: 'audit_status',
      name: '状态',
      is_show: true,
      formatter: ({ cellValue }) => Enum2Map(PurchaseOrderAuditStatusEnum)[cellValue],
      cellRender: {
        name: 'status',
      },
    },
    { key: 'fix_option', name: '操作', is_show: true, width: 0 },
  ],
  15: [
    {
      key: 'number',
      name: '编号',
      is_show: true,
      width: 100,
      cellRender: {
        name: 'copy',
      },
    },
    {
      key: 'jst_sku_id',
      name: '商品聚水潭编号',
      is_show: true,
      width: 100,
      cellRender: {
        name: 'copy',
      },
    },
    {
      name: '成品申请单来源',
      key: 'finished_source_types',
      is_detail: true,
      width: 130,
      formatter: 'join',
    },
    { key: 'external_id', name: 'MRP计划单号', is_show: true, width: 100, cellRender: { name: 'copy' } },
    { key: 'finished_numbers', name: '成品申请单编号', is_show: true, width: 120, cellRender: { name: 'copy' } },
    { key: 'finished_external_ids', name: '成品计划单号', is_show: true, width: 120, cellRender: { name: 'copy' } },
    // { key: 'purchase_order_numbers', name: '关联的采购单号', is_show: true, width: 200, cellRender: { name: 'copy' } },
    { key: 'order_type', name: '申请类型', is_show: true, width: 100, formatter: ({ row }) => Enum2Map(PurchaseApplyOrderEnum)[row.order_type] },
    { key: 'order_detail_process_type_name', name: '加工方式', is_show: true, width: 70 },
    { key: 'image_url', name: '商品主图', is_show: true, width: 70, cellRender: { name: 'image' } },
    {
      key: 'k3_sku_id',
      name: '商品编码',
      is_show: true,
      width: 100,
      cellRender: {
        name: 'copy',
      },
    },
    { key: 'sku_name', name: '商品名称', is_show: true, width: 140 },
    { key: 'srs_platform_prod_code', name: 'SRS平台商品编码', is_show: true, width: 140 },
    { key: 'supplier_name', name: '供应商', is_show: true, width: 100, is_sort: true },
    { key: 'company_supplier_name', name: '供应商子公司', is_show: true, width: 100, is_sort: true },
    { key: 'supplier_category', name: '供应商分类', is_show: true, width: 100 },
    {
      key: 'category',
      name: '商品分类',
      is_show: true,
      width: 100,
      formatter: ({ cellValue }) => Enum2Map(productTypeEnum)[cellValue],
    },
    { key: 'quantity', name: '计划采购数量', is_show: true, width: 100, is_sort: true, align: 'right' },
    { key: 'purchase_quantityed', name: '已执行数量', is_show: true, width: 100, is_sort: true, tooltip: '采购单的采购总数合计', align: 'right' },
    { key: 'created', name: '申请时间', is_show: true, width: 140 },
    { key: 'predict_delivery_date', name: '预计交期', is_show: true, width: 140 },
    { key: 'warehouse_name', name: '仓库', is_show: true, width: 100, is_sort: true },
    { key: 'applicant_name', name: '申请人', is_show: true, width: 100 },
    { key: 'source_type', name: '来源', is_show: true, width: 70, formatter: ({ row }) => Enum2Map(PurchaseApplyOrderSourceTypeEnum)[row.source_type] },
    { key: 'audit_time', name: '审核时间', is_show: true, width: 140 },
    {
      key: 'audit_status',
      name: '审核状态',
      is_show: true,
      width: 100,
      formatter: ({ cellValue }) => Enum2Map(PurchaseOrderAuditStatusEnum)[cellValue],
      cellRender: {
        name: 'status',
      },
    },
    {
      key: 'order_status',
      name: '单据状态',
      is_show: true,
      width: 100,
      formatter: ({ row }) => Enum2Map(PurchaseApplyOrderStatusEnum)[row.order_status],
      cellRender: {
        name: 'status',
      },
    },
    { key: 'tags', name: '标签', is_show: true, width: 100, cellRender: { name: 'label' } },
    { key: 'turnover_days', name: '周转天数', is_show: true, width: 80, formatter: 'infinity', is_sort: true, align: 'right' },
    { key: 'saleable_inventory', name: '可售库存', is_show: true, width: 80, is_sort: true, align: 'right' },
    { key: 'stockout_days', name: '缺货天数', is_show: true, width: 80, is_sort: true, align: 'right' },
    { key: 'stockout_quantity', name: '缺货数量', is_show: true, width: 80, is_sort: true, align: 'right' },
    { key: 'remark', name: '备注', is_show: true, width: 120 },
    { key: 'fix_option', name: '操作', is_show: true, width: 150, freeze: 2 },
  ],
  16: [
    // {
    //   key: 'no',
    //   name: '序号',
    //   width: 50,
    //   freeze: 1,
    //   is_show: true,
    // },
    {
      key: 'number',
      name: '采购单编号',
      is_show: true,
      width: 150,
      headerMoreList: ['copy'],
      freezeRow: true,
      freeze: 1,
      type: 'expand',
      // cellRender: {
      //   name: 'copy',
      // },
    },
    { key: 'source_order_number', name: '原采购单号', is_show: true, width: 150, cellRender: { name: 'copy' } },
    { key: 'type', name: '采购单类型', is_show: true, width: 120, formatter: ({ row }) => Enum2Map(PurchaseTypeEnum)[row.type] },
    // { key: 'image_urls', name: '商品图片', width: 140 },
    {
      key: 'purchase_time',
      name: '采购时间',
      is_show: true,
      width: 140,
      is_sort: true,
      formatter: 'dateTime',
      cellRender: {
        name: 'text',
        options: {
          defaultColor: 'rgba(0, 0, 0, 0.50)',
        },
      },
    },
    { key: 'product_type', name: '商品类型', is_show: true, width: 80, formatter: ({ cellValue }) => Enum2Map(productTypeEnum)[cellValue] },
    { key: 'supplier_name', name: '供应商', is_show: true, width: 120, is_sort: true },
    { key: 'company_supplier_name', name: '供应商子公司', is_show: true, width: 120, is_sort: true },
    { key: 'warehourse_name', name: '收货仓库', is_show: true, width: 120, is_sort: true },
    {
      key: 'shipments_address',
      name: '收货地址',
      is_show: true,
      width: 120,
      formatter: ({ row }) => {
        return [...new Set([row.province, row.city, row.area])].reduce((acc, cur) => acc + (cur || ''), '') + (row.shipments_address || '')
      },
    },

    {
      key: 'settlement_type',
      name: '结算方式',
      is_show: true,
      width: 80,
      formatter: ({ cellValue }) => Enum2Map(SettlementTypeEnum)[cellValue],
    },
    {
      name: '商品图片',
      key: 'image_url',
      width: 150,
      is_show: true,
      cellRender: { name: 'image' },
    },
    {
      name: '商品名称',
      key: 'sku_name',
      width: 130,
      is_show: true,
      is_detail: true,
    },
    {
      name: '商品编号',
      key: 'k3_sku_id',
      width: 130,
      is_show: true,
      is_detail: true,
      cellRender: {
        name: 'copy',
      },
    },
    {
      name: '聚水潭商品编号',
      key: 'jst_sku_id',
      is_detail: true,
      width: 130,
      is_show: true,
      cellRender: {
        name: 'copy',
      },
    },
    {
      name: 'SRS平台商品编码',
      key: 'srs_platform_prod_code',
      width: 130,
      is_show: true,
      is_detail: true,
      cellRender: {
        name: 'copy',
      },
    },
    {
      key: 'create_at',
      name: '需求时间',
      is_show: true,
      width: 130,
      is_sort: true,
    },
    {
      name: '采购申请单号',
      key: 'apply_numbers',
      is_detail: true,
      width: 130,
      cellRender: {
        name: 'copy',
      },
    },
    {
      name: '计划单来源',
      key: 'finished_source_types',
      is_detail: true,
      width: 130,
      formatter: 'join',
    },
    {
      name: 'MRP计划单号',
      key: 'external_ids',
      is_detail: true,
      width: 130,
      cellRender: {
        name: 'copy',
      },
    },
    {
      key: 'finished_numbers',
      name: '成品申请单编号',
      is_show: true,
      is_detail: true,
      width: 120,
      cellRender: { name: 'copy' },
    },
    {
      key: 'finished_external_ids',
      name: '成品计划单号',
      is_show: true,
      is_detail: true,
      width: 120,
      cellRender: { name: 'copy' },
    },

    {
      name: '成品周转天数',
      key: 'shortest_turnover_days',
      is_show: true,
      is_detail: true,
      width: 80,
      formatter: 'infinity',
      align: 'right',
    },
    {
      key: 'shortest_saleable_inventory',
      name: '成品可售库存',
      is_show: true,
      is_detail: true,
      width: 80,
      align: 'right',
    },
    {
      key: 'longest_stockout_days',
      name: '成品缺货天数',
      is_show: true,
      is_detail: true,
      width: 80,
      align: 'right',
    },
    {
      key: 'total_stockout_quantity',
      name: '成品缺货数量',
      is_show: true,
      is_detail: true,
      width: 80,
      align: 'right',
    },

    {
      name: '颜色规格',
      key: 'type_specification',
      width: 130,
      is_show: true,
      is_detail: true,
    },
    {
      name: '款式编码',
      key: 'style_code',
      width: 130,
      is_show: true,
      is_detail: true,
    },
    {
      name: '品牌',
      key: 'product_brand',
      width: 130,
      is_show: true,
      is_detail: true,
    },
    {
      name: '商品分类',
      key: 'all_category',
      width: 130,
      is_show: true,
      is_detail: true,
    },
    { name: '材质', key: 'material_name', width: 130, is_show: true },
    {
      name: '面积',
      key: 'k3_reference_acreage',
      width: 90,
      cellRender: {
        name: 'price',
        precision: 8,
      },
      is_show: true,
      align: 'right',
    },
    {
      name: '采购单位',
      key: 'valuation_unit',
      width: 130,
      is_show: true,
      is_detail: true,
    },
    {
      name: '上次采购价',
      key: 'last_purchase_price',
      tooltip: '供应商上一笔采购订单的合计单价',
      width: 130,
      is_show: true,
      is_detail: true,
      formatter: 'price',
      align: 'right',
      params: { precision: 8 },
    },
    {
      name: '系统单价',
      key: 'system_unit_price',
      tooltip: '价目表合计含税单价',
      width: 130,
      is_show: true,
      is_detail: true,
      formatter: 'price',
      align: 'right',
      params: { precision: 8 },
    },
    {
      name: '采购总数',
      key: 'total_purchase_quantity',
      tooltip: '采购数量+赠品数量',
      width: 130,
      is_show: true,
      is_detail: true,
      align: 'right',
    },
    {
      name: '成本数量',
      key: 'cost_num',
      width: 130,
      is_show: true,
      is_detail: true,
      align: 'right',
    },
    // { name: '金蝶采购价', key: 'k3_reference_price', width: 130, formatter: ({ cellValue }) => (cellValue != null ? `${Number(cellValue).roundNext(8)}` : ''), is_show: true },
    // { name: '成本材料单价', key: 'material_price', width: 130, is_show: true },
    // { name: '成本加工单价', key: 'process_fee', width: 130, is_show: true },
    {
      name: '成本含税单价',
      key: 'tax_unit_price',
      tooltip: '成本合计单价*税率%+成本合计单价',
      width: 130,
      formatter: ({ cellValue, row }) => (cellValue === '...' ? cellValue : cellValue != null ? `${Number(cellValue).roundNext(8)}（￥/${row.cost_unit || ''}）` : ''),
      is_show: true,
      is_detail: true,
      align: 'right',
    },
    {
      name: '采购含税单价',
      key: 'purchase_tax_price',
      width: 130,
      formatter: ({ cellValue, row }) => (cellValue === '...' ? cellValue : cellValue != null ? `${Number(cellValue).roundNext(8)}（￥/${row.valuation_unit || ''}）` : ''),
      is_show: true,
      is_detail: true,
      align: 'right',
    },
    {
      key: 'tax_sum_price',
      name: '商品含税总金额',
      width: 130,
      is_show: true,
      is_detail: true,
      formatter: 'price',
      align: 'right',
    },
    {
      key: 'total_purchase_amount',
      name: '采购总金额',
      is_show: true,
      width: 120,
      formatter: 'price',
      align: 'right',
      is_detail: true,
    },
    {
      name: '价格趋势',
      key: 'price_trend',
      width: 130,
      is_show: true,
      is_detail: true,
    },
    {
      key: 'shipment_status',
      name: '发货状态',
      is_show: true,
      is_detail: true,
      width: 80,
      formatter: ({ cellValue }) => Enum2Map(ShipmentStatusEnum)[cellValue],
      cellRender: {
        name: 'status',
      },
    },
    {
      key: 'warehoused_status',
      name: '入库情况',
      is_show: true,
      width: 80,
      formatter: ({ cellValue }) => Enum2Map(PurchaseOrderWarehousedStatusEnum)[cellValue],
    },
    {
      key: 'total_scheduled_quantity',
      name: '已预约入库数量',
      is_show: true,
      is_detail: true,
      width: 120,
      className: 'text-green',
      tooltip: '预约单的本次预约入库数量合计',
      align: 'right',
    },
    {
      key: 'total_actual_inbound',
      name: '实际入库数量',
      is_show: true,
      is_detail: true,
      width: 120,
      className: 'text-green',
      tooltip: '入库单入库数量合计',
      align: 'right',
    },
    {
      key: 'not_stock_num',
      name: '未入库数量',
      is_show: true,
      is_detail: true,
      width: 90,
      align: 'right',
    },
    {
      key: 'take_quantity',
      name: '1688推单数量',
      is_show: true,
      is_detail: true,
      width: 120,
      cellRender: {
        name: 'text',
        options: {
          defaultColor: 'rgba(0, 128, 0, 1)',
        },
      },
      tooltip: '拍单子表的采购数量合计',
      align: 'right',
    },
    {
      name: '1688订单编号',
      key: 'online_order_numbers',
      width: 230,
      is_show: true,
      is_detail: true,
      showOverflow: false,
    },
    // {
    //   key: 'account_name',
    //   name: '拍单账号',
    //   is_show: true,
    //   width: 120,
    // },
    { key: 'buyer_name', name: '申请人', is_show: true, width: 120 },
    {
      key: 'tags',
      name: '标签',
      is_show: true,
      width: 180,
      cellRender: { name: 'tag' },
    },
    {
      key: 'remark',
      name: '备注',
      is_show: true,
      is_detail: true,
      width: 120,
    },
    {
      name: '协议到货日期',
      key: 'predict_delivery_date',
      width: 130,
      is_show: true,
      is_detail: true,
    },
    {
      name: '异常状态',
      key: 'abnormal_status',
      width: 130,
      is_show: true,
      is_detail: true,
      formatter: ({ cellValue }) => Enum2Map(PurchaseOrderAbnormalStatusEnum)[cellValue],
      cellRender: {
        name: 'status',
      },
    },
    { key: 'order_completed_time', name: '完成时间', is_show: true, width: 140 },
    {
      key: 'audit_status',
      name: '审核状态',
      is_show: true,
      width: 120,
      formatter: ({ cellValue }) => Enum2Map(PurchaseOrderAuditStatusEnum)[cellValue],
      cellRender: {
        name: 'status',
      },
    },
    {
      key: 'supplier_type',
      name: '供应商状态',
      is_show: true,
      width: 120,
      formatter: ({ cellValue }) => Enum2Map(SupplierStatusEnum)[cellValue],
      cellRender: {
        name: 'status',
      },
    },
    {
      key: 'order_status',
      name: '订单状态',
      is_show: true,
      width: 120,
      formatter: ({ cellValue }) => Enum2Map(PurchaseOrderStatusEnum)[cellValue],
      cellRender: {
        name: 'status',
      },
    },
    {
      key: 'settlement_status',
      name: '结算状态',
      is_show: true,
      width: 120,
      formatter: ({ cellValue }) => Enum2Map(BillSettlementStatusEnum)[cellValue],
    },
    {
      key: 'reconcile_status',
      name: '对账状态',
      is_show: true,
      width: 120,
      formatter: ({ cellValue }) => Enum2Map(PurchaseReconciliationStatusEnum)[cellValue],
    },
    { key: 'fix_option', name: '操作', is_show: true, width: 150, freeze: 2, showOverflow: false },
  ],
  17: [
    {
      key: 'number',
      name: '变更单编号',
      is_show: true,

      cellRender: {
        name: 'copy',
      },
    },
    {
      key: 'purchase_order_number',
      name: '采购单编号',
      is_show: true,

      cellRender: {
        name: 'copy',
      },
    },
    { key: 'purchase_time', name: '采购时间', is_show: true, formatter: 'dateTime' },
    { key: 'purchase_num', name: '采购总数', is_show: true },
    { key: 'supplier_name', name: '供应商', is_show: true, width: 0 },
    { key: 'company_supplier_name', name: '供应商子公司', is_show: true, width: 0 },
    { key: 'warehourse_name', name: '采购收料仓', is_show: true, width: 0 },
    { key: 'creator_name', name: '申请人', is_show: true, width: 0 },
    { key: 'create_at', name: '申请时间', is_show: true, width: 0 },
    { key: 'audit_time', name: '审核时间', is_show: true, width: 0 },
    {
      key: 'audit_status',
      name: '状态',
      is_show: true,

      formatter: ({ cellValue }) => Enum2Map(PurchaseOrderAuditStatusEnum)[cellValue],
      cellRender: {
        name: 'status',
      },
    },
    { key: 'fix_option', name: '操作', is_show: true, width: 150, freeze: 2 },
  ],
  18: [
    {
      key: 'number',
      name: '采购单编号',
      is_show: true,

      cellRender: {
        name: 'copy',
      },
    },
    { key: 'purchase_time', name: '采购时间', is_show: true, formatter: 'dateTime' },
    { key: 'settlement_type', name: '结算方式', is_show: true, width: 0 },
    { key: 'product_type', name: '商品类型', is_show: true, width: 0 },
    { key: 'total_purchase_quantity', name: '采购数量', is_show: true, align: 'right' },
    { key: 'warehourse_id', name: '采购收料仓', is_show: true, width: 0 },
    { key: 'order_status', name: '订单状态', is_show: true, width: 0, cellRender: { name: 'status' } },
    { key: 'supplier_type', name: '供应商状态', is_show: true, width: 0, cellRender: { name: 'status' } },
    { key: 'fix_option', name: '操作', is_show: true, freeze: 2 },
  ],
  19: [
    { key: 'image_url', name: '商品图片', is_show: true, width: 70, cellRender: { name: 'image' } },
    { key: 'style_code', name: '商品款号', is_show: true, width: 120 },
    {
      key: 'k3_sku_id',
      name: '商品编号',
      is_show: true,
      width: 120,
      cellRender: {
        name: 'copy',
      },
    },
    { key: 'company_supplier_name', name: '系统供应商子公司', is_show: true, width: 120 },
    { key: 'sku_name', name: '商品名称', is_show: true, width: 120 },
    { key: 'type_specification', name: '颜色规格', is_show: true, width: 120 },
    { key: 'srm_purchase_quantity', name: 'SRM系统数量', is_show: true, width: 100, align: 'right' },
    { key: 'ali_purchase_quantity', name: '1688采购数量', is_show: true, width: 100, align: 'right' },
    { key: 'ali_product_url', name: '1688地址', is_show: true, width: 120 },
    { key: 'ali_start_quantity', name: '1688起批数量', is_show: true, width: 100, align: 'right' },
    { key: 'brand', name: '品牌', is_show: true, width: 120 },
    { key: 'modified_at', name: '修改时间', is_show: true, width: 140 },
    { key: 'create_at', name: '创建时间', is_show: true, width: 140 },
    { key: 'status', name: '状态', is_show: true, width: 120, cellRender: { name: 'status' }, formatter: ({ cellValue }) => Enum2Map(AliProductMapMatchStatusEnum)[cellValue] },
    { key: 'fix_option', name: '操作', is_show: true, width: 120, freeze: 2 },
  ],
  20: [
    {
      key: 'number',
      name: '采购账号编号',
      is_show: true,

      cellRender: {
        name: 'copy',
      },
    },
    { key: 'authorized_account', name: '授权账号', is_show: true, width: 0 },
    { key: 'account_name', name: '账号名称', is_show: true, width: 0 },
    { key: 'account_abbreviation', name: '账号简称', is_show: true, width: 0 },
    { key: 'creator', name: '创建人', is_show: true, width: 0 },
    { key: 'create_at', name: '创建时间', is_show: true, width: 0 },
    { key: 'modifier', name: '修改人', is_show: true, width: 0 },
    { key: 'modified_at', name: '修改时间', is_show: true, width: 0 },
    { key: 'status', name: '启用状态', is_show: true, width: 0, cellRender: { name: 'status' } },
    { key: 'authorization_status', name: '授权状态', is_show: true, width: 0, cellRender: { name: 'status' } },
    { key: 'fix_option', name: '操作', is_show: true, width: 300, freeze: 2 },
  ],
  21: [
    {
      key: 'number',
      name: '地址编码',
      is_show: true,

      cellRender: {
        name: 'copy',
      },
    },
    { key: 'full_name', name: '收货人', is_show: true, width: 0 },
    { key: 'mobile_phone', name: '手机号码', is_show: true, width: 0 },
    { key: 'province', name: '省份', is_show: true, width: 0 },
    { key: 'city', name: '城市', is_show: true, width: 0 },
    { key: 'area', name: '区域', is_show: true, width: 0 },
    { key: 'address', name: '详细地址', is_show: true, width: 0 },
    { key: 'create_at', name: '创建时间', is_show: true, width: 0 },
    { key: 'status', name: '状态', is_show: true, width: 0, cellRender: { name: 'status' } },
    { key: 'fix_option', name: '操作', is_show: true, width: 200, freeze: 2 },
  ],
  22: [
    {
      key: 'warehouse_id',
      name: '仓库编号',
      is_show: true,

      cellRender: {
        name: 'copy',
      },
    },
    { key: 'warehouse_name', name: '仓库名称', is_show: true, width: 0 },
    { key: 'supplier_name', name: '供应商主公司', is_show: true, width: 0 },
    { key: 'company_supplier_name', name: '供应商子公司', is_show: true, width: 0 },
    { key: 'consignee', name: '联系人姓名', is_show: true, width: 0 },
    { key: 'phone_number', name: '联系电话', is_show: true, width: 0 },
    { key: 'province', name: '收货地址', is_show: true, width: 0 },
    { key: 'shipments_address', name: '详细地址', is_show: true, width: 0 },
    { key: 'fix_option', name: '操作', is_show: true, width: 200, freeze: 2 },
  ],
  23: [
    { key: 'company_name', name: '物流公司', is_show: true, width: 0 },
    {
      key: 'company_no',
      name: '物流公司编号',
      is_show: true,

      cellRender: {
        name: 'copy',
      },
    },
    {
      key: 'jst_logistics_company_id',
      name: '聚水潭编号',
      is_show: true,

      cellRender: {
        name: 'copy',
      },
    },
    { key: 'company_phone', name: '物流公司服务电话', is_show: true, width: 0 },
    { key: 'fix_option', name: '操作', is_show: true, width: 200, freeze: 2 },
  ],
  24: [
    {
      key: 'number',
      name: '预约单号',
      is_show: true,
      width: 120,
      cellRender: {
        name: 'copy',
      },
    },
    {
      key: 'jst_po_id',
      name: '聚水潭预约单号',
      is_show: true,
      width: 120,
      cellRender: {
        name: 'copy',
      },
    },
    {
      key: 'purchase_order_numbers',
      name: '采购单号',
      is_show: true,
      cellRender: {
        name: 'copy',
      },
    },
    // { key: 'srs_platform_prod_code', name: 'SRS平台商品编码', is_show: true, width: 140 },
    { key: 'supplier_name', name: '供应商', is_show: true, width: 0 },
    { key: 'company_supplier_name', name: '供应商子公司', is_show: true },
    { key: 'scheduled_arrival_time', name: '预计到货时间', is_show: true, width: 140 },
    { key: 'warehouse_name', name: '采购收料仓', is_show: true, width: 0 },
    { key: 'total_num', name: '预约入库数量', is_show: true, align: 'right' },
    {
      key: 'jst_poi_id',
      name: '入库单编号',
      is_show: true,

      cellRender: {
        name: 'copy',
      },
    },
    { key: 'actual_quantity', name: '实际入库数量', is_show: true, tooltip: '入库单入库数量合计', align: 'right' },
    { key: 'creator_name', name: '创建人', is_show: true, width: 0 },
    { key: 'create_at', name: '创建时间', is_show: true, width: 140 },
    {
      key: 'audit_status',
      name: '状态',
      is_show: true,
      width: 80,
      formatter: ({ cellValue }) => Enum2Map(BookingOrderAuditStatusEnum)[cellValue],
      cellRender: {
        name: 'status',
      },
    },
    {
      key: 'tracking_status',
      name: '发货状态',
      is_show: true,
      width: 80,
      cellRender: {
        name: 'status',
      },
    },
    { key: 'fix_option', name: '操作', is_show: true, width: 150, freeze: 2, showOverflow: false },
  ],
  99: [
    {
      key: 'supplier_id',
      name: '供应商编号',

      index: 10,
      is_show: true,
      freeze: 0,
      is_sort: false,
      width: 250,

      cellRender: {
        name: 'copy',
      },
    },
    { key: 'supplier_name', name: '供应商', index: 20, is_show: true, freeze: 0, is_sort: true, width: 400 },
    { key: 'status', name: '状态', index: 30, is_show: true, freeze: 0, is_sort: true, width: 180 },
    { key: 'create_at', name: '创建时间', index: 40, is_show: true, freeze: 0, is_sort: true, width: 210 },
    { key: 'modified_at', name: '最后修改时间', index: 50, is_show: true, freeze: 0, is_sort: true },
    { key: 'source_type', name: '来源', index: 60, is_show: true, freeze: 0, is_sort: true },
    { key: 'fix_option', name: '操作', is_must: true, index: 61, is_show: true, freeze: 2, is_sort: false, width: 100 },
  ],

  25: [
    { key: 'image_url', name: '商品主图', is_show: true, width: 80, cellRender: { name: 'image' }, showOverflow: false },
    {
      key: 'sku_id',
      name: '商品编码',
      is_show: true,
      width: 120,
      cellRender: {
        name: 'copy',
      },
    },
    { key: 'sku_name', name: '商品名称', is_show: true, width: 190 },
    { key: 'type_specification', name: '规格型号', is_show: true, width: 190 },
    {
      key: 'style_code',
      name: '款式编码',
      is_show: true,
      width: 150,
      cellRender: {
        name: 'copy',
      },
    },
    { key: 'category', name: '商品分类', is_show: true, width: 100 },
    { key: 'product_tags', name: '商品标签', is_show: true, width: 190, cellRender: { name: 'tag' }, showOverflow: false },
    { key: 'material_name', name: '材质', is_show: true, width: 100 },
    { key: 'cost_unit', name: '成本单位', is_show: true, width: 100 },
    { key: 'conversion_value', name: '换算值', is_show: true, width: 100 },
    { key: 'conversion_formula', name: '换算公式', is_show: true, width: 140 },
    { key: 'valuation_unit', name: '采购单位', is_show: true, width: 100 },
    { key: 'packing_qty', name: '标准装箱数', is_show: true, width: 100 },
    {
      key: 'jst_sku_id',
      name: '聚水潭编号',
      is_show: true,
      width: 190,
      cellRender: {
        name: 'copy',
      },
    },
    {
      key: 'srs_platform_prod_code',
      name: 'SRS平台商品编码',
      is_show: true,
      width: 190,
      cellRender: {
        name: 'copy',
      },
    },
    {
      key: 'srs_supplier_prod_code',
      name: 'SRS供应商商品编码',
      is_show: true,
      width: 190,
      cellRender: {
        name: 'copy',
      },
    },
    { key: 'supplier_name', name: '默认供应商', is_show: true, width: 190 },
    { key: 'create_time', name: '创建时间', is_show: true, width: 140 },
    { key: 'update_time', name: '修改时间', is_show: true, width: 140 },
  ],
  26: [
    { key: 'create_at', name: '申请日期', is_show: true, width: 100, formatter: 'date' },
    {
      key: 'payment_order_number',
      name: '付款单编号',
      is_show: true,
      width: 150,
      cellRender: {
        name: 'copy',
      },
      // freezeRow: true,
      // type: 'expand',
    },
    { key: 'bill_purchase_numbers', name: '账单/采购单号', is_show: true, width: 100 },
    { key: 'payment_type_string', name: '付款类型', is_show: true, width: 100 },
    {
      key: 'pinvid',
      name: '应付单号',
      is_show: true,
      width: 150,
      cellRender: {
        name: 'copy',
      },
    },
    { key: 'expected_payment_date', name: '期望付款日期', is_show: true, width: 100 },
    { key: 'settlement_method_string', name: '结算方式', is_show: true, width: 100 },
    { key: 'common_private_account_string', name: '付款账户类型', is_show: true, width: 100 },
    { key: 'payment_account_name', name: '付款账户', is_show: true, width: 140 },
    { key: 'applicant_department_name', name: '申请部门', is_show: true, width: 140 },
    { key: 'applicant_name', name: '申请人', is_show: true, width: 80 },
    { key: 'bill_month', name: '账单月份', is_show: true, width: 100, formatter: ({ cellValue }) => (cellValue ? dayjs(cellValue).format('YYYY-MM') : null) },
    { key: 'supplier_name', name: '供应商', is_show: true, width: 140 },
    { key: 'company_supplier_name', name: '供应商子公司', is_show: true, width: 140 },
    {
      key: 'company_supplier_id',
      name: '供应商编码',
      is_show: true,
      width: 100,
      cellRender: {
        name: 'copy',
      },
    },
    {
      key: 'total_bill_purchase_amount',
      name: '账单总金额/采购单总金额',
      // formatter: ({ row }) => `￥${(row[row.payment_type === 20 ? 'payment_jstbill_amount' : 'payment_order_amount'] ?? 0).roundNext(2)}（${row.payment_type === 20 ? '应' : '预'}）`,
      is_show: true,
      width: 160,
      // is_sort: true,
      align: 'right',
    },
    { key: 'other_fees', name: '其他费用', is_show: true, width: 100, formatter: 'price', align: 'right' },
    { key: 'optimize_amount', name: '优化金额', is_show: true, width: 100, formatter: 'price', align: 'right' },

    { key: 'deduct_amount', name: '扣款金额', is_show: true, width: 140, is_total: true, align: 'right', formatter: 'price' },
    { key: 'the_actual_amount', name: '本次实付金额', is_show: true, width: 160, is_total: true, is_sort: true, align: 'right', formatter: 'price' },
    { key: 'remark', name: '备注', is_show: true, width: 140 },
    { key: 'account_type', name: '账户类型', is_show: true, width: 90 },
    { key: 'receipt_account_name', name: '收款账户名称', is_show: true, width: 140 },
    { key: 'receipt_card_number', name: '收款账号', is_show: true, width: 140 },
    { key: 'receipt_bank', name: '收款银行', is_show: true, width: 140 },
    { key: 'attachment_array', name: '附件', is_show: true, width: 140, cellRender: { name: 'file' } },
    {
      key: 'audit_status',
      name: '审核状态',
      is_show: true,
      width: 90,
      formatter: ({ cellValue }) => Enum2Map(PaymentOrderAuditStatusEnum)[cellValue],
      cellRender: {
        name: 'status',
      },
    },
    {
      key: 'order_status_string',
      name: '单据状态',
      is_show: true,
      width: 90,
      cellRender: {
        name: 'status',
      },
    },
    {
      key: 'payment_status_string',
      name: '打款状态',
      is_show: true,
      width: 90,
      cellRender: {
        name: 'status',
      },
    },
    {
      key: 'invoice_status',
      name: '发票状态',
      is_show: true,
      width: 90,
      formatter: ({ cellValue }) => Enum2Map(InvoiceStatusEnum)[cellValue],
      cellRender: {
        name: 'status',
      },
    },
    { key: 'fix_option', name: '操作', is_must: true, index: 61, is_show: true, freeze: 2, is_sort: false, width: 130 },
  ],
  [PageTypeEnum.PurchaseInto]: [
    {
      key: 'io_id',
      name: '入库单号',
      is_show: true,
      width: 120,
      cellRender: {
        name: 'copy',
      },
    },
    {
      key: 'merge_so_id',
      name: '预约单号',
      is_show: true,
      width: 120,
      cellRender: {
        name: 'copy',
      },
    },
    {
      key: 'booking_order_number',
      name: 'SRM预约入库单号',
      is_show: true,
      width: 140,
      cellRender: {
        name: 'copy',
      },
    },
    {
      key: 'purchase_order_numbers',
      name: '采购单号',
      is_show: true,
      width: 120,
      cellRender: {
        name: 'copy',
      },
    },
    { key: 'supplier_category', name: '供应商分类', is_show: true, width: 120 },
    { key: 'company_supplier_name', name: '供应商子公司', is_show: true, width: 140 },
    { key: 'po_date', name: '采购日期', is_show: true, width: 120 },
    { key: 'io_date', name: '入库日期', is_show: true, width: 120 },
    { key: 'interval_time', name: '间隔时间', is_show: true, width: 120 },
    { key: 'modified', name: '修改时间', is_show: true, width: 120 },
    { key: 'warehouse', name: '仓库名称', is_show: true, width: 140 },
    {
      key: 'wms_co_id',
      name: '分仓编号',
      is_show: true,
      width: 140,
      cellRender: {
        name: 'copy',
      },
    },
    {
      key: 'wh_id',
      name: '仓库编码',
      is_show: true,
      width: 140,
      cellRender: {
        name: 'copy',
      },
    },
    { key: 'creator_name', name: '制单人', is_show: true, width: 120 },
    {
      key: 'l_id',
      name: '物流单号',
      is_show: true,
      width: 140,
      cellRender: {
        name: 'copy',
      },
    },
    { key: 'remark', name: '备注', is_show: true, width: 200 },
    { key: 'fix_option', name: '操作', is_show: true, width: 120, freeze: 2 },
  ],
  [PageTypeEnum.PurchaseOut]: [
    {
      key: 'io_id',
      name: '退货单号',
      is_show: true,
      width: 120,
      cellRender: {
        name: 'copy',
      },
    },
    {
      key: 'purchase_order_numbers',
      name: '采购单号',
      is_show: true,
      width: 120,
      cellRender: {
        name: 'copy',
      },
    },
    {
      key: 'number',
      name: '退库申请单号',
      is_show: true,
      width: 140,
      cellRender: {
        name: 'copy',
      },
    },
    { key: 'warehouse', name: '仓库', is_show: true, width: 120 },
    {
      key: 'wms_co_id',
      name: '分仓编号',
      is_show: true,
      width: 140,
      cellRender: {
        name: 'copy',
      },
    },
    { key: 'labels', name: '标记|多标签', is_show: true, width: 120 },
    { key: 'receiver_name', name: '收货人', is_show: true, width: 120 },
    { key: 'io_date', name: '退货日期', is_show: true, width: 120 },
    { key: 'remark', name: '备注', is_show: true, width: 120 },
    { key: 'modified', name: '修改时间', is_show: true, width: 140 },
    { key: 'creator_name', name: '创建人', is_show: true, width: 140 },
    { key: 'status_string', name: '状态', is_show: true, width: 140, cellRender: { name: 'status' } },
    { key: 'fix_option', name: '操作', is_show: true, width: 120, freeze: 2 },
  ],
  [PageTypeEnum.PurchaseOther]: [
    {
      key: 'inbound_order_number',
      name: '委外领料单编号',
      is_show: true,
      width: 120,
      cellRender: {
        name: 'copy',
      },
    },
    {
      key: 'reservation_order_number',
      name: '出仓单号',
      is_show: true,
      width: 120,
      cellRender: {
        name: 'copy',
      },
    },
    { key: 'srm_reservation_order_number', name: '单据日期', is_show: true, width: 140 },
    { key: 'purchase_order_number', name: '单据状态', is_show: true, width: 120, cellRender: { name: 'status' } },
    {
      key: 'supplier_subsidiary',
      name: '分仓编号',
      is_show: true,
      width: 140,
      cellRender: {
        name: 'copy',
      },
    },
    { key: 'purchase_date', name: '单据类型', is_show: true, width: 120 },
    { key: 'inbound_date', name: '仓库名', is_show: true, width: 120 },
    {
      key: 'interval_time',
      name: '分仓编号',
      is_show: true,
      width: 120,
      cellRender: {
        name: 'copy',
      },
    },
    {
      key: 'modify_time',
      name: '仓库编号',
      is_show: true,
      width: 120,
      cellRender: {
        name: 'copy',
      },
    },
    { key: 'warehouse_name', name: '备注', is_show: true, width: 140 },
    { key: 'sub_warehouse_code', name: '创建时间', is_show: true, width: 140 },
    { key: 'warehouse_code', name: '修改时间', is_show: true, width: 140 },
    { key: 'logistics_order_number', name: '创建人', is_show: true, width: 140 },
    { key: 'fix_option', name: '操作', is_show: true, width: 120, freeze: 2 },
  ],
  [PageTypeEnum.PurchaseReturnApplication]: [
    {
      key: 'number',
      name: '退库申请单编号',
      is_show: true,
      width: 120,
      cellRender: {
        name: 'copy',
      },
    },
    {
      key: 'jst_return_no',
      name: '退货单编号',
      is_show: true,
      width: 120,
      cellRender: {
        name: 'copy',
      },
    },
    {
      key: 'purchase_order_numbers',
      name: '采购单编号',
      is_show: true,
      width: 140,
      cellRender: {
        name: 'copy',
      },
    },
    // { key: 'srs_platform_prod_code', name: 'SRS平台商品编码', is_show: true, width: 140 },
    { key: 'application_type_string', name: '申请类型', is_show: true, width: 120 },
    { key: 'return_reason_type_string', name: '退货原因', is_show: true, width: 140 },
    { key: 'company_supplier_name', name: '供应商子公司', is_show: true, width: 120 },
    { key: 'supplier_name', name: '供应商', is_show: true, width: 120 },
    { key: 'warehouse_name', name: '仓库', is_show: true, width: 120 },
    { key: 'consignee', name: '收货人', is_show: true, width: 120 },
    { key: 'creator_name_department', name: '申请人', is_show: true, width: 140 },
    { key: 'is_refunded', name: '供应商是否已退款', is_show: true, width: 140, formatter: ({ cellValue }) => (cellValue ? '是' : '否') },
    { key: 'create_at', name: '创建时间', is_show: true, width: 140 },
    { key: 'audit_time', name: '审核时间', is_show: true, width: 140 },
    {
      key: 'audit_status_string',
      name: '状态',
      is_show: true,
      width: 140,
      cellRender: {
        name: 'status',
      },
    },
    {
      key: 'order_status_string',
      name: '单据状态',
      is_show: true,
      width: 140,
      cellRender: {
        name: 'status',
      },
    },
    { key: 'fix_option', name: '操作', is_show: true, width: 150, freeze: 2 },
  ],
  27: [
    {
      key: 'warehouse_id',
      name: '仓库id',
      is_show: true,
      cellRender: {
        name: 'copy',
      },
    },
    { key: 'warehouse_name', name: '仓库名称', is_show: true },
    { key: 'image_url', name: '图片', is_show: true, width: 80, cellRender: { name: 'image' }, showOverflow: false },
    {
      key: 'style_code',
      name: '款式编号',
      is_show: true,
      cellRender: {
        name: 'copy',
      },
    },
    {
      key: 'sku_id',
      name: '商品编码',
      is_show: true,
      cellRender: {
        name: 'copy',
      },
    },
    { key: 'sku_name', name: '商品名称', is_show: true },
    { key: 'type_specification', name: '颜色规格', is_show: true },
    { key: 'actual_inventory_num', name: '主仓库库存', is_show: true },
    { key: 'update_time', name: '库存更新时间', is_show: true },
  ],
  28: [
    {
      key: 'number',
      name: '委外领料单编号',
      is_show: true,
      width: 160,
      cellRender: {
        name: 'copy',
      },
    },
    {
      key: 'purchase_order_numbers',
      name: '采购单号',
      is_show: true,
      formatter: 'join',
      width: 160,
      cellRender: {
        name: 'copy',
      },
    },
    {
      key: 'delivery_number',
      name: '聚水潭出库单编号',
      is_show: true,
      width: 160,
      cellRender: {
        name: 'copy',
      },
    },
    { key: 'company_supplier_name', name: '供应商子公司', is_show: true, width: 160 },
    { key: 'purchase_quantity', name: '采购数量', is_show: true, width: 100, align: 'right' },
    { key: 'material_quantity', name: '本次领料数量', is_show: true, width: 100, formatter: 'number', align: 'right' },
    { key: 'warehouse_name', name: '领料仓库', is_show: true, width: 160 },
    { key: 'create_at', name: '创建时间', is_show: true, width: 130 },
    { key: 'creator', name: '申请人', is_show: true, width: 100 },
    { key: 'delivery_time', name: '出库时间', is_show: true, width: 130 },
    {
      key: 'audit_status',
      name: '审核状态',
      is_show: true,
      width: 80,
      formatter: ({ cellValue }) => Enum2Map(PurchaseOrderAuditStatusEnum)[cellValue],
      cellRender: {
        name: 'status',
      },
    },
    {
      key: 'order_status',
      name: '订单状态',
      is_show: true,
      width: 80,
      formatter: ({ row }) => Enum2Map(OutboundWarehouseOrderStatusEnum)[row.order_status],
      cellRender: {
        name: 'status',
      },
    },
    { key: 'fix_option', name: '操作', is_show: true, width: 160, freeze: 2 },
  ],
  33: [
    {
      key: 'io_warehouse_id',
      name: '进出仓单号',
      width: 100,
    },
    { key: 'order_type_str', name: '单据类型', width: 80 },
    { key: 'supplier_name', name: '供应商', width: 140 },
    { key: 'company_supplier_name', name: '供应商子公司', width: 160 },
    // { key: 'reconciliation_status_str', name: '对账状态', is_show: true, width: 160 },
    {
      key: 'bill_payable_status_string',
      name: '应付状态',
      width: 80,
      cellRender: {
        name: 'status',
      },
    },
    { key: 'reserve_io_warehouse_number', name: '预约进出仓单号', width: 140 },
    { key: 'reserve_io_warehouse_date', name: '进出仓日期', width: 140 },
    { key: 'purchase_order_number', name: '采购单号', width: 140 },
    { key: 'purchase_time', name: '采购日期', width: 140 },
    { key: 'k3_sku_id', name: '商品编号' },
    { key: 'sku_name', name: '商品名称' },
    { key: 'style_code', name: '款式编码' },
    { key: 'all_category', name: '商品分类' },
    { key: 'product_tags', name: '商品标签', cellRender: { name: 'tag' } },
    { key: 'tax_rate', name: '税率', formatter: 'rate' },
    { key: 'tax_unit_price', name: '采购含税单价', formatter: 'price', align: 'right', params: { precision: 8 } },
    { key: 'actual_tax_unit_price', name: '实际单价', formatter: 'price', align: 'right', params: { precision: 8 }, tooltip: '入库单：采购总金额/采购数量\n退库单：实际退库金额/退库数量' },
    { key: 'purchase_inbound_quantity', name: '采购入库数量', align: 'right' },
    { key: 'inv_amount', name: '应付金额', formatter: 'price', align: 'right', tooltip: '入库单：采购入库数量*实际单价\n退库单：实际退库金额' },
    { key: 'io_remark', name: '进出仓单备注', width: 160 },
  ].map((f) => ({ ...f, width: f.width || 100, is_show: true })),
  [PageTypeEnum.BillPayable]: [
    {
      key: 'number',
      name: '应付单号',
      is_show: true,
      width: 150,
      cellRender: { name: 'copy' },
    },
    { key: 'pay_order_date', name: '单据日期', is_show: true, width: 100, formatter: 'date' },
    { key: 'supplier_name', name: '供应商', is_show: true, width: 150 },
    { key: 'company_supplier_name', name: '供应商子公司', is_show: true, width: 150 },
    { key: 'inv_amount', name: '应付总额', is_show: true, width: 100, formatter: 'price', is_total: true, align: 'right' },
    {
      key: 'invoice_type',
      name: '发票类型',
      is_show: true,
      width: 120,
      formatter: ({ cellValue }) => Enum2Map(InvoiceTypeEnum)[cellValue],
    },
    { key: 'invoice_number', name: '发票号码', is_show: true, width: 100, cellRender: { name: 'copy' } },
    { key: 'invoice_subject', name: '发票主体', is_show: true, width: 100 },
    { key: 'amount_invoiced', name: '已开票金额', is_show: true, width: 100, formatter: 'price', is_total: true, align: 'right' },
    { key: 'invoice_date', name: '发票日期', is_show: true, width: 160 },
    {
      key: 'audit_status',
      name: '审核状态',
      is_show: true,
      width: 80,
      formatter: ({ cellValue }) => Enum2Map(BillPayableOrderAuditEnum)[cellValue],
      cellRender: {
        name: 'status',
      },
    },
    {
      key: 'settlement_status',
      name: '结算状态',
      is_show: true,
      width: 80,
      formatter: ({ cellValue }) => Enum2Map(BillPayableOrderSettlementEnum)[cellValue],
      cellRender: {
        name: 'status',
      },
    },
    { key: 'creator_name', name: '创建人', is_show: true, width: 100 },
    { key: 'remark', name: '备注', is_show: true, width: 160 },
    { key: 'fix_option', name: '操作', is_show: true, width: 220, freeze: 2 },
  ],
  [PageTypeEnum.billVerificationUnDoneBillList]: [
    { key: 'purchase_number', name: '采购单编号', is_show: true, width: 150, cellRender: { name: 'copy' } },
    { key: 'return_application_number', name: '退库申请单号', is_show: true, width: 140 },
    { key: 'purchase_time', name: '采购时间', is_show: true, width: 150 },
    {
      key: 'purchase_order_status',
      name: '采购单状态',
      is_show: true,
      width: 150,
      formatter: ({ cellValue }) => Enum2Map(PurchaseOrderStatusEnum)[cellValue],
      cellRender: {
        name: 'status',
      },
    },
    { key: 'reconcile_status', name: '对账状态', is_show: true, width: 100, formatter: ({ cellValue }) => Enum2Map(PurchaseReconciliationStatusEnum)[cellValue] },
    { key: 'supplier_name', name: '供应商', is_show: true, width: 150 },
    { key: 'company_supplier_name', name: '供应商子公司', is_show: true, width: 150 },
    { key: 'image_url', name: '商品图片', is_show: true, width: 100, cellRender: { name: 'image' }, showOverflow: false },
    { key: 'sku_name', name: '商品名称', is_show: true, width: 150 },
    { key: 'k3_sku_id', name: '商品编号', is_show: true, width: 120 },
    { key: 'tax_unit_price', name: '采购含税单价', is_show: true, width: 120, formatter: 'price', align: 'right', params: { precision: 8 } },
    { key: 'actual_unit_price', name: '实际单价', is_show: true, width: 120, formatter: 'price', align: 'right', params: { precision: 8 }, tooltip: '采购总金额/采购数量' },
    { key: 'purchase_quantity', name: '采购数量', is_show: true, width: 100, align: 'right' },
    { key: 'purchase_inbound_quantity', name: '采购入库数量', is_show: true, width: 120, align: 'right' },
    { key: 'return_quantity', name: '退库数量', is_show: true, width: 100, align: 'right' },
    { key: 'undelivered_quantity', name: '未入库数量', is_show: true, width: 120, align: 'right' },
    { key: 'total_purchase_amount', name: '采购总金额', is_show: true, width: 120, formatter: 'price', align: 'right' },
    { key: 'refund_amount', name: '退款金额', is_show: true, width: 100, formatter: 'price', align: 'right', tooltip: '实际退库金额\n未交付数量*实际单价' },
    { key: 'prepayment_amount', name: '预付金额', is_show: true, width: 100, formatter: 'price', align: 'right' },
    { key: 'inv_amount', name: '采购单应付金额', is_show: true, width: 140, formatter: 'price', align: 'right' },
    { key: 'bill_payable_ids', name: '应付单号', is_show: true, width: 150, cellRender: { name: 'copy' }, formatter: 'join', showOverflow: true },
  ],
  [PageTypeEnum.billVerificationDoneBillList]: [
    { key: 'number', name: '账单单号', is_show: true, width: 150, cellRender: { name: 'copy' } },
    { key: 'bill_month', name: '账单月份', is_show: true, width: 120, formatter: ({ cellValue }) => (cellValue ? dayjs(cellValue).format('YYYY-MM') : '-') },
    { key: 'supplier_name', name: '供应商', is_show: true, width: 150 },
    { key: 'company_supplier_name', name: '供应商子公司', is_show: true, width: 150 },
    { key: 'inv_amount', name: '应付总额', is_show: true, width: 120, formatter: 'price', is_total: true, align: 'right' },
    { key: 'optimize_amount', name: '优化金额', is_show: true, width: 120, formatter: 'price', is_total: true, align: 'right' },
    { key: 'deduct_amount', name: '扣款金额', is_show: true, width: 120, formatter: 'price', is_total: true, align: 'right' },
    { key: 'other_fee', name: '其他费用', is_show: true, width: 120, formatter: 'price', is_total: true, align: 'right' },
    { key: 'actual_payable_amount', name: '实际应付金额', is_show: true, width: 140, formatter: 'price', is_total: true, align: 'right' },
    { key: 'paid_amount', name: '已付金额', is_show: true, width: 120, formatter: 'price', is_total: true, align: 'right' },
    {
      key: 'audit_status_string',
      name: '审核状态',
      is_show: true,
      width: 100,
    },
    {
      key: 'settlement_status_string',
      name: '结算状态',
      is_show: true,
      width: 100,
    },
    { key: 'creator_name', name: '创建人', is_show: true, width: 100 },
    { key: 'create_at', name: '创建日期', is_show: true, width: 140 },
    { key: 'remark', name: '备注', is_show: true, width: 160 },
    { key: 'fix_option', name: '操作', is_show: true, width: 200, freeze: 2 },
  ],
}
