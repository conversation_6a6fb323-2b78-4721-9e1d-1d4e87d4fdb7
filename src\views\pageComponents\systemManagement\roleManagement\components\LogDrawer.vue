<template>
  <a-drawer :footer="false" v-model:open="logVisible" width="35vw" title="角色日志" placement="right" :maskClosable="false" :footer-style="{ textAlign: 'left' }">
    <div class="detailBox">
      <LoadingOutlined v-show="logloading" class="loadingIcon" />
      <div v-if="!logloading && log.length != 0" class="contentBox">
        <div class="detail" v-for="(item, index) in log" :key="index">
          <div class="point">
            <div class="pointItem"></div>
          </div>
          <div class="drawer-title">
            {{ item.user_name }}
            <span class="description">{{ item.user_department }}</span>
          </div>
          <div class="detailType">{{ item.op_type }}了 此记录</div>
          <div class="detailItem" style="display: flex" v-for="(detail, detailIndex) in item.edits" :key="detailIndex">
            <div style="white-space: nowrap">· {{ detail.name }}：</div>
            <div class="oldVal">
              {{ detail.old_val ? detail.old_val : '空' }}
            </div>
            <div class="to">></div>
            <div class="newVal">
              {{ detail.new_val ? detail.new_val : '空' }}
            </div>
          </div>
          <div class="detailTime">{{ item.op_at }}</div>
        </div>
      </div>
      <div class="emptyText" v-show="!logloading && log.length === 0">该角色无操作日志</div>
    </div>
  </a-drawer>
</template>

<script lang="ts" setup>
import { GetOpLogInfos } from '@/servers/Role'
import { LoadingOutlined } from '@ant-design/icons-vue'

const logVisible = ref(false)
const logloading = ref(false)
const target = ref(null)
interface Log {
  user_name: string
  user_department: string
  op_type: string
  edits: any[]
  op_at: string
}
const log = ref<Log[]>([])
const open = (item) => {
  log.value = []
  target.value = item
  logVisible.value = true
  logloading.value = true
  GetOpLogInfos({ id: item.id })
    .then((res) => {
      log.value = res.data.reverse()
      logloading.value = false
    })
    .catch(() => {
      logloading.value = false
    })
}
// 暴露方法
defineExpose({
  open,
})
</script>

<style lang="scss" scoped>
.loadingIcon {
  font-size: 30px;
  color: #1890ff;
}

.contentBox {
  padding: 5px 20px;
  margin-top: 20px;
  margin-left: 20px;
  border-left: 1px solid #c1c1c1;

  .detail {
    position: relative;
    margin-bottom: 25px;

    .point {
      position: absolute;
      top: 4px;
      left: -28px;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 16px;
      height: 16px;
      background-color: #fff;
      border-radius: 50%;

      .pointItem {
        width: 6px;
        height: 6px;
        background-color: #c1c1c1;
        border-radius: 50%;
      }
    }

    .detailTitle {
      display: flex;
      align-items: center;
      margin-bottom: 4px;
      color: rgb(0 0 0);
      opacity: 0.8;
    }

    .description {
      padding-left: 10px;
      font-size: 12px;
      color: rgb(0 0 0 / 50%);
    }

    .detailType {
      margin-bottom: 4px;
      color: rgb(0 0 0 / 60%);
    }

    .detailItem {
      margin-bottom: 4px;
      margin-left: 20px;
      color: rgb(0 0 0 / 80%);

      .oldVal {
        height: auto;
        color: rgb(0 0 0 / 40%);
        text-decoration: line-through;
        word-break: break-all;
      }

      .newVal {
        height: auto;
        color: #000;
        word-break: break-all;
      }

      .to {
        margin: 0 10px;
        color: rgb(0 0 0 / 40%);
      }
    }

    .detailTime {
      font-size: 12px;
      color: rgb(0 0 0 / 50%);
    }
  }
}

.emptyText {
  font-size: 14px;
  color: rgb(0 0 0 / 70%);
}
</style>
