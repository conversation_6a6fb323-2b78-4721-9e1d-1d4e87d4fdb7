<template>
  <a-select
    maxTagCount="responsive"
    :value="value"
    mode="multiple"
    :showArrow="true"
    :open="false"
    style="width: 100%"
    :placeholder="props.label"
    allowClear
    @click="showSelectDlg()"
    @change="handleChange"
  >
    <!-- 使用 tagRender 插槽显示 label -->
    <template #tagRender="{ value: val }">
      <a-tag style="margin-right: 3px">{{ getLabelByValue(val) }}&nbsp;&nbsp;</a-tag>
    </template>
  </a-select>
  <a-modal v-model:open="isShowDlg" :confirmLoading="confirmLoading" title="请选择" okText="确定" cancelText="取消" @ok="closeDlg" width="900px">
    <div>
      <slot name="topSlot"></slot>
    </div>
    <a-input-search v-model:value="searchText" placeholder="请输入" style="width: 400px" @search="getList" />
    <a-tabs v-model:activeKey="active">
      <a-tab-pane key="1" tab="全部">
        <div class="content-box">
          <a-checkbox v-model:checked="isCheckAll" :indeterminate="indeterminate" @change="onCheckAllChange">全选</a-checkbox>

          <a-checkbox-group class="mt-4" v-model:value="checkList" style="width: 100%">
            <a-row>
              <a-col class="mb-4" :span="8" v-for="(item, index) in list" :key="index">
                <a-checkbox :value="item.value" :key="item">{{ item.label }}</a-checkbox>
              </a-col>
            </a-row>
          </a-checkbox-group>
        </div>
        <a-pagination v-if="total" show-quick-jumper :total="total" :show-size-changer="false" v-model:current="page" :page-size="45" size="small" @change="getList">
          <template #buildOptionText="props">
            <span>{{ props.value }}条/页</span>
          </template>
        </a-pagination>
      </a-tab-pane>
      <a-tab-pane key="2" :tab="'已选项（' + checkList?.length + '）'">
        <div class="content-box">
          <a-checkbox-group v-model:value="checkList" style="width: 100%">
            <a-row>
              <a-col :span="8" v-for="(item, index) in saveData" :key="index">
                <a-checkbox :value="item.value" :key="item.value">{{ item.label }}</a-checkbox>
              </a-col>
            </a-row>
          </a-checkbox-group>
        </div>
      </a-tab-pane>
    </a-tabs>
  </a-modal>
</template>

<script lang="ts" setup>
import { GetSupplierOptions } from '@/servers/PaymentApply'

const props = defineProps({
  modelValue: {
    default: [],
  },
  label: {
    default: '供应商名称',
  },
  api: {
    type: Function,
    default: GetSupplierOptions,
  },
  formatter: {
    type: Function,
    default: (item) => ({
      label: item.value,
      value: item.key,
    }),
  },
  onBeforeConfirm: {
    type: Function,
  },
  keyword: {
    default: 'name',
  },
  pageSize: {
    default: 30,
  },
})

const searchText = ref()
onMounted(() => {
  getList()
})
const list = ref([] as any)
const saveData = computed(() => {
  return list.value.filter((v) => checkList.value.includes(v.value))
})

const total = ref(0)
const emit = defineEmits(['update:modelValue', 'clear'])

const getList = () => {
  props
    .api({
      [props.keyword]: searchText.value,
      PageSize: props.pageSize,
      Page: page.value,
    })
    .then((res) => {
      const data = (res.data?.list || res.data).map(props.formatter)

      total.value = res.data.total
      // 拼接上 saveData 并去重
      list.value = data.concat(
        saveData.value.filter((item) => {
          return !data.some((v) => v.value === item.value)
        }),
      )
    })
}

const checkList = ref([] as any)
const isCheckAll = computed(() => checkList.value.length === list.value.length && list.value.length > 0)
const indeterminate = computed(() => list.value.some((item) => checkList.value.find((v) => v === item) && !isCheckAll.value))
const isShowDlg = ref(false)
const confirmLoading = ref(false)
const page = ref(1)
const active = ref('1')
const showSelectDlg = async () => {
  active.value = '1'
  searchText.value = ''
  page.value = 1
  isShowDlg.value = true
  confirmLoading.value = false
  checkList.value = props.modelValue || []
  getList()
}

const closeDlg = async () => {
  confirmLoading.value = true
  const value = saveData.value.map((v) => v.value)
  emit('update:modelValue', value)
  if (props.onBeforeConfirm instanceof Function) {
    try {
      const fn = props.onBeforeConfirm(value)
      if ([0, false].includes(fn)) throw 'confirm failed'
      if (fn instanceof Promise) {
        const flag = await fn
        if (!flag) throw 'confirm Promise failed'
      }
    } catch (e) {
      console.warn(e)
      confirmLoading.value = false
      return
    }
  }
  isShowDlg.value = false
  confirmLoading.value = false
}
const onCheckAllChange = (e: any) => {
  checkList.value = e.target.checked ? list.value.map((item) => item.value) : []
}

const value = computed(() => (props.modelValue ? props.modelValue : []))

watch(
  () => props.modelValue,
  (val) => {
    if (!val) checkList.value = []
    else checkList.value = val
  },
)

// 获取 label 根据 value
const getLabelByValue = (val: string) => {
  const item = list.value.find((i) => i.value === val)
  return item ? item.label : val
}

const handleChange = (e) => {
  emit('update:modelValue', e)
  if (!e?.length) {
    emit('clear')
  }
}
</script>

<style lang="scss" scoped>
//
:deep(.ant-checkbox-wrapper) {
  width: 100%;

  .ant-checkbox {
    flex: none;
    flex-shrink: 0;
  }

  span {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.content-box {
  // h-400px overflow-auto mb-2
  max-height: 422px;
  margin-bottom: 8px;
  overflow: auto;
}

.mb-4 {
  margin-bottom: 16px;
}

.mt-4 {
  margin-top: 16px !important;
}
</style>
