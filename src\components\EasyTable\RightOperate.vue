<template>
  <div class="right-operate">
    <span class="right-operate-btn btn-hover" v-for="(item, index) in btnList" :key="index" @click="item.onClick({ row, column })">
      <span style="z-index: 2">{{ item.label }}</span>
    </span>

    <a-dropdown v-if="btnDropList.length" v-model:open="openFlag" placement="bottomRight">
      <span class="right-operate-btn">
        更多
        <DownOutlined class="ml-1" style="font-size: 10px; transition: all 0.2s" :style="{ transform: openFlag ? `rotate(180deg)` : undefined }" />
      </span>
      <template #overlay>
        <a-menu>
          <a-menu-item v-for="item in btnDropList" :key="item.label" @click="item.onClick({ row, column })">
            {{ item.label }}
          </a-menu-item>
        </a-menu>
      </template>
    </a-dropdown>
    <!-- <a-button v-for="(item, index) in formatList()" :key="index" :disabled="item.disabled" :class="{ 'ml-10': index !== 0 }" @click="item.onClick"></a-button> -->
  </div>
</template>

<script lang="ts" setup>
import { DownOutlined } from '@ant-design/icons-vue'
import type { VxeTableDefines } from 'vxe-table'

const btnPermission: any = inject('btnPermission')

type RightOperateBtnParamsType = {
  row: Record<string, any>
  column: VxeTableDefines.ColumnInfo
}

type RightOperateBtnPropType = {
  label: string | ((params: RightOperateBtnParamsType) => string)
  show?: number | number[] | ((params: RightOperateBtnParamsType) => boolean)
  disabled?: boolean | (() => boolean)
  onClick: (params: RightOperateBtnParamsType) => void
  tooltip?: string | ((params: RightOperateBtnParamsType) => string)
}

type RightOperatePropsType = {
  list: RightOperateBtnPropType[]
} & RightOperateBtnParamsType

const props = withDefaults(defineProps<RightOperatePropsType>(), {})

const btnList = ref<any[]>([])
const btnDropList = ref<any[]>([])

const openFlag = ref(false)

watch(
  [() => props.column?.renderWidth, () => props.row],
  () => {
    const list: any[] = []
    const list2: any[] = []
    // const width = 0
    // const moreWidth = 45
    // const fontSize = 12
    // const padding = 8
    // const margin = 12
    // const maxWidth = props.column.renderWidth - moreWidth

    const shouldShowList = props.list.filter(({ show }) =>
      typeof show === 'function' ? show({ row: props.row, column: props.column }) : show === undefined || [show].flat().find((f) => btnPermission[f as number]),
    )

    for (const [index, item] of Object.entries(shouldShowList)) {
      // const isLast = Number(index) === shouldShowList.length - 1
      const label = typeof item.label === 'function' ? item.label({ row: props.row, column: props.column }) : item.label
      // const itemWidth = label.length * fontSize + margin
      // const params = { ...item, label, show: undefined }

      // if (width + itemWidth > maxWidth) {
      //   if (isLast && maxWidth + moreWidth - width > itemWidth && list2.length === 0) {
      //     list.push(params)
      //   } else {
      //     list2.push(params)
      //   }
      // } else {
      //   list.push(params)
      // }
      // width += itemWidth

      const params = { ...item, label, show: undefined }
      if (Number(index) > 1) {
        list2.push(params)
      } else {
        list.push(params)
      }
    }
    btnList.value = list
    if (list2.length > 1) {
      btnList.value = list
      btnDropList.value = list2
    } else {
      btnDropList.value = []
      btnList.value = [...list, ...list2]
    }
  },
  { immediate: true },
)
</script>

<style lang="scss" scoped>
.right-operate {
  @apply flex items-center;

  &-btn {
    position: relative;
    display: inline-flex;
    align-items: center;
    height: 24px;

    // padding: 0 4px;
    margin-right: 12px;
    color: $color;
    white-space: nowrap;
    cursor: pointer;

    &::before {
      position: absolute;
      right: -4px;
      left: -4px;
      z-index: 1;
      display: block;
      height: 24px;
      content: '';
      border-radius: 4px;
    }

    &.btn-hover:hover::before {
      background: linear-gradient(0deg, rgb(255 255 255 / 86%), rgb(255 255 255 / 86%)), $color;
    }

    &:last-child {
      margin-right: 0;
    }
  }
}
</style>
