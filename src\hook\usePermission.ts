// import { checkedBtnPermission } from '@/utils'

export const usePermission = () => {
  const btnPermission = ref<any>({})

  const fetchPermissions = () => {
    const userData = localStorage.getItem('userData') as any
    const permissionInfo = JSON.parse(userData).permissions_infos
    const result = {}
    const deepFound = (arr: any[]) => {
      arr.forEach((f) => {
        if (f.btnList) {
          for (const btn of f.btnList) {
            result[btn.id] = true
            for (const btn2 of btn?.children || []) {
              result[btn2.id] = true
            }
          }
        } else {
          deepFound(f?.children || [])
        }
      })
    }
    deepFound(permissionInfo)
    btnPermission.value = result
  }

  onMounted(() => {
    fetchPermissions()
  })

  return {
    btnPermission,
  }
}
