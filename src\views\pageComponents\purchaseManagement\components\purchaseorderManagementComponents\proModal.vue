<template>
  <a-drawer v-model:open="open" title="选择商品" placement="right" width="75vw" :bodyStyle="{ padding: '0' }" destroyOnClose @close="CloseOpen">
    <div class="pageContent">
      <div class="pageContentBox">
        <div class="proRow">
          <a-input placeholder="商品编号/聚水潭编号" v-model:value="fromItem.product_sku_code" :maxlength="200" allowClear />
          <a-input placeholder="商品名称" v-model:value="fromItem.product_name" :maxlength="200" allowClear />
          <a-input placeholder="供应商名称" v-model:value="fromItem.k3_supplier_number" :maxlength="200" allowClear />
          <a-button @click="search">查询</a-button>
        </div>
        <div class="tableBox">
          <a-table :columns="columns" :data-source="data" :pagination="false" :row-selection="{ selectedRowKeys: state.selectedRowKeys, onChange: onSelectChange }">
            <template #bodyCell="{ column, text }">
              <template v-if="column.dataIndex === 'name'">
                <a>{{ text }}</a>
              </template>
              <template v-if="column.dataIndex === 'image_url'">
                <a-image :width="50" :src="text" :preview="{ maskClassName: '预览' }" referrerpolicy="no-referrer" />
              </template>
              <template v-if="column.dataIndex === 'category'">
                <span>{{ productTypeMap[text] }}</span>
              </template>
            </template>
          </a-table>
          <div class="pageBox">
            <a-pagination v-model:current="current" v-model:page-size="pageSize" :total="total" :show-total="(total, range) => `共${total}条`" show-quick-jumper @change="onChange" />
          </div>
        </div>
      </div>
      <div class="footerBox">
        <a-button @click="submitPro">确认</a-button>
        <a-button @click="CloseOpen">取消</a-button>
      </div>
    </div>
  </a-drawer>
</template>
<script setup lang="ts">
import { message } from 'ant-design-vue'

import { productTypeMap } from '@/common/map'

import { GetK3SkuInfo } from '@/servers/BusinessCommon'
import { GetPurchaseOrderProductInfo } from '@/servers/PurchaseManage'

type Key = string | number
const state = reactive<{
  selectedRowKeys: Key[]
  loading: boolean
}>({
  selectedRowKeys: [], // Check here to configure the default column
  loading: false,
})
const emit = defineEmits(['submitPro'])
const open = ref(false)
const fromItem = ref({ product_sku_code: '', product_name: '', k3_supplier_number: '' })

const columns = ref([
  {
    dataIndex: 'category',
    key: 'category',
    title: '商品分类',
    width: 120,
  },
  {
    dataIndex: 'image_url',
    key: 'image_url',
    title: '商品主图',
    width: 120,
  },
  {
    dataIndex: 'sku_id',
    key: 'sku_id',
    title: '商品编码',
    width: 120,
  },
  {
    dataIndex: 'sku_name',
    key: 'sku_name',
    title: '商品名称',
    width: 120,
  },
  {
    dataIndex: 'type_specification',
    key: 'type_specification',
    title: '规格型号',
    width: 120,
  },
  {
    dataIndex: 'style_code',
    key: 'style_code',
    title: '款式编码',
    width: 120,
  },
  {
    dataIndex: 'material_name',
    key: 'material_name',
    title: '材质',
    width: 120,
  },
  {
    dataIndex: 'conversion_value',
    key: 'conversion_value',
    title: '换算值',
    width: 120,
  },
  {
    dataIndex: 'conversion_formula',
    key: 'conversion_formula',
    title: '换算公式',
    width: 120,
  },
  {
    dataIndex: 'valuation_unit',
    key: 'valuation_unit',
    title: '采购单位',
    width: 120,
  },
  {
    dataIndex: 'packing_qty',
    key: 'packing_qty',
    title: '标准装箱数',
    width: 120,
  },
  {
    dataIndex: 'jst_sku_id',
    key: 'jst_sku_id',
    title: '聚水潭编号',
    width: 120,
  },
  {
    dataIndex: 'company_supplier_name',
    key: 'company_supplier_name',
    title: '默认供应商',
    width: 120,
  },
])
const current = ref<number>(1)
const pageSize = ref<number>(20)
const total = ref(20)
const data = ref([])
const selectTable = ref([])
const company_supplier_id = ref()

const onSelectChange = (selectedRowKeys: Key[]) => {
  selectTable.value = []
  data.value.forEach((item) => {
    if (selectedRowKeys.indexOf((item as any).key) > -1) {
      selectTable.value.push(item)
    }
  })
  state.selectedRowKeys = selectedRowKeys
}
const search = () => {
  state.selectedRowKeys = []
  current.value = 1
  getdata()
}
const setModel = (bol, id) => {
  open.value = bol
  company_supplier_id.value = id
  current.value = 1
  pageSize.value = 20
  fromItem.value = { product_sku_code: '', product_name: '', k3_supplier_number: '' }
  getdata()
}
const getdata = async () => {
  const params = {
    page: current.value,
    pageSize: pageSize.value,
    product_sku_code: fromItem.value.product_sku_code,
    product_name: fromItem.value.product_name,
    k3_supplier_number: fromItem.value.k3_supplier_number,
  }
  const res = await GetK3SkuInfo(params)
  console.log('res', res)
  if (res.success) {
    const resdata = res.data
    total.value = resdata.total
    resdata.list.forEach((item, index) => {
      item.key = index + 1
      item.key = item.key.toString()
    })
    data.value = resdata.list
    console.log('total', total.value)
    console.log('data.value', data.value)
  }
}

const onChange = (pageNumber: number) => {
  console.log('Page: ', pageNumber)
  state.selectedRowKeys = []
  selectTable.value = []
  current.value = pageNumber
  getdata()
}
const submitPro = async () => {
  console.log('selectTable.value', selectTable.value)
  const paramList: any[] = []
  selectTable.value.forEach((item) => {
    const obj: any = {
      k3_sku_id: (item as any).sku_id,
      jst_sku_id: (item as any).jst_sku_id,
      supplier_compeny_id: company_supplier_id.value,
    }
    paramList.push(obj)
  })
  const params = {
    paramList,
  }
  const res = await GetPurchaseOrderProductInfo(params)
  console.log('res', res)
  if (res.success) {
    state.selectedRowKeys = []
    selectTable.value = []
    open.value = false
    emit('submitPro', res.data)
  } else {
    message.info(res.message)
  }
}
const CloseOpen = () => {
  state.selectedRowKeys = []
  selectTable.value = []
  open.value = false
}
defineExpose({ setModel })
</script>
<style scoped lang="scss">
.pageContent {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  height: 100%;
  font-size: 12px;
  color: #000;

  .pageContentBox {
    width: 100%;
    height: calc(100vh - 120px);
    padding-right: 24px;
    padding-left: 24px;
    overflow: hidden;
    overflow-y: auto;

    .proRow {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      width: 60%;
      margin-top: 24px;
      margin-bottom: 20px;

      .ant-input {
        margin-right: 20px;
      }
    }

    .tableBox {
      width: 100%;

      .pageBox {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        width: 100%;
        margin-top: 20px;
      }
    }
  }

  .footerBox {
    position: absolute;
    bottom: 0;
    left: 0;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    width: 100%;
    height: 60px;
    padding-left: 24px;
    background: #fff;
    border-top: 1px solid #dedede;

    .ant-btn {
      margin-right: 20px;
    }
  }
}
</style>
