<template>
  <div class="flex flex-col h-full main">
    <Form ref="formRef" v-model:form="formArr" :page-type="PageTypeEnum.PurchaseApply" @search="tableRef?.search()" @setting="tableRef?.showTableSetting()">
      <template #labelTop>
        <a-radio-group class="mb-20px" v-model:value="tagsSearchType">
          <a-radio value="1">包含任意一个标签</a-radio>
          <a-radio value="2">同时包含指定标签</a-radio>
        </a-radio-group>
      </template>
      <template #header>
        <StatusTabs v-model:status="status" :options="statusList" @change="tabChange" />
      </template>
      <template #formTop>
        <div class="inline-flex items-center text-#333 text-12 mb-10" v-if="status === '3'">
          <span class="font-bold w-50 mr-4">采购方式</span>
          <div class="easy-tabs-single">
            <span
              v-for="tag in [
                { label: '待合并采购', value: '1', code: 'await_count' },
                { label: '其余待采购', value: '2', code: 'other_count' },
              ]"
              :key="tag.value"
              class="easy-tabs-single-btn"
              @click="tagTypeChange(tag.value)"
              :class="{ current: purchaseType === tag.value }"
            >
              <span>{{ tag.label }}</span>
              <span class="count" v-if="countMap[tag.code]">{{ countMap[tag.code] }}</span>
            </span>
          </div>
        </div>
        <div class="flex items-center text-#333 text-12 mb-10" v-if="status === '4'">
          <span class="font-bold w-50 mr-4">审核进度</span>
          <div class="easy-tabs-single">
            <span
              v-for="tag in [
                { label: '待一级审核', value: '20', code: 'one_auditing_count' },
                { label: '已通过', value: '90' },
                { label: '已拒绝', value: '95' },
              ]"
              :key="tag.value"
              class="easy-tabs-single-btn"
              @click="tagTypeChange(tag.value)"
              :class="{ current: auditStatus === tag.value }"
            >
              <span>{{ tag.label }}</span>
              <span class="count" v-if="labelStatusCountMap[tag.code as string]">{{ labelStatusCountMap[tag.code as string] }}</span>
            </span>
          </div>
        </div>
        <div class="w-1 h-12 m-ie-12 m-is-12 mt-7 bg-[#dcdcdc]" v-if="status === '3' || status === '4'"></div>
      </template>
    </Form>

    <BaseTable
      ref="tableRef"
      :page-type="PageTypeEnum.PurchaseApply"
      v-model:form="formArr"
      :isCheckbox="true"
      :keyField="'detail_id'"
      :get-list="GetPurchaseApplyOrderList"
      :form-format="formatData"
      :autoSearch="false"
      :hidePagination="hidePagination"
    >
      <template #left-btn>
        <a-space>
          <a-button type="primary" @click="addPurchaseOrder">生成采购单</a-button>
          <a-button type="primary" v-if="btnPermission[83008]" @click="batchAddPurchaseOrder" :disabled="!tableRef?.checkItemsArr?.length" :loading="batchAddPurchaseOrderLoading">
            批量生成采购单
          </a-button>
        </a-space>
      </template>
      <template #right-btn>
        <a-button v-if="btnPermission[83002]" type="primary" @click="handleShowAdd">添加申请单</a-button>
      </template>
      <!-- <template #image_url="{ row }">
        <EasyImage :src="row.image_url"></EasyImage>
      </template> -->
      <template #remark="{ row, column }">
        <component :is="remarkRender({ column, row })"></component>
      </template>
      <!-- <template #warehouse_name="{ row }">
        <span>{{ warehouseOption.find((item) => item.key === row.warehouse_id)?.value }}</span>
      </template> -->
      <template #fix_option="{ row, column }">
        <RightOperate :list="rightOperateList" :row="row" :column="column" />
      </template>
    </BaseTable>

    <DetailDrawer ref="detailDrawerRef" @refresh="tableRef?.search()"></DetailDrawer>

    <!-- <price-drawer ref="priceDrawerRef" @query="tableRef?.search()" /> -->
    <BatchEditModal ref="BatchEditModalRef"></BatchEditModal>
  </div>
</template>

<script setup lang="ts">
import { message, Modal, Input } from 'ant-design-vue'

import { DetailTypeEnum } from '@/common/enum'
import { Enum2Options, generateUUID } from '@/utils/index'
import { textRender } from '@/utils/VxeRender'
import { productTypeEnum } from '@/types/enums'
import { PurchaseApplyOrderStatusEnum, PurchaseOrderAuditStatusEnum } from '@/types/Purchase'

import BatchEditModal from './components/BatchEditModal.vue'
import DetailDrawer from './components/DetailDrawer.vue'

import { GetPurchaseApplyOrderList, GetWarehouses, GetApplyers, OpenOrClosePurchaseApplyOrder, GetPurchaseMethodCount, BatchUpdateRemark, BatchAddPurchaseOrder } from '@/servers/Purchaseapplyorder'
import {
  GetPageSuppliersSelect,
  GetPageMRPSupplierCompanySelect,
  GetPLMMachiningType,
  GetSupplierCategorySelect,
  GetPurchaseApplyOrderTagsSelect,
  AllProductCategorySelect,
} from '@/servers/BusinessCommon'

import { PageTypeEnum } from '@/enums/tableEnum'

const router = useRouter()
const detailDrawerRef = ref()

const { btnPermission } = usePermission()
const { labelStatusCountMap, labelStatusRefresh } = useLabelStatus(PageTypeEnum.PurchaseApply)

const warehouseOption = ref<any[]>([])

const tableRef = ref()
const formRef = ref()

const status = ref('3')
const purchaseType = ref()
const auditStatus = ref()
const tagsSearchType = ref<'1' | '2' | null>('1')

usePage({ refresh: () => tableRef.value.search() })
const statusList = computed(() => {
  return [
    { value: '1', label: '待提审', count: labelStatusCountMap.value.await_arraigned_count },
    { value: '2', label: '审核中', count: labelStatusCountMap.value.one_auditing_count },
    { value: '3', label: '待采购', count: countMap.value.await_count + countMap.value.other_count },
    { value: '4', label: '全部' },
  ]
})

const formArr = ref([
  { label: '申请单编号', value: null, type: 'inputDlg', key: 'number' },
  // { label: '采购单编号', value: null, type: 'inputDlg', key: 'purchase_order_numbers' },
  { label: '聚水潭编号', value: null, type: 'inputDlg', key: 'jst_sku_ids' },
  { label: 'MRP计划单号', value: null, type: 'inputDlg', key: 'external_ids' },
  { label: '商品编码', value: null, type: 'inputDlg', key: 'k3_sku_id' },
  { label: '成品计划单号', value: null, type: 'inputDlg', key: 'finished_external_ids' },
  { label: '成品申请单号', value: null, type: 'inputDlg', key: 'finished_numbers' },
  { label: '商品名称', value: null, type: 'input', key: 'sku_name' },
  { label: 'SRS平台商品编码', value: null, type: 'input', key: 'srs_platform_prod_code' },
  {
    label: '供应商',
    value: undefined,
    type: 'select-supplier',
    key: 'supplier_id',
    // selectInitArr: [{ label: '无', value: '0' }],
    mode: 'multiple',
    api: GetPageSuppliersSelect,
  },
  {
    label: '供应商子公司',
    value: undefined,
    type: 'select-supplier',
    key: 'company_supplier_id',
    mode: 'multiple',
    api: GetPageMRPSupplierCompanySelect,
    apiParams: { is_contains_srs: true },
  },
  {
    label: '仓库',
    value: [],
    type: 'select',
    key: 'warehourse_ids',
    multiple: true,
    selectArr: [],
    api: GetWarehouses,
    isQuicks: true,
    quickIndex: 4,
    line: true,
    useFixedQuicks: true, // 使用固定快捷筛选选项
    quicks: [
      { label: '皓物供应链', value: 14938157 },
      { label: 'PMC专用仓-H13', value: 12590060 },
      { label: 'PMC专用仓-德吉清', value: 14393133 },
      { label: 'PMC专用仓-简素净', value: 14670898 },
      { label: 'PMC专用仓-开达', value: 12590255 },
    ],
    defaultQuickValue: ['14938157', '12590060', '14393133', '14670898', '12590255'],
  },
  {
    label: '商品分类',
    value: [],
    type: 'select_Tree',
    key: 'm_group_ids',
    api: AllProductCategorySelect,
    multiple: true,
    fieldNames: { label: 'value', value: 'key', children: 'children' },
    isQuicks: true,
    quickIndex: 5,
    line: true,
    quickLabelWidth: 50,
    quicks: [
      { label: '纸盒', value: 107068 },
      { label: '标签', value: 107043 },
      { label: '袋子', value: 107057 },
      { label: '软管', value: 107046 },
    ],
  },
  {
    label: '供应商分类',
    value: [],
    type: 'select',
    key: 'supplier_categorys',
    selectArr: [],
    multiple: true,
    api: GetSupplierCategorySelect,
  },
  {
    label: '商品类型',
    value: [],
    type: 'select',
    key: 'categorys',
    selectArr: Enum2Options(productTypeEnum),
    isQuicks: true,
    quickIndex: 1,
    multiple: true,
    line: true,
    quickLabelWidth: 50,
  },
  {
    label: '加工方式',
    key: 'process_type',
    type: 'select',
    multiple: true,
    isQuicks: true,
    quickIndex: 2,
    api: GetPLMMachiningType,
    line: true,
    quickLabelWidth: 50,
  },
  {
    label: '选择申请人',
    value: [],
    type: 'select',
    multiple: true,
    key: 'applicant_id',
    selectArr: [],
    api: GetApplyers,
  },
  {
    label: '单据状态',
    value: [],
    type: 'select',
    key: 'order_status',
    selectArr: Enum2Options(PurchaseApplyOrderStatusEnum),
    isQuicks: true,
    quickIndex: 3,
    multiple: true,
    quickLabelWidth: 50,
  },
  {
    label: '时间',
    value: null,
    type: 'range-picker',
    key: 'create_at',
    formKeys: ['start_time', 'end_time'],
    placeholder: ['申请开始时间', '申请结束时间'],
  },
  {
    label: '成品申请单来源',
    value: [],
    type: 'select',
    key: 'finished_source_types',
    multiple: true,
    selectArr: Enum2Options(FinishedApplyOrderSourceTypeEnum),
  },
  // { label: '标签', value: null, type: 'input', key: 'tag' },
  {
    label: '标签',
    value: [],
    multiple: true,
    type: 'supplier',
    selectArr: [],
    key: 'tags',
    topSlot: 'labelTop',
    api: GetPurchaseApplyOrderTagsSelect,
    keyword: 'keyword',
    pageSize: 9999,
    // isQuicks: true,
    line: true,
    quickLabelWidth: 50,
    formatter: (item) => ({
      label: item.label,
      value: item.value,
    }),
    onClear: () => {
      tagsSearchType.value = null
    },
    onBeforeConfirm: (value) => {
      let msg = ''
      if (!tagsSearchType.value) msg = '请选择包含标签方式'
      if (!value?.length) msg = '请选择商品标签'
      if (msg) {
        message.warn(msg)
        return false
      }
    },
  },
  { label: '备注', value: null, type: 'input', key: 'remark' },

  {
    label: '周转天数',
    value: [],
    type: 'number-range',
    key: 'turnover_days',
    placeholder: ['开始周转天数', '结束周转天数'],
    formKeys: ['start_turnover_days', 'end_turnover_days'],
  },
  {
    label: '可售库存',
    value: [],
    type: 'number-range',
    key: 'saleable_inventory',
    placeholder: ['开始可售库存', '结束可售库存'],
    formKeys: ['start_saleable_inventory', 'end_saleable_inventory'],
  },
  {
    label: '缺货天数',
    value: [],
    type: 'number-range',
    key: 'stockout_days',
    placeholder: ['开始缺货天数', '结束缺货天数'],
    formKeys: ['start_stockout_days', 'end_stockout_days'],
  },
  {
    label: '缺货数量',
    value: [],
    type: 'number-range',
    key: 'stockout_quantity ',
    placeholder: ['开始缺货数量', '结束缺货数量'],
    formKeys: ['start_stockout_quantity', 'end_stockout_quantity'],
  },
])
useSearchForm(formArr)

const tabChange = () => {
  purchaseType.value = status.value === '3' ? '1' : undefined
  setTimeout(() => {
    tableRef.value?.search()
  }, 0)
}
const tagTypeChange = (value) => {
  let auditStatusValue = value
  if (auditStatus.value === value) {
    auditStatusValue = undefined
  }
  if (status.value === '3') {
    purchaseType.value = value
  } else {
    purchaseType.value = undefined
  }
  if (status.value === '4') {
    auditStatus.value = auditStatusValue
  } else {
    auditStatus.value = undefined
  }
  tableRef.value?.search()
}

const hidePagination = computed(() => purchaseType.value === '1')
const formatData = (data: any) => {
  labelStatusRefresh()
  getPurchaseMethodCount()

  return {
    ...data,
    query_type: status.value,
    type: purchaseType.value,
    audit_status: auditStatus.value,
    tag_query_type: tagsSearchType.value ? Number(tagsSearchType.value) : null,
  }
}

const handleShowAdd = () => {
  detailDrawerRef.value?.open(DetailTypeEnum.ADD)
}

const handleShowDetail = (id: string, type: DetailTypeEnum) => {
  detailDrawerRef.value?.open(type, id)
}
// 获取仓库选项
const getWarehouseOption = async () => {
  try {
    const res = await GetWarehouses()
    const allWarehouses = res.data.map((i) => ({ label: i.value, value: i.key })) || []
    warehouseOption.value = allWarehouses
    const item = formArr.value.find((item) => item.key === 'warehourse_ids')
    if (item) {
      // 只更新下拉列表的选项，不影响快捷筛选的固定选项
      item.selectArr = allWarehouses as any[]
    }
  } catch (error) {
    console.error('获取仓库数据失败:', error)
  }
}

// 添加采购订单
const BatchEditModalRef = ref()

// 批量生成采购单
const batchAddPurchaseOrderLoading = ref(false)
const batchAddPurchaseOrder = async () => {
  // 防抖：如果正在加载中，直接返回
  if (batchAddPurchaseOrderLoading.value) {
    return
  }

  const items = tableRef.value.checkItemsArr
  if (!items?.length) {
    message.error('请选择要批量生成采购单的数据')
    return
  }

  batchAddPurchaseOrderLoading.value = true

  try {
    const applyOrderIds = items.map((item) => item.id)
    const res = await BatchAddPurchaseOrder({ apply_order_ids: applyOrderIds })

    if (res.success) {
      message.success(res.data)
      tableRef.value?.search() // 刷新列表
    } else {
      message.error(res.message || '批量生成采购单失败')
    }
  } catch (error) {
    console.error('批量生成采购单失败:', error)
    message.error('批量生成采购单失败')
  } finally {
    batchAddPurchaseOrderLoading.value = false
  }
}

const addPurchaseOrder = () => {
  const items = tableRef.value.checkItemsArr
  const [first, ...other] = items
  const count = items.length
  if (!count) return message.error('请选择生成采购单数据')

  let errMsg = ''
  const hasSRSCount = items.filter((f) => /SRS/g.test(f.finished_source_types?.[0])).length
  // console.log('hasSRSCount', hasSRSCount, items)
  items.some((item) => {
    if (item.audit_status != PurchaseOrderAuditStatusEnum.已通过) {
      errMsg = '审核状态非已通过,请重新选择'
      return true
    }
    if (item.quantity <= item.purchase_quantityed) {
      errMsg = '存在已无可执行数量的申请单,请重新选择'
      return true
    }
    if (item.order_status == PurchaseApplyOrderStatusEnum.已关闭) {
      errMsg = '存在已关闭的申请单,请重新选择'
      return true
    }
    if (item.order_type !== first.order_type) {
      errMsg = '申请类型必须相同,请重新选择'
      return true
    }
    if (item.order_detail_process_type_name !== first.order_detail_process_type_name) {
      errMsg = '加工方式必须相同,请重新选择'
      return true
    }
    if (item.supplier_source_type !== first.supplier_source_type) {
      errMsg = '供应商来源必须相同,请重新选择'
      return true
    }
    if (hasSRSCount) {
      if (first.warehouse_id !== item.warehouse_id || first.company_supplier_id !== item.company_supplier_id || hasSRSCount !== count) {
        errMsg = 'SRS来源的申请单，不允许预处理，请选择相同供应商、仓库的申请单'
        return true
      }
    }
    return false
  })
  console.log(errMsg)
  if (errMsg) return message.error(errMsg)

  const editArr: string[] = []
  // 单个或者所有都一致则不需打开预处理
  const supplierLen = count === 1 || (first.company_supplier_id && other.every((f) => f.company_supplier_id === first.company_supplier_id))
  if (!supplierLen) {
    // message.error('供应商子公司不一致,请重新选择')
    editArr.push('company_supplier_id')
  }
  const warehouseLen = count === 1 || (first.warehouse_id && other.every((f) => f.warehouse_id === first.warehouse_id))
  if (!warehouseLen) {
    // message.error('仓库不一致,请重新选择')
    editArr.push('warehouse_id')
  }

  if (editArr.length > 0) {
    BatchEditModalRef.value.open(editArr, items)
    return
  }

  const ids: any[] = []
  items.forEach((item) => {
    ids.push({
      apply_order_id: item.id,
      apply_order_detail_id: item.detail_id,
      k3_sku_id: item.k3_sku_id,
    })
  })
  const list = JSON.parse(localStorage.getItem('srm_apply_purchase') || '{}')
  const uuid = generateUUID()
  localStorage.setItem(
    'srm_apply_purchase',
    JSON.stringify({
      ...list,
      [uuid]: ids,
    }),
  )

  router.push(`/purchasebatchorder/${uuid}`)
}

// 关闭订单
const CloseOrder = (item, bol) => {
  Modal.confirm({
    title: bol ? '确认是否开启当前订单?' : '确认是否关闭当前订单?',
    icon: () => {},
    content: '',
    async onOk() {
      const params = {
        id: item.id,
        isClose: !bol,
      }
      const res = await OpenOrClosePurchaseApplyOrder(params)
      console.log('res', res)
      if (res.success) {
        tableRef.value.search()
        message.success('操作成功')
      } else {
        message.error(res.message)
      }
    },
    // eslint-disable-next-line @typescript-eslint/no-empty-function
    onCancel() {},
  })
  console.log('item', item)
}

const countMap = ref<Record<string, number>>({})
const getPurchaseMethodCount = async () => {
  GetPurchaseMethodCount({}).then((res) => {
    countMap.value = res.data
  })
}
const remarkRender = ({ column, row }: { column: any; row: any; parentRow?: any }) => {
  const icon = 'iconfont icon-edit link text-14px!'
  return textRender(null, {
    column,
    row,
    options: {
      textFormat: ({ cellValue }) => cellValue,
      icon,
      iconClick: ({ row, column, cellValue }) => {
        const remark = ref(cellValue)
        Modal.confirm({
          title: '修改备注',
          icon: () => {},
          content: () =>
            h(Input.TextArea, {
              value: remark.value,
              placeholder: '请输入内容',
              rows: 4,
              autofocus: true,
              onInput: (e) => {
                remark.value = e.target.value
              },
            }),
          onOk: async () => {
            const api = BatchUpdateRemark
            await api({ type: 1, remark: remark.value, applyOrderIds: [row.id] })
            message.success('修改成功')
            row[column.field] = remark.value
          },
          okText: '保存',
          cancelText: '取消',
          width: 500,
          maskClosable: true,
          closable: true,
        })
      },
    },
  })
}
const rightOperateList = ref([
  {
    label: '查看',
    show: 83001,
    onClick: ({ row }) => {
      handleShowDetail(row.id, DetailTypeEnum.VIEW)
    },
  },
  {
    label: '开启订单',
    show: ({ row }) => {
      return [PurchaseApplyOrderStatusEnum.已关闭].includes(row.order_status)
    },
    onClick: ({ row }) => {
      CloseOrder(row, true)
    },
  },
  {
    label: '关闭订单',
    show: ({ row }) => {
      return [PurchaseApplyOrderStatusEnum.进行中, PurchaseApplyOrderStatusEnum.待开始].includes(row.order_status) && PurchaseOrderAuditStatusEnum.已通过 === row.audit_status
    },
    onClick: ({ row }) => {
      CloseOrder(row, false)
    },
  },
  {
    label: '审核',
    show: ({ row }) => {
      return [PurchaseOrderAuditStatusEnum.待一级审核, PurchaseOrderAuditStatusEnum.待二级审核].includes(row.audit_status) && btnPermission.value[83004]
    },
    onClick: ({ row }) => {
      handleShowDetail(row.id, DetailTypeEnum.AUDIT)
    },
  },
  {
    label: '编辑',
    show: ({ row }) => {
      return [PurchaseOrderAuditStatusEnum.已拒绝, PurchaseOrderAuditStatusEnum.待提审].includes(row.audit_status) && btnPermission.value[83003] && row.is_update
    },
    onClick: ({ row }) => {
      handleShowDetail(row.id, DetailTypeEnum.EDIT)
    },
  },
])
onMounted(() => {
  getWarehouseOption()
  tabChange()
})
</script>

<style scoped lang="scss"></style>
