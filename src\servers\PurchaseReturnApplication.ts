import { request } from './request'

// 获取退库申请列表
export const GetPurchaseRetrueApplications = (data: any) => {
  return request({ url: '/api/PurchaseReturnApplication/GetPurchaseRetrueApplications', data })
}

// 添加退库申请
export const AddPurchaseReturnApplication = (data: any) => {
  return request({ url: '/api/PurchaseReturnApplication/AddPurchaseReturnApplication', data })
}

// 修改退库申请
export const UpdatePurchaseReturnApplication = (data: any) => {
  return request({ url: '/api/PurchaseReturnApplication/UpdatePurchaseReturnApplication', data })
}

// 获取退库申请详情
export const GetPurchaseRetrueApplicationDetail = (data: any) => {
  return request({ url: '/api/PurchaseReturnApplication/GetPurchaseRetrueApplicationDetail', data })
}

// 查询采购单
export const GetPurchaseOrderList = (data: any) => {
  return request({ url: '/api/PurchaseReturnApplication/GetPurchaseOrderList', data })
}

// 查询商品
export const GetPurchaseReturnSkuDetail = (data: any) => {
  return request({ url: '/api/PurchaseReturnApplication/GetPurchaseReturnSkuDetail', data })
}

// 审核退库申请
export const BatchAudit = (data: any) => {
  return request({ url: '/api/PurchaseReturnApplication/BatchAudit', data })
}

// 退库申请单申请人
export const GetRetrueCreaterNameList = () => {
  return request({ url: '/api/PurchaseReturnApplication/GetRetrueCreaterNameList' }, 'GET')
}

// 根据供应商id获取地址
export const SupplierAddressInfo = (data: any) => {
  return request({ url: `/api/BusinessCommon/SupplierAddressInfo?company_supplier_id=${data}` }, 'GET')
}

// 获取仓库
export const GetWarehouses = () => {
  return request({ url: '/api/PurchaseReturnApplication/GetWarehouses' }, 'GET')
}

// 获取审核记录
export const GetAuditRecord = (data) => {
  return request({ url: '/api/BusinessCommon/GetAuditRecord', data })
}

// 财务修改退库申请
export const FinanceUpdatePurchaseReturnApplication = (data) => {
  return request({ url: '/api/PurchaseReturnApplication/FinanceUpdatePurchaseReturnApplication', data })
}
