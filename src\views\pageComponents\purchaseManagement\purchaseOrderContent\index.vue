<template>
  <div class="flex flex-col h-full main">
    <EasyForm v-if="type" :mode="mode" ref="formRef" :formItems="formItems" v-model:form="formData">
      <!-- 明细信息 -->
      <template #details>
        <div class="relative">
          <a-button class="absolute top-10 right-0 z-10" :icon="h(SettingOutlined)" @click="handleSetting" type="text">设置</a-button>
          <a-tabs v-model:activeKey="activeTabKey" class="mb-20 custom-tabs" @change="handleActiveChange">
            <a-tab-pane key="details" tab="采购明细">
              <div>
                <a-button class="mr-10 mb-20" @click="handleAddPro" :loading="addProLoading" v-if="type === 'new' && btnPermission[84013]" type="primary">添加商品</a-button>
              </div>
              <BaseTable ref="tableRef" v-bind="tableConfig" :data="tableData">
                <template #number="{ row, column, rowIndex }" v-if="mode !== 'detail'">
                  <a-input-number
                    class="order-input-number w-full"
                    v-model:value="row[column.field]"
                    placeholder="请输入"
                    :controls="false"
                    :precision="column.params?.precision ?? 0"
                    :min="0"
                    :step="0.01 * Math.pow(10, -(column.params?.precision ?? 0))"
                    :disabled="
                      (['gift_purchase_quantity', 'process_fee', 'mould_fees', 'discount_amount'].includes(column.field) && isSRS) ||
                      (calculatePriceIndex && calculatePriceIndex?.[0] !== `${rowIndex}`)
                    "
                    @change="changePrice(column.field, row, rowIndex)"
                  >
                    <template #addonAfter v-if="calculatePriceIndex?.[0] === `${rowIndex}` && calculatePriceIndex?.[1] === `${column.field}`">
                      <LoadingOutlined class="link text-14px" />
                    </template>
                  </a-input-number>
                </template>
                <template #upPice="{ row, column }">
                  <div class="upPice">
                    <div>{{ row[column.field] }}</div>
                    <a-button @click="setHistory(row)" type="link">查看历史</a-button>
                  </div>
                </template>
                <template #predict_delivery_date="{ row, column }" v-if="type !== 'review'">
                  <a-date-picker :valueFormat="'YYYY-MM-DD'" format="YYYY-MM-DD" v-model:value="row[column.field]" style="width: 100%" />
                </template>
                <template #remark="{ row, column }" v-if="type !== 'review'">
                  <a-input v-model:value="row[column.field]" placeholder="请输入" :maxlength="2000" />
                </template>
                <template #tax_rate_id="{ row, column, rowIndex }">
                  <div v-if="mode === 'detail' && formData.order_status !== 2">
                    {{ (taxRateOption || []).find((f) => f.value === row['tax_rate_id'])?.label }}
                  </div>
                  <a-select v-else v-model:value="row[column.field]" :options="taxRateOption" style="width: 100px" @change="changePrice(column.field, row, rowIndex)"></a-select>
                </template>

                <template #operation="{ row, rowIndex }">
                  <a-button v-if="type === 'new' && !row.has_take_quantity" class="ml mr" @click="deleteItem(row, rowIndex)">删除</a-button>
                  <a-button v-if="$route.params.id" @click="openChildrenList(row)">拍单子表</a-button>
                </template>
              </BaseTable>
            </a-tab-pane>

            <a-tab-pane key="booking" tab="入库明细">
              <vxe-grid ref="bookingGridRef" v-bind="bookingGridOptions"></vxe-grid>
            </a-tab-pane>

            <a-tab-pane key="payment" tab="付款明细" v-if="!hidePaymentTab">
              <div class="mb-20">
                <a-button class="mr-10" type="primary" @click="handleAddPrepayment" :disabled="type === 'new' || !canAddPrepayment || formData.audit_status !== 90">新增预付款</a-button>
              </div>
              <a-table :columns="paymentColumns.filter((item) => (item as any).is_show)" :data-source="paymentTableData" :pagination="false" size="small" :scroll="{ x: 1750, y: 700 }">
                <template #bodyCell="{ column, record, index }">
                  <template v-if="column.key === 'key'">{{ index + 1 }}</template>
                  <template v-if="column.key === 'payment_amount'">
                    <span>{{ Number(record[column.dataIndex as string]).roundNext(2) }}</span>
                  </template>
                  <template v-if="column.key === 'payment_time'">
                    <span>{{ record[column.dataIndex as string] ? dayjs(record[column.dataIndex as string]).format('YYYY-MM-DD') : '' }}</span>
                  </template>
                </template>
              </a-table>
            </a-tab-pane>

            <a-tab-pane key="refund" tab="退库明细">
              <a-table :columns="refundColumns.filter((item) => (item as any).is_show)" :data-source="refundTableData" :pagination="false" size="small" :scroll="{ x: 1750, y: 700 }">
                <template #bodyCell="{ column, record, index }">
                  <template v-if="column.key === 'image_url'">
                    <EasyImage :src="record[column.dataIndex as string]"></EasyImage>
                  </template>
                  <template v-if="column.key === 'key'">{{ index + 1 }}</template>
                  <template v-if="column.key === 'refund_amount'">
                    <span>{{ Number(record[column.dataIndex as string]).roundNext(2) }}</span>
                  </template>
                  <template v-if="column.key === 'refund_time'">
                    <span>{{ record[column.dataIndex as string] ? dayjs(record[column.dataIndex as string]).format('YYYY-MM-DD') : '-' }}</span>
                  </template>
                  <template v-if="column.key === 'refund_status'">
                    <a-tag :color="record[column.dataIndex as string] === 1 ? 'green' : 'orange'">
                      {{ record[column.dataIndex as string] === 1 ? '已退款' : '退款中' }}
                    </a-tag>
                  </template>
                </template>
              </a-table>
            </a-tab-pane>
          </a-tabs>
        </div>
      </template>
    </EasyForm>
    <div class="mb-50">&nbsp;</div>
    <div class="submit-row" v-if="initFlag">
      <!-- 新增 & 编辑 & 批量创建 -->
      <template v-if="type == 'new'">
        <a-button class="ml mr" :disabled="!!calculatePriceIndex" :loading="submitLoading" type="primary" @click="handleSubmit(true)">提交审核</a-button>
        <a-button class="ml mr" :disabled="!!calculatePriceIndex" :loading="submitLoading" @click="handleSubmit(false)">保存暂不提交</a-button>
        <template v-if="formData.id && [10, 95].includes(formData.audit_status)">
          <a-button class="ml mr" @click="reviewType()">审核记录</a-button>
          <a-button class="ml mr" @click="openLog">日志</a-button>
        </template>
      </template>
      <!-- 查看 -->
      <template v-if="type == 'detail'">
        <a-button class="ml mr" :disabled="!!calculatePriceIndex" :loading="submitLoading" @click="handleSubmitByDetail()">保存文字修改</a-button>
        <a-button class="ml mr" @click="reviewType()">审核记录</a-button>
        <a-button class="ml mr" @click="openLog">日志</a-button>
      </template>
      <!-- 审核 -->
      <template v-if="type == 'review'">
        <a-button class="ml mr" @click="reviewType(1)">审核通过</a-button>
        <a-button class="ml mr" @click="reviewType(2)">审核拒绝</a-button>
        <a-button class="ml mr" @click="reviewType()">审核记录</a-button>
        <a-button class="ml mr" @click="openLog">日志</a-button>
      </template>
    </div>
    <SelectProduct ref="selectProductRef" @confirm="onAddProduct" />
    <historyModal ref="historyModalRef"></historyModal>
    <childrenListDlg ref="ChildrenListDlgRef" />
    <reviewTimer ref="reviewTimerRef"></reviewTimer>

    <AuditConfirm ref="auditConfirmRef" @audit="onAuditConfirm" />

    <SelectSupplier ref="selectSupplierRef" hide />

    <OperationLog ref="operationLogRef"></OperationLog>
    <paragraphDrawer
      ref="paragraphDrawerRef"
      :purchaseOrderIds="formData.id ? [formData.id] : []"
      :fieldConfig="{ disableSupplier: true }"
      :supplierInfo="{ supplier_name: formData.supplier_name, company_supplier_id: formData.company_supplier_id }"
      :orderType="'PREPAY'"
      @success="handlePrepaymentSuccess"
    />
    <AntTableSetting ref="antTableSettingRef" v-model:visible="visible" @save="handleSaveTableSetting" v-if="showSetting" v-model:columns="tableSettingKeys" @reset="handleReSet" />
  </div>
</template>
<script setup lang="ts">
import { Button, message } from 'ant-design-vue'
import dayjs from 'dayjs'
import { SettingOutlined, LoadingOutlined } from '@ant-design/icons-vue'
import type { VxeComponentSizeType } from 'vxe-table'

import { setFormItems, setColumns, setInnerColumns, setDetailColumns, setDetailInnerColumns, setBookingColumns, setPaymentColumns, setRefundColumns } from './index.data'
import AntTableSetting from './antTableSetting.vue'

import historyModal from '../components/purchaseorderManagementComponents/historyModal.vue'
import childrenListDlg from '../components/purchaseorderManagementComponents/childrenListDlg.vue'
import reviewTimer from '../components/purchaseorderManagementComponents/reviewTimer.vue'
import paragraphDrawer from '../components/paragraphDrawer.vue'

import { GetPurchaseDetailBookingList } from '@/servers/ReservationApi'
import {
  GetPurchaseOrderProductInfo,
  AddPurchaseOrder,
  GetPurchaseOrderDetail,
  UpdatePurchaseOrder,
  BatchAudit,
  GetPurchaseOrderPaymentOrderList,
  GetPurchaseOrderReturnApplyOrderList,
  GetPurchaseApplyOrderSummary,
  GetPurchaseOrderDetailList,
  UpdatePurchaseOrderDetail,
} from '@/servers/PurchaseManage'
import { GetTaxRateSelect, GetPageMRPSupplierCompanySelect } from '@/servers/BusinessCommon'
import { GetSystemConfigValue } from '@/servers/System'
import { GetTableConfig, SetTableConfig } from '@/servers/Common'

const userInfo = inject('userInfo') as UserInfo
const route = useRoute()
const { btnPermission } = usePermission()

const type = ref()
const formRef = ref()
const { closePage, setRefreshByPath } = usePage({
  initMethod: () => initMethod(),
  formRef,
})
const visible = ref(false)
const selectSupplierRef = ref()
const paragraphDrawerRef = ref()
const showSetting = ref(false)
const formData = ref<Record<string, any>>({})

// 付款明细相关
const paymentTableData = ref<any>([])

// 计算属性：检查付款明细是否有数据
const hasPaymentData = computed(() => {
  return paymentTableData.value && paymentTableData.value.length > 0
})

// 计算属性：采购单总金额
const totalPurchaseAmount = computed(() => {
  if (!tableData.value || tableData.value.length === 0) return 0
  return tableData.value.reduce((total, item) => {
    return total + (item.total_purchase_amount || 0)
  }, 0)
})

// 计算属性：累计本次付款金额
const totalPaymentAmount = computed(() => {
  if (!paymentTableData.value || paymentTableData.value.length === 0) return 0
  return paymentTableData.value.reduce((total, item) => {
    return total + (Number(item.payment_amount) || 0)
  }, 0)
})

// 计算属性：预付比例金额上限
const prepaymentLimit = computed(() => {
  const ratio = formData.value.prepayment_ratio || 100
  return totalPurchaseAmount.value * (ratio / 100)
})

// 计算属性：是否可以新增预付款
const canAddPrepayment = computed(() => {
  // 如果没有预付比例，则不能新增预付款
  if (!formData.value.prepayment_ratio || formData.value.prepayment_ratio <= 0) {
    return false
  }
  // 如果累计付款金额已经达到或超过预付比例上限，则不能新增
  return totalPaymentAmount.value < prepaymentLimit.value
})

// 添加tabs相关变量
const activeTabKey = ref('details')
// 当采购类型为寄售订单(4)时隐藏付款明细tab
const hidePaymentTab = computed(() => {
  return formData.value.type === 4
})
const handleSetting = async () => {
  showSetting.value = true
  await nextTick()
  visible.value = true
}
const handleActiveChange = () => {
  switch (activeTabKey.value) {
    case 'details':
      tableSettingKeys.value = columns.value
      break
    case 'booking':
      tableSettingKeys.value = bookingColumns.value.map((f) => ({ ...f, key: f.field, name: f.title }))
      break
    case 'refund':
      tableSettingKeys.value = refundColumns.value.map((f) => ({ ...f, name: f.title }))
      break
    case 'payment':
      tableSettingKeys.value = paymentColumns.value.map((f) => ({ ...f, name: f.title }))
      break
    default:
      tableSettingKeys.value = columns.value
      break
  }
}
const handleSaveTableSetting = (tableKeys) => {
  const data = tableKeys.map((item) => {
    return {
      freeze: item.fixed == 'left' ? 1 : item.fixed == 'right' ? 2 : 0,
      name: item.name || item.title,
      width: item.width,
      sort_type: '',
      sort_index: 0,
      key: item.dataIndex || item.key,
      is_sort: false,
      index: item.index,
      is_show: item.is_show,
    }
  })

  if (activeTabKey.value === 'booking') {
    bookingColumns.value = tableSettingKeys.value
    saveSetting({ maps: data, page_type: 1602 })
    return
  }
  if (activeTabKey.value === 'payment') {
    paymentColumns.value = tableSettingKeys.value
    saveSetting({ maps: data, page_type: 1603 })
    return
  }
  if (activeTabKey.value === 'refund') {
    refundColumns.value = tableSettingKeys.value
    saveSetting({ maps: data, page_type: 1604 })
    return
  }
  if (activeTabKey.value === 'details') {
    columns.value = tableSettingKeys.value
    saveSetting({ maps: data, page_type: formData.value.id ? 1605 : 1601 })
  }
}
const handleReSet = () => {
  const obj: any = {
    data: null,
    page_type: null,
  }
  if (activeTabKey.value === 'details' && !formData.value.id) {
    columns.value = setColumns()
    tableSettingKeys.value = columns.value
    obj.data = columns.value
    obj.page_type = 1601
  }
  if (activeTabKey.value === 'booking') {
    bookingColumns.value = setBookingColumns
    tableSettingKeys.value = bookingColumns.value.map((f) => ({ ...f, key: f.field, name: f.title }))
    obj.data = tableSettingKeys.value
    obj.page_type = 1602
  }
  if (activeTabKey.value === 'payment') {
    paymentColumns.value = setPaymentColumns
    tableSettingKeys.value = paymentColumns.value
    obj.data = paymentColumns.value
    obj.page_type = 1603
  }
  if (activeTabKey.value === 'refund') {
    refundColumns.value = setRefundColumns
    tableSettingKeys.value = refundColumns.value
    obj.data = refundColumns.value
    obj.page_type = 1604
  }
  if (activeTabKey.value === 'details' && formData.value.id) {
    columns.value = setDetailColumns()
    tableSettingKeys.value = columns.value
    obj.data = columns.value
    obj.page_type = 1605
  }
  const maps = obj.data.map((item) => {
    return {
      freeze: item.fixed == 'left' ? 1 : item.fixed == 'right' ? 2 : 0,
      name: item.name || item.title,
      width: item.width,
      sort_type: '',
      sort_index: 0,
      key: item.key,
      is_sort: false,
      index: item.index,
      is_show: item.is_show,
    }
  })
  saveSetting({ maps, page_type: obj.page_type })
}
const saveSetting = async (obj: any) => {
  await SetTableConfig(obj)
  message.success('保存成功')
  console.log('obj', obj)
}
const formatColumn = (data: any[], old: any[]) => {
  if (!data) {
    return old
  }
  const arr: any[] = []
  old.forEach((oldItem) => {
    const obj = data.find((f) => f.key == oldItem.key || f.key == oldItem.field)
    if (obj) {
      arr.push({
        ...oldItem,
        fixed: obj.freeze == 1 ? 'left' : obj.freeze == 2 ? 'right' : null,
        width: obj.width,
        is_show: obj.is_show,
        visible: obj.is_show,
      })
    } else {
      arr.push(oldItem)
    }
  })
  return arr
}

const formItems = ref<EasyFormItemProps[]>([])
formItems.value = setFormItems(
  formData,
  formRef,
  {
    company_supplier_id: () => {
      tableData.value.forEach((item, index) => {
        InputPrice('purchase_quantity', item, index)
      })
    },
    company_supplier_id_ck: ({ item }) => {
      // 根据采购类型确定type参数：1688类型传2，其他类型传1
      const supplierType = formData.value.type === 2 ? 2 : 1
      if (item.disabled) return
      selectSupplierRef.value.open(
        {
          key: 'company_supplier_id',
          mode: 'single',
          labelInValue: false,
          api: GetPageMRPSupplierCompanySelect,
          apiParams: {
            type: supplierType,
          },
          callback: (data) => {
            onSupplierIdChange(data, true)
          },
        },
        item.options,
      )
    },
    type: (val) => onTypeChange(val),
    process_type: () => {
      tableData.value.forEach((item, index) => {
        InputPrice('purchase_quantity', item, index)
      })
    },
    hasPaymentData,
    settlementTypeChange: (val) => {
      // 当结算方式改变时，动态更新预付比例字段的required状态和disabled状态
      const isPrepayment = val === 4
      const isOnlinePayment = val === 5
      formRef.value?.changeItem('prepayment_ratio', {
        required: isPrepayment,
        disabled: isOnlinePayment || hasPaymentData.value,
      })

      // 重新验证预付比例字段
      nextTick(() => {
        formRef.value?.validate(['prepayment_ratio'])
      })
    },
  },
  route.params.id,
)
const onTypeChange = (val) => {
  for (const name of ['consignee', 'phone_number', 'shipments_address', 'province', 'city', 'area', 'warehourse_id']) {
    formRef.value.changeItem(name, { disabled: name === 'warehourse_id' ? false : val == 2 })
  }

  // 确保预付比例字段不被禁用
  formRef.value.changeItem('prepayment_ratio', {
    disabled: false,
  })
}
const onSupplierIdChange = async (data, isChange = false) => {
  let _data = data
  if (data instanceof Array) {
    _data = data[0]
  }
  // 合并现有formData，保留purchase_time
  await formRef.value.changeValue(
    {
      company_supplier_id: _data.value,
    },
    isChange,
  )
  formRef.value.changeItem('company_supplier_id', {
    options: [{ label: _data.label, value: _data.value }],
  })
}

// 操作日志
const operationLogRef = ref()
const openLog = () => {
  operationLogRef.value.open({
    params: {
      id: formData.value.id,
      pageType: OpLogPageTypeEnum.采购单管理,
    },
  })
}

// 监听付款明细数据变化，动态更新表单字段禁用状态
watch(
  hasPaymentData,
  (newValue) => {
    if (formRef.value) {
      formRef.value.changeItem('settlement_type', { disabled: newValue })
      formRef.value.changeItem('prepayment_ratio', { disabled: newValue })
    }
  },
  { immediate: true },
)

// 采购明细
const tableData = ref<any>([])
const columns = ref<Record<string, any>[]>([])
const innerColumns = ref<Record<string, any>[]>([])
const tableConfig = computed(() => {
  return {
    stripe: false,
    hideTop: true,
    hidePagination: true,
    autoSearch: false,
    tableColumns: columns.value,
    boxCls: ['relative!', 'mt-10', 'mb-20'],
    maxHeight: 600,
    minHeight: 106,
    height: null,
    rowConfig: {
      keyField: 'k3_sku_id',
      isHover: true,
      height: 55,
    },
    keyField: 'k3_sku_id',
    expandData: ({ row }) => row.purchaseOrderApplyDetails,
    expandColumns: innerColumns.value,
    expandCellMinWidth: 150,
    loading: true,
  }
})

// 入库明细
const bookingTableData = ref<any>([])
const bookingColumns = ref<any>(setBookingColumns)
const bookingGridRef = ref()
const bookingGridOptions = computed(() => ({
  maxHeight: 600,
  minHeight: 100,
  border: true,
  size: 'mini' as VxeComponentSizeType,
  columnConfig: { resizable: true },
  virtualXConfig: {
    enabled: true,
    gt: 20,
  },
  virtualYConfig: {
    enabled: true,
    gt: 20,
  },
  rowConfig: {
    height: 34,
    isHover: true,
  },
  columns: bookingColumns.value,
  data: bookingTableData.value,
  spanMethod: ({ row, column }) => {
    const bookingMergeFields = [
      'key',
      'image_url',
      'sku_name',
      'k3_sku_id',
      'jst_sku_id',
      'type_specification',
      'valuation_unit',
      'booking_number',
      'warehouse_name',
      'audit_status',
      'purchase_scheduled_quantity',
      'gift_scheduled_quantity',
      'scheduled_quantity',
      'box_num',
      'create_at',
      'logistics_company',
      'tracking_number',
      'scheduled_arrival_time',
    ]
    if (bookingMergeFields.includes(column.field)) {
      if (row.index === 0) {
        return { rowspan: row.count, colspan: 1 }
      }
      return { rowspan: 0, colspan: 0 }
    }
    return { rowspan: 1, colspan: 1 }
  },
}))

// 付款明细相关
const paymentColumns = ref(setPaymentColumns)

// 退款明细相关
const refundTableData = ref<any>([])
const refundColumns = ref(setRefundColumns)

const tableSettingKeys = ref()
// 选择商品
const selectProductRef = ref()
const handleAddPro = () => {
  if (!formData.value.company_supplier_id) {
    message.info('请先选择供应商子公司')
    return
  }
  if (!formData.value.process_type) {
    message.info('请先选择加工方式')
    return
  }
  selectProductRef.value.open({
    search: ['商品编号/聚水潭编号', '商品名称', '供应商名称'],
    left: ['商品分类', '商品主图', '商品编码', '商品名称', '规格型号', '款式编码', '材质', '换算值', '换算公式', '采购单位', '标准装箱数', '聚水潭编号', '默认供应商'],
  })
}

const addProLoading = ref(false)
const onAddProduct = async (arr) => {
  const paramList: any[] = arr.map((item) => {
    return {
      k3_sku_id: (item as any).sku_id,
      jst_sku_id: (item as any).jst_sku_id,
      supplier_compeny_id: formData.value.company_supplier_id,
    }
  })
  const res = await GetPurchaseOrderProductInfo({ paramList, process_type: formData.value.process_type }, { loading: addProLoading })
  if (!res.success) {
    return message.info(res.message)
  }
  const tableDataIdArr = tableData.value.map((v: any) => {
    return v.k3_sku_id
  })
  res.data
    .filter((f) => !tableDataIdArr.includes(f.k3_sku_id))
    .forEach(async (item) => {
      for (const key of ['mould_fees', 'other_fees', 'discount_amount', 'purchase_quantity', 'gift_purchase_quantity', 'ordernum', 'process_fee', 'material_price', 'system_unit_price', 'tax']) {
        item[key] = item[key] || 0
      }
      console.log(item, '----item')

      const params = {
        ...item,
        predict_delivery_date: item.predict_delivery_date ? dayjs(item.predict_delivery_date, 'YYYY-MM-DD') : null,
        purchaseOrderApplyDetails: [],
        tax_rate_id: item.tax_rate_id || taxRateOption.value[0].value,
        tax_rate: item.tax_rate || taxRateOption.value[0].tax_rate,
        // reverse_price: false,
        all_category: item.all_category,
        product_brand: item.product_brand,
      }
      tableData.value.push(params)
      console.log(tableData.value)

      await nextTick()
      InputPrice('conversion_value', params, tableData.value.length - 1)
    })
}

// 防抖
const timer = ref()
const debounce = (func, delay, dataIndex, item, index) => {
  if (timer.value) clearTimeout(timer.value)
  timer.value = setTimeout(() => {
    func(dataIndex, item, index)
  }, delay)
}

// 查看历史
const historyModalRef = ref()
const setHistory = (item) => {
  if (formData.value.company_supplier_id > 0) {
    historyModalRef.value.setModel(true, item, formData.value.company_supplier_id, {
      process_type: formData.value.process_type,
    })
  } else {
    message.info('请先选择供应商子公司')
  }
}

const calculatePriceIndex = ref()
const calculatePriceItem = ref()
const calculatePriceItemWatcher = ref()
const InputPrice = async (dataIndex, item, index) => {
  console.log('InputPrice', dataIndex)
  const isDataIndex = ['purchase_quantity', 'gift_purchase_quantity', 'conversion_value'].includes(dataIndex)
  const isVal = ['process_type', 'company_supplier_id'].every((key) => !!formData.value[key])
  // 需求为有成本数量才请求接口, 但是从0开始输入时成本数量还未计算, 所以取purchase_quantity判断
  if (isDataIndex && item.purchase_quantity > 0 && isVal) {
    const params = {
      paramList: [
        {
          k3_sku_id: (item as any).k3_sku_id,
          jst_sku_id: (item as any).jst_sku_id,
          supplier_compeny_id: formData.value.company_supplier_id,
          sum_quantity: item.purchase_quantity,
        },
      ],
      process_type: formData.value.process_type,
    }
    calculatePriceIndex.value = [`${index}`, `${dataIndex}`]
    const res = await GetPurchaseOrderProductInfo(params)
    if (res.success && res.data?.[0]) {
      // console.log('res.data[0]', res.data[0])
      item.process_fee = res.data[0].process_fee || 0
      item.system_unit_price = res.data[0].system_unit_price
      item.last_purchase_price = res.data[0].last_purchase_price
      item.material_price = res.data[0].material_price || 0
      item.tax_rate_id = res.data[0].tax_rate_id || taxRateOption.value[0].value
      item.tax_rate = res.data[0].tax_rate || taxRateOption.value[0].tax_rate
    }
  }
  const itemState = {
    ...item,
    [dataIndex]: item[dataIndex],
  }
  for (const key in itemState) {
    if (Object.values(decimalKeys.value).flat().includes(key)) {
      itemState[key] = Number.isNaN(Number(itemState[key])) ? 0 : Number(itemState[key])
    }
  }
  calculatePriceItem.value = {
    ...itemState,
    dataIndex,
    index,
  }
}

const changePrice = (dataIndex, row, index) => {
  // 处理 conversion_value 字段，如果输入为0则自动赋值为1
  if (dataIndex === 'conversion_value' && row[dataIndex] === 0) {
    row[dataIndex] = 1
  }

  // 处理税率字段，当tax_rate_id改变时，同步更新tax_rate
  if (dataIndex === 'tax_rate_id') {
    const selectedTaxRate = taxRateOption.value.find((f) => f.value == row[dataIndex])
    if (selectedTaxRate) {
      row.tax_rate = selectedTaxRate.tax_rate
    }
  }

  debounce(InputPrice, 300, dataIndex, row, index)
}

const deleteChildItem = (item, index) => {
  if (item.quantity > 0) {
    message.info('当前存在采购数量不可以删除')
    return
  }
  tableData.value.forEach((titem) => {
    if (titem.parentkey == item.key) {
      titem.purchaseOrderApplyDetails.splice(index, 1)
    }
  })

  // 如果申请单全部删除，同步删除该商品
  const tableDataState: any = []
  tableData.value.forEach((titem) => {
    if (titem.purchaseOrderApplyDetails.length > 0) {
      tableDataState.push(titem)
    }
  })
  tableData.value = tableDataState
}
const deleteItem = (_, index) => {
  tableData.value.splice(index, 1)
}

// 获取税率下拉列表
const taxRateOption = ref<Record<string, any>[]>([])
const getTaxRateList = async () => {
  const res = await GetTaxRateSelect({})
  if (res.success) {
    taxRateOption.value = res.data
      .map((v) => {
        return {
          label: v.label,
          value: Number(v.value),
          tax_rate: v.tax_rate,
        }
      })
      .sort((a, b) => a.tax_rate - b.tax_rate)
  }
}

// 拍单子表
const ChildrenListDlgRef = ref()
const openChildrenList = (row) => {
  const bol = type.value == 'new'
  ChildrenListDlgRef.value.open(row, bol)
}

const isBatchCreate = ref(false)
const allowOverApply = ref(false)
const mode = computed(() => {
  return ['detail', 'review'].includes(type.value) ? 'detail' : 'add'
}) as any

const tableRef = ref()
const initFlag = ref(false)
const isSRS = ref(false)
const initMethod = async () => {
  console.log('purchaseOrderDetail: initMethod')
  const systemConfigRes = await GetSystemConfigValue({ type: 1, key: 'allowOverApply' })
  if (systemConfigRes.success) {
    allowOverApply.value = systemConfigRes.data?.switch_case || false
  }
  type.value = route.name === '查看采购单' ? 'detail' : route.name === '审核采购单' ? 'review' : 'new'

  getTaxRateList()
  formData.value = {
    supplier_type: SupplierStatusEnum.待确认,
    order_status: PurchaseOrderStatusEnum.进行中,
    audit_status: PurchaseOrderAuditStatusEnum.待提审,
    received_status: PurchaseOrderReceivedStatusEnum.未领料,
    prepayment_ratio: 100,
    id: route.params.id,
    purchase_time: dayjs().format('YYYY-MM-DD HH:mm:ss'),
    settlement_type: '',
  }

  const RSRMode = (data) => {
    if (data.supplier_source_type === SupplierSourceTypeEnum.SRS) {
      isSRS.value = true
      formRef.value?.changeItem('company_supplier_id', {
        disabled: true,
      })
      formRef.value?.changeItem('type', {
        disabled: true,
      })
    }
  }

  // 存在ID时
  if (formData.value.id) {
    const res = await GetPurchaseOrderDetail({
      id: formData.value.id,
      is_check: type.value === 'detail',
    })

    if (['detail', 'review'].includes(type.value)) {
      // 查看的 & 审核的
      await formRef.value.changeValue({
        type: res.data.type,
      })
      formData.value = {
        ...formData.value,
        ...res.data,
      }
      // 预付比例为null时显示为0
      if (formData.value.prepayment_ratio == null) {
        formData.value.prepayment_ratio = 0
      }
      // if (type.value === 'detail') {
      //   formRef.value.changeItem('remark', {
      //     isDetail: false,
      //   })
      // }
    } else {
      // 编辑的
      const { supplier_name, company_supplier_id, ..._data } = res.data

      RSRMode(res.data)

      await formRef.value.changeValue({
        ..._data,
        process_type: !res.data.process_type ? '' : `${res.data.process_type}`,
        order_status: res.data.order_status || PurchaseOrderStatusEnum.进行中,
        supplier_type: res.data.supplier_type || SupplierStatusEnum.待确认,
        tags: (res.data.tags || []).join(','),
        purchaseOrderDetails: undefined,
        settlement_type: res.data.settlement_type ?? formData.value.settlement_type ?? '',
      })
      // 预付比例为null时显示为0
      if (formData.value.prepayment_ratio == null) {
        formData.value.prepayment_ratio = 0
      }
      if (company_supplier_id) {
        await onSupplierIdChange({ label: supplier_name, value: company_supplier_id })
      }
      // 连续操作赋值需nextTick等前面formData赋值完成再继续
      await nextTick()
      onTypeChange(res.data.type)
    }

    const bookingRes = await GetPurchaseDetailBookingList({ id: formData.value.id })
    if (bookingRes.success) {
      bookingTableData.value = bookingRes.data.reduce((acc, cur) => {
        const items = cur.purchaseinRelationDos?.length
          ? cur.purchaseinRelationDos.map((f, index) => ({
              ...cur,
              ...f,
              purchaseinRelationDos: undefined,
              count: cur.purchaseinRelationDos.length,
              index,
            }))
          : [{ ...cur, count: 1, index: 0 }]
        return [...acc, ...items]
      }, [])
    }

    // 获取采购明细
    const orderRes = await GetPurchaseOrderDetailList({
      id: formData.value.id,
      is_check: type.value === 'detail',
      is_edit: type.value === 'edit',
    })
    if (orderRes.success) {
      tableData.value = orderRes.data.map(formatPriceDecimal)
    }
    // 获取付款明细数据
    const paymentRes = await GetPurchaseOrderPaymentOrderList({ id: formData.value.id })
    if (paymentRes.success) {
      paymentTableData.value = paymentRes.data
    }
    // 获取退款明细数据
    const refundRes = await GetPurchaseOrderReturnApplyOrderList({ purchase_order_id: formData.value.id })
    if (refundRes.success) {
      refundTableData.value = refundRes.data
    }

    // 根据回显的结算方式设置预付比例字段的必填状态
    if (formData.value.settlement_type === 4) {
      formRef.value?.changeItem('prepayment_ratio', {
        required: true,
      })
    }

    // 初始数据加载完成后，触发重新计算
    await nextTick()
    tableData.value.forEach((item, index) => {
      let count = 0
      item.purchaseOrderApplyDetails.forEach((applyItem) => {
        count += applyItem.await_purchase_quantity
        applyItem.key = index + 1
      })
      item.ordernum = count
      item.parentkey = index + 1
      // 兼容旧数据 没材料单价 & 加工费 塞到系材料单价
      if (!item.material_price && !item.process_fee && item.unit_price) {
        item.material_price = item.unit_price
      }
      // 兼容旧数据 税率tax_rate_id === 0
      if (item.tax_rate_id === 0) {
        item.tax_rate_id = taxRateOption.value.find((f) => f.tax_rate == item.tax_rate)?.value
      }
      if (item.tax_rate_id && item.tax_rate_id !== 0) {
        // 确保税率值正确
        const selectedTaxRate = taxRateOption.value.find((f) => f.value == item.tax_rate_id)
        if (selectedTaxRate) {
          item.tax_rate = selectedTaxRate.tax_rate
        }
      }
    })
  }

  // 从申请单批量创建的
  const { ids, preprocessing } = route.params || {}
  if (ids) {
    isBatchCreate.value = true
    const applyItem = JSON.parse(localStorage.getItem('srm_apply_purchase') || '{}')[ids as string]
    if (!applyItem) return message.error('申请单不存在, 请重新生成')
    const [company_supplier_name, warehourse_name, company_supplier_id, warehourse_id] = (preprocessing as any).split(':')

    const params = {
      company_supplier_name,
      company_supplier_id,
      warehourse_id: warehourse_id === 'undefined' ? undefined : warehourse_id,
      warehourse_name: warehourse_name === 'undefined' ? undefined : warehourse_name,
      is_preprocessing: !!company_supplier_name,
      datas: applyItem,
    }
    const res = await GetPurchaseApplyOrderSummary(params)

    RSRMode(res.data)

    await formRef.value.changeValue(
      {
        ...res.data,
        process_type: !res.data.process_type ? '' : `${res.data.process_type}`,
        supplier_type: SupplierStatusEnum.待确认,
        order_status: PurchaseOrderStatusEnum.进行中,
        audit_status: PurchaseOrderAuditStatusEnum.待提审,
        received_status: PurchaseOrderReceivedStatusEnum.未领料,
        tags: (res.data.tags || []).join(','),
        buyer_id: userInfo.id,
        purchase_time: (res.data.purchase_time ? dayjs(res.data.purchase_time) : dayjs()).format('YYYY-MM-DD HH:mm:ss'),
      },
      undefined,
      true,
      ['company_supplier_id'],
    )
    if (res.data.company_supplier_id) {
      await onSupplierIdChange({
        label: res.data.supplier_name,
        value: res.data.company_supplier_id,
      })
    }
    tableData.value = res.data.purchaseOrderDetails.map((item, index) => {
      let quantity = 0
      const result = {
        ...item,
        predict_delivery_date: item.predict_delivery_date ? dayjs(item.predict_delivery_date, 'YYYY-MM-DD') : null,
        conversion_value: item.conversion_value || 1,
        purchaseOrderApplyDetails: item.purchaseOrderApplyDetails.map((applyItem) => {
          quantity += applyItem.await_purchase_quantity
          applyItem.quantity = item.take_quantity
          return {
            ...applyItem,
            key: index + 1,
          }
        }),
        parentkey: index + 1,
      }
      result.ordernum = quantity
      result.tax_rate_id = taxRateOption.value[0].value
      result.tax_rate = taxRateOption.value[0].value
      return result
    })

    // 根据回显的结算方式设置预付比例字段的必填状态
    if (formData.value.settlement_type === 4) {
      await formRef.value?.changeItem('prepayment_ratio', {
        required: true,
      })
    }
  }

  const promiseArr: Promise<any>[] = [
    GetTableConfig({ page_type: 1601 }),
    GetTableConfig({ page_type: 1602 }),
    GetTableConfig({ page_type: 1603 }),
    GetTableConfig({ page_type: 1604 }),
    GetTableConfig({ page_type: 1605 }),
  ]
  const tableColumns = await Promise.all(promiseArr)
  if (!formData.value.id) {
    columns.value = formatColumn(tableColumns[0].data?.maps, setColumns())
  } else {
    columns.value = formatColumn(tableColumns[4].data?.maps, setDetailColumns())
  }
  tableSettingKeys.value = columns.value
  bookingColumns.value = formatColumn(tableColumns[1].data?.maps, setBookingColumns)
  paymentColumns.value = formatColumn(tableColumns[2].data?.maps, setPaymentColumns)
  refundColumns.value = formatColumn(tableColumns[3].data?.maps, setRefundColumns)

  // 公共处理
  // columns.value = formData.value.id ? setDetailColumns() : setColumns()
  innerColumns.value = [
    ...(formData.value.id ? setDetailInnerColumns : setInnerColumns),
    {
      title: '操作',
      key: 'operation',
      align: 'center',
      fixed: 'right',
      render: ({ row, rowIndex }) => {
        return type.value === 'new' && h(Button, { style: { height: '24px', padding: '0 12px' }, onClick: () => deleteChildItem(row, rowIndex) }, { default: () => '删除' })
      },
    },
  ]

  initFlag.value = true
  tableRef.value?.updateLoading(false)
  await nextTick()
}

// 格式化金额后小数点（去除科学计数法）
const decimalKeys = ref({
  2: ['tax', 'optimize_sum_price', 'tax_sum_price', 'total_purchase_amount', 'sum_price'],
  8: ['unit_price', 'tax_unit_price', 'purchase_tax_price', 'k3_reference_price', 'k3_reference_acreage', 'process_fee', 'material_price'],
  5: ['cost_num'],
})
const formatPriceDecimal = (data) => {
  const result = {}
  for (const name of Object.keys(data)) {
    let precision = 0
    if (decimalKeys.value[8].includes(name)) {
      precision = 8
    } else if (decimalKeys.value[2].includes(name)) {
      precision = 2
    } else if (decimalKeys.value[5].includes(name)) {
      precision = 5
    }
    result[name] = precision ? (+data[name]).roundNext(precision) : data[name]
  }
  return result
}
onActivated(() => {
  console.log('purchaseOrderDetail: onActivated')
  calculatePriceItemWatcher.value = watchEffect(() => {
    if (!calculatePriceItem.value) return
    const {
      purchase_quantity, // 采购数量
      gift_purchase_quantity, // 赠品数量
      conversion_value, // 换算值
      purchaseOrderApplyDetails, // 采购子订单
      ordernum, // 订单数量
      cost_unit, // 成本单位
      valuation_unit, // 计量单位
      process_fee,
      material_price,
      tax_rate_id,
      // is_contain_tax, // 是否含税价目表
      system_unit_price,
      other_fees,
      mould_fees,
      discount_amount,
      index,
      dataIndex,
      sum_price,
    } = calculatePriceItem.value
    const result: Record<string, any> = {
      tax_rate: taxRateOption.value.find((f) => f.value == tax_rate_id)?.tax_rate,
    }
    if (purchaseOrderApplyDetails.length && ordernum < purchase_quantity && !allowOverApply.value) {
      console.log('purchase_quantity=ordernum', ordernum)
      result.purchase_quantity = ordernum
    } else {
      result.purchase_quantity = purchase_quantity
    }
    // 计算成本数量
    result.cost_num = Math.max(0, result.purchase_quantity * conversion_value)

    // 计算合计单价 & 商品总金额
    // 商品总金额: 合计单价*数量
    if (dataIndex == 'sum_price') {
      result.unit_price = result.cost_num === 0 ? 0 : sum_price / result.cost_num
      result.material_price = result.unit_price
      result.process_fee = 0
      result.sum_price = sum_price
    } else {
      result.unit_price = (process_fee || 0) + (material_price || 0)
      result.sum_price = result.unit_price * result.cost_num
    }
    // 含税单价
    result.tax_unit_price = result.unit_price + (result.tax_rate === 0 ? 0 : (result.unit_price * result.tax_rate) / 100)
    // 计算采购总数
    result.total_purchase_quantity = result.purchase_quantity + gift_purchase_quantity
    // 修改换算值时换算公式同步修改
    result.conversion_formula = `${conversion_value}${cost_unit}=1${valuation_unit}`
    // 数量改变要按顺序同步采购总数到采购子订单
    let quantity = result.total_purchase_quantity
    result.purchaseOrderApplyDetails = purchaseOrderApplyDetails.map((item, index) => {
      // 超采打开时分摊到最后一个
      if (allowOverApply.value && index === purchaseOrderApplyDetails.length - 1) {
        item.quantity = quantity
      } else {
        item.quantity = Math.min(item.await_purchase_quantity, quantity)
      }
      quantity -= item.quantity
      return item
    })

    // debugger
    // 税额: 商品总金额*税率
    result.tax = result.tax_rate === 0 ? 0 : result.sum_price * (result.tax_rate * 0.01)
    // 含税总金额: 商品总金额+税额
    result.tax_sum_price = result.sum_price + result.tax

    // 采购含税单价 = 商品含税总金额 / 采购数量（四舍五入到8位小数）
    result.purchase_tax_price = result.purchase_quantity > 0 ? result.tax_sum_price / result.purchase_quantity : 0

    // 采购总金额：含税总金额+其他费用-优惠金额
    result.total_purchase_amount = result.tax_sum_price + Number(mould_fees ?? 0) + Number(other_fees ?? 0) - Number(discount_amount ?? 0)

    // 验证采购总金额是否小于预付金额
    if (result.prepaid_amount && Number(result.total_purchase_amount) < Number(result.prepaid_amount)) {
      message.warning('采购总金额小于已预付金额')
    }

    // 优化金额:（成本合计含税单价-系统单价）*数量
    result.optimize_sum_price = (result.tax_unit_price - system_unit_price) * result.cost_num

    tableData.value[index] = {
      ...tableData.value[index],
      ...formatPriceDecimal(result),
    }

    calculatePriceItem.value = null
    calculatePriceIndex.value = null
  })
})
onDeactivated(() => {
  calculatePriceItemWatcher.value()
  calculatePriceItem.value = null
  calculatePriceIndex.value = null
})

// 审核记录
const reviewTimerRef = ref()
const reviewType = (val?: number) => {
  if (val && [1, 2].includes(val)) {
    reviewTypeMode(val)
  } else {
    const typeId = ['new', 'review', 'detail'].includes(type.value) ? 4 : ''
    reviewTimerRef.value.setModel(true, formData.value.id, typeId)
  }
}

const auditConfirmRef = ref()
const reviewTypeMode = (type) => {
  const useData = JSON.parse(localStorage.getItem('userData') as any)
  const permissions_infos = useData.permissions_infos.find((item) => item.id == 80000)
  const permissions_infos_child = permissions_infos.children.find((item) => item.id == 84000)
  const btnList = {}
  permissions_infos_child.btnList.forEach((item) => {
    btnList[item.id] = true
  })
  if (
    (formData.value.audit_status == PurchaseOrderAuditStatusEnum['待一级审核'] && btnList[84004]) ||
    (formData.value.audit_status == PurchaseOrderAuditStatusEnum['待二级审核'] && btnList[84005]) ||
    (formData.value.audit_status == PurchaseOrderAuditStatusEnum['待三级审核'] && btnList[84006]) ||
    (formData.value.audit_status == PurchaseOrderAuditStatusEnum['待四级审核'] && btnList[84007])
  ) {
    if (type == 1) {
      auditConfirmRef.value.open(true)
    } else {
      auditConfirmRef.value.open(false)
    }
  } else {
    message.error('您没有该状态的审核权限')
  }
}
const onAuditConfirm = async (data) => {
  const { audit_opinion, is_pass } = data
  const params = {
    is_pass,
    audit_opinion,
    ids: [formData.value.id],
  }
  const res = await BatchAudit(params)
  if (res.success) {
    message.success('操作成功')
    setRefreshByPath('/purchaseorderManagement')
    setRefreshByPath('/purchaseapplyorder')
    closePage()
  } else {
    message.error(res.message)
  }
}

const submitLoading = ref(false)
const handleSubmit = async (is_pass) => {
  if (tableData.value.length == 0) {
    message.info('请选择采购商品')
    return
  }
  let check = true
  tableData.value.forEach((item) => {
    if ((check && parseFloat(item.total_purchase_quantity.toString()) > 0) == false) {
      check = false
    }
  })

  if (!check) {
    message.info('采购数量不能为0')
    return
  }
  const formState = await formRef.value.validate()
  const params = {
    id: route.params.id || undefined,
    ...formState,
  }
  const api = route.params.id ? UpdatePurchaseOrder : AddPurchaseOrder
  if (type.value === 'new') {
    params.ali_purchase_account_list_id = params.ali_purchase_account_list_id ?? 0
    params.ali_purchase_account_receive_address_id = params.ali_purchase_account_receive_address_id ?? 0
    params.is_pass = is_pass
    params.purchaseOrderDetails = tableData.value.map((item) => {
      return {
        ...item,
        // purchaseOrderApplyDetails: item.purchaseOrderApplyDetails.filter(({ quantity }) => quantity > 0),
        predict_delivery_date: item.predict_delivery_date && item.predict_delivery_date !== '' ? item.predict_delivery_date : null,
        cost_num:
          item.cost_num !== undefined && item.cost_num !== null ? item.cost_num : item.purchase_quantity && item.conversion_value ? Number(item.purchase_quantity) * Number(item.conversion_value) : 0,
      }
    })
    params.tags = [
      ...new Set(
        (formState.tags || '').split(',').reduce((acc, cur) => {
          const result = cur.split('，')
          return [...acc, ...result]
        }, []),
      ),
    ]
  }

  console.log('params', params)
  // if (params) return
  const res = await api(params, { loading: submitLoading })
  if (res.success == true) {
    message.success('提交成功')
    setRefreshByPath('/purchaseorderManagement')
    setRefreshByPath('/purchaseapplyorder')
    closePage()
  } else {
    message.error(res.message)
  }
}
const handleSubmitByDetail = async () => {
  const params = tableData.value.map((item) => {
    return {
      id: item.id,
      remark: item.remark,
      predict_delivery_date: item.predict_delivery_date ?? null,
    }
  })
  const res = await UpdatePurchaseOrderDetail(params, { loading: submitLoading })
  if (res.success == true) {
    message.success('修改成功')
    setRefreshByPath('/purchaseorderManagement')
    setRefreshByPath('/purchaseapplyorder')
    closePage()
  } else {
    message.error(res.message)
  }
}

// 预付款抽屉
const handleAddPrepayment = () => {
  if (!formData.value.id) {
    message.warning('请先保存采购单信息')
    return
  }

  // 检查预付比例
  if (!formData.value.prepayment_ratio || formData.value.prepayment_ratio <= 0) {
    message.warning('请先设置预付比例')
    return
  }

  // 检查是否已达到预付比例上限
  if (!canAddPrepayment.value) {
    message.warning(`累计付款金额已达到预付比例上限：${prepaymentLimit.value.roundNext(2)}元`)
    return
  }

  if (paragraphDrawerRef.value) {
    paragraphDrawerRef.value.open()
  }
}

const handlePrepaymentSuccess = async () => {
  // 刷新付款明细列表
  if (formData.value.id) {
    const paymentRes = await GetPurchaseOrderPaymentOrderList({ id: formData.value.id })
    if (paymentRes.success) {
      paymentTableData.value = paymentRes.data
    }
  }
}
</script>

<style lang="scss" scoped>
:deep {
  .ant-table-cell {
    padding: 7px 12px !important;
    color: rgb(0 0 0 / 90%) !important;
  }

  .table-striped {
    td {
      background-color: #fafafa !important;
    }
  }
}

.custom-tabs {
  :deep(.ant-tabs-tab) {
    font-size: 14px;
    font-weight: bold;
  }

  :deep(.ant-tabs-tab-btn) {
    font-size: 14px;
    font-weight: bold;
  }
}

.readonly-text {
  line-height: 32px;
  color: rgb(0 0 0 / 85%);
}
</style>
