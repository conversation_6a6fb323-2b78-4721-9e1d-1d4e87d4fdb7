<template>
  <a-drawer
    title="添加采购单"
    :width="width"
    :open="openFlag"
    :mask-closable="false"
    :destroyOnClose="true"
    :body-style="{ display: 'flex', padding: '16px', justifyContent: 'space-between' }"
    @close="handleClose"
  >
    <div class="h-full flex flex-col flex-1">
      <a-space class="mb-12">
        <template v-for="item in searchFormList" :key="item.name">
          <a-input v-if="item.type === 'input'" :placeholder="item.placeholder" v-model:value="queryParams[item.name]" :maxlength="200" allowClear :disabled="item.disabled" />
          <div class="w-140" v-else-if="item.type === 'SelectSupplier'">
            <SelectSupplier
              v-model:value="queryParams[item.name]"
              :title="item.placeholder"
              :mode="'single'"
              :labelInValue="true"
              :disabled="item.disabled"
              :api="item.api"
              :apiParams="item.apiParams"
              :key="`${item.name}_${JSON.stringify(item.apiParams || {})}`"
            />
          </div>
        </template>
        <a-button type="primary" @click="tableRef.search()">查询</a-button>
      </a-space>
      <BaseTable ref="tableRef" v-bind="tableConfig" @loadData="setCheckedRows" />
    </div>
    <div
      class="flex flex-col ml-12"
      :style="{
        width: selectTableConfig.tableColumns.map((f) => f.resizeWidth || f.width || f.minWidth || 100).reduce((acc, cur) => (acc += cur), 0) + 'px',
      }"
      v-if="selectTableConfig.tableColumns?.length"
    >
      <div class="flex mb-12 h-30 items-center justify-between">
        <div class="text-14px text-#333">已选{{ selectRowsMapCount }}个单据</div>
        <a-button v-if="selectRowsMapCount" class="ml-8" type="primary" @click="handleClear">清空</a-button>
      </div>
      <BaseTable ref="selectTableRef" v-bind="selectTableConfig" />
    </div>
    <template #footer>
      <a-space>
        <a-button type="primary" @click="handleConfirm">确定</a-button>
        <a-button @click="handleClose">取消</a-button>
      </a-space>
    </template>
  </a-drawer>
</template>

<script lang="ts" setup>
import { message } from 'ant-design-vue'

import SelectSupplier from './SelectSupplier'

import { GetPurchaseOrderList } from '@/servers/billVerification'
import { GetPageSuppliersSelect, GetPageMRPSupplierCompanySelect } from '@/servers/BusinessCommon'

const emits = defineEmits(['confirm'])
const props = defineProps({
  syncConfirm: {
    type: Function,
    default: null,
  },
  // 供应商校验配置
  supplierValidation: {
    type: Object,
    default: () => ({
      enabled: false, // 是否启用供应商校验
      validateFunc: null, // 自定义校验函数
      message: '供应商不一致，请重新选择', // 校验失败时的提示消息
    }),
  },
})

const processWidth = ref()
const width = computed(() => {
  return Math.min(processWidth.value, window.innerWidth - 180)
})
const openFlag = ref(false)

const searchFormList = ref<Record<string, any>[]>([])
const tableRef = ref()
const selectTableRef = ref()
const watcher = ref()

const baseParams = ref<Record<string, any>>({})
const queryParams = ref<Record<string, any>>({})
const formFormat = (data) => {
  // 需要处理的字段
  const selectSupplierFields = ['supplier_id', 'company_supplier_id']
  const formFormatQueryParams = { ...queryParams.value }
  selectSupplierFields.forEach((field) => {
    if (formFormatQueryParams[field]) {
      // 如果是数组类型，需要特殊处理
      if (Array.isArray(formFormatQueryParams[field])) {
        // 只取 value
        formFormatQueryParams[field] = formFormatQueryParams[field].map((item) => {
          if (typeof item === 'string') {
            try {
              return JSON.parse(item).value
            } catch {
              return item
            }
          }
          // 如果是对象
          return item.value ?? item
        })
        // 如果是单选，直接取第一个
        if (formFormatQueryParams[field].length === 1) {
          formFormatQueryParams[field] = formFormatQueryParams[field][0]
        }
      } else if (typeof formFormatQueryParams[field] === 'string') {
        // 如果是字符串，尝试解析JSON
        try {
          const parsed = JSON.parse(formFormatQueryParams[field])
          if (parsed && typeof parsed === 'object' && parsed.value) {
            formFormatQueryParams[field] = parsed.value
          }
        } catch (_e) {
          // 如果不是JSON，保持原值
        }
      }
    }
  })
  return {
    ...baseParams.value,
    ...formFormatQueryParams,
    ...data,
  }
}

const formMap = {
  采购单号: { type: 'input', name: 'purchase_number' },
  供应商: { type: 'SelectSupplier', name: 'supplier_id', api: GetPageSuppliersSelect }, // 使用supplier_name来显示供应商名字
  供应商子公司: { type: 'SelectSupplier', name: 'company_supplier_id', api: GetPageMRPSupplierCompanySelect }, // 使用company_supplier_name来显示供应商子公司名字
  退库申请号: { type: 'input', name: 'return_application_number' },
  应付单号: { type: 'input', name: 'bill_payable_number' },
  商品编码: { type: 'input', name: 'k3_sku_id' },
}

const defaultColumns = ref([
  {
    key: 'index',
    name: '序号',
    width: 50,
    formatter: ({ rowIndex }) => {
      return 1 + rowIndex
    },
    align: 'center',
  },
  { key: 'purchase_number', name: '采购单编号', width: 150 },
  { key: 'return_application_number', name: '退库申请单号', width: 150 },
  { key: 'purchase_time', name: '采购时间', width: 150 },
  { key: 'purchase_order_status_string', name: '采购单状态', width: 120 },
  { key: 'supplier_name', name: '供应商' },
  { key: 'company_supplier_name', name: '供应商子公司' },
  { key: 'image_url', name: '商品图片', width: 100, cellRender: { name: 'image' } },
  { key: 'sku_name', name: '商品名称' },
  { key: 'k3_sku_id', name: '商品编号' },
  { key: 'tax_unit_price', name: '采购含税单价', width: 120 },
  { key: 'purchase_quantity', name: '采购数量', width: 100 },
  { key: 'purchase_inbound_quantity', name: '采购入库数量', width: 120 },
  { key: 'undelivered_quantity', name: '未交付数量', width: 120 },
  { key: 'return_quantity', name: '退库数量', width: 100 },
  { key: 'total_purchase_amount', name: '采购总金额', width: 120 },
  { key: 'refund_amount', name: '退款金额', width: 100 },
  { key: 'inv_amount', name: '采购单应付金额', width: 150 },
  { key: 'gift_purchase_quantity', name: '赠品数量', width: 100 },
  { key: 'gift_purchase_unit_price', name: '赠品单价', width: 100 },
  { key: 'gift_amount_due', name: '赠品应付金额', width: 120 },
])

const tableConfig = ref<Record<string, any>>({
  isCheckbox: true,
  isIndex: false,
  hideTop: true,
  autoSearch: false,
  tableColumns: [],
  formFormat,
  rowConfig: {},
  checkboxConfig: {},
  clearCheckboxFlag: false,
})

const selectTableConfig = ref<Record<string, any>>({
  hideTop: true,
  hidePagination: true,
  autoSearch: false,
  tableColumns: [],
  rowConfig: {},
})

const selectRowsMap = ref({})
const selectRowsMapCount = computed(() => Object.keys(selectRowsMap.value).length)

// 添加标志位避免循环执行
const isProcessingSelection = ref(false)

const setCheckedRows = () => {
  const $table = tableRef.value.tableRef
  const rows: any[] = []
  const tableData = $table.getData()

  for (const item of tableData) {
    // 使用多种方式匹配
    const itemKey = item[tableKeyField.value] || item.$uuid
    let isSelected = selectRowsMap.value[itemKey]

    // 如果通过 $uuid 没有匹配到，尝试通过 detail_id 匹配
    if (!isSelected) {
      const detailId = item.detail_id || item.id
      const keys = Object.keys(selectRowsMap.value)
      for (const key of keys) {
        const selectedItem = selectRowsMap.value[key]
        const selectedDetailId = selectedItem.detail_id || selectedItem.id
        if (detailId === selectedDetailId) {
          isSelected = true
          break
        }
      }
    }

    if (isSelected) {
      rows.push(item)
    }
  }

  // 确保表格已经加载完成后再设置选中状态
  if (rows.length > 0) {
    $table.setCheckboxRow(rows, true)
  }
}

// 添加一个函数来检查当前选中的数据是否来自同一供应商
const checkCurrentSupplierConsistency = (selectedData: any[]) => {
  // 如果没有启用供应商校验，直接返回一致
  if (!props.supplierValidation.enabled) {
    return { consistent: true, supplierName: null }
  }

  if (selectedData.length <= 1) {
    return { consistent: true, supplierName: selectedData[0]?.supplier_name || null }
  }

  const firstSupplierName = selectedData[0]?.supplier_name
  const isConsistent = selectedData.every((item) => item.supplier_name === firstSupplierName)

  return {
    consistent: isConsistent,
    supplierName: isConsistent ? firstSupplierName : null,
  }
}

// 处理取消选择逻辑
const handleUnselectRows = (unselectedRows: any[]) => {
  if (!unselectedRows || unselectedRows.length === 0) return

  console.log('处理取消选择，行数:', unselectedRows.length)

  // 按采购单分组处理取消选择
  const purchaseNumberGroups = new Map<string, any[]>()

  unselectedRows.forEach((row) => {
    if (row.purchase_number) {
      if (!purchaseNumberGroups.has(row.purchase_number)) {
        purchaseNumberGroups.set(row.purchase_number, [])
      }
      purchaseNumberGroups.get(row.purchase_number)!.push(row)
    }
  })

  // 处理每个采购单组
  purchaseNumberGroups.forEach((rows, purchaseNumber) => {
    console.log(`处理采购单 ${purchaseNumber} 的取消选择，行数:`, rows.length)

    // 获取当前表格数据
    const tableData = tableRef.value?.tableRef?.getData() || []

    // 找到所有具有相同采购单号的行
    const samePurchaseRows = tableData.filter((row: any) => row.purchase_number === purchaseNumber)
    console.log(`采购单 ${purchaseNumber} 下的总明细行数:`, samePurchaseRows.length)

    // 取消选择这些行
    samePurchaseRows.forEach((row: any) => {
      const rowKey = row[tableKeyField.value] || row.$uuid
      if (rowKey && selectRowsMap.value[rowKey]) {
        delete selectRowsMap.value[rowKey]
        console.log('取消选中明细行:', row.detail_id || row.id)
      }
    })

    // 取消表格中的选中状态
    if (tableRef.value?.tableRef) {
      tableRef.value.tableRef.setCheckboxRow(samePurchaseRows, false)
    }
  })
}

// 检查采购单是否可以创建账单
const checkBillCreationEligibility = (selectedData: any[]) => {
  if (selectedData.length === 0) {
    return { eligible: true, message: null }
  }

  // 检查是否有 is_create_bill: false 的数据
  const ineligibleItems = selectedData.filter((item) => item.is_create_bill === false)

  if (ineligibleItems.length > 0) {
    const ineligibleNumbers = ineligibleItems.map((item) => item.purchase_number || item.number).join('、')
    return {
      eligible: false,
      message: `包含采购单${ineligibleNumbers}未生成应付单，请重新选择 提示：请手动复制订单号`,
      ineligibleItems,
    }
  }

  return { eligible: true, message: null }
}

const tableKeyField = ref('$uuid')

const open = async ({ left, right, search, width, api, keyField, dataFormat, supplierFilter, supplierName, disabledSupplier }: any, sourceRows = [], validationConfig?: any) => {
  processWidth.value = width || 1200
  queryParams.value = {}
  selectRowsMap.value = {}
  baseParams.value = {}

  // 设置校验配置
  if (validationConfig) {
    // 合并校验配置
    Object.assign(props.supplierValidation, validationConfig)
  }

  // 设置 keyField，优先使用传入的 keyField，否则使用默认值
  tableKeyField.value = keyField || '$uuid'

  // 处理供应商过滤
  if (supplierFilter && supplierName) {
    // 禁用供应商字段
    if (disabledSupplier) {
      // 只禁用供应商字段，不禁用供应商子公司字段
      searchFormList.value = search.map((placeholder) => {
        const formItem = { ...formMap[placeholder], placeholder: formMap[placeholder]?.alias || placeholder }
        if (placeholder === '供应商') {
          formItem.disabled = true
        }
        // 为供应商子公司字段添加供应商过滤条件
        if (placeholder === '供应商子公司' && supplierFilter) {
          formItem.apiParams = { supplier_id: supplierFilter }
        }
        return formItem
      })
    } else {
      searchFormList.value = search.map((placeholder) => {
        const formItem = { ...formMap[placeholder], placeholder: formMap[placeholder]?.alias || placeholder }
        // 为供应商子公司字段添加供应商过滤条件
        if (placeholder === '供应商子公司' && supplierFilter) {
          formItem.apiParams = { supplier_id: supplierFilter }
        }
        return formItem
      })
    }

    // 设置查询参数
    // 为SelectSupplier组件设置正确的值
    queryParams.value.supplier_id = JSON.stringify({ label: supplierName, value: supplierFilter }) // 传递JSON字符串
    baseParams.value.supplier_id = supplierFilter // 用于接口查询
  } else {
    searchFormList.value = search.map((placeholder) => ({ ...formMap[placeholder], placeholder: formMap[placeholder]?.alias || placeholder }))
  }

  // 设置 dataFormat 函数
  if (dataFormat) {
    tableConfig.value.dataFormat = dataFormat
  }

  tableConfig.value.getList = api || GetPurchaseOrderList
  tableRef.value && tableRef.value.clearCheckbox()

  tableConfig.value.rowConfig.keyField = tableKeyField.value
  selectTableConfig.value.rowConfig.keyField = tableKeyField.value

  tableConfig.value.tableColumns = left.map((key: string) => {
    const found = defaultColumns.value.find((f) => [f.name, f.key].includes(key))
    if (!found) throw new Error(`tableConfig columns ${key} not found`)
    return {
      ...found,
      name: found.name,
    }
  })

  selectTableConfig.value.tableColumns = (right || []).reduce((acc, key: string) => {
    const found = defaultColumns.value.find((f) => [f.name, f.key].includes(key))
    if (!found) throw new Error(`selectTableConfig columns ${key} not found`)
    return [
      ...acc,
      {
        ...found,
        name: found.name,
      },
    ]
  }, [])

  // 放开选择条件，允许选择所有采购单
  tableConfig.value.checkboxConfig.checkMethod = undefined

  openFlag.value = true

  // 处理 sourceRows，应用 dataFormat 生成 $uuid
  let processedSourceRows = sourceRows
  if (dataFormat && sourceRows.length > 0) {
    // 检查 sourceRows 是否已经有 $uuid 字段
    const hasUUID = sourceRows.some((item: any) => item.$uuid)
    if (!hasUUID) {
      // 只有在没有 $uuid 字段时才应用 dataFormat
      processedSourceRows = dataFormat(sourceRows)
    }
  }

  selectRowsMap.value = processedSourceRows.reduce((acc: any, item: any) => {
    const key = item[tableKeyField.value] || item.$uuid
    return { ...acc, [key]: item }
  }, {})

  await nextTick()
  await tableRef.value?.search()

  // 确保右边表格显示初始数据
  if (processedSourceRows.length > 0) {
    selectTableRef?.value?.tableRef?.reloadData(processedSourceRows)
  }

  // 设置左侧表格的选中状态
  if (Object.keys(selectRowsMap.value).length > 0) {
    setCheckedRows()
  }

  // 监听选择变化
  watcher.value = watch(
    () => tableRef.value?.checkItemsArr,
    () => {
      // 安全检查：确保tableRef已经初始化
      if (!tableRef.value || isProcessingSelection.value) {
        return
      }

      // 设置处理标志
      isProcessingSelection.value = true

      try {
        const checkItems = tableRef.value.tableRef.getCheckboxRecords()
        const keys = Object.keys(selectRowsMap.value)

        // 清理已取消选中的项目
        const unselectedItems: any[] = []
        for (const key of keys) {
          const isStillChecked = checkItems.find((f) => {
            const itemKey = f[tableKeyField.value] || f.$uuid
            return itemKey === key
          })
          if (!isStillChecked) {
            // 获取被取消选择的项目
            const unselectedItem = selectRowsMap.value[key]
            if (unselectedItem) {
              unselectedItems.push(unselectedItem)
            }
            // 如果没有采购单号，直接删除
            if (!unselectedItem?.purchase_number) {
              delete selectRowsMap.value[key]
            }
          }
        }

        // 如果有取消选择的项目，使用专门的取消选择处理函数
        if (unselectedItems.length > 0) {
          handleUnselectRows(unselectedItems)
          // 更新右边表格数据
          const selectedData = Object.values(selectRowsMap.value)
          selectTableRef?.value?.tableRef?.reloadData(selectedData)
          return // 取消选择后直接返回，避免重新选择
        }

        // 添加新选中的项目，并实现按采购单分组选择
        const newSelectedItems: any[] = []
        const processedPurchaseNumbers = new Set() // 记录已处理的采购单号

        for (const item of checkItems) {
          const itemKey = item[tableKeyField.value] || item.$uuid
          if (itemKey) {
            // 检查是否已经处理过这个采购单
            if (item.purchase_number && !processedPurchaseNumbers.has(item.purchase_number)) {
              console.log('检测到新选择的采购单:', item.purchase_number)

              // 获取当前表格数据
              const tableData = tableRef.value.tableRef.getData()

              // 找到所有具有相同采购单号的行
              const samePurchaseRows = tableData.filter((row: any) => row.purchase_number === item.purchase_number)
              console.log(`采购单 ${item.purchase_number} 下的明细行数:`, samePurchaseRows.length)

              // 将这些行都添加到选择中
              samePurchaseRows.forEach((row: any) => {
                const rowKey = row[tableKeyField.value] || row.$uuid
                if (rowKey && !selectRowsMap.value[rowKey]) {
                  selectRowsMap.value[rowKey] = row
                  newSelectedItems.push(row)
                  console.log('自动选中明细行:', row.detail_id || row.id)
                }
              })

              // 标记这个采购单已处理
              processedPurchaseNumbers.add(item.purchase_number)
            } else if (!item.purchase_number) {
              // 如果没有采购单号，直接添加
              if (!selectRowsMap.value[itemKey]) {
                selectRowsMap.value[itemKey] = item
                newSelectedItems.push(item)
              }
            }
          }
        }

        // 如果有新选中的项目，更新表格选中状态
        if (newSelectedItems.length > 0) {
          console.log('新选中的项目数量:', newSelectedItems.length)
          // 设置新项目的选中状态
          tableRef.value.tableRef.setCheckboxRow(newSelectedItems, true)
        }

        // 更新右边表格数据
        const selectedData = Object.values(selectRowsMap.value)
        selectTableRef?.value?.tableRef?.reloadData(selectedData)

        // 实时校验
        if (selectedData.length > 0) {
          // 检查采购单是否可以创建账单
          const billEligibilityCheck = checkBillCreationEligibility(selectedData)
          if (!billEligibilityCheck.eligible) {
            message.error(billEligibilityCheck.message)

            // 取消所有不符合条件的选择
            if (billEligibilityCheck.ineligibleItems) {
              billEligibilityCheck.ineligibleItems.forEach((item) => {
                const itemKey = item[tableKeyField.value] || item.$uuid
                if (itemKey) {
                  // 从selectRowsMap中移除
                  delete selectRowsMap.value[itemKey]
                  // 取消表格中的选中状态
                  tableRef.value.tableRef.setCheckboxRow([item], false)
                }
              })
            }

            // 更新右边表格数据
            const updatedSelectedData = Object.values(selectRowsMap.value)
            selectTableRef?.value?.tableRef?.reloadData(updatedSelectedData)
            return
          }

          // 检查当前选中的数据是否来自同一供应商
          const consistencyCheck = checkCurrentSupplierConsistency(selectedData)

          if (consistencyCheck.consistent) {
            console.log('供应商校验通过，所有数据来自同一供应商:', consistencyCheck.supplierName)
          } else {
            message.warning(props.supplierValidation.message)

            // 取消最后一个选中的项目
            const lastSelectedItem = checkItems[checkItems.length - 1]
            if (lastSelectedItem && tableRef.value) {
              const lastItemKey = lastSelectedItem[tableKeyField.value] || lastSelectedItem.$uuid
              if (lastItemKey) {
                // 从selectRowsMap中移除
                delete selectRowsMap.value[lastItemKey]
                // 取消表格中的选中状态
                tableRef.value.tableRef.setCheckboxRow([lastSelectedItem], false)
                // 更新右边表格数据
                const updatedSelectedData = Object.values(selectRowsMap.value)
                selectTableRef?.value?.tableRef?.reloadData(updatedSelectedData)
              }
            }
          }
        }
      } finally {
        // 重置处理标志
        isProcessingSelection.value = false
      }
    },
    { immediate: true },
  )

  // 强制刷新供应商子公司字段的选项
  const refreshCompanySupplierOptions = (supplierId: string) => {
    // 找到供应商子公司字段的配置
    const companySupplierField = searchFormList.value.find((item) => item.name === 'company_supplier_name')
    if (companySupplierField) {
      // 更新供应商子公司字段的过滤条件
      companySupplierField.apiParams = { supplier_id: supplierId }

      // 清空供应商子公司字段的值
      queryParams.value.company_supplier_name = ''

      // 强制触发响应式更新
      searchFormList.value = [...searchFormList.value]
    }
  }

  // 监听供应商字段值变化，动态更新供应商子公司过滤条件
  watch(
    () => queryParams.value.supplier_name,
    (newSupplierName) => {
      // 处理字符串格式的数据
      let supplierId: string | null = null
      if (typeof newSupplierName === 'string' && newSupplierName) {
        // 如果是字符串，说明是直接的值
        supplierId = newSupplierName
      }

      if (supplierId) {
        refreshCompanySupplierOptions(supplierId)
      }
    },
    { immediate: false },
  )
}

const handleClear = () => {
  selectRowsMap.value = {}
  tableRef.value?.clearCheckbox()
}

const handleClose = () => {
  watcher.value && watcher.value()
  openFlag.value = false
}

const handleConfirm = async () => {
  const selectedData = Object.values(selectRowsMap.value)

  // 最终检查：采购单是否可以创建账单
  const billEligibilityCheck = checkBillCreationEligibility(selectedData)
  if (!billEligibilityCheck.eligible) {
    message.error(billEligibilityCheck.message)
    return
  }

  if (props.syncConfirm) {
    const flag = await props.syncConfirm(selectedData)
    if (flag) handleClose()
  } else {
    emits('confirm', selectedData)
    handleClose()
  }
}

defineExpose({
  open,
})
</script>

<style lang="scss" scoped>
//
</style>
