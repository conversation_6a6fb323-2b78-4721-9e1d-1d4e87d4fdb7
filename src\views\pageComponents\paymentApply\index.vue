<template>
  <div class="main">
    <BaseForm ref="formRef" v-model:form="formArr" @search="search" @setting="tableRef?.showTableSetting()" :page-type="PageTypeEnum.PaymentApply">
      <template #header>
        <StatusTabs :options="statusList" v-model:status="status" @change="changeTip" />
      </template>
    </BaseForm>

    <BaseTable
      ref="tableRef"
      :page-type="PageTypeEnum.PaymentApply"
      v-model:form="formArr"
      :get-list="List"
      :form-format="formFormat"
      show-footer
      :footer-data="footerData"
      :check-cb="statTotal"
      :isCheckbox="true"
    >
      <template #left-btn>
        <a-button @click="passAllBtn" v-if="status == 10 && btnPermission[11001]">批量审核通过</a-button>
        <a-button @click="rejectAllBtn" v-if="status == 10 && btnPermission[11002]">批量驳回</a-button>
        <a-button @click="deleteBtn" v-if="status == 10 && btnPermission[11004]">删除</a-button>
      </template>
      <template #right-btn>
        <a-button @click="exportData" v-if="btnPermission[11005]" type="primary">按筛选结果导出</a-button>
      </template>
      <template #apply_time="{ row }">
        <span>{{ formatTime(row.apply_time, 'YYYY-MM-DD') }}</span>
      </template>
      <template #bill_month="{ row }">
        <span>{{ formatTime(row.bill_month, 'YYYY-MM') }}</span>
      </template>
      <template #payment_time="{ row }">
        <span>{{ formatTime(row.payment_time, 'YYYY-MM') }}</span>
      </template>

      <template #receipt_account_seal_voucher="{ row, rowIndex }">
        <div class="bcall">
          <a-tooltip v-for="(item, index) in row.receipt_account_seal_voucher_list" :key="item">
            <div class="fileContent" @click="previewBtn(item, index, rowIndex, 'receipt_account_seal_voucher_list')">
              <div class="bcspan">{{ item.org_name }}</div>
              <div class="status">
                <DashOutlined style="color: #1890ff" v-if="!item.status" />
                <CheckCircleFilled style="color: #49cc90" v-if="item.status == 2" />
                <LoadingOutlined style="color: #1890ff" v-if="item.status == 1" />
                <CloseCircleOutlined style="color: #ff4d4f" v-if="item.status == 3" />
              </div>
            </div>
          </a-tooltip>
        </div>
      </template>

      <template #expense_apply_attachment="{ row, rowIndex }">
        <div class="bcall">
          <a-tooltip v-for="(item, index) in row.expense_apply_attachment_list" :key="item">
            <div class="fileContent" @click="previewBtn(item, index, rowIndex, 'expense_apply_attachment_list')">
              <div class="bcspan">{{ item.org_name }}</div>
              <div class="status">
                <DashOutlined style="color: #1890ff" v-if="!item.status" />
                <CheckCircleFilled style="color: #49cc90" v-if="item.status == 2" />
                <LoadingOutlined style="color: #1890ff" v-if="item.status == 1" />
                <CloseCircleOutlined style="color: #ff4d4f" v-if="item.status == 3" />
              </div>
            </div>
          </a-tooltip>
        </div>
      </template>
      <template #contract_attachment="{ row, rowIndex }">
        <div class="bcall">
          <a-tooltip v-for="(item, index) in row.contract_attachment_list" :key="item">
            <div class="fileContent" @click="previewBtn(item, index, rowIndex, 'contract_attachment_list')">
              <div class="bcspan">{{ item.org_name }}</div>
              <div class="status">
                <DashOutlined style="color: #1890ff" v-if="!item.status" />
                <CheckCircleFilled style="color: #49cc90" v-if="item.status == 2" />
                <LoadingOutlined style="color: #1890ff" v-if="item.status == 1" />
                <CloseCircleOutlined style="color: #ff4d4f" v-if="item.status == 3" />
              </div>
            </div>
          </a-tooltip>
        </div>
      </template>

      <template #bank_receipt="{ row }">
        <a-image :width="50" :src="row.bank_receipt" :preview="{ maskClassName: '预览' }" referrerpolicy="no-referrer" />
      </template>
      <template #audit_status="{ row }">
        <span :class="formatAuditStatusClass(row)">{{ formatAuditStatus(row) }}</span>
      </template>
      <template #is_already_reject="{ row }">
        <span v-if="row.apply_time">{{ row.is_already_reject === true ? '是' : '否' }}</span>
      </template>
      <template #prepay_order_number="{ row }">
        <CopyOutlined class="cursor-pointer mr-4px" @click="copyText(row.prepay_order_number)"></CopyOutlined>
        <span>{{ row.prepay_order_number }}</span>
        <a-tag color="orange" class="ml5" v-if="row.prepay_order_qty > 1">多单</a-tag>
      </template>
      <template #fix_option="{ row, column }">
        <RightOperate :list="rightOperateList" :row="row" :column="column" />
      </template>
    </BaseTable>

    <a-modal v-model:open="modelShow" :title="modalArr[modalIdx].title" okText="确定" cancelText="取消" @ok="modalArr[modalIdx].okFn" @cancel="modalIdx = 0" style="top: 200px">
      <span v-if="modalIdx != 2 && modalIdx != 4">{{ modalArr[modalIdx].cont }}</span>

      <a-form>
        <a-form-item label="驳回原因" :label-col="{ span: 5 }" v-if="modalIdx == 2 || modalIdx == 4" v-bind="rejectInfos.rejectRemark">
          <a-textarea v-model:value="rejectForm.rejectRemark" placeholder="请填写驳回原因" />
        </a-form-item>
      </a-form>
    </a-modal>

    <a-modal v-model:open="uploadShow" :title="'上传银行回单'" okText="确定" cancelText="取消" @ok="submitPic" @cancel="uploadShow = false" style="top: 200px">
      <a-form-item label="本次实际付款金额" :label-col="{ span: 7 }" v-bind="receiptInfos.amount">
        <a-input v-model:value="bankReceiptForm.amount" placeholder="请输入本次实际付款金额" type="number" />
      </a-form-item>
      <a-form-item class="image-upload" label="上传银行回单" :label-col="{ span: 7 }" v-bind="receiptInfos.bank_receipt_file_id">
        <a-upload
          name="idCardzm"
          list-type="picture-card"
          class="avatar-uploader"
          :show-upload-list="false"
          :before-upload="beforeUpload"
          :customRequest="handleUploadChange"
          v-if="!formTableData.idCardzm"
        >
          <div>
            <plus-outlined></plus-outlined>
            <div class="ant-upload-text">银行回单</div>
          </div>
        </a-upload>
        <a-image :width="200" :preview="{ maskClassName: '预览' }" referrerpolicy="no-referrer" v-if="formTableData.idCardzm" :src="formTableData.idCardzm"></a-image>
        <close-outlined v-if="formTableData.idCardzm" class="delete-icon" type="close-circle" @click.stop="deleteImage('idCardzm')" />

        <div class="upload-desc">图片文件大小不超过10mb</div>
      </a-form-item>
    </a-modal>

    <a-modal
      v-model:open="isBatchReview"
      title="批量审核"
      okText="确定"
      cancelText="取消"
      @ok="serPassAllData"
      @cancel="isBatchReview = false"
      style="top: 200px"
      :maskClosable="false"
      :keyboard="false"
      :confirmLoading="isConfirmLoading"
      :closable="false"
    >
      <div class="batchReviewMain">
        <div class="list">
          <div class="item" v-for="(item, i) in flowIdArr" :key="i">
            <div class="label">OA流水号</div>
            <div class="text">{{ item.oa_running_number }}</div>
            <div class="icon">
              <a-spin v-if="item.isPass == 0" />
              <CheckCircleFilled class="icon-success" v-if="item.isPass == 1" />
              <CloseCircleFilled class="icon-error" v-if="item.isPass == 2" />
            </div>
          </div>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import { CheckCircleFilled, CloseCircleFilled, CloseCircleOutlined, CloseOutlined, ExclamationCircleOutlined, PlusOutlined, DashOutlined, LoadingOutlined, CopyOutlined } from '@ant-design/icons-vue'
import { Form, message, Modal } from 'ant-design-vue'
import dayjs from 'dayjs'
import { createVNode, onMounted, reactive, ref, watch } from 'vue'

import { checkFormParams, copyText, number2, upLoadXlsx } from '@/utils'
import BaseForm from '@/components/Form.vue'

import {
  ApplicantFillterOptions,
  AuditPass,
  AuditReject,
  Count,
  DeleteApply,
  Export,
  List,
  PaymentAccountFillterOptions,
  PaymentMethodFillterOptions,
  Preview,
  Total,
  Upload,
  UploadBankReceipt,
  ReDownloadFile,
} from '@/servers/PaymentApply'

import { PageTypeEnum } from '@/enums/tableEnum'

const { btnPermission } = usePermission()
const useForm = Form.useForm

const tableRef = ref()
const formRef = ref()
const search = () => {
  getTotal()
  tableRef.value.search()
}

const formFormat = (data) => ({
  audit_status: status.value,
  ...data,
  apply_start_time: data.apply_start_time ? dayjs(data.apply_start_time).startOf('month').format('YYYY-MM-DD') : null,
  apply_end_time: data.apply_end_time ? dayjs(data.apply_end_time).endOf('month').format('YYYY-MM-DD') : null,
  bill_month_start_time: data.bill_month_start_time ? dayjs(data.bill_month_start_time).startOf('month').format('YYYY-MM-DD') : null,
  bill_month_end_time: data.bill_month_end_time ? dayjs(data.bill_month_end_time).endOf('month').format('YYYY-MM-DD') : null,
})

const rightOperateList = ref([
  {
    label: '审批通过',
    show: ({ row }) => {
      return btnPermission.value[11001] && row.audit_status === 10
    },
    onClick: ({ row }) => {
      passBtn(row)
    },
  },
  {
    label: '驳回',
    show: ({ row }) => {
      return btnPermission.value[11002] && row.audit_status === 10
    },
    onClick: ({ row }) => {
      rejectBtn(row)
    },
  },
  {
    label: '上传回单',
    show: ({ row }) => {
      return btnPermission.value[11003] && row.audit_status === 20
    },
    onClick: ({ row }) => {
      openUploadDlg(row)
    },
  },
])

const isBatchReview = ref(false)
const isConfirmLoading = ref(false)
const flowIdArr = ref([] as any)

const countData = ref({
  alreadyPaymentCount: 0,
  alreadyRejectCount: 0,
  awaitAuditCount: 0,
  awaitPaymentCount: 0,
})
const formArr: any = ref([
  {
    label: '搜索OA流水号',
    value: '',
    type: 'input',
    key: 'oa_running_number',
    isShow: true,
  },
  {
    label: '搜索单号',
    value: '',
    type: 'input',
    key: 'prepay_order_number',
    isShow: true,
  },
  {
    label: '供应商名称',
    value: [],
    multiple: true,
    type: 'supplier',
    selectArr: [],
    key: 'supplier_id',
    isShow: true,
  },
  {
    label: '结算方式',
    value: [],
    type: 'select',
    multiple: true,
    selectArr: [
      {
        label: '线上账期-月结',
        value: '线上账期-月结',
      },
      {
        label: '线下账期-月结',
        value: '线下账期-月结',
      },
      {
        label: '线下-预付款',
        value: '线下-预付款',
      },
      {
        label: '线下-分期预付款',
        value: '线下-分期预付款',
      },
    ],
    key: 'settlement_method',
    isShow: true,
    isQuicks: true,
    quickIndex: 2,
    quickLabelWidth: 105,
    line: true,
  },
  {
    label: '申请部门',
    value: [],
    type: 'select',
    key: 'applicant_department_name',
    selectArr: [],
    multiple: true,
    isShow: true,
  },
  {
    label: '申请人',
    value: [],
    type: 'select',
    key: 'applicant_id',
    selectArr: [],
    multiple: true,
    isShow: true,
  },
  {
    label: '支付方式',
    value: [],
    type: 'select',
    key: 'payment_method',
    selectArr: [],
    isShow: true,
    isQuicks: true,
    quickIndex: 1,
    line: true,
  },
  {
    label: '付款账户名称(公户)',
    value: [],
    type: 'select',
    key: 'payment_account_name_public',
    selectArr: [],
    quickLabelWidth: 105,
    multiple: true,
    isShow: true,
    isQuicks: true,
    quicks: ['汕头市西月电子商务有限公司', '广东云月仓供应链管理有限公司', '广东依欧好生物科技有限公司'],
    quickIndex: 3,
    line: true,
  },
  {
    label: '付款账户名称(私户)',
    value: [],
    type: 'select',
    key: 'payment_account_name_private',
    selectArr: [],
    quickLabelWidth: 105,
    multiple: true,
    isShow: true,
    isQuicks: true,
    quicks: [
      { label: '周婵柔(中信7401)', value: '周婵柔|****************' },
      { label: '张育玲(中信7419)', value: '张育玲|****************' },
      { label: '张娜娜(民生8013)', value: '张娜娜|****************' },
      { label: '月仓一吴伟民(中信3965)', value: '吴伟民|****************' },
      { label: '月仓一郑熙悦(民生9898)', value: '郑熙悦|****************' },
      { label: '加工厂一陈静(中信7393)', value: '陈静|****************' },
      { label: '加工厂一周婵柔(9171)', value: '周婵柔|****************' },

      // 周婵柔(中信7401) 周禅柔 中信银行汕头分行营业部 ****************
      // 张育玲(中信7419) 张育玲 中信银行汕头分行营业部 ****************
      // 张娜娜(民生8013) 张娜娜 民生汕头分行 ****************
      // 月仓一吴伟民(中信3965) 吴伟民 中信银行汕头分行营业部 ****************
      // 月仓一郑熙悦(民生9898) 郑熙悦 中国民生银行汕头分行营业部 ****************
      // 加工厂一陈静(中信7393) 陈静 中信银行汕头分行营业部 ****************
      // 加工厂-周婵柔(9171) 周婵柔 中国民生银行股份有限公司汕头分行营业部 ****************
    ],
    quickIndex: 4,
  },
  {
    label: '申请时间',
    value: null,
    type: 'range-picker',
    key: 'time_type',
    showTime: false,
    valueFormat: 'YYYY-MM-DD',
    formKeys: ['apply_start_time', 'apply_end_time'],
    isShow: true,
    placeholder: ['申请开始时间', '申请结束时间'],
  },
  {
    label: '账单月份',
    value: null,
    type: 'range-picker',
    picker: 'month',
    key: 'bill_time_type',
    formKeys: ['bill_month_start_time', 'bill_month_end_time'],
    isShow: true,
    placeholder: ['账单开始月份', '账单结束月份'],
  },
])
const tableRecords = computed(() => {
  return tableRef?.value?.tableRef?.getCheckboxRecords() || []
})
const auditStatusList = [
  { value: 10, label: '待审核' },
  { value: 20, label: '待出纳付款' },
  { value: 90, label: '已付款' },
  { value: 95, label: '已驳回作废' },
]

const tableAllid = ref()
const modalIdx = ref(0)
const modelShow = ref(false)
const modalArr = reactive([
  {
    title: '',
    cont: '',
    okFn: () => {},
  },
  {
    title: '提示',
    cont: '确认通过此单？',
    okFn: () => {
      passFlow()
    },
  },
  {
    title: '驳回',
    cont: '确认驳回此单？',
    okFn: () => {
      rejectFlow()
    },
  },
  {
    title: '提示',
    cont: '确认批量审批？',
    okFn: () => {
      passAllFlow()
    },
  },
  {
    title: '驳回',
    cont: '确认批量驳回？',
    okFn: () => {
      rejectAllFlow()
    },
  },
])
// 上传弹框
const uploadShow = ref(false)
const formTableData = ref({ idCardzm: '' })
const bankReceiptForm = reactive({
  id: 0,
  amount: 0,
  bank_receipt_file_id: 0,
} as any)

const checkAmount = async (_rule, value: number) => {
  if (value > **********) {
    return Promise.reject('最大金额不超过十位数')
  }
  return Promise.resolve()
}
const {
  clearValidate: receiptClearValidate,
  validate: receiptValidate,
  validateInfos: receiptInfos,
} = useForm(
  bankReceiptForm,
  reactive({
    amount: [{ required: true, message: '请输入实际付款金额' }, { validator: checkAmount }],
    bank_receipt_file_id: [{ required: true, message: '请上传银行回单！' }],
  }),
)

const rejectForm = ref({
  rejectRemark: '',
})

const { validate: rejectValidate, validateInfos: rejectInfos } = useForm(
  rejectForm,
  reactive({
    rejectRemark: [
      { required: true, message: '请输入驳回原因' },
      { max: 200, message: '驳回原因不能超过200字' },
    ],
  }),
)
watch(modalIdx, (newvalue) => {
  if (newvalue != 0) {
    modelShow.value = true
  } else {
    modelShow.value = false
  }
})

onMounted(() => {
  initApi()
})

const statTotal = (data) => {
  const $table = tableRef?.value?.tableRef
  if (!$table) return []
  const keys = tableRef.value.tableKey

  const columns = $table.getColumns()
  const isCheck = data.length

  const obj = {} as any

  columns.forEach((column, _columnIndex) => {
    if (_columnIndex === 0) {
      obj.checkbox = isCheck ? '勾选合计' : '合计'
      return
    }
    if (!keys.find((v) => v.key === column.field)?.is_total) return

    if (isCheck) {
      if (totalField.includes(column.field)) {
        obj[column.field] = number2(data, column.field)
      }
    } else {
      obj[column.field] = totalData.value[column.field] ? totalData.value[column.field] : null
    }
  })
  footerData.value = [obj]
  console.log(footerData.value)
}

const totalData = ref({})
const totalField = ['purchase_order_amount', 'payable_order_amount', 'other_fee', 'optimize_amount', 'deduct_amount', 'the_payable_amount', 'the_actual_amount']

const getTotal = () => {
  const obj = {
    totalField: tableRef.value?.tableKey?.length ? totalField.filter((v) => tableRef.value.tableKey.find((k) => k.is_total && k.key === v)) : totalField,
    audit_status: status.value,
  }
  checkFormParams({ formArr: formArr.value, obj })
  Total(obj).then((res) => {
    totalData.value = res.data
    statTotal(tableRef.value.checkItemsArr)
  })
}

const initApi = async () => {
  ApplicantFillterOptions(1).then((res) => {
    formArr.value.find((v) => v.key === 'applicant_id').selectArr = res.data.map((v) => ({ label: v.value, value: v.key }))
  })
  ApplicantFillterOptions(2).then((res) => {
    formArr.value.find((v) => v.key === 'applicant_department_name').selectArr = res.data
  })
  PaymentAccountFillterOptions({ paymentAccountOptionType: 1 }).then((res) => {
    formArr.value.find((v) => v.key === 'payment_account_name_public').selectArr = res.data.map((v) => ({ label: v.value, value: v.key }))
  })
  PaymentAccountFillterOptions({ paymentAccountOptionType: 2 }).then((res) => {
    formArr.value.find((v) => v.key === 'payment_account_name_private').selectArr = res.data.map((v) => ({ label: v.value, value: v.key }))
  })
  PaymentMethodFillterOptions({}).then((res) => {
    formArr.value.find((v) => v.key === 'payment_method').selectArr = res.data.map((v) => ({ label: v.value, value: v.key }))
  })
  getTotal()
  getCount()
}

const getPassAllData = async () => {
  isConfirmLoading.value = false
  const arr = [] as any
  tableRecords.value.forEach((item) => {
    if (item.apply_time) {
      arr.push({
        id: item.id,
        oa_running_number: item.oa_running_number,
        isPass: -1,
      })
    }
  })
  flowIdArr.value = arr
}
const allAuditPass = async (i) => {
  flowIdArr.value[i].isPass = 0
  const res = await AuditPass({ ids: [flowIdArr.value[i].id] })
  if (res.success) {
    flowIdArr.value[i].isPass = 1
  } else {
    flowIdArr.value[i].isPass = 2
  }
  if (i < flowIdArr.value.length - 1 && isBatchReview.value) {
    allAuditPass(i + 1)
  } else {
    isConfirmLoading.value = false
    isBatchReview.value = false
    flowIdArr.value = []
    tableRef.value.refresh()
    await getCount()
  }
}
const serPassAllData = () => {
  isConfirmLoading.value = true
  allAuditPass(0)
}

const formatTime = (val, type) => {
  if (!val) return ''
  return dayjs(val).format(type)
}

const formatAuditStatus = (row) => {
  const obj = auditStatusList.find((item) => item.value == row.audit_status)
  if (obj) {
    if (obj.value === 10 && row.is_already_reject) return '驳回待审核'
    return obj.label
  }
  return row.audit_status
}

const formatAuditStatusClass = (row) => {
  if (row.audit_status == 90) {
    return 'greenFont'
  }
  if (row.audit_status == 95) {
    return 'redFont'
  }
  return ''
}

const getCount = () => {
  const obj = { page: 1, pageSize: 1 }
  Count(obj).then((res) => {
    countData.value = res.data
  })
}

const passBtn = (row) => {
  tableAllid.value = [row.id]
  modalIdx.value = 1
}

const rejectBtn = (row) => {
  tableAllid.value = [row.id]
  modalIdx.value = 2
  rejectForm.value.rejectRemark = ''
}

const passAllBtn = () => {
  if (tableRecords.value.length) {
    // modalIdx.value = 3;
    isBatchReview.value = true
    getPassAllData()
  } else {
    message.info('请勾选需要的申请单！')
  }
}

const rejectAllBtn = () => {
  if (tableRecords.value.length) {
    modalIdx.value = 4
    rejectForm.value.rejectRemark = ''
    tableAllid.value = tableRecords.value.map((v) => v.id)
  } else {
    message.info('请勾选需要的申请单！')
  }
}

const deleteBtn = () => {
  if (!tableRecords.value.length) {
    message.info('请勾选需要的申请单')
    return
  }

  Modal.confirm({
    title: '是否删除选中的申请单',
    icon: createVNode(ExclamationCircleOutlined),
    onOk() {
      DeleteApply(tableRecords.value.map((v) => v.id)).then(() => {
        message.success('删除成功')
      })
    },
  })
}

const passFlow = () => {
  const data = { ids: tableAllid.value }
  modalIdx.value = 0
  message.info('正在执行中，请稍等...')

  AuditPass(data).then((res) => {
    if (res.success) {
      tableRef.value.refresh()
      getCount()
      tableAllid.value = ''
      message.success('已通过此单')
      // modalIdx.value = 0;
    } else {
      message.error(res.data.msg)
    }
  })
}

const rejectFlow = () => {
  rejectValidate().then(() => {
    const data = { ids: tableAllid.value, remark: rejectForm.value.rejectRemark }
    modalIdx.value = 0
    message.info('正在执行中，请稍等...')
    AuditReject(data).then((res) => {
      if (res.success) {
        tableRef.value.refresh()
        getCount()
        tableAllid.value = ''
        message.success('已驳回此单')
        // modalIdx.value = 0;
      } else {
        message.error(res.data.msg)
      }
    })
  })
}

const passAllFlow = () => {
  const data = { ids: [] } as any
  tableRecords.value.forEach((item) => {
    data.ids.push(item.id)
  })
  // 可能会包含子单，需要去重
  data.ids = Array.from(new Set(data.ids))
  modalIdx.value = 0
  message.info('正在执行中，请稍等...')
  AuditPass(data).then((res) => {
    if (res.success) {
      message.success('批量审批通过')
      // modalIdx.value = 0;
      tableRef.value.refresh()
      getCount()
    } else {
      message.error(res.data.msg)
    }
  })
}

const rejectAllFlow = () => {
  rejectValidate().then(() => {
    const data = { ids: tableAllid.value, remark: rejectForm.value.rejectRemark }
    modalIdx.value = 0
    message.info('正在执行中，请稍等...')
    AuditReject(data).then((res) => {
      if (res.success) {
        message.success('已批量驳回')
        // modalIdx.value = 0;
        tableRef.value.refresh()
        getCount()
      } else {
        message.error(res.data.msg)
      }
    })
  })
}

// 在线预览
const previewBtn = (row, rowIndex, tableRowIdx, key) => {
  if (row.status == 2) {
    if (row.org_name.indexOf('doc') != -1 || row.org_name.indexOf('pdf') != -1 || row.org_name.indexOf('xls') != -1 || row.org_name.indexOf('ppt') != -1) {
      const id = row.id
      Preview(id).then((res) => {
        const url = res.data
        window.open(url, '_blank')
      })
    } else {
      window.open(row.url, '_blank')
    }
  } else if (row.status == 1 || !row.status) {
    message.info('当前文件未下载完成，请下载完成后再点击查看!')
  } else if (row.status == 3) {
    message.info('文件重新下载中，请等待下载结果后刷新页面查看!')
    setTimeout(() => {
      tableRef.value.tableData[tableRowIdx][key][rowIndex].status = 1
      ReDownloadFile({ id: row.id }).then((res) => {
        if (res.success) {
          message.info(res.data)
          search()
        } else {
          message.error(res.message)
        }
      })
    }, 200)
  }
}

const beforeUpload = (file) => {
  const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png'
  if (!isJpgOrPng) {
    message.error('只能上传JPG、PNG格式图片!')
  }
  const isLt2M = file.size / 1024 / 1024 < 10
  if (!isLt2M) {
    message.error('图片尺寸不能超过10MB!')
  }
  return isJpgOrPng && isLt2M
}
// 图片上传
const handleUploadChange = async (info) => {
  const idCard = info.filename
  const FormDatas = new FormData()
  FormDatas.append('file', info.file)
  FormDatas.append('module', String(UploadFileModuleEnum.PaymentApply))
  const data = (await Upload(FormDatas)).data
  formTableData.value[idCard] = data.url
  bankReceiptForm.bank_receipt_file_id = data.id
}

// 删除当前图片
const deleteImage = (idCard) => {
  formTableData.value[idCard] = '' // 清除图片数据
  bankReceiptForm.bank_receipt_file_id = null
}

// 打开银行回单弹框
const openUploadDlg = (row) => {
  formTableData.value = { idCardzm: '' }
  uploadShow.value = true
  nextTick(() => {
    bankReceiptForm.id = row.id
    bankReceiptForm.amount = row.the_payable_amount ? row.the_payable_amount : 0
    bankReceiptForm.bank_receipt_file_id = null
    nextTick(() => {
      receiptClearValidate()
    })
  })
}

const submitPic = () => {
  receiptValidate().then((res) => {
    console.log(res)
    const obj = bankReceiptForm
    UploadBankReceipt(obj).then(() => {
      message.success('上传成功!')
      uploadShow.value = false
      tableRef.value.refresh()
      getCount()
    })
  })
}

const status = ref(10)
const statusList = ref([
  { label: '待审核', value: 10, count: countData.value.awaitAuditCount },
  { label: '待出纳付款', value: 20, count: countData.value.awaitPaymentCount },
  { label: '已付款', value: 90 },
  { label: '已驳回作废', value: 95 },
  { label: '查看全部', value: '' },
])

const changeTip = () => {
  search()
  getCount()
}

const footerData = ref<VxeTablePropTypes.FooterData>([{}])

const route = useRoute()
const exportLoading = ref(false)
const exportData = () => {
  const obj = {
    audit_status: status.value,
    sortField: tableRef.value.orderby,
    sorttype: tableRef.value.ordersort ? 'asc' : 'desc',
  }
  checkFormParams({ formArr: formArr.value, obj })
  console.log(obj)
  exportLoading.value = true
  Export(obj).then((res) => {
    upLoadXlsx(res, `${route.name as string} - ${dayjs().format('YYYY-MM-DD')}`)
    exportLoading.value = true
  })
}
</script>

<style lang="scss" scoped>
.ml5 {
  margin-left: 5px;
}

.fileBox {
  margin-bottom: 3px;
}

.redFont {
  color: red;
}

.greenFont {
  color: green;
}

.iconBox {
  .redIcon {
    color: red;
  }

  .yellowIcon {
    color: orange;
  }

  .title {
    margin-left: 5px;
  }
}

.beibox {
  .beiboxfrom {
    display: flex;
    align-items: center;
    margin: 10px;

    .beiboxinput {
      width: 300px;
    }
  }
}

.batchReviewMain {
  .list {
    width: 100%;
    height: 400px;
    overflow: hidden scroll;

    .item {
      display: flex;
      align-items: center;
      margin-bottom: 10px;

      .label {
        width: 100px;
      }

      .text {
        width: 300px;
      }

      .icon {
        width: 100px;
        font-size: 20px;
        text-align: center;

        .icon-success {
          color: #1890ff;
        }

        .icon-error {
          color: #ff4d4f;
        }
      }
    }
  }
}

.avatar-uploader > .ant-upload {
  width: 128px;
  height: 128px;
}

.img {
  max-width: 100%;
  max-height: 100%;
}

.ant-upload-select-picture-card i {
  font-size: 32px;
  color: #999;
}

.ant-upload-select-picture-card .ant-upload-text {
  margin-top: 8px;
  color: #666;
}

.upload-desc {
  font-size: 12px;
}

.image-upload {
  position: relative;
}

.delete-icon {
  position: absolute;
  top: 5px;
  left: 205px;
  font-size: 18px;
  color: black;
  cursor: pointer;
}

.tipBox {
  margin-bottom: 16px;

  .btn {
    margin-right: 20px;
  }
}

.operateBox {
  display: flex;
  align-items: center;
  justify-content: space-around;
  width: 100%;
}

.bcall {
  display: flex;
  overflow: hidden;

  .fileContent {
    display: flex;
    margin: 0 0.125rem;
    cursor: pointer;
    border: 0.0625rem solid #c8d3e7;

    .bcspan {
      width: 70px;
      padding: 0.075rem 0.1875rem;
      overflow: hidden;
      font-size: 12px;
      color: #1890ff;
      text-overflow: ellipsis;
      white-space: nowrap;
      background-color: white;
      border-right: 0.0625rem solid #c8d3e7;
    }

    .status {
      width: 20px;
      font-size: 16px;
      text-align: center;
    }
  }
}

:deep(.ant-radio-group) {
  .ant-badge {
    .ant-badge-count {
      z-index: 2;
    }
  }
}

.clear-radius {
  border-end-start-radius: 0;
  border-end-end-radius: 0;
}
</style>
