<template>
  <div class="flex flex-col h-full main">
    <Form ref="formRef" v-model:form="formArr" :page-type="PageTypeEnum.PurchaseManagement" @search="tableRef?.search()" @setting="tableRef?.showTableSetting()">
      <template #header>
        <StatusTabs :options="purchaseAdjustPriceStatusOption" v-model:status="status" :count-map="labelStatusCountMap" @change="tableRef?.search()" />
      </template>
      <template #labelTop>
        <a-radio-group class="mb-20px" v-model:value="tagsSearchType">
          <a-radio value="1">包含任意一个标签</a-radio>
          <a-radio value="2">同时包含指定标签</a-radio>
        </a-radio-group>
      </template>
    </Form>
    <BaseTable
      ref="tableRef"
      :page-type="PageTypeEnum.PurchaseManagement"
      v-model:form="formArr"
      :get-list="GetPurchaseOrderList"
      :isCheckbox="true"
      :isIndex="true"
      :virtualYConfig="{ enabled: true }"
      :form-format="formFormat"
      :data-format="dataFormat"
      keyField="id"
      :page-size-options="['20', '50', '100', '250']"
      :expandData="({ row }) => row.purchaseOrderDetails"
      :expandUnHeader="true"
      :expandCellMinWidth="1"
      :expandColumnRenderMap="childrenRenderKeys"
      expandStateKey="SRM_ORDER_LIST_EXPAND_FLAG"
      @loadData="onLoadData"
    >
      <template #left-btn>
        <a-button v-if="btnPermission[84004] || btnPermission[84005] || btnPermission[84006] || btnPermission[84007]" @click="openReview(true)">批量审核通过</a-button>
        <a-button v-if="btnPermission[84004] || btnPermission[84005] || btnPermission[84006] || btnPermission[84007]" @click="openReview(false)">批量审核拒绝</a-button>
        <a-button @click="addReservationBtn()" v-if="btnPermission[84016]">生成预约入库单</a-button>
        <a-button @click="downLoadPurchaseOrder" v-if="btnPermission[84017]">下载电子采购单</a-button>
        <a-select v-if="btnPermission[84014]" v-model:value="batchModifyType" :dropdownMatchSelectWidth="false" style="width: 120px" @change="onBatchModifyTypeChange">
          <a-select-option value="" disabled>批量修改</a-select-option>
          <a-select-option value="1">修改备注</a-select-option>
          <a-select-option value="2" v-if="Number(status) === PurchaseOrderAuditStatusEnum.待提审">修改采购单信息</a-select-option>
          <a-select-option value="3">协议到货日期</a-select-option>
        </a-select>
        <a-button v-if="btnPermission[84019]" @click="handleBatchPayBy1688">1688批量支付</a-button>

        <!-- <a-dropdown>
          <template #overlay>
            <a-menu>
              <a-menu-item key="1">1st menu item</a-menu-item>
            </a-menu>
          </template>
          <a-button>
            订单操作
            <DownOutlined />
          </a-button>
        </a-dropdown> -->
      </template>
      <template #right-btn>
        <div class="ml-auto">
          <a-select v-model:value="exportType" :dropdownMatchSelectWidth="false" style="width: 120px; margin-right: 10px" @change="onExportTypeChange" :disabled="exportLoading">
            <a-select-option value="" disabled>导出</a-select-option>
            <a-select-option v-for="item in exportOptions" :key="item.value">{{ item.label }}</a-select-option>
          </a-select>
        </div>
        <a-button type="primary" @click="addPurchaseOrder">添加采购单</a-button>
      </template>
      <template #pagination-left>
        <span class="text-#999">当前汇总：</span>
        <template v-for="item in Object.keys(sumData)" :key="item">
          <span>{{ item }}：</span>
          <span class="text-#1890FF font-bold mr-12">{{ sumData[item].roundNext(item === '采购单总金额' ? 2 : 0) }}</span>
        </template>
      </template>
      <template #image_url="{ row, column, rowHeight }">
        <component :is="imageUrlListRender({ column, row, rowHeight })"></component>
      </template>
      <template #number="{ row, column }">
        <component :is="numberRender({ column, row })"></component>
      </template>
      <template #remark="{ row, column }">
        <component :is="remarkRender({ column, row })"></component>
      </template>

      <template #price_trend="{ row }">
        <component :is="priceTrendRender(row)"></component>
      </template>

      <template #online_order_numbers="{ row }">
        <component :is="onlineOrder({ row })"></component>
      </template>
      <template #fix_option="{ row, column }">
        <RightOperate :list="rightOperateList" :row="row" :column="column" />
      </template>
    </BaseTable>
    <AuditConfirm ref="auditConfirmRef" @audit="onAuditConfirm" />

    <pushOrderDlg ref="pushOrderDlgRef" @search="tableRef.search()" />
    <reviewTimer ref="reviewTimerRef"></reviewTimer>
    <BatchUpdateRemarkModal ref="batchUpdateRemarkModalRef" @success="tableRef.search()" />
    <BatchUpdateCompanyModal ref="batchUpdateCompanyModalRef" @success="tableRef.search()" />
    <historyModal ref="historyModalRef" @search="tableRef.search()" />
    <OperationLog ref="operationLogRef"></OperationLog>
    <CustomDrawer ref="customDrawerRef"></CustomDrawer>
  </div>
</template>
<script setup lang="ts">
import { DatePicker, message, Input, InputNumber } from 'ant-design-vue'
import { CopyOutlined, FallOutlined, RiseOutlined } from '@ant-design/icons-vue'
import dayjs from 'dayjs'

import { purchaseAdjustPriceStatusOption } from '@/common/options'
import { checkFormParams, copyText, Enum2Options, filterOption, generateUUID } from '@/utils'
import eventBus from '@/utils/eventBus'
import { useSearchForm } from '@/hook/useSearchForm'
import { textRender, imageListRender, copyRender } from '@/utils/VxeRender'
import DivGrid from '@/components/EasyTable/DivGrid.vue'

import pushOrderDlg from '../components/purchaseorderManagementComponents/pushOrderDlg.vue'
import reviewTimer from '../components/purchaseorderManagementComponents/reviewTimer.vue'
import BatchUpdateRemarkModal from '../components/purchaseorderManagementComponents/BatchUpdateRemarkModal.vue'
import BatchUpdateCompanyModal from '../components/purchaseorderManagementComponents/BatchUpdateCompanyModal.vue'
import historyModal from '../components/purchaseorderManagementComponents/historyModal.vue'

import { GetPageSuppliersSelect, GetPageMRPSupplierCompanySelect, GetSupplierCategorySelect, GetPLMMachiningType, ProductBrandSelect, AllProductCategorySelect } from '@/servers/BusinessCommon'
import { CheckPurchaseStatus } from '@/servers/ReservationApi'
import { GetWarehouses } from '@/servers/Purchaseapplyorder'
import {
  GetPurchaseOrderList,
  BatchAudit,
  OpenPurchaseOrder,
  ClosePurchaseOrder,
  GetBuyers,
  CancelledPurchaseOrder,
  CounterAuthorization,
  PurchaseOrderExportTypeEnum,
  ExportPurchaseOrderDetails,
  GetPurchaseOrderTagsSelect,
  BatchUpdatePurchaseOrderPredictDeliveryDate,
  BatchUpdatePurchaseOrderDetailRemark,
  BatchUpdatePurchaseOrderRemark,
  GetAliPayInfoList,
  BatchAliPay,
  UpdatePurchaseOrderPredictDeliveryDate,
  CompletedOrder,
  CheckPurchaseOrderAllowPriceChange,
  GetSplitPurchaseOrderApplyList,
  SplitPurchaseOrder,
} from '@/servers/PurchaseManage'
import { GetAliPurchaseAccountSelect } from '@/servers/PurchaseAccount'

import { PageTypeEnum, pageTableKeyEnum } from '@/enums/tableEnum'

const { btnPermission } = usePermission()
const { pushPage } = usePage({ refresh: () => tableRef.value.search() })
const { labelStatusCountMap, labelStatusRefresh } = useLabelStatus(PageTypeEnum.PurchaseManagement)
const router = useRouter()
const pushOrderDlgRef = ref()
const tableRef = ref()
const formRef = ref()
const tagsSearchType = ref<'1' | '2' | null>('1')
const modal: any = inject('modal')

const status = ref('')

const formArr = ref([
  { label: '采购单编号', value: '', type: 'inputDlg', key: 'number' },
  { label: '采购申请单编号', value: '', type: 'inputDlg', key: 'apply_number' },
  { label: '线上1688订单号', value: '', type: 'inputDlg', key: 'online_order_number' },
  { label: 'MRP计划单号', value: '', type: 'inputDlg', key: 'plan_number' },
  { label: '成品计划单号', value: '', type: 'inputDlg', key: 'finished_external_ids' },
  { label: '成品申请单号', value: '', type: 'inputDlg', key: 'finished_numbers' },
  {
    label: '订单状态',
    value: [],
    type: 'select',
    key: 'order_status',
    multiple: true,
    isQuicks: true,
    quickIndex: 1,
    line: true,
    selectArr: Enum2Options(PurchaseOrderStatusEnum),
  },
  {
    label: '采购单类型',
    value: [],
    type: 'select',
    multiple: true,
    selectArr: [
      {
        label: '标准采购申请',
        value: 1,
      },
      {
        label: '1688采购订单',
        value: 2,
      },
      {
        label: '委外加工单',
        value: 3,
      },
      {
        label: '寄售订单',
        value: 4,
      },
    ],
    key: 'types',
    isShow: false,
    isQuicks: true,
    quickIndex: 1,
    line: true,
  },
  { label: '商品编码', value: '', type: 'inputDlg', key: 'product_number' },
  { label: '商品名称', value: '', type: 'input', key: 'product_name' },
  {
    label: '供应商',
    value: undefined,
    type: 'select-supplier',
    key: 'supplier_id',
    mode: 'multiple',
    api: GetPageSuppliersSelect,
  },
  {
    label: '供应商分类',
    value: [],
    type: 'select',
    key: 'supplier_categories',
    multiple: true,
    showSearch: true,
    filterOption,
    api: GetSupplierCategorySelect,
  },
  {
    label: '供应商子公司',
    value: undefined,
    type: 'select-supplier',
    key: 'company_supplier_id',
    mode: 'multiple',
    api: GetPageMRPSupplierCompanySelect,
    apiParams: { is_contains_srs: true },
  },
  {
    label: '选择仓库',
    value: [],
    type: 'select',
    key: 'warehourse_ids',
    selectArr: [],
    multiple: true,
    api: GetWarehouses,
  },
  {
    label: '申请人',
    value: [],
    type: 'select',
    multiple: true,
    key: 'buyer_ids',
    selectArr: [],
    showSearch: true,
    filterOption: (input, option) => option.label?.toLowerCase().includes(input.toLowerCase()),
    api: GetBuyers,
  },
  {
    label: '订单状态',
    value: [],
    type: 'select',
    key: 'order_status',
    multiple: true,
    isQuicks: true,
    quickIndex: 1,
    selectArr: Enum2Options(PurchaseOrderStatusEnum),
  },
  { label: 'SRS平台商品编码', value: '', type: 'input', key: 'srs_platform_prod_code' },
  {
    label: '发货状态',
    value: [],
    type: 'select',
    key: 'shipment_status',
    multiple: true,
    selectArr: Enum2Options(ShipmentStatusEnum),
  },
  {
    label: '计划单来源',
    value: [],
    type: 'select',
    key: 'finished_source_types',
    multiple: true,
    selectArr: Enum2Options(FinishedApplyOrderSourceTypeEnum),
  },
  {
    label: '加工方式',
    value: [],
    type: 'select',
    multiple: true,
    selectArr: [],
    key: 'process_types',
    isShow: false,
    isQuicks: true,
    api: GetPLMMachiningType,
    line: true,
  },
  {
    label: '结算方式',
    value: [],
    type: 'select',
    multiple: true,
    selectArr: Enum2Options(SettlementTypeEnum),
    key: 'settlement_types',
    isShow: false,
    isQuicks: true,
    line: true,
  },
  {
    label: '商品标签',
    value: [],
    multiple: true,
    type: 'supplier',
    selectArr: [
      { label: 'pmc-新品', value: 'pmc-新品' },
      { label: 'pmc-定制', value: 'pmc-定制' },
      { label: '计划类--定制新品', value: '计划类--定制新品' },
      { label: '计划类—缺货', value: '计划类-缺货' },
      { label: '计划类—即将缺货', value: '计划类-即将缺货' },
    ],
    key: 'tags',
    topSlot: 'labelTop',
    api: GetPurchaseOrderTagsSelect,
    keyword: 'keyword',
    pageSize: 9999,
    isQuicks: true,
    line: true,
    formatter: (item) => ({
      label: item.label,
      value: item.value,
    }),
    onClear: () => {
      tagsSearchType.value = null
    },
    onBeforeConfirm: (value) => {
      let msg = ''
      if (!tagsSearchType.value) msg = '请选择包含标签方式'
      if (!value?.length) msg = '请选择商品标签'
      if (msg) {
        message.warn(msg)
        return false
      }
    },
  },
  {
    label: '商品类型',
    value: [],
    type: 'select',
    multiple: true,
    selectArr: Enum2Options(ProductTypeEnum),
    key: 'product_types',
    isShow: false,
    isQuicks: true,
    line: true,
  },
  {
    label: '商品分类',
    value: [],
    type: 'select_Tree',
    key: 'm_group_ids',
    api: AllProductCategorySelect,
    multiple: true,
    fieldNames: { label: 'value', value: 'key', children: 'children' },
  },
  {
    label: '商品品牌',
    value: undefined,
    type: 'select',
    multiple: true,
    showSearch: true,
    selectArr: [],
    key: 'product_brands',
    api: ProductBrandSelect,
    formatter: (item) => {
      return { label: item.value, value: item.value }
    },
  },
  {
    label: '入库情况',
    value: undefined,
    type: 'select',
    selectArr: Enum2Options(PurchaseOrderWarehousedStatusEnum),
    key: 'warehoused_status',
    isShow: true,
  },
  {
    label: '拍单账号',
    value: undefined,
    type: 'select',
    showSearch: true,
    selectArr: [],
    key: 'ali_purchase_account_list_id',
    api: GetAliPurchaseAccountSelect,
  },
  {
    label: '价格趋势',
    value: [],
    key: 'price_trend',
    type: 'select',
    isQuicks: true,
    line: true,
    quickIndex: 1,
    selectArr: Enum2Options(PurchaseReturnPriceTrendEnum),
  },
  {
    label: '供应商类别',
    value: [],
    key: 'supplier_category',
    type: 'select',
    isQuicks: true,
    line: true,
    quickIndex: 1,
    selectArr: Enum2Options(PurchaseReturnSupplierCategoryEnum),
  },
  {
    label: '异常状态',
    value: [],
    key: 'abnormal_status',
    type: 'select',
    isQuicks: true,
    quickIndex: 1,
    selectArr: Enum2Options(PurchaseOrderAbnormalStatusEnum),
  },
  {
    label: '对账状态',
    value: [],
    key: 'reconcile_status',
    type: 'select',
    selectArr: Enum2Options(PurchaseReconciliationStatusEnum),
  },
  // {
  //   text: '协议到货日期',
  //   value: null,
  //   key: 'predict_delivery_date',
  // },
  { label: '采购备注', value: null, type: 'input', key: 'purchase_remark' },
  { label: '明细备注', value: null, type: 'input', key: 'detail_remark' },
  { label: '协议到货日期', value: null, type: 'range-picker', formKeys: ['predict_delivery_date_start', 'predict_delivery_date_end'], placeholder: ['协议到货开始时间', '协议到货结束时间'] },
  { label: '采购时间', value: null, type: 'range-picker', formKeys: ['purchase_start_time', 'purchase_end_time'], placeholder: ['采购开始时间', '采购结束时间'] },
  { label: '审核时间', value: null, type: 'range-picker', formKeys: ['audit_start_time', 'audit_end_time'], placeholder: ['审核开始时间', '审核结束时间'] },
  { label: '完成时间', value: null, type: 'range-picker', formKeys: ['order_completed_time_start', 'order_completed_time_end'], placeholder: ['完成开始时间', '完成结束时间'] },
  {
    label: '周转天数',
    value: [],
    type: 'number-range',
    key: 'turnover_days',
    placeholder: ['开始周转天数', '结束周转天数'],
    formKeys: ['start_turnover_days', 'end_turnover_days'],
  },
  {
    label: '可售库存',
    value: [],
    type: 'number-range',
    key: 'saleable_inventory',
    placeholder: ['开始可售库存', '结束可售库存'],
    formKeys: ['start_saleable_inventory', 'end_saleable_inventory'],
  },
  {
    label: '缺货天数',
    value: [],
    type: 'number-range',
    key: 'stockout_days',
    placeholder: ['开始缺货天数', '结束缺货天数'],
    formKeys: ['start_stockout_days', 'end_stockout_days'],
  },
  {
    label: '缺货数量',
    value: [],
    type: 'number-range',
    key: 'stockout_quantity ',
    placeholder: ['开始缺货数量', '结束缺货数量'],
    formKeys: ['start_stockout_quantity', 'end_stockout_quantity'],
  },
])

useSearchForm(formArr)

const rightOperateList = ref([
  {
    label: '查看',
    show: 84001,
    onClick: ({ row }) => {
      LookOrder(row)
    },
  },
  {
    label: '日志',
    show: 84001,
    onClick: ({ row }) => {
      openLog(row)
    },
  },
  {
    label: '审核',
    show: ({ row }) => {
      return (
        ((btnPermission.value[84004] && row.audit_status == PurchaseOrderAuditStatusEnum.待一级审核) ||
          (btnPermission.value[84005] && row.audit_status == PurchaseOrderAuditStatusEnum.待二级审核) ||
          (btnPermission.value[84006] && row.audit_status == PurchaseOrderAuditStatusEnum.待三级审核) ||
          (btnPermission.value[84007] && row.audit_status == PurchaseOrderAuditStatusEnum.待四级审核)) &&
        ![PurchaseOrderStatusEnum.已作废, PurchaseOrderStatusEnum.已关闭].includes(row.order_status)
      )
    },
    onClick: ({ row }) => {
      ReviewOrder(row)
    },
  },
  {
    label: '申请变更',
    show: ({ row }) => {
      return (
        btnPermission.value[84009] && [PurchaseOrderAuditStatusEnum.已通过].includes(row.audit_status) && [PurchaseOrderStatusEnum.进行中, PurchaseOrderStatusEnum.已完成].includes(row.order_status)
      )
    },
    onClick: ({ row }) => {
      ChangeOrder(row)
    },
  },
  {
    label: '开启订单',
    show: ({ row }) => PurchaseOrderStatusEnum.已关闭 === row.order_status,
    onClick: ({ row }) => {
      toggleOrder(row, true)
    },
  },
  {
    label: '关闭订单',
    show: ({ row }) => {
      return btnPermission.value[84010] && ![PurchaseOrderStatusEnum.已作废, PurchaseOrderStatusEnum.已关闭, PurchaseOrderStatusEnum.变更中].includes(row.order_status)
    },
    onClick: ({ row }) => {
      toggleOrder(row, false)
    },
  },
  {
    label: '编辑',
    show: ({ row }) => {
      return (
        [PurchaseOrderAuditStatusEnum.待提审, PurchaseOrderAuditStatusEnum.已拒绝].includes(row.audit_status) &&
        ![PurchaseOrderStatusEnum.已作废, PurchaseOrderStatusEnum.已关闭].includes(row.order_status)
      )
    },
    onClick: ({ row }) => {
      EditOrder(row)
    },
  },
  {
    label: '作废订单',
    show: ({ row }) => {
      return btnPermission.value[84011] && [PurchaseOrderStatusEnum.进行中, PurchaseOrderStatusEnum.待开始, PurchaseOrderStatusEnum.已关闭].includes(row.order_status)
    },
    onClick: ({ row }) => {
      cancelledPurchaseOrder(row)
    },
  },
  {
    label: '反审核',
    show: ({ row }) => {
      return (
        [PurchaseOrderAuditStatusEnum.已通过].includes(row.audit_status) &&
        ![PurchaseOrderStatusEnum.已作废, PurchaseOrderStatusEnum.已关闭, PurchaseOrderStatusEnum.变更中, PurchaseOrderStatusEnum.已完成].includes(row.order_status) &&
        btnPermission[84018]
      )
    },
    onClick: ({ row }) => {
      counterAuthorization(row)
    },
  },
  {
    label: '推单1688',
    show: ({ row }) => {
      return (
        row.type == 2 &&
        ![PurchaseOrderStatusEnum.已完成, PurchaseOrderStatusEnum.已关闭, PurchaseOrderStatusEnum.已作废, PurchaseOrderStatusEnum.变更中].includes(row.order_status) &&
        ![PurchaseOrderAuditStatusEnum.已拒绝].includes(row.audit_status) &&
        btnPermission.value[84012]
      )
    },
    onClick: ({ row }) => {
      pushOrder(row)
    },
  },
  {
    label: '完成订单',
    show: ({ row }) => {
      return (
        btnPermission.value[84021] &&
        [PurchaseOrderAuditStatusEnum.已通过].includes(row.audit_status) &&
        [PurchaseOrderStatusEnum.进行中, PurchaseOrderStatusEnum.已关闭].includes(row.order_status) &&
        row.total_actual_inbound > 0
      )
    },
    onClick: ({ row }) => {
      handleCompleteOrder(row)
    },
  },

  {
    label: '拆单',
    show: ({ row }) =>
      btnPermission.value[84020] && ![PurchaseOrderStatusEnum.已作废, PurchaseOrderStatusEnum.已关闭, PurchaseOrderStatusEnum.变更中, PurchaseOrderStatusEnum.已完成].includes(row.order_status),
    onClick: ({ row }) => {
      handleSplitOrder(row)
    },
  },
])

const imageUrlListRender = ({ column, row, rowHeight }) => {
  return imageListRender(null, {
    key: 'image_urls',
    column,
    row,
    size: rowHeight,
    options: {
      width: '40vw',
      render: ({ maxHeight }) =>
        h(
          'div',
          { class: 'overflow-auto' },
          h(DivGrid, {
            columns: childrenTablekeys.value.map((f) => ({
              ...f,
              width: f.width || 100,
              render: childrenRenderKeys.value[f.field as string],
            })),
            data: row.purchaseOrderDetails,
            rowHeight: 46,
            height: Math.min(maxHeight, row.purchaseOrderDetails.length * 46 + 30),
            parentRow: row,
            border: true,
          }),
        ),
    },
  })
}

const remarkRender = ({ column, row, parentRow }: { column: any; row: any; parentRow?: any }) => {
  let icon = 'iconfont icon-edit link text-14px!'
  if (!btnPermission.value[84014]) {
    icon = ''
  }

  return textRender(null, {
    column,
    row,
    options: {
      textFormat: ({ cellValue }) => cellValue,
      icon,
      iconClick: async ({ row, cellValue }) => {
        modal.openEdit({
          comp: Input.TextArea,
          compProps: { rows: 4, autofocus: true },
          title: '修改备注',
          placeholder: '请输入内容',
          api: parentRow ? BatchUpdatePurchaseOrderDetailRemark : BatchUpdatePurchaseOrderRemark,
          params: (value) => ({ type: 1, remark: value, ids: [row.id] }),
          value: cellValue,
          callback: () => {
            tableRef.value.refresh({ keepPage: true })
          },
          width: 500,
        })
      },
    },
  })
}

const predictDeliveryDateRender = ({ column, row }) => {
  const icon = 'iconfont icon-edit link text-14px!'
  return textRender(null, {
    column,
    row,
    options: {
      textFormat: ({ cellValue }) => (cellValue ? dayjs(cellValue).format('YYYY-MM-DD') : ''),
      icon,
      iconClick: async ({ cellValue }) => {
        await modal.openEdit({
          title: '修改协议到货日期',
          api: UpdatePurchaseOrderPredictDeliveryDate,
          params: (value) => ({
            id: row.id,
            predict_delivery_date: value,
          }),
          value: cellValue,
          comp: DatePicker,
          compProps: { format: 'YYYY-MM-DD', valueFormat: 'YYYY-MM-DD' },
        })
        tableRef.value.refresh({ keepPage: true })
      },
    },
  })
}

const numberRender = ({ column, row }) => {
  const isUp = row.purchaseOrderDetails.some((item) => item.price_trend === 1)
  const isDown = row.purchaseOrderDetails.some((item) => item.price_trend === 2) && !row.purchaseOrderDetails.some((item) => item.price_trend === 1)
  return copyRender(null, {
    column,
    row,
    options: {
      suffix: isUp ? h(RiseOutlined, { class: 'text-red-500 mr ml' }) : isDown ? h(FallOutlined, { class: 'text-green-500 mr ml' }) : undefined,
    },
  })
}

const historyRender = ({ row, parentRow }) => {
  return h('div', { class: 'flex gap-6 items-center' }, [row.last_purchase_price, h('div', { class: 'link', onClick: () => openHistoryModal(row, parentRow) }, '查看历史')])
}
const tooltip: any = inject('tooltip')
const onlineOrder = ({ row }) => {
  if (!row?.online_order_numbers?.length) return h('span', '-')
  return h(
    'div',
    {
      class: 'overflow-hidden whitespace-nowrap text-ellipsis',
      onMouseenter: (event) => {
        const node = event.target as HTMLElement
        if (node.scrollWidth > node.clientWidth) {
          tooltip.open(event, row.online_order_numbers.join(' '))
        }
      },
      onMouseleave: () => {
        tooltip.close()
      },
    },
    [
      h(CopyOutlined, {
        onClick: () => copyText(row.online_order_numbers.join(' ')),
        class: 'mr-5',
      }),
      ...row.online_order_numbers.map((orderNumber, index) =>
        h(
          'a',
          {
            href: `https://air.1688.com/app/ctf-page/trade-order-detail/index.html?orderId=${orderNumber}`,
            target: '_blank',
            class: 'link  mr-5px',
            key: index,
          },
          orderNumber,
        ),
      ),
    ],
  )
}

const priceTrendRender = (row: any) => {
  const priceTrendClass = (value) => {
    if (value === null) return 'text-black'
    if (value === 1) return 'text-red-500'
    if (value === 2) return 'text-green-500'
  }
  const priceTrendEnum = [
    { label: '平价', value: null },
    { label: '涨价', value: 1 },
    { label: '价优', value: 2 },
  ]
  return h('span', { class: priceTrendClass(row.price_trend) }, row.price_trend === '...' ? row.price_trend : priceTrendEnum.find((item) => item.value === row.price_trend)?.label || '')
}

const childrenRenderKeys = ref({
  price_trend: ({ row }) => priceTrendRender(row),
  last_purchase_price: historyRender,
  online_order_numbers: onlineOrder,
  remark: remarkRender,
  predict_delivery_date: predictDeliveryDateRender,
})
const childrenTablekeys = ref([
  {
    title: '商品图片',
    field: 'image_url',
    width: 60,
    cellRender: { name: 'image' },
  },
  ...pageTableKeyEnum[PageTypeEnum.PurchaseManagement]
    .filter((f) => f.is_detail)
    .map((item) => {
      return {
        title: item.name,
        field: item.key,
        width: item.width,
        formatter: item.formatter,
        cellRender: item.cellRender,
        render: childrenRenderKeys.value[item.name],
      }
    }),
])

const exportOptions = ref(Enum2Options(PurchaseOrderExportTypeEnum))
const exportType = ref('')
const batchModifyType = ref('')
const exportLoading = ref(false)
const batchUpdateRemarkModalRef = ref()
const batchUpdateCompanyModalRef = ref()
const historyModalRef = ref()
// 操作日志
const operationLogRef = ref()
const openLog = ({ id }) => {
  operationLogRef.value.open({
    params: {
      id,
      pageType: OpLogPageTypeEnum.采购单管理,
    },
  })
}

const onBatchModifyTypeChange = (value) => {
  const checkedItems = tableRef.value.checkItemsArr
  if (!checkedItems || checkedItems.length === 0) {
    message.info('请选择需要操作的采购单')
    batchModifyType.value = ''
    return
  }
  const ids = checkedItems.map((item) => item.id)

  if (value === '1') {
    batchUpdateRemarkModalRef.value.open(ids)
  } else if (value === '2') {
    batchUpdateCompanyModalRef.value.open(ids)
  } else if (value === '3') {
    const flag = tableRef.value.checkItemsArr.some((f) => f.order_status === PurchaseOrderStatusEnum.已完成)
    if (flag) {
      message.info('存在已完成的采购单，无法修改')
      batchModifyType.value = ''
      return
    }
    modal.openEdit({
      title: '批量修改协议到货日期',
      placeholder: '协议到货日期',
      api: BatchUpdatePurchaseOrderPredictDeliveryDate,
      params: (value) => ({ ids, predict_delivery_date: value }),
      callback: () => {
        tableRef.value.refresh({ keepPage: true })
      },
      comp: DatePicker,
      compProps: { format: 'YYYY-MM-DD', valueFormat: 'YYYY-MM-DD' },
    })
  }

  nextTick(() => {
    batchModifyType.value = ''
  })
}

const formFormat = (data: any) => {
  labelStatusRefresh()
  return {
    ...data,
    audit_status: status.value,
    tag_query_type: tagsSearchType.value ? Number(tagsSearchType.value) : null,
    // 添加排序参数
    sortField: data.sortField || null,
    sortType: data.sortType || null,
  }
}

const dataFormat = (data) => {
  return data.map((item) => {
    const [first, ...other] = item.purchaseOrderDetails || []

    return {
      ...item,
      sku_name: other?.length ? '...' : first?.sku_name,
      k3_sku_id: other?.length ? '...' : first?.k3_sku_id,
      type_specification: other?.length ? '...' : first?.type_specification,
      style_code: other?.length ? '...' : first?.style_code,
      product_brand: other?.length ? '...' : first?.product_brand,
      all_category: other?.length ? '...' : first?.all_category,
      material_name: other?.length ? '...' : first?.material_name,
      k3_reference_acreage: other?.length ? '...' : first?.k3_reference_acreage,
      valuation_unit: other?.length ? '...' : first?.valuation_unit,
      last_purchase_price: other?.length ? '...' : first?.last_purchase_price,
      system_unit_price: other?.length ? '...' : first?.system_unit_price,
      tax_unit_price: other?.length ? '...' : first?.tax_unit_price,
      purchase_tax_price: other?.length ? '...' : first?.purchase_tax_price,
      price_trend: other?.length ? '...' : first?.price_trend,
      predict_delivery_date: other?.length ? '...' : first?.predict_delivery_date,
      abnormal_status: other?.length ? '...' : first?.abnormal_status,
      jst_sku_id: other?.length ? '...' : first?.jst_sku_id,
    }
  })
}

// 审核订单
const auditConfirmRef = ref()
const openReview = (flag) => {
  if (tableRef.value.checkItemsArr.length > 0) {
    const audit_status = Array.from(
      new Set(
        tableRef.value.checkItemsArr
          .filter((v) => {
            return v.audit_status
          })
          .map((v) => {
            return v.audit_status
          }),
      ),
    )

    if (audit_status.length > 1) {
      message.error('请选择同一状态的审核数据')
      return
    }

    if (
      (audit_status[0] == 20 && btnPermission.value[84004]) ||
      (audit_status[0] == 30 && btnPermission.value[84005]) ||
      (audit_status[0] == 40 && btnPermission.value[84006]) ||
      (audit_status[0] == 50 && btnPermission.value[84007])
    ) {
      auditConfirmRef.value.open(flag)
    } else {
      message.error('您没有该状态的审核权限')
    }
  } else {
    message.error('请选择批量审核数据')
  }
}
const onAuditConfirm = async (data, callback, failback) => {
  const { audit_opinion, is_pass } = data
  const params = {
    is_pass,
    audit_opinion,
    ids: [...new Set(tableRef.value.checkItemsArr.map((f) => f.id))],
  }
  const res = await BatchAudit(params)
  if (res.success) {
    message.success('操作成功')
    tableRef.value.search()
    callback()
  } else {
    message.error(res.message)
    failback()
  }
}

// 开启/关闭订单
const toggleOrder = (item, bol) => {
  modal.open({
    title: bol ? '确认是否开启当前订单?' : '确认是否关闭当前订单?',
    async onOk() {
      const params = {
        id: item.id,
        isClose: !bol,
      }
      const api = bol ? OpenPurchaseOrder : ClosePurchaseOrder
      const res = await api(params)
      if (res.success) {
        tableRef.value.refresh({ keepPage: true })
        message.success('操作成功')
      } else {
        message.error(res.message)
      }
    },
  })
}

// 关闭订单
const cancelledPurchaseOrder = (item) => {
  modal.open({
    title: '确定是否作废当前采购单?',
    async onOk() {
      const params = {
        id: item.id,
      }
      const res = await CancelledPurchaseOrder(params)
      if (res.success) {
        tableRef.value.refresh({ keepPage: true })
        message.success('操作成功')
      } else {
        message.error(res.message)
      }
    },
  })
}
// 添加采购订单
const addPurchaseOrder = () => {
  pushPage('/purchaseneworder', { source: true })
}

const downLoadPurchaseOrder = () => {
  if (tableRef.value.checkItemsArr.length === 0) return message.warn('请先勾选要下载的采购单')
  const items = tableRef.value.checkItemsArr
  const pass = items.every((f) => f.supplier_id === items[0].supplier_id)
  if (!pass) return message.warn('请先勾选相同供应商的采购单')
  const routeData = router.resolve({ path: '/print', query: { t: encodeURIComponent(items.map((f) => f.id).join(',')), n: encodeURIComponent(items[0].supplier_name) } })
  window.open(routeData.href, '_blank')
}

const ReviewOrder = (row) => {
  pushPage(`/purchaseOrderReview/${row.id}`, { source: true })
}
const LookOrder = (row) => {
  pushPage(`/purchaseOrderLook/${row.id}`, { source: true })
}
const EditOrder = (row) => {
  pushPage(`/purchaseOrderEdit/${row.id}`, { source: true })
}
const ChangeOrder = async (row) => {
  const { data } = await CheckPurchaseOrderAllowPriceChange({ id: row.id })
  if (!data) return message.error('采购单已关联应付单，变更需先删除应付单')
  pushPage(`/purchaseOrderAlter/${row.id}`, { source: true })
}

// 反审核
const counterAuthorization = (item) => {
  modal.open({
    title: '是否确定反审核?',
    async onOk() {
      const params = {
        id: item.id,
      }
      const res = await CounterAuthorization(params)
      if (res.success) {
        tableRef.value.search()
        message.success('操作成功')
      } else {
        message.error(res.message)
      }
    },
  })
}

const pushOrder = (row) => {
  pushOrderDlgRef.value.open(row)
}

const addReservationBtn = async () => {
  if (tableRef.value.checkItemsArr.length > 0) {
    const supplierLen = Array.from(
      new Set(
        tableRef.value.checkItemsArr
          .filter((v) => {
            return v.supplier_name
          })
          .map((v) => {
            return v.supplier_name
          }),
      ),
    )
    if (supplierLen.length > 1) {
      message.error('供应商子公司不一致!')
      return
    }
    const warehouseLen = Array.from(
      new Set(
        tableRef.value.checkItemsArr
          .filter((v) => {
            return v.warehouse_id
          })
          .map((v) => {
            return v.warehouse_id
          }),
      ),
    )
    if (warehouseLen.length > 1) {
      message.error('收料仓库不一致!')
      return
    }
    const productTypeLen = Array.from(
      new Set(
        tableRef.value.checkItemsArr
          .filter((v) => {
            return v.product_type
          })
          .map((v) => {
            return v.product_type
          }),
      ),
    )
    if (productTypeLen.length > 1) {
      message.error('商品类型不一致!')
      return
    }
    const orderStatusArr = tableRef.value.checkItemsArr.filter((item) => {
      return item.order_status != 1
    })
    if (orderStatusArr.length > 0) {
      message.error('请选择进行中的采购单生成预约入库单!')
      return
    }
    const auditStatusArr = tableRef.value.checkItemsArr.filter((item) => {
      return item.audit_status != 90
    })
    if (auditStatusArr.length > 0) {
      message.error('请选择审核通过的采购单生成预约入库单!')
      return
    }
    const checkArr = tableRef.value.checkItemsArr.filter((item) => {
      return item.purchase_quantity <= item.scheduled_quantity
    })
    if (checkArr.length > 0) {
      message.error('存在没有可预约入库数量的采购单!')
      return
    }
    const numbers = [...new Set(tableRef.value.checkItemsArr.map((i) => i.number))]
    const result = await CheckPurchaseStatus(numbers)
    if (!result.success) {
      message.error(result.message)
      return
    }
    const params = {
      purchase_order_numbers: numbers,
      product_type: tableRef.value.checkItemsArr[0].product_type,
      creator_id: tableRef.value.checkItemsArr[0].creator_id,
      creator_name: tableRef.value.checkItemsArr[0].creator_name,
      company_supplier_id: tableRef.value.checkItemsArr[0].company_supplier_id,
      warehouse_id: tableRef.value.checkItemsArr[0].warehouse_id,
    }
    const list = JSON.parse(localStorage.getItem('srm_booking_order_create') || '{}')
    const uuid = generateUUID()
    localStorage.setItem(
      'srm_booking_order_create',
      JSON.stringify({
        ...list,
        [uuid]: params,
      }),
    )
    router.push({
      path: '/addReservation',
      query: {
        uc: uuid,
      },
    })
  } else {
    message.error('请选择生成预约入库单数据')
  }
}

// 导出(异步)
const onExportTypeChange = (value) => {
  const count = tableRef.value.checkItemsArr.length
  const type: PurchaseOrderExportTypeEnum = value
  let msg = ''
  if ([1, 2, 3].includes(type) && count > 50000) {
    msg = '单次操作不得大于5万条！'
  }
  if ([4, 5, 6].includes(type) && count > 5000) {
    msg = '单次操作不得大于5千条！'
  }
  if ([1, 4].includes(type) && !count) {
    msg = '请勾选数据'
  }
  if (msg) {
    exportType.value = ''
    return message.info(msg)
  }
  const params = {
    exportType: type > 3 ? type - 3 : type,
    isExportImg: [4, 5, 6].includes(type),
    purchaseOrderIds: [...new Set(tableRef.value.checkItemsArr.map((f) => f.id))],
  }
  modal.open({
    title: '是否确定导出采购单数据?',
    async onOk() {
      exportData(params)
    },
  })
}

const exportData = (params) => {
  const obj = {
    audit_status: status.value,
    sortField: tableRef.value.orderby,
    sortType: tableRef.value.ordersort ? 'asc' : 'desc',
  }
  checkFormParams({ formArr: formArr.value, obj })
  exportLoading.value = true
  ExportPurchaseOrderDetails({
    ...params,
    searchListParam: formFormat(obj),
  })
    .then((res) => {
      eventBus.emit('downLoadId', res.data as number | string)
    })
    .finally(() => {
      exportLoading.value = false
    })
}

const openHistoryModal = (row, parentRow) => {
  // 打开历史价格弹窗
  historyModalRef.value.setModel(true, row, parentRow.company_supplier_id, { process_type: parentRow.process_type })
}

// 批量支付1688
const customDrawerRef = ref()
const handleBatchPayBy1688 = async () => {
  const [first] = tableRef.value.checkItemsArr
  if (!first) return message.warning('请选择一条数据')
  let msg = ''
  if (tableRef.value.checkItemsArr.some((f) => f.type !== PurchaseTypeEnum['1688线上采购订单'])) {
    msg = '请选择1688订单'
  }
  if (tableRef.value.checkItemsArr.some((f) => f.ali_purchase_account_list_id !== first.ali_purchase_account_list_id)) {
    msg = '请选择相同拍单账号的订单进行支付'
  }
  if (msg) return message.warning(msg)

  customDrawerRef.value.open({
    drawerOptions: {
      title: '批量1688支付',
      width: 840,
    },
    getList: async (formData, { gridRef }) => {
      const { data } = await GetAliPayInfoList({
        ids: [...new Set(tableRef.value.checkItemsArr.map((f) => f.id))],
        ...formData,
      })
      gridRef && gridRef.value.reloadData(data)
    },
    formItems: [{ label: '线上1688订单号', type: 'input', name: 'online_order_number', width: 200 }],
    gridOptions: {
      columns: [
        { type: 'checkbox', width: 50, align: 'center' },
        { title: '供应商', field: 'supplier_name' },
        { title: '供应商子公司', field: 'company_supplier_name' },
        { title: '采购单号', field: 'number', width: 120 },
        { title: '1688订单号', field: 'online_order_number', width: 160 },
        { title: '商品编码', field: 'k3_sku_id' },
        { title: '采购总金额', field: 'sum_price' },
      ],
    },
    confirmText: '确认支付',
    onConfirm: async ({ list }) => {
      if (!list.length) return Promise.resolve(false)
      try {
        const batchRes = await BatchAliPay({
          ids: [...new Set(list.map((f) => f.id))],
          ali_purchase_account_list_id: tableRef.value.checkItemsArr[0].ali_purchase_account_list_id,
        })
        if (typeof batchRes.data === 'string' && batchRes.data) {
          window.open(batchRes.data, '_blank')
          return Promise.resolve(true)
        }
      } catch (_) {
        Promise.resolve(false)
      }
    },
  })
}

// 合计 采购总金额、采购总数、已预约入库数量、实际入库数量
const sumData = ref({})
const onLoadData = (data) => {
  sumData.value = data.reduce(
    (acc, cur) => ({
      采购单总金额: acc.采购单总金额 + (cur.total_purchase_amount ?? 0),
      采购总数: acc.采购总数 + (cur.purchase_quantity ?? 0),
      已预约入库数量: acc.已预约入库数量 + (cur.total_scheduled_quantity ?? 0),
      实际入库数量: acc.实际入库数量 + (cur.total_actual_inbound ?? 0),
    }),
    {
      采购单总金额: 0,
      采购总数: 0,
      已预约入库数量: 0,
      实际入库数量: 0,
    },
  )
}

// 完成订单
const handleCompleteOrder = (row) => {
  modal.open({
    title: '完成订单将进入结算，无法重新开启订单，确认操作吗？',
    async onOk() {
      await CompletedOrder(row.id)
      message.success('操作成功')
      tableRef.value.search()
    },
  })
}
// 拆单
const handleSplitOrder = (row) => {
  console.log(row)
  customDrawerRef.value.open({
    drawerOptions: {
      title: '选择拆单商品',
      width: 940,
    },
    getList: async (_, { gridRef }) => {
      const { data } = await GetSplitPurchaseOrderApplyList({
        purchase_order_id: row.id,
      })
      gridRef && gridRef.value.reloadData(data.map((item) => ({ ...item, purchase_split_quantity: undefined })))
    },
    gridOptions: {
      columns: [
        // { type: 'checkbox', width: 50, align: 'center' },
        { title: '申请单编号', field: 'apply_number' },
        { title: '计划单号', field: 'plan_number' },
        { title: '商品编码', field: 'sku_id' },
        { title: '商品名称', field: 'sku_name' },
        { title: '颜色与规格', field: 'type_specification' },
        { title: '采购数量', field: 'apply_purchase_quantity' },
        {
          title: '采购拆分数量',
          field: 'purchase_split_quantity',
          slots: {
            default: ({ row }) => {
              return h(InputNumber, {
                value: row.purchase_split_quantity,
                min: 0,
                max: row.apply_purchase_quantity,
                precision: 0,
                onChange: (value) => {
                  row.purchase_split_quantity = value
                },
              })
            },
          },
        },
      ],
    },
    confirmText: '确认',
    onConfirm: ({ data }) => {
      return new Promise((resolve) => {
        let flag = false
        const params = {
          purchase_order_id: row.id,
          split_purchase_order_details: Object.values(
            data.reduce((acc, { purchase_order_detail_id, purchase_order_apply_detail_id, purchase_split_quantity }) => {
              if (!acc[purchase_order_detail_id]) {
                acc[purchase_order_detail_id] = {
                  purchase_order_detail_id,
                  split_purchase_order_apply_details: [],
                }
              }
              if (purchase_split_quantity) {
                flag = true
                acc[purchase_order_detail_id].split_purchase_order_apply_details.push({
                  purchase_order_apply_detail_id,
                  quantity: purchase_split_quantity,
                })
              }

              return acc
            }, {}),
          ),
        }
        if (!flag) {
          message.warn('商品拆分数量为空，请确认后重试')
          resolve(false)
          return
        }
        modal.open({
          title: '是否确认拆单',
          onOk: async () => {
            try {
              await SplitPurchaseOrder(params)
              message.success('操作成功')
              tableRef.value?.search()
              resolve(true)
              return Promise.resolve(true)
            } catch (_) {
              resolve(false)
              Promise.resolve(false)
            }
          },
          onCancel: () => {
            resolve(false)
          },
        })
      })
    },
  })
}
</script>
<style scoped lang="scss">
.btnBox2 {
  display: flex;
  align-items: center;
  justify-content: flex-start;

  .ant-btn {
    margin-right: 10px;
  }
}

:deep(.vxe-table--render-default:not(.is--empty).is--footer.is--scroll-x .vxe-body--expanded-cell .vxe-table--body-wrapper) {
  overflow-x: auto;
}
</style>
