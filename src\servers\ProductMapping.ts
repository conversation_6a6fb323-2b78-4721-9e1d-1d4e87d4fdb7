import { request } from './request'
// 获取1688商品映射列表
export const GetAliProductMapList = (data) => {
  return request({ url: '/api/Ali1688ProductMap/GetAliProductMapList', data })
}

// 根据1688商品链接获取1688商品列表
export const GetAli1688ProductList = (data) => {
  return request({ url: '/api/Ali1688ProductMap/GetAli1688ProductList', data })
}

// 根据id获取1688商品映射信息
export const GetAliProductMapInfo = (data) => {
  return request({ url: `/api/Ali1688ProductMap/GetAliProductMapInfo?id=${data.id}`, data }, 'GET')
}

// 根据采购单id获取1688商品映射信息列表
export const GetAliProductMapInfoByPurchaseOrderId = (data) => {
  return request({ url: `/api/Ali1688ProductMap/GetAliProductMapInfoByPurchaseOrderId?order_id=${data.order_id}`, data }, 'GET')
}

// 添加1688商品映射
export const AddAliProductMapInfo = (data) => {
  return request({ url: '/api/Ali1688ProductMap/AddAliProductMapInfo', data })
}

// 修改1688商品映射
export const UpdateAliProductMapInfo = (data) => {
  return request({ url: '/api/Ali1688ProductMap/UpdateAliProductMapInfo', data })
}

// 获取1688类型的子供应商
// export const getSupplier = (data) => {
//   return request({ url: '/api/Supplier/Get1688MRPSupplierCompanySelect', data }, 'GET')
// }

// 获取1688类型的子供应商(用户名下的供应商)
// export const getChilrdSupplier = (data) => {
//   return request({ url: '/api/Supplier/Get1688MRPUsersAndChilrdSupplierCompanySelect', data }, 'GET')
// }

// 根据商品链接和子供应商ID获取商品映射信息
export const GetAliProductMapInfoByUrlAndSupplierCompanyId = (data) => {
  return request({ url: '/api/Ali1688ProductMap/GetAliProductMapInfoByUrlAndSupplierCompanyId', data })
}
