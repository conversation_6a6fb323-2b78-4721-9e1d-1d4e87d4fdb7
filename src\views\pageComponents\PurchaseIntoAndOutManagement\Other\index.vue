<template>
  <ContentPage
    :get-list="getData"
    @details="handleDetails"
    :details-code="117001"
    :sync-code="117002"
    :btn-permission="btnPermission"
    v-model:form-arr="formArr"
    :page-type="PageTypeEnum.PurchaseOther"
  ></ContentPage>
  <PurchaseDetails @close="showDetails = false" ref="detailsRef" v-if="showDetails"></PurchaseDetails>
</template>

<script lang="ts" setup>
import PurchaseDetails from './PurchaseDetails.vue'

import ContentPage from '../components/ContentPage.vue'
import { mockData1 } from '../mock'

import { PageTypeEnum } from '@/enums/tableEnum'

const showDetails = ref(false)
const detailsRef = useTemplateRef('detailsRef')
const { btnPermission } = usePermission()

const getData = () => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        data: { data: mockData1 },
      })
    }, 1000)
  })
}
//

const formArr = ref<any>([
  { label: '出仓单号', value: '', type: 'input', key: 'test1' },

  { label: '入库单编号', value: '', type: 'input', key: 'test3' },
  { label: '商品编码', value: '', type: 'input', key: 'test4' },
  { label: '商品名称', value: '', type: 'input', key: 'test5' },
  { label: '供应商', value: '', type: 'input', key: 'test2' },
  {
    label: '单据类型',
    value: null,
    type: 'select',
    selectArr: [],
    key: 'test6',
    api: () => [],
  },
  {
    label: '选择仓库',
    value: null,
    type: 'select',
    selectArr: [],
    key: 'test7',
    api: () => [],
  },
  {
    label: '选择创建人',
    value: null,
    type: 'select',
    selectArr: [],
    key: 'test8',
    api: () => [],
  },

  {
    label: '单据',
    value: null,
    type: 'range-picker',
    key: 'test10',
    formKeys: ['created_start_time', 'created_end_time'],
    placeholder: ['单据开始时间', '单据结束时间'],
  },
  {
    label: '创建',
    value: null,
    type: 'range-picker',
    key: 'test11',
    formKeys: ['created_start_time', 'created_end_time'],
    placeholder: ['创建开始时间', '创建结束时间'],
  },
  {
    label: '修改',
    value: null,
    type: 'range-picker',
    key: 'test11',
    formKeys: ['created_start_time', 'created_end_time'],
    placeholder: ['修改开始时间', '修改结束时间'],
  },
])
const handleDetails = async (row: any) => {
  showDetails.value = true
  await nextTick()
  detailsRef?.value?.open(row)
}
</script>

<style lang="scss" scoped>
//
</style>
