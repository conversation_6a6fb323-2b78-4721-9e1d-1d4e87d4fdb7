<template>
  <a-drawer v-model:open="show" width="700px" title="查看采购退库单" @close="handleClose">
    <div v-if="loading" class="w-full flex h-full justify-center items-center">
      <a-spin tip="加载中..."></a-spin>
    </div>
    <div v-else-if="!loading && formData">
      <div class="drawer-title">基本信息</div>
      <div class="w-full">
        <a-form :model="formData" :label-col="{ style: { width: '100px' } }">
          <a-row>
            <a-col :span="12">
              <a-form-item label="采购单号">
                {{ formData.purchase_order_numbers && formData.purchase_order_numbers.length > 0 ? formData.purchase_order_numbers.join('，') : '' }}
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="退货单号">
                {{ formData.io_id }}
              </a-form-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="12">
              <a-form-item label="仓库">
                {{ formData.warehouse }}
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="分仓编号">
                {{ formData.wms_co_id }}
              </a-form-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="12">
              <a-form-item label="标记|多标签">
                {{ formData.labels }}
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="收货人">
                {{ formData.receiver_name }}
              </a-form-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="12">
              <a-form-item label="退货日期">
                {{ formData.io_date }}
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="修改时间">
                {{ formData.modified }}
              </a-form-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="12">
              <a-form-item label="创建人">
                {{ formData.creator_name }}
              </a-form-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="12">
              <a-form-item label="备注">
                {{ formData.remark }}
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </div>
      <div class="drawer-title">明细信息</div>
      <div class="w-full">
        <a-table :pagination="false" :scroll="{ x: 1000 }" bordered :data-source="tableData" :columns="tableCoulmns"></a-table>
      </div>
    </div>
    <template #footer>
      <a-button @click="handleClose">关闭</a-button>
    </template>
  </a-drawer>
</template>

<script lang="ts" setup>
import { OaErpapiPurchaseReturnDetail } from '@/servers/JushuitanOrder'

const emits = defineEmits(['close'])

const formData = ref()

const loading = ref(false)

const tableData = ref([])

const tableCoulmns = [
  { title: '明细id', dataIndex: 'ioi_id', key: 'ioi_id', width: 80 },
  { title: '商品编码', dataIndex: 'sku_id', key: 'sku_id', width: 120 },
  { title: '款式编码', dataIndex: 'i_id', key: 'i_id', width: 100 },
  { title: '商品名称', dataIndex: 'name', key: 'name', width: 100 },
  { title: '颜色及规格', dataIndex: 'properties_value', key: 'properties_value', width: 100 },
  { title: '单价', dataIndex: 'cost_price', key: 'cost_price', width: 100 },
  { title: '数量', dataIndex: 'qty', key: 'qty', width: 100 },
  { title: '金额', dataIndex: 'cost_amount', key: 'cost_amount', width: 100 },
  { title: '公司编号', dataIndex: 'co_id', key: 'co_id', width: 120 },
  { title: '备注', dataIndex: 'remark', key: 'remark', width: 150 },
]

const handleClose = () => {
  emits('close')
}
//
const show = ref(false)
const open = async (data) => {
  show.value = true
  loading.value = true
  const res = await OaErpapiPurchaseReturnDetail({ id: data.id }).finally(() => {
    loading.value = false
  })
  formData.value = res.data
  tableData.value = res.data.purchase_out_items
}

defineExpose({
  open,
})
</script>

<style lang="scss" scoped>
//
</style>
