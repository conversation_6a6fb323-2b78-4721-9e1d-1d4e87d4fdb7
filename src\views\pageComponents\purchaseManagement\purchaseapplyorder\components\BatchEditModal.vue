<template>
  <a-drawer v-model:open="visible" title="采购单预处理" :width="width" :maskClosable="false">
    <div class="flex flex-col h-full">
      <!-- <a-form class="w-400" ref="updateFormRef" :model="formData" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
        <template v-for="item in formItems" :key="item.name">
          <a-form-item v-if="item.isShow" :label="item.label" :name="item.name" :rules="item.rules">
            <a-select class="select" v-model:value="formData[item.name]" style="width: 100%" :placeholder="`选择${item.label}`" :options="item.selectArr"></a-select>
          </a-form-item>
        </template>
      </a-form> -->
      <EasyForm ref="formRef" :formItems="formItems" v-model:form="formData" :labelWidth="140" />
      <div class="flex-1">
        <SimpleTable :tableKey="tableKeys" :data="tableData" max-height="100%" :auto-resize="true">
          <template #company_supplier_name2>
            <span>{{ formData.company_supplier_name }}</span>
          </template>
          <template #warehouse_name2>
            <span>{{ formSelectMap['warehouseId']?.[formData.warehouse_id as any] }}</span>
          </template>
        </SimpleTable>
      </div>
    </div>
    <template #footer>
      <a-button type="primary" @click="handleSubmit">确定</a-button>
      <a-button class="ml-20" @click="visible = false">取消</a-button>
    </template>
    <SelectSupplier ref="selectSupplierRef" hide />
  </a-drawer>
</template>

<script lang="ts" setup>
import { generateUUID } from '@/utils'

import { GetWarehouses, GetPurchaseOrderPreProcessingList } from '@/servers/Purchaseapplyorder'
import { GetPageMRPSupplierCompanySelect } from '@/servers/BusinessCommon'

type FormItemProps = {
  company_supplier_id?: string
  company_supplier_name?: string
  warehouse_id?: string
}

const router = useRouter()
const visible = ref(false)
const formData = ref<FormItemProps>({})
const formRef = ref()
const selectSupplierRef = ref()
const formItems = ref<EasyFormItemProps[]>([
  {
    label: '采购供应商子公司',
    name: 'company_supplier_id',
    alias: 'company_supplier_name',
    type: 'select',
    span: 24,
    required: true,
    props: {
      class: 'w-240!',
      open: false,
      onClick: ({ item }: any) => {
        if (item.disabled) return
        selectSupplierRef.value.open(
          {
            key: 'company_supplier_id',
            mode: 'single',
            labelInValue: false,
            api: GetPageMRPSupplierCompanySelect,
            apiParams: {
              type: 1,
              is_contains_srs: true,
            },
            callback: (data) => {
              onSupplierChange(data)
            },
          },
          item.options,
        )
      },
    },
  },
  {
    label: '采购收料仓库',
    name: 'warehouse_id',
    type: 'select',
    api: GetWarehouses,
    required: true,
    span: 24,
    props: {
      class: 'w-240!',
    },
  },
])
const { formSelectMap } = useSearchForm(formItems)

const tableKeys = ref([
  {
    title: '申请单编号',
    field: 'number',
    width: 120,
  },
  {
    title: '商品名称',
    field: 'sku_name',
  },
  {
    title: '商品编号',
    field: 'k3_sku_id',
    width: 120,
  },
  {
    title: '计划采购数量',
    field: 'quantity',
    width: 100,
  },
  {
    title: '已执行采购数量',
    field: 'purchase_quantityed',
    width: 120,
  },
  {
    title: '需求到货日期',
    field: 'predict_delivery_date',
    width: 100,
    formatter: 'date',
  },
  {
    title: '建议供应商子公司',
    field: 'company_supplier_name',
    width: 150,
  },
  {
    title: '建议收料仓库',
    field: 'warehouse_name',
    width: 150,
  },
  {
    title: '采购供应商子公司',
    field: 'company_supplier_name2',
    width: 150,
  },
  {
    title: '采购收料仓库',
    field: 'warehouse_name2',
    width: 150,
  },
])
const tableData = ref([])

const checkItems = ref([])
const width = Math.min(1400, window.innerWidth - 150)
const open = async (editArr, items) => {
  console.log(editArr, 'editArr')
  console.log(items, 'items')
  // confirmLoading.value = false
  visible.value = true
  formData.value = {}
  checkItems.value = items
  const { data } = await GetPurchaseOrderPreProcessingList(items.map((f) => f.detail_id))
  tableData.value = data

  formItems.value.map((item: any) => {
    item.disabled = !editArr.includes(item.name)
    if (item.disabled) {
      formData.value[item.name] = `${items[0][item.name] || ''}`
    }
    return item
  })
  formData.value.company_supplier_id &&
    onSupplierChange({
      value: data?.[0]?.company_supplier_id,
      label: data?.[0]?.company_supplier_name,
    })
}
const onSupplierChange = (data) => {
  const _data = [data].flat()[0]
  console.log('_data', _data)
  formRef.value.changeValue({
    company_supplier_id: _data.value,
    company_supplier_name: _data.label,
  })
  formRef.value.changeItem('company_supplier_id', {
    options: [_data],
  })
}

const handleSubmit = async () => {
  await formRef.value.validate()
  const ids: any[] = []
  checkItems.value.forEach((item: any) => {
    ids.push({
      apply_order_id: item.id,
      apply_order_detail_id: item.detail_id,
      k3_sku_id: item.k3_sku_id,
    })
  })
  const list = JSON.parse(localStorage.getItem('srm_apply_purchase') || '{}')
  const uuid = generateUUID()
  localStorage.setItem(
    'srm_apply_purchase',
    JSON.stringify({
      ...list,
      [uuid]: ids,
    }),
  )

  const { warehouseId } = formSelectMap.value
  const { company_supplier_id, warehouse_id, company_supplier_name } = formData.value
  const preprocessing = `${company_supplier_name}:${warehouseId[warehouse_id as string]}:${company_supplier_id}:${warehouse_id}`
  // if (preprocessing) {
  //   console.log('preprocessing', preprocessing, company_supplier_id)
  //   return
  // }
  visible.value = false
  router.push(`/purchasebatchorder/${uuid}/${preprocessing}`)
}

// const confirmLoading = ref(false)
// const handleConfirm = () => {
//   console.log('handleConfirm')
//   confirmLoading.value = true
// }

defineExpose({
  open,
})
</script>
