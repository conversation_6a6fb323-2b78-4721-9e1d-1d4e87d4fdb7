<template>
  <div class="flex flex-col h-full main">
    <Form ref="formRef" v-model:form="formArr" :page-type="PageTypeEnum.LogisticsList" @search="tableRef?.search()" @setting="tableRef?.showTableSetting()"></Form>
    <BaseTable ref="tableRef" :page-type="PageTypeEnum.LogisticsList" v-model:form="formArr" :get-list="List" :form-format="formFormat" :isIndex="true">
      <template #fix_option="{ row, column }">
        <RightOperate :list="rightOperateList" :row="row" :column="column" />
      </template>
      <template #left-btn>
        <a-button type="primary" @click="getAliCompany()" v-if="btnPermission[113003]">获取1688物流公司</a-button>
      </template>
    </BaseTable>
    <LogisticsInfo ref="LogisticsInfoRef" @query="tableRef?.search()" />
  </div>
</template>
<script lang="ts" setup>
import { Modal, message } from 'ant-design-vue'

import LogisticsInfo from './components/LogisticsInfo.vue'

import { List, GetAliCompany, Delete } from '@/servers/LogisticsApi'

import { PageTypeEnum } from '@/enums/tableEnum'

const { btnPermission } = usePermission()
const LogisticsInfoRef = ref()
const tableRef = ref()
const rightOperateList = ref([
  {
    label: '编辑',
    show: 113001,
    onClick: ({ row }) => {
      openLogisticsInfo(row)
    },
  },
  {
    label: '删除',
    show: 113002,
    onClick: ({ row }) => {
      delBtn(row)
    },
  },
])

const formRef = ref()
const formArr = ref([
  { label: '物流公司编号', value: '', type: 'input', key: 'logistics_company_no' },
  { label: '物流公司名称', value: '', type: 'input', key: 'logistics_company_name' },
])
const saveId = ref()

const formFormat = (data) => ({
  ...data,
})

const getAliCompany = () => {
  GetAliCompany().then((res) => {
    if (res.success) {
      message.success('已获取1688物流公司信息')
      tableRef.value.search()
    } else {
      message.error(res.message)
    }
  })
}

const delBtn = (row) => {
  saveId.value = row.id
  Modal.confirm({
    title: '确定要删除吗?',
    icon: () => {},
    content: '',
    async onOk() {
      delCompany()
    },
    onCancel() {},
  })
}

const delCompany = () => {
  Delete([saveId.value]).then((res) => {
    if (res.success) {
      message.success('已删除')
      tableRef.value.search()
    }
  })
}

const openLogisticsInfo = (row) => {
  LogisticsInfoRef.value.open(row)
}
</script>
<style lang="scss" scoped></style>
