<template>
  <a-drawer title="选择应付单" width="85vw" :visible="visible" @close="handleClose" :maskClosable="false" destroyOnClose>
    <div class="flex h-full overflow-auto">
      <div class="p-16px h-full overflow-auto w65vw">
        <a-space class="mb-12">
          <a-input placeholder="应付单号" v-model:value="queryParams.pinvid" :maxlength="200" allowClear />
          <a-input placeholder="供应商子公司" v-model:value="queryParams.company_supplier_name" :maxlength="200" allowClear />
          <!-- <SelectSupplier v-model:value="queryParams.supplier_id" v-model:label="queryParams.supplier_name" :title="'请选择供应商'" :mode="'single'" :labelInValue="false" /> -->
          <a-button type="primary" @click="handleSearch">查询</a-button>
          <a-button @click="handleReset">重置</a-button>
        </a-space>
        <vxe-table
          v-if="leftVisible"
          :border="true"
          ref="productTableRef"
          size="mini"
          :row-config="{ isHover: true, keyField: 'id', height: 40 }"
          :custom-config="{ mode: 'popup' }"
          :data="data"
          :show-overflow="true"
          :show-header-overflow="true"
          :show-footer-overflow="true"
          :column-config="{ resizable: true }"
          class="tableBoxwidth"
          :checkbox-config="{
            trigger: 'row',
          }"
          min-height="0"
          stripe
          v-bind="$attrs"
          @checkbox-all="handleSelectAll"
          @checkbox-change="handleSelectChange"
        >
          <vxe-column type="checkbox" width="50" fixed="left"></vxe-column>
          <slot name="column">
            <template v-for="i in tableKey" :key="i.field">
              <vxe-column v-bind="i">
                <template #default="{ row }">
                  <span>{{ row[i.field] }}</span>
                </template>
              </vxe-column>
            </template>
          </slot>
        </vxe-table>
        <div class="paginationBox">
          <div class="pagination">
            <a-pagination
              show-quick-jumper
              :total="total"
              show-size-changer
              v-model:current="queryParams.page"
              v-model:page-size="queryParams.pageSize"
              :page-size-options="['10', '20', '30', '40', '50']"
              @change="handlePageChange"
              size="small"
            >
              <template #buildOptionText="props">
                <span>{{ props.value }}条/页</span>
              </template>
            </a-pagination>
          </div>
          <div class="totalBox">
            <div class="text">总数:</div>
            <div class="total">{{ total }}</div>
          </div>
        </div>
      </div>
      <div class="flex-1 overflow-y-auto p-16 text-black">
        <div class="flex mb-12 h30px line-height-30px h-30px">
          <div class="text-16px">已选{{ selectProductList.length }}个应付单</div>
          <a-button class="ml-auto" type="primary" @click="cleanCheck">清空</a-button>
        </div>
        <vxe-table
          v-if="leftVisible"
          :border="true"
          ref="selectTableRef"
          size="mini"
          :row-config="{ isHover: true, keyField: 'payable_order_id', height: 40 }"
          :custom-config="{ mode: 'popup' }"
          :data="selectProductList"
          :show-overflow="true"
          :show-header-overflow="true"
          :show-footer-overflow="true"
          :column-config="{ resizable: true }"
          class="tableBoxwidth"
          min-height="0"
          stripe
          v-bind="$attrs"
        >
          <vxe-column type="seq" title="序号" width="60" fixed="left"></vxe-column>
          <template v-for="i in selectTableKey" :key="i.field">
            <vxe-column v-bind="i">
              <template #default="{ row }">
                <div class="flex items-center justify-between">
                  <span>{{ row[i.field] }}</span>
                  <a-button type="link" class="ml-2" @click="handleUnselectRow(row)">取消选中</a-button>
                </div>
              </template>
            </vxe-column>
          </template>
        </vxe-table>
      </div>
    </div>
    <template #footer>
      <a-space>
        <a-button type="primary" @click="handleSelectProduct" :disabled="hasSupplierConflict">确定</a-button>
        <a-button @click="handleClose">取消</a-button>
      </a-space>
    </template>
  </a-drawer>
</template>

<script setup lang="ts">
import { VxeTableInstance } from 'vxe-table'
import { ref, computed, nextTick } from 'vue'
import { message } from 'ant-design-vue'

// eslint-disable-next-line import/extensions
import { GetJstPayPayableList } from '@/servers/paymentOrder.ts'

interface ProductItem {
  id: number
  pinvid: string
  [key: string]: any
}

// 定义 props
const props = defineProps<{
  supplierSubsidiaryId?: number | string | null
  supplierSubsidiaryName?: string
  selectedProducts?: any[]
  paymentOrderId?: number | string | null // 新增：付款单ID，用于编辑时过滤数据
}>()

const productTableEl = useTemplateRef<VxeTableInstance>('productTableRef')

const emits = defineEmits(['selectProduct', 'updateProduct'])

const visible = ref(false)
const leftVisible = ref(false)

const data = ref<ProductItem[]>([])

const total = ref(0)

const selectProductList = ref<ProductItem[]>([])

const queryParams = ref<any>({
  page: 1,
  pageSize: 10,
  pinvid: null,
  company_supplier_name: null,
  id: null, // 新增：付款单ID字段
})

const tableKey = ref([
  {
    field: 'pinvid',
    title: '应付单号',
    width: 140,
  },
  {
    field: 'payable_date',
    title: '单据日期',
    width: 140,
  },
  {
    field: 'supplier_name',
    title: '供应商',
    width: 160,
  },
  {
    field: 'company_supplier_name',
    title: '供应商子公司',
    width: 160,
  },
  {
    field: 'inv_amount',
    title: '应付总额',
    width: 120,
  },
  {
    field: 'payable_amount',
    title: '已付金额',
    width: 120,
  },
])

const selectTableKey = ref([
  {
    field: 'pinvid',
    title: '应付单号',
    width: '100%',
  },
])

// 添加临时选中列表
const tempSelectedList = ref<any[]>([])

// 添加一个变量来存储初始选中状态
const initialSelection = ref<any[]>([])

// 添加一个标志来防止翻页时触发选中事件
const isUpdatingSelection = ref(false)

// 判断当前临时选中应付单是否存在不同供应商
const hasSupplierConflict = computed(() => {
  if (tempSelectedList.value.length <= 1) return false

  const firstItem = tempSelectedList.value[0]
  // 优先使用 supplier_name，因为编辑模式下传入的数据使用 supplier_name
  const firstSupplierName = firstItem.supplier_name
  const firstSupplierId = firstItem.supplier_id

  return tempSelectedList.value.some((item) => {
    // 优先使用 supplier_name 进行比较
    if (firstSupplierName && item.supplier_name) {
      return item.supplier_name !== firstSupplierName
    }
    // 如果 supplier_name 不存在，则使用 supplier_id
    if (firstSupplierId !== undefined && firstSupplierId !== null) {
      return item.supplier_id !== firstSupplierId
    }
    return false
  })
})

const handleSelectChange = () => {
  selectChangeEvent('change')
}

const handleSelectAll = () => {
  selectChangeEvent('all')
}
// 修改 selectChangeEvent 函数
const selectChangeEvent = (type: string) => {
  const $table = productTableEl.value
  if (!$table) return

  // 如果正在更新选中状态，不处理选中事件
  if (isUpdatingSelection.value) return

  // 获取当前页面选中的行
  const currentPageSelectedRows = $table.getCheckboxRecords() || []

  // 找出新选中的行（当前选中但不在临时选中列表中的）
  const newlySelectedRows = currentPageSelectedRows.filter(
    (row) => !tempSelectedList.value.some((selected) => selected.id === row.id || selected.payable_order_id === row.id || selected.pinvid === row.pinvid),
  )

  // 如果有新选中的行，需要校验供应商
  if (newlySelectedRows.length > 0) {
    // 获取当前已选中的供应商信息（如果有的话）
    const existingItem = tempSelectedList.value[0]

    if (existingItem) {
      // 如果已有选中的应付单，检查新选中的应付单是否与已选中的供应商一致

      // 从当前页面数据中获取已选中应付单的供应商信息
      const existingItemInCurrentPage = data.value.find((row) => row.id === existingItem.id || row.pinvid === existingItem.pinvid || row.payable_order_id === existingItem.payable_order_id)

      const existingSupplierName = existingItemInCurrentPage?.supplier_name || existingItem.supplier_name
      const existingSupplierId = existingItemInCurrentPage?.supplier_id || existingItem.supplier_id

      // 检查新选中的应付单是否与已选中的供应商一致
      const hasDifferentSupplier = newlySelectedRows.some((item) => {
        // 优先使用 supplier_name 进行比较
        if (existingSupplierName && item.supplier_name) {
          return item.supplier_name !== existingSupplierName
        }
        // 如果 supplier_name 不存在，则使用 supplier_id
        if (existingSupplierId !== undefined && existingSupplierId !== null) {
          return item.supplier_id !== existingSupplierId
        }
        return false
      })

      if (hasDifferentSupplier) {
        message.warning(`供应商不一致，请重新选择`)
        // 只取消新选中的行
        if (type == 'change') {
          newlySelectedRows.forEach((row) => {
            let shouldUnselect = false
            if (existingSupplierName && row.supplier_name) {
              shouldUnselect = row.supplier_name !== existingSupplierName
            } else if (existingSupplierId !== undefined && existingSupplierId !== null) {
              shouldUnselect = row.supplier_id !== existingSupplierId
            }
            if (shouldUnselect) {
              $table.setCheckboxRow(row, false)
            }
          })
          return
        }
      }
    } else {
      // 如果是第一次选择，检查新选中的应付单之间是否有供应商冲突
      // 检查新选中的应付单之间是否有供应商冲突
      if (newlySelectedRows.length > 0) {
        // 获取当前页面所有选中的应付单的供应商信息
        const allSelectedSuppliers = currentPageSelectedRows.map((row) => ({
          supplier_name: row.supplier_name,
          supplier_id: row.supplier_id,
        }))

        // 检查新选中的应付单是否与当前页面所有选中的应付单供应商一致
        const hasConflict = newlySelectedRows.some((newRow) => {
          return allSelectedSuppliers.some((existingSupplier) => {
            // 优先使用 supplier_name 进行比较
            if (existingSupplier.supplier_name && newRow.supplier_name) {
              return newRow.supplier_name !== existingSupplier.supplier_name
            }
            // 如果 supplier_name 不存在，则使用 supplier_id
            if (existingSupplier.supplier_id !== undefined && existingSupplier.supplier_id !== null) {
              return newRow.supplier_id !== existingSupplier.supplier_id
            }
            return false
          })
        })

        if (hasConflict) {
          message.warning(`供应商不一致，请重新选择`)
          // 取消所有新选中的行
          if (type == 'change') {
            newlySelectedRows.forEach((row) => {
              $table.setCheckboxRow(row, false)
            })
            return
          }
        }
      }
    }
  }

  // 更新临时选中列表
  // 1. 保留不在当前页的已选中项
  // 2. 添加当前页新选中的项
  // 3. 移除当前页取消选中的项
  tempSelectedList.value = [
    ...tempSelectedList.value.filter((selected) => !data.value.some((row) => row.id === selected.id || row.id === selected.payable_order_id || row.pinvid === selected.pinvid)),
    ...currentPageSelectedRows.map((row) => ({
      ...row,
      payable_order_id: row.id,
      pinvid: row.pinvid,
    })),
  ]

  // 更新当前页数据的选择状态
  const currentPageIds = new Set(currentPageSelectedRows.map((row) => row.id))
  data.value = data.value.map((row) => ({
    ...row,
    checked: currentPageIds.has(row.id),
  }))

  // 更新显示列表（右侧面板）
  selectProductList.value = JSON.parse(JSON.stringify(tempSelectedList.value))
}

// open方法初始化临时选中列表
const open = (selectedProducts: any[] = [], supplierId: any = null) => {
  // 使用 props 中的供应商子公司信息，如果没有传入参数的话
  const finalSupplierSubsidiaryId = supplierId || props.supplierSubsidiaryId
  const finalSelectedProducts = selectedProducts || props.selectedProducts || []
  // const finalSelectedProducts = []

  // 先重置查询条件
  queryParams.value = {
    page: 1,
    pageSize: 10,
    pinvid: null,
    company_supplier_name: props.supplierSubsidiaryName || null,
    id: props.paymentOrderId || null, // 新增：设置付款单ID
  }

  // 用父组件传入的 selectedProducts 初始化临时选中列表
  initialSelection.value = JSON.parse(JSON.stringify(finalSelectedProducts))
  tempSelectedList.value = JSON.parse(JSON.stringify(finalSelectedProducts))
  selectProductList.value = JSON.parse(JSON.stringify(finalSelectedProducts))
  data.value = []

  visible.value = true
  leftVisible.value = true

  // 如果有供应商子公司信息，自动执行查询
  if (finalSupplierSubsidiaryId) {
    // 构建查询参数
    const searchParams = {
      ...queryParams.value,
      page: 1,
    }

    // 移除空值和未定义的参数
    Object.keys(searchParams).forEach((key) => {
      if (searchParams[key] === null || searchParams[key] === undefined || searchParams[key] === '') {
        delete searchParams[key]
      }
    })

    // 更新查询参数
    queryParams.value = searchParams
  } else {
    queryParams.value = {
      page: 1,
      pageSize: 10,
      pinvid: null,
      company_supplier_name: null,
      id: props.paymentOrderId || null, // 新增：设置付款单ID
    }
  }

  // 获取列表并设置选中状态
  getProductList().then(() => {
    // 确保表格选中状态与数据一致，只选中已存在的应付单
    const $table = productTableEl.value
    if ($table) {
      // 设置标志，防止触发选中事件
      isUpdatingSelection.value = true

      // 清空所有选中状态
      $table.clearCheckboxRow()

      // 使用 nextTick 确保数据更新后再设置选中状态
      nextTick(() => {
        // 只选中已存在的应付单
        data.value.forEach((row) => {
          const isSelected = tempSelectedList.value.some((selected) => selected.id === row.id || selected.payable_order_id === row.id || selected.pinvid === row.pinvid)
          if (isSelected) {
            $table.setCheckboxRow(row, true)
          }
        })

        // 重置标志
        isUpdatingSelection.value = false
      })
    }
  })
}

// 修改 handleSelectProduct 函数
const handleSelectProduct = () => {
  // 无论是否有选择，都触发事件并关闭抽屉
  emits('selectProduct', tempSelectedList.value, null, null)
  visible.value = false
  leftVisible.value = false
}

// 修改 handleClose 方法
const handleClose = () => {
  // 恢复初始选中状态
  tempSelectedList.value = JSON.parse(JSON.stringify(initialSelection.value))
  selectProductList.value = JSON.parse(JSON.stringify(initialSelection.value))

  // 只关闭抽屉
  visible.value = false
  leftVisible.value = false
}

// 获取应付单列表
const getProductList = async () => {
  const res = await GetJstPayPayableList(queryParams.value)

  // 直接使用原始数据，不添加checked字段
  data.value = res.data.list
  total.value = res.data.total

  // 设置选中状态
  const $table = productTableEl.value
  if ($table) {
    // 设置标志，防止触发选中事件
    isUpdatingSelection.value = true

    // 先清空所有选中状态
    $table.clearCheckboxRow()

    // 使用 nextTick 确保数据更新后再设置选中状态
    await nextTick()

    // 只选中已存在的应付单
    data.value.forEach((row) => {
      const isSelected = tempSelectedList.value.some((selected) => selected.id === row.id || selected.payable_order_id === row.id || selected.pinvid === row.pinvid)
      if (isSelected) {
        $table.setCheckboxRow(row, true)
      }
    })

    // 重置标志
    isUpdatingSelection.value = false
  }
}

// 分页
const handlePageChange = (page: number, pageSize: number) => {
  queryParams.value.page = page
  queryParams.value.pageSize = pageSize
  // 翻页时不清空选中状态，只更新数据
  getProductList()
}

// 查询
const handleSearch = () => {
  // 构建查询参数
  const searchParams = {
    ...queryParams.value,
    page: 1,
  }

  // 移除空值和未定义的参数
  Object.keys(searchParams).forEach((key) => {
    if (searchParams[key] === null || searchParams[key] === undefined || searchParams[key] === '') {
      delete searchParams[key]
    }
  })

  // 更新查询参数
  queryParams.value = searchParams
  // 查询时保持选中状态
  getProductList()
}

// 修改 handleUnselectRow 方法
const handleUnselectRow = (row: any) => {
  // 从临时选中列表中移除
  tempSelectedList.value = tempSelectedList.value.filter((item) => item.payable_order_id !== row.payable_order_id && item.pinvid !== row.pinvid && item.id !== row.id)

  // 更新左侧表格的选中状态
  const $table = productTableEl.value
  if ($table) {
    // 查找当前页中对应的行
    const targetRow = data.value.find((item) => item.id === row.payable_order_id || item.pinvid === row.pinvid || item.id === row.id)
    if (targetRow) {
      $table.setCheckboxRow(targetRow, false)
    }
  }

  // 更新显示列表
  selectProductList.value = JSON.parse(JSON.stringify(tempSelectedList.value))
}

// 修改 cleanCheck 方法
const cleanCheck = async () => {
  // 只清空临时选中列表，保持 initialSelection 不变
  tempSelectedList.value = []
  const $table = productTableEl.value
  if ($table) {
    $table.clearCheckboxRow()
  }

  // 更新显示列表
  selectProductList.value = []
}

// 修改重置方法
const handleReset = () => {
  // const company_supplier_id = queryParams.value.company_supplier_id
  queryParams.value = {
    page: 1,
    pageSize: 10,
    pinvid: null,
    company_supplier_name: null,
    id: props.paymentOrderId || null, // 新增：设置付款单ID
  }
  // 重置时保持选中状态
  getProductList()
}

// 暴露open和close方法供父组件调用
defineExpose({
  open,
  close: () => {
    visible.value = false
    leftVisible.value = false
  },
})
</script>

<style scoped lang="scss">
.paginationBox {
  display: flex;
  align-items: center;
  margin-top: 0.83rem;

  .totalBox {
    display: flex;
    align-items: flex-end;
    margin-left: 20px;
    color: #000;

    .text {
      margin-right: 8px;
      font-size: 14px;
    }

    .total {
      font-size: 16px;
      color: #1890ff;
    }
  }
}
</style>
