import { request } from './request'

export const Detail = (data) => request({ url: '/notice/detail', data })

export const Log = (data) => request({ url: '/notice/log', data })
// 添加系统通知
export const AddNotify = (data) => request({ url: '/api/Notify/Add', data })
// 编辑系统通知
export const EditNotify = (data) => request({ url: '/api/Notify/Update', data })
// 批量发布系统通知
export const BatchPublishNotify = (data) => request({ url: '/api/Notify/BatchPublishs', data })
// 删除系统通知
export const DeleteNotify = (data) => request({ url: '/api/Notify/Delete', data })
// 获取系统通知列表
export const GetNotifyList = (data) => request({ url: '/api/Notify/GetList', data })
// 获取系统通知详情
export const DetailByEdit = (data) => request({ url: '/notice/detailByEdit', data })
// 撤回发布
export const RevocationNotify = (data) => request({ url: '/api/Notify/Revocation', data })
// 获取通知相关下拉框
export const GetDropsSource = () => request({ url: '/api/Notify/GetDropsSource' }, 'GET')
// 获取最新通知
export const GetNewNotify = () => request({ url: '/api/Notify/GetNewNotify' }, 'GET')
// 保存并发布
export const SaveAndPublish = (data) => request({ url: '/api/Notify/SaveAndPublish', data })
// 查看通知
export const GetNotice = (id) => request({ url: `/api/Notify/GetNotifyDetails?id=${id}` })
