<template>
  <div class="flex flex-col h-full main">
    <Form
      ref="formRef"
      v-model:form="formArr"
      :page-type="PageTypeEnum.PurchaseReturnApplication"
      @resetForm="
        () => {
          formArr.find((item) => item.key === 'audit_status')!.value = ''
        }
      "
      @search="tableRef?.search()"
      @setting="tableRef?.showTableSetting()"
    >
      <template #header>
        <StatusTabs v-model:status="formArr.find((item) => item.key === 'audit_status')!.value" :options="reservationStatusOption" :count-map="labelStatusCountMap" @change="tableRef?.search()" />
      </template>
    </Form>
    <BaseTable ref="tableRef" :page-type="PageTypeEnum.PurchaseReturnApplication" v-model:form="formArr" :is-checkbox="true" :get-list="GetPurchaseRetrueApplications" :form-format="formatData">
      <template #left-btn>
        <div class="flex gap-12px">
          <a-button type="primary" @click="handleOpenAudit(true)" v-if="btnPermission[118002] || btnPermission[118003] || btnPermission[118004] || btnPermission[118005]">批量审核通过</a-button>
          <a-button type="primary" @click="handleOpenAudit(false)" v-if="btnPermission[118002] || btnPermission[118003] || btnPermission[118004] || btnPermission[118005]">批量审核拒绝</a-button>
        </div>
      </template>
      <template #right-btn>
        <a-button type="primary" @click="handleApplication(0)" v-if="btnPermission[118006]">新建退库申请单</a-button>
      </template>
      <template #fix_option="{ row, column }">
        <RightOperate :list="rightOperateList" :row="row" :column="column" />
      </template>
    </BaseTable>
    <Application
      ref="applicationRef"
      v-if="showApplication"
      @update="
        () => {
          tableRef?.search()
          showApplication = false
        }
      "
      @close="showApplication = false"
    ></Application>
    <AuditConfirm ref="auditConfirmRef" @audit="onAuditConfirm" />
  </div>
</template>

<script lang="ts" setup>
import { message } from 'ant-design-vue'

import Application from './components/Application.vue'

import { BatchAudit, GetPurchaseRetrueApplications, GetRetrueCreaterNameList } from '@/servers/PurchaseReturnApplication'
import { GetPageSuppliersSelect, GetPageMRPSupplierCompanySelect } from '@/servers/BusinessCommon'

import { PageTypeEnum } from '@/enums/tableEnum'

const { btnPermission } = usePermission()
//
const { labelStatusCountMap, labelStatusRefresh } = useLabelStatus(PageTypeEnum.PurchaseReturnApplication)
const formArr = ref([
  { label: '状态', value: '', type: '', key: 'audit_status' },
  { label: '退库申请单编号', value: '', type: 'inputDlg', key: 'number' },
  { label: '退库单编号', value: '', type: 'inputDlg', key: 'jst_return_no' },
  { label: '商品编号', value: '', type: 'inputDlg', key: 'sku_id' },
  { label: '采购单编号', value: '', type: 'inputDlg', key: 'purchase_order_number' },
  { label: '商品名称', value: '', type: 'input', key: 'sku_name' },
  { label: 'SRS平台商品编码', value: '', type: 'input', key: 'srs_platform_prod_code' },
  {
    label: '供应商',
    value: null,
    type: 'select-supplier',
    key: 'supplier_id',
    mode: 'single',
    api: GetPageSuppliersSelect,
  },
  {
    label: '供应商子公司',
    value: null,
    type: 'select-supplier',
    key: 'company_supplier_id',
    mode: 'single',
    api: GetPageMRPSupplierCompanySelect,
    apiParams: { is_contains_srs: true },
  },
  {
    label: '申请类型',
    value: null,
    type: 'select',
    selectArr: [
      { label: '仅退款', value: 10 },
      { label: '退货退库', value: 20 },
    ],
    key: 'application_type',
  },
  {
    label: '退货原因',
    value: null,
    type: 'select',
    selectArr: [
      { label: '采购退货', value: 10 },
      { label: '质检退货', value: 20 },
    ],
    key: 'return_reason_type',
  },
  {
    label: '申请人',
    value: null,
    type: 'select',
    selectArr: [],
    key: 'creator_id',
    api: () => [],
  },
])
useSearchForm(formArr)
const tableRef = useTemplateRef('tableRef')
const applicationRef = useTemplateRef('applicationRef') // 退库申请单
const reservationStatusOption = ref([
  { label: '全部', value: '' },
  { label: '待提审', value: '10', code: 'await_arraigned_count' },
  { label: '待一级审核', value: '20', code: 'one_auditing_count' },
  { label: '待二级审核', value: '30', code: 'two_auditing_count' },
  { label: '待三级审核', value: '40', code: 'three_auditing_count' },
  { label: '待四级审核', value: '50', code: 'four_auditing_count' },
  { label: '已通过', value: '90' },
  { label: '已拒绝', value: '95' },
])
const showApplication = ref(false) // 显示退库申请单详情页
const handleApplication = async (type: any, id?: any) => {
  showApplication.value = true
  await nextTick()
  applicationRef.value?.open(type, id)
}

const formatData = (data: any) => {
  labelStatusRefresh()

  if (data.jst_return_no) {
    data.jst_return_no = data.jst_return_no.split(',')
  }
  console.log(data.jst_return_no)

  return {
    ...data,
  }
}

const getOptions = async () => {
  const res = await GetRetrueCreaterNameList()
  formArr.value.forEach((item) => {
    if (item.key === 'creator_id') {
      item.selectArr = res.data.map((i) => ({ label: i.value, value: i.key })) || []
    }
  })
}

const auditConfirmRef = useTemplateRef('auditConfirmRef')
const handleOpenAudit = async (isPass: boolean) => {
  if (tableRef.value?.checkItemsArr.length === 0) {
    message.warning('请选择需要审核的退库申请单')
    return
  }

  const audit_status = [...new Set(tableRef.value?.checkItemsArr.map((v) => v.audit_status))]
  if (audit_status.length > 1) {
    message.warning('请选择同一状态的数据')
    return
  }
  if (![PurchaseOrderAuditStatusEnum.待一级审核, PurchaseOrderAuditStatusEnum.待二级审核, PurchaseOrderAuditStatusEnum.待三级审核, PurchaseOrderAuditStatusEnum.待四级审核].includes(audit_status[0])) {
    message.warning('该状态无需审核')
    return
  }
  if (
    (audit_status[0] == PurchaseOrderAuditStatusEnum.待一级审核 && btnPermission.value[118002]) ||
    (audit_status[0] == PurchaseOrderAuditStatusEnum.待二级审核 && btnPermission.value[118003]) ||
    (audit_status[0] == PurchaseOrderAuditStatusEnum.待三级审核 && btnPermission.value[118004]) ||
    (audit_status[0] == PurchaseOrderAuditStatusEnum.待四级审核 && btnPermission.value[118005])
  ) {
    auditConfirmRef.value?.open(isPass)
  } else {
    message.error('您没有该状态的审核权限')
  }
}
const onAuditConfirm = async (data, callback, failback) => {
  const { audit_opinion, is_pass } = data
  const params = {
    is_pass,
    audit_opinion,
    ids: [...new Set(tableRef.value?.checkItemsArr.map((f) => f.id))],
  }
  try {
    const res = await BatchAudit(params)
    if (res.success) {
      message.success('操作成功')
      tableRef.value?.search()
      callback()
    } else {
      message.error(res.message)
      failback()
    }
  } catch {
    failback()
  }
}

onMounted(() => {
  getOptions()
})

const rightOperateList = ref([
  {
    label: '查看',
    show: 118001,
    onClick: ({ row }) => {
      handleApplication(2, row.id)
    },
  },
  {
    label: '编辑',
    show: ({ row }) => {
      return (row.audit_status === 10 || row.audit_status == 95) && btnPermission.value[118007]
    },
    onClick: ({ row }) => {
      handleApplication(1, row.id)
    },
  },
  {
    label: '审核',
    show: ({ row }) => {
      return (
        (btnPermission.value[118002] && row.audit_status === 20) ||
        (btnPermission.value[118003] && row.audit_status === 30) ||
        (btnPermission.value[118004] && row.audit_status === 40) ||
        (btnPermission.value[118005] && row.audit_status === 50)
      )
    },
    onClick: ({ row }) => {
      handleApplication(3, row.id)
    },
  },
])
</script>

<style lang="scss" scoped>
//
</style>
