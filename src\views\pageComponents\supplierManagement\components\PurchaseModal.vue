<template>
  <a-modal :width="modalWidth + 'px'" v-model:open="visible" title="选择对接采购员" @cancel="close">
    <div class="flex">
      <a-input-search class="flex-1" v-model:value="keyword1" placeholder="请输入部门" @search="handleSearch"></a-input-search>
      <div class="w-10"></div>
      <a-input-search class="flex-1" v-model:value="keyword2" placeholder="请输入岗位/姓名" @search="getPurchaseByDept"></a-input-search>
    </div>
    <div class="h-400px overflow-hidden mt-10 mb-25 flex justify-between">
      <div class="w-270">
        <a-tree :height="400" :tree-data="deptOptions" :fieldNames="{ children: 'children', title: 'value', key: 'key' }" @select="handleSelectDept">
          <template #title="row">
            {{ row.value }}
          </template>
        </a-tree>
      </div>
      <div class="overflow-auto w-270">
        <a-empty class="mt-150" v-if="!purchaseList?.length"></a-empty>
        <a-checkbox-group class="mt-4" v-model:value="checkedList" @change="checkedListChange">
          <a-row>
            <a-col v-for="item in purchaseList" :key="item.value" :span="24">
              <a-checkbox class="flex mb-2" :value="item.value" :disabled="item.disabled">{{ item.label }}</a-checkbox>
            </a-col>
          </a-row>
        </a-checkbox-group>
      </div>
    </div>
    <template #footer>
      <div class="flex justify-end items-center">
        <div class="mr-auto" v-if="checkedPeoples.length && !isSingle">
          <a-popover trigger="hover" :overlayInnerStyle="{ padding: '5px' }">
            <template #content>
              <div class="h-full max-h-300px overflow-auto">
                <div class="pr-10px pb-4px" v-for="item in checkedPeoples" :key="item">{{ item }}</div>
              </div>
            </template>
            已选 {{ checkedPeoples.length }} 人
          </a-popover>
        </div>
        <a-button key="back" @click="close">取消</a-button>
        <a-button key="submit" type="primary" @click="confirm">确定</a-button>
      </div>
    </template>
  </a-modal>
</template>

<script lang="ts" setup>
import { px2 } from '@/utils'

import { GetDeptByTreeSelect, GetPurchaseByDeptSelect } from '@/servers/BusinessCommon'

interface ItemProp {
  label: string
  value: number
  disabled?: boolean
}
interface OpenOptions {
  single?: boolean
  disabledList?: any[]
}

const modalWidth = px2(600) < 600 ? 600 : px2(600)

const emit = defineEmits(['confirm'])
const visible = ref(false)
const checkedListMap = ref<Record<number, string>>({})
const checkedList = ref<number[]>([])
const isSingle = ref()
const disabledMap = ref({})
const open = (data: ItemProp[] = [], options: OpenOptions = {}) => {
  visible.value = true
  isSingle.value = options.single
  deptId.value = null
  keyword1.value = null
  keyword2.value = null
  checkedList.value = []
  checkedListMap.value = data.reduce((acc, cur) => ({ ...acc, [cur.value]: cur.label }), {})
  getDept()
  if (options?.disabledList?.length) {
    disabledMap.value = options.disabledList.reduce((acc, cur) => ({ ...acc, [cur]: true }), {})
  }
}

const deptOptions = ref<ItemProp[]>([])
const deptId = ref<number | null>(null)
const getDept = async () => {
  const res = await GetDeptByTreeSelect({
    keyword: keyword1.value,
  })
  deptOptions.value = res.data
  getPurchaseByDept()
}
const handleSearch = () => {
  deptId.value = null
  getDept()
}
const handleSelectDept = ([id]) => {
  deptId.value = id
  getPurchaseByDept()
}

const keyword1 = ref()
const keyword2 = ref()
const purchaseList = ref<ItemProp[]>([])
const getPurchaseByDept = async () => {
  const res = await GetPurchaseByDeptSelect({ dept_id: deptId.value, keyword: keyword2.value, is_chilrd_query: false })
  purchaseList.value = res.data.map((f) => ({
    ...f,
    disabled: !!disabledMap.value[f.value],
  }))
  checkedList.value = purchaseList.value.filter((f) => checkedListMap.value[f.value]).map((f) => f.value)
}

const checkedListChange = (arr) => {
  let target = [...arr]
  if (isSingle.value) {
    target = [arr[arr.length - 1]]
    checkedList.value = target
  }
  const dataMap = purchaseList.value.reduce((acc, cur) => {
    const found = target.find((f) => f === `${cur.value}`)
    return { ...acc, [cur.value]: found ? cur.label : false }
  }, {})
  checkedListMap.value = {
    ...checkedListMap.value,
    ...dataMap,
  }
}
const checkedPeoples = computed(() => {
  return Object.values(checkedListMap.value).filter((f) => f)
})

const close = () => {
  visible.value = false
}
const confirm = () => {
  visible.value = false
  emit(
    'confirm',
    Object.keys(checkedListMap.value)
      .filter((value) => checkedListMap.value[value])
      .map((value) => ({ value, label: checkedListMap.value[value] })),
  )
}

defineExpose({ open })
</script>

<style lang="scss" scoped>
//
:deep(.ant-tabs-tab) {
  margin: 0 !important;
}

:deep(.ant-tabs-nav-operations) {
  display: none !important;
}
</style>
