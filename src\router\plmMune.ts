const defaultMenu = [
  {
    path: '/systemManagement',
    title: '系统管理',
    name: '系统管理',
    children: [
      {
        path: '/roleManagement',
        title: '角色管理',
        name: '角色管理',
        component: () => import('@/views/pageComponents/systemManagement/roleManagement/index.vue'),
        meta: { KeepAlive: true },
      },
    ],
  },
  {
    path: '/notifyManagement',
    title: '通知管理',
    name: '通知管理',
    children: [
      {
        path: '/systemNotify',
        title: '系统通知',
        name: '系统通知',
        component: () => import('@/views/pageComponents/notifyManagement/systemNotify/index.vue'),
        meta: { KeepAlive: true },
      },
      {
        path: '/paramConfig',
        title: '参数配置',
        name: '参数配置',
        component: () => import('@/views/pageComponents/systemManagement/ParamConfig/index.vue'),
        meta: { KeepAlive: true },
      },
      {
        path: '/generalManagement',
        title: '通用管理',
        name: '通用管理',
        component: () => import('@/views/pageComponents/systemManagement/generalManagement/index.vue'),
        meta: { KeepAlive: true },
      },
    ],
  },
  {
    path: '/clientModule',
    title: '用户管理1',
    name: '用户管理1',
    children: [
      {
        path: '/userLists',
        title: '用户管理',
        name: '用户管理',
        component: () => import('@/views/pageComponents/clientModule/userList/index.vue'),
        meta: { KeepAlive: true },
      },
      {
        path: '/supplierOrganizationalLists',
        title: '供应商组织架构管理',
        name: '供应商组织架构管理',
        component: () => import('@/views/pageComponents/clientModule/supplierOrganizationalLists/index.vue'),
        meta: { KeepAlive: true },
      },
    ],
  },
  {
    path: '/personalCenter',
    title: '个人中心',
    name: '个人中心',
    children: [
      {
        path: '/corporateInformation',
        title: '公司信息',
        name: '公司信息',
        component: () => import('@/views/pageComponents/personalCenter/corporateInformation.vue'),
        meta: { KeepAlive: true },
      },
    ],
  },
  {
    path: '/paymentOrderManager',
    title: '财务管理',
    name: '财务管理',
    children: [
      {
        path: '/paymentApply',
        name: '付款申请单',
        component: () => import('@/views/pageComponents/paymentApply/index.vue'),
      },
      {
        path: '/paymentStatistics',
        name: '财务付款统计',
        component: () => import('@/views/pageComponents/paymentStatistics/index.vue'),
      },
      {
        path: '/paymentOrder',
        title: '付款订单',
        name: '付款订单',
        component: () => import('@/views/pageComponents/paymentOrder/index.vue'),
        meta: { KeepAlive: true },
      },
      {
        path: '/supplierLiquidation',
        title: '供应商清算',
        name: '供应商清算',
        component: () => import('@/views/pageComponents/financial/SupplierLiquidation/index.vue'),
        meta: { KeepAlive: true },
      },
      {
        path: '/billPayable',
        title: '应付单',
        name: '应付单',
        component: () => import('@/views/pageComponents/financial/billPayable/index.vue'),
        meta: { KeepAlive: true },
      },
      ...[
        { type: '新增', key: 'new', isNew: true },
        { type: '查看', key: 'view' },
        { type: '编辑', key: 'edit' },
      ].map((item) => ({
        path: `/billPayable/${item.key}${item.isNew ? '' : '/:id'}`,
        title: `${item.type}应付单`,
        name: `${item.type}应付单`,
        meta: {
          isChildPage: true,
          code: `billPayable${item.key.replace(/^(.)/, (match) => match.toUpperCase())}`,
          KeepAlive: true,
        },
        component: () => import('@/views/pageComponents/financial/billPayable/details.vue'),
      })),
    ],
  },
  {
    path: 'purchaseManager',
    title: '采购管理',
    name: '采购管理',
    children: [
      {
        path: '/purchaseprice',
        title: '采购价目表',
        name: '采购价目表',
        component: () => import('@/views/pageComponents/purchaseManagement/purchaseprice/index.vue'),
        meta: { KeepAlive: true },
      },
      {
        path: '/purchaseadjustprice',
        title: '采购调价表',
        name: '采购调价表',
        component: () => import('@/views/pageComponents/purchaseManagement/purchaseAdjustPrice/index.vue'),
        meta: { KeepAlive: true },
      },
      {
        path: '/purchaseapplyorder',
        title: '采购申请单',
        name: '采购申请单',
        component: () => import('@/views/pageComponents/purchaseManagement/purchaseapplyorder/index.vue'),
        meta: { KeepAlive: true },
      },
      {
        path: '/purchaseorderManagement',
        title: '采购单管理',
        name: '采购单管理',
        component: () => import('@/views/pageComponents/purchaseManagement/purchaseorderManagement/index.vue'),
        meta: { KeepAlive: true },
      },
      {
        path: '/purchaseneworder',
        title: '采购单新增',
        name: '采购单新增',
        meta: {
          isChildPage: true,
          code: 'purchaseneworder',
          KeepAlive: true,
        },
        component: () => import('@/views/pageComponents/purchaseManagement/purchaseOrderContent/index.vue'),
      },
      {
        path: '/purchaseOrderReview/:id',
        title: '审核采购单',
        name: '审核采购单',
        meta: {
          isChildPage: true,
          code: 'purchaseOrderReview',
          KeepAlive: true,
        },
        component: () => import('@/views/pageComponents/purchaseManagement/purchaseOrderContent/index.vue'),
      },
      {
        path: '/purchaseOrderLook/:id',
        title: '查看采购单',
        name: '查看采购单',
        meta: {
          isChildPage: true,
          code: 'purchaseOrderLook',
          KeepAlive: true,
        },
        component: () => import('@/views/pageComponents/purchaseManagement/purchaseOrderContent/index.vue'),
      },
      {
        path: '/purchaseOrderEdit/:id',
        title: '编辑采购单',
        name: '编辑采购单',
        meta: {
          isChildPage: true,
          code: 'purchaseOrderEdit',
          KeepAlive: true,
        },
        component: () => import('@/views/pageComponents/purchaseManagement/purchaseOrderContent/index.vue'),
      },
      {
        path: '/purchaseOrderAlter/:id',
        title: '变更采购单',
        name: '变更采购单',
        meta: {
          isChildPage: true,
          code: 'purchaseOrderAlter',
          KeepAlive: true,
        },
        component: () => import('@/views/pageComponents/purchaseManagement/purchaseorderChange/detail.vue'),
      },
      {
        path: '/purchaseorderChange',
        title: '采购单变更',
        name: '采购单变更',
        component: () => import('@/views/pageComponents/purchaseManagement/purchaseorderChange/index.vue'),
        meta: { KeepAlive: true },
      },
      {
        path: '/purchaseOrderAlterReview/:id',
        title: '审核变更单',
        name: '审核变更单',
        meta: {
          isChildPage: true,
          code: 'purchaseOrderAlterReview',
          KeepAlive: true,
        },
        component: () => import('@/views/pageComponents/purchaseManagement/purchaseorderChange/detail.vue'),
      },
      {
        path: '/purchaseOrderAlterLook/:id',
        title: '查看变更单',
        name: '查看变更单',
        meta: {
          isChildPage: true,
          code: 'purchaseOrderAlterLook',
          KeepAlive: true,
        },
        component: () => import('@/views/pageComponents/purchaseManagement/purchaseorderChange/detail.vue'),
      },
      {
        path: '/purchaseOrderAlterEdit/:id',
        title: '编辑变更单',
        name: '编辑变更单',
        meta: {
          isChildPage: true,
          code: 'purchaseOrderAlterEdit',
          KeepAlive: true,
        },
        component: () => import('@/views/pageComponents/purchaseManagement/purchaseorderChange/detail.vue'),
      },
      {
        path: '/myPurchaseOrder',
        title: '我的订单',
        name: '我的订单',
        component: () => import('@/views/pageComponents/purchaseManagement/myPurchaseOrder/index.vue'),
        meta: { KeepAlive: true },
      },
      // {
      //   path: '/verifyMyOrder/:id/:type',
      //   title: '确认订单',
      //   name: '确认订单',
      //   meta: {
      //     isChildPage: true,
      //     code: 'verifyMyOrder',
      //     KeepAlive: true,
      //   },
      //   component: () => import('@/views/pageComponents/purchaseManagement/verifyMyOrder/index.vue'),
      // },
      {
        path: '/purchasebatchorder/:ids?/:preprocessing?',
        title: '采购申请单新增采购',
        name: '采购申请单新增采购',
        meta: {
          isChildPage: true,
          code: 'purchasebatchorder',
          KeepAlive: true,
        },
        component: () => import('@/views/pageComponents/purchaseManagement/purchaseOrderContent/index.vue'),
      },
    ],
  },
  {
    path: '/billManagement',
    title: '账单管理',
    name: '账单管理',
    children: [
      {
        path: '/bill',
        title: '账单核对',
        name: '账单核对',
        component: () => import('@/views/pageComponents/billManagement/billVerification/index.vue'),
        meta: { KeepAlive: true },
      },
      {
        path: '/billVerification/generateBill/:id?',
        title: '生成账单',
        name: '生成账单',
        component: () => import('@/views/pageComponents/billManagement/billVerification/components/GenerateBill.vue'),
        meta: {
          KeepAlive: false,
          isChildPage: true,
          code: 'billVerificationGenerateBill', // 添加明确的 code 字段
        },
      },
      ...[
        { type: '新增', key: 'new', isNew: true },
        { type: '查看', key: 'view' },
        { type: '编辑', key: 'edit' },
      ].map((item) => ({
        path: `/billVerification/generateBill${item.isNew ? '/new' : `/${item.key}/:id`}`,
        title: `${item.type}账单`,
        name: `${item.type}账单`,
        meta: {
          isChildPage: true,
          code: `billVerification${item.key.replace(/^(.)/, (match) => match.toUpperCase())}`,
          KeepAlive: false, // 修复：禁用缓存，防止自动恢复
        },
        component: () => import('@/views/pageComponents/billManagement/billVerification/components/GenerateBill.vue'),
      })),
    ],
  },
  {
    path: '/systemDocking',
    title: '系统对接',
    name: '系统对接',
    children: [
      {
        path: '/ali1688ProductMap',
        title: '1688商品映射',
        name: '1688商品映射',
        component: () => import('@/views/pageComponents/ProductMapping/index.vue'),
        meta: { KeepAlive: true },
      },
      {
        path: '/ali1688PurchaseAccountManagement',
        title: '1688采购账号管理',
        name: '1688采购账号管理',
        component: () => import('@/views/pageComponents/PurchaseAccount/index.vue'),
        meta: { KeepAlive: true },
      },
      {
        path: '/receivingAddress/:id',
        title: '采购账号收货地址',
        name: '采购账号收货地址',
        component: () => import('@/views/pageComponents/ReceivingAddress/index.vue'),
        meta: { isChildPage: true, code: 'ReceivingAddress', KeepAlive: true },
      },
    ],
  },
  {
    path: '/wareHouseManagement',
    title: '入库管理',
    name: '入库管理',
    children: [
      {
        path: '/bookingOrder',
        title: '预约入库单',
        name: '预约入库单',
        component: () => import('@/views/pageComponents/ReservationList/index.vue'),
        meta: { KeepAlive: true },
      },
      {
        path: '/addReservation',
        title: '预约入库单新增',
        name: '预约入库单新增',
        meta: {
          isChildPage: true,
          code: 'addReservation',
          KeepAlive: true,
        },
        component: () => import('@/views/pageComponents/ReservationList/detail.vue'),
      },
      {
        path: '/lookReservation/:id',
        title: '预约入库单查看',
        name: '预约入库单查看',
        meta: {
          isChildPage: true,
          code: 'lookReservation',
          KeepAlive: true,
        },
        component: () => import('@/views/pageComponents/ReservationList/detail.vue'),
      },
      {
        path: '/reviewReservation/:id',
        title: '预约入库单审核',
        name: '预约入库单审核',
        meta: {
          isChildPage: true,
          code: 'reviewReservation',
          KeepAlive: true,
        },
        component: () => import('@/views/pageComponents/ReservationList/detail.vue'),
      },
      {
        path: '/editReservation/:id',
        title: '预约入库单编辑',
        name: '预约入库单编辑',
        meta: {
          isChildPage: true,
          code: 'editReservation',
          KeepAlive: true,
        },
        component: () => import('@/views/pageComponents/ReservationList/detail.vue'),
      },
      {
        path: '/modifyLogistics/:id',
        title: '预约入库单发货',
        name: '预约入库单发货',
        meta: {
          isChildPage: true,
          code: 'modifyLogistics',
          KeepAlive: true,
        },
        component: () => import('@/views/pageComponents/ReservationList/detail.vue'),
      },
      {
        path: '/confirmArrival/:id',
        title: '预约入库单确认到货',
        name: '预约入库单确认到货',
        meta: {
          isChildPage: true,
          code: 'confirmArrival',
          KeepAlive: true,
        },
        component: () => import('@/views/pageComponents/ReservationList/detail.vue'),
      },
      {
        path: '/getWarehouseList',
        title: '仓库管理',
        name: '仓库管理',
        component: () => import('@/views/pageComponents/WarehouseManagement/index.vue'),
        meta: { KeepAlive: true },
      },
      {
        path: '/logisticsCompany',
        title: '物流管理',
        name: '物流管理',
        component: () => import('@/views/pageComponents/LogisticsList/index.vue'),
        meta: { KeepAlive: true },
      },
      {
        path: '/outboundWarehouseOrder',
        title: '委外领料单',
        name: '委外领料单',
        component: () => import('@/views/pageComponents/WarehouseManagement/outboundWarehouseOrder/index.vue'),
        meta: { KeepAlive: true },
      },
      ...[
        { type: '新增', key: 'new', isNew: true },
        { type: '查看', key: 'view' },
        { type: '编辑', key: 'edit' },
        { type: '审核', key: 'review' },
      ].map((item) => ({
        path: `/outboundWarehouseOrderDetails/${item.key}${item.isNew ? '' : '/:id'}`,
        title: `${item.type}委外领料单`,
        name: `${item.type}委外领料单`,
        meta: {
          isChildPage: true,
          code: `outboundWarehouseOrder${item.key.replace(/^(.)/, (match) => match.toUpperCase())}`,
          KeepAlive: true,
        },
        component: () => import('@/views/pageComponents/WarehouseManagement/outboundWarehouseOrder/details.vue'),
      })),
      {
        path: '/getProcurementInList',
        title: '采购入库单管理',
        name: '采购入库单管理',
        component: () => import('@/views/pageComponents/PurchaseIntoAndOutManagement/PurchaseInto/index.vue'),
        meta: { KeepAlive: true },
      },
      {
        path: '/getProcurementOutList',
        title: '采购退库单管理',
        name: '采购退库单管理',
        component: () => import('@/views/pageComponents/PurchaseIntoAndOutManagement/PurchaseOut/index.vue'),
        meta: { KeepAlive: true },
      },
      {
        path: '/getProcurementOtherInOutList',
        title: '其他出入库管理',
        name: '其他出入库管理',
        component: () => import('@/views/pageComponents/PurchaseIntoAndOutManagement/Other/index.vue'),
        meta: { KeepAlive: true },
      },
      {
        path: '/getPurchaseReturnApplicationList',
        title: '退库申请单',
        name: '退库申请单',
        component: () => import('@/views/pageComponents/PurchaseReturnApplication/index.vue'),
        meta: { KeepAlive: true },
      },
    ],
  },
  {
    path: '/baseData',
    title: '基础资料',
    name: '基础资料',
    children: [
      {
        path: '/productSku',
        title: '商品库',
        name: '商品库',
        component: () => import('@/views/pageComponents/baseData/ProductSku.vue'),
        meta: { KeepAlive: true },
      },
      {
        path: '/productInventory',
        title: '商品库存管理',
        name: '商品库存管理',
        component: () => import('@/views/pageComponents/baseData/ProductInventory.vue'),
        meta: { KeepAlive: true },
      },
    ],
  },
]
export default defaultMenu
