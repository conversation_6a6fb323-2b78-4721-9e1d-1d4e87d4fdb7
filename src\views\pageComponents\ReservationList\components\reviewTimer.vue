<template>
  <a-drawer v-model:open="open" title="审核记录" placement="right" width="500" :bodyStyle="{ padding: '0' }" destroyOnClose>
    <div class="reviewTime">
      <a-timeline>
        <a-timeline-item v-for="(item, index) in AuditRecord" :key="index">
          <div class="contentBox">{{ (item as any).message }}</div>
          <!-- <div>【一级审核】已通过：备注信息123</div> -->
          <div class="timerBox">
            <div>{{ (item as any).audit_time }}</div>
            <!-- <div>2024-12-12</div> -->
          </div>
        </a-timeline-item>
      </a-timeline>
    </div>
  </a-drawer>
</template>
<script setup lang="ts">
import { GetAuditRecord } from '@/servers/BusinessCommon'

const open = ref(false)
const AuditRecord = ref([])
const setModel = async (bol, id) => {
  console.log('id', id)

  open.value = bol
  const res = await GetAuditRecord({ id, type: 6 })
  console.log('res', res)
  if (res.success) {
    AuditRecord.value = res.data
  }
}
defineExpose({ setModel })
</script>
<style scoped lang="scss">
.ant-timeline .ant-timeline-item-last::before {
  display: none;
}

.ant-timeline .ant-timeline-item-last::after {
  display: none;
}

.reviewTime {
  padding: 24px;
  margin-left: 60px;

  .contentBox {
    width: 300px;
    text-align: left;
  }

  .timerBox {
    position: absolute;
    top: 0;
    left: -95px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 62px;
    text-align: center;
  }
}
</style>
