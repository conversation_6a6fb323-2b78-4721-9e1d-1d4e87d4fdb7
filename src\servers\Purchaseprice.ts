import { request } from './request'

// 添加采购价目表
export const AddPurchasePrice = (data) => {
  return request({ url: '/api/PurchasePrice/AddPurchasePrice', data })
}

// 更新采购价目表
export const UpdatePurchasePrice = (data) => {
  return request({ url: '/api/PurchasePrice/UpdatePurchasePrice', data })
}

// 获取采购价目列表
export const GetPurchasePriceList = (data) => {
  return request({ url: '/api/PurchasePrice/GetPurchasePriceList', data })
}

// 批量审核通过 / 拒绝
export const BatchAuditPurchasePrice = (data) => {
  return request({ url: '/api/PurchasePrice/BatchAuditPurchasePrice', data })
}

// 查看采购价目表详情
export const GetPurchasePrice = (id) => {
  return request({ url: `/api/PurchasePrice/GetPurchasePrice?id=${id}` }, 'GET')
}

// 根据Id获取采购明细详情
export const GetPurchasePriceDetail = (data) => {
  return request({ url: '/api/PurchasePrice/GetPurchasePriceDetail', data })
}

// 根据Id获取采购材质明细详情
export const GetPurchaseMaterialDetail = (data) => {
  return request({ url: '/api/PurchasePrice/GetPurchaseMaterialDetail', data })
}

// 获取金蝶商品信息
export const GetK3SkuInfo = (data) => {
  return request({ url: '/api/PurchasePrice/GetK3SkuInfo', data })
}

// 获取材质信息
export const GetProductCaizhiSelect = (data) => {
  return request({ url: '/api/BusinessCommon/GetProductCaizhiSelect', data })
}

// 获取审核记录
export const GetAuditRecord = (data) => {
  return request({ url: '/api/PurchasePrice/GetAuditRecord', data })
}

// 审核通过 / 拒绝
export const Audit = (data) => {
  return request({ url: '/api/PurchasePrice/Audit', data })
}

// 导出采购价目详情单
export const ExportPurchasePriceDetail = (data) => {
  return request({ url: '/api/PurchasePrice/ExportPurchasePriceDetail', data, responseType: 'blob' })
}
// 导出采购价目详情单
export const ExportPurchasePriceMaterialDetail = (data) => {
  return request({ url: '/api/PurchasePrice/ExportPurchasePriceMaterialDetail', data, responseType: 'blob' })
}

// 导入金蝶商品明细
export const ImportK3SkuInfo = (data) => {
  return request({ url: '/api/PurchasePrice/ImportK3SkuInfo', data, isFormData: true, timeout: 0 })
}

// 导入金蝶材质明细
export const ImportMaterialInfo = (data) => {
  return request({ url: '/api/PurchasePrice/ImportMaterialInfo', data, isFormData: true })
}

// 下载价目表导入模板
export const DownloadImportTemplate = (bol) => {
  return request({ url: `/api/PurchasePrice/DownloadImportTemplate?isMaterial=${bol}`, responseType: 'blob' }, 'GET')
}

// 获取供应商选项
export const GetPurchasePriceSelects = (params) => {
  return request({ url: '/api/SupplierFilter/GetSupplierOptions', params }, 'GET')
}

// 获取供应商的价目表
export const GetSupplierOption = () => {
  return request({ url: `/api/Supplier/GetSuppliersSelect` })
}

// 获取未绑定价目表
export const GetNotCorrelationPurchasePriceList = () => {
  return request({ url: '/api/PurchasePrice/GetNotCorrelationPurchasePriceList' }, 'GET')
}

// 关联价目表
export const SupplierCompanyPriceCorrelation = (data) => {
  return request({ url: '/api/Supplier/SupplierCompanyPriceCorrelation', data })
}
// 获取用户下拉列表
export const GetUsers = (data) => {
  return request({ url: '/api/UserModule/GetUsers', data })
}
// 计算子供应商数 /api/Supplier/Calculate
export const Calculate = (data) => {
  return request({ url: '/api/Supplier/Calculate', data }, 'GET')
}
