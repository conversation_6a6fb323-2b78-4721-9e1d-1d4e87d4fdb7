<template>
  <div class="flex flex-col h-full main">
    <Form ref="formRef" v-model:form="formArr" :page-type="PageTypeEnum.SupplierAudit" @search="search" :clear-cb="clearForm" @setting="tableRef?.showTableSetting()" />

    <BaseTable ref="tableRef" :page-type="PageTypeEnum.SupplierAudit" v-model:form="formArr" :get-list="GetSupplierAuditList" :auto-search="false">
      <template #right-btn>
        <a-button v-if="btnPermission[43101]" type="primary" @click="handleAdd">新增供应商</a-button>
      </template>
      <template #settlement_type="{ row }">
        {{ settlementTypeMap[row.settlement_type] }}
      </template>
      <template #supplier_type="{ row }">
        {{ supplierTypeMap[row.supplier_type] }}
      </template>

      <template #sync_k3_status="{ row }">
        {{ kingdeeStatusMap[row.sync_k3_status] }}
      </template>
      <template #sync_jst_status="{ row }">
        {{ kingdeeStatusMap[row.sync_jst_status] }}
      </template>
      <template #audit_type="{ row }">
        {{ row.type === 1 ? '准入申请' : '变更申请' }}
      </template>
      <template #source_type="{ row }">
        {{ formatOptionLabel(row.source_type, sourceOption) }}
      </template>

      <template #fix_option="{ row, column }">
        <RightOperate :list="rightOperateList" :row="row" :column="column" />
      </template>
    </BaseTable>

    <Detail ref="detailRef" @refresh="search" />
    <Update ref="updateRef" @refresh="search" />
  </div>
</template>

<script lang="ts" setup>
import { Enum2Options, filterOption, formatOptionLabel } from '@/utils'
import { settleAccountOption } from '@/common/options'
import { settlementTypeMap, supplierTypeMap, kingdeeStatusMap } from '@/common/map'

import Detail from '../components/SupplierDetail.vue'
import Update from '../components/SupplierUpdate.vue'

import { GetMRPSupplierGroupSelectByParent, GetDeptSelect, GetPurchaseByDeptSelect } from '@/servers/BusinessCommon'
import { GetSupplierAuditList } from '@/servers/Supplier'

import { PageTypeEnum } from '@/enums/tableEnum'

const { btnPermission } = usePermission()

const tableRef = ref()
const formRef = ref()
const search = () => tableRef.value.search()

const clearForm = () => {
  formArr.value.find((v) => v.key === 'buyer_id')!.selectArr = []
}

const sourceOption = Enum2Options(SupplierSourceTypeEnum)
const formArr = ref([
  { label: '编号', value: '', type: 'input', key: 'supplier_audit_number', precision: 0 },
  { label: '供应商编码', value: '', type: 'inputNumber', key: 'supplier_id', precision: 0 },
  { label: '供应商名称', value: '', type: 'input', key: 'supplier_name' },
  { label: '供应商分组', value: null, type: 'select', key: 'supplier_group_id', selectArr: [], showSearch: true, filterOption },
  {
    label: '结算方式',
    value: null,
    type: 'select',
    key: 'settlement_type',
    selectArr: settleAccountOption,
  },
  {
    label: '供应商类型',
    value: null,
    type: 'select',
    key: 'supplier_type',
    selectArr: [
      { label: '线下供应商', value: 1 },
      { label: '1688线上供应商', value: 2 },
    ],
  },
  {
    label: '对接部门',
    value: null,
    type: 'select',
    key: 'dept_id',
    selectArr: [],
    showSearch: true,
    filterOption,
    onChange: (val) => getDeptMember(val),
  },
  { label: '对接采购员', value: null, type: 'select', key: 'buyer_id', selectArr: [], showSearch: true, filterOption },
  {
    label: '审核类型',
    value: null,
    type: 'select',
    key: 'type',
    selectArr: [
      { label: '准入申请', value: 1 },
      { label: '变更申请', value: 2 },
    ],
  },
  {
    label: '审核状态',
    value: null,
    type: 'select',
    key: 'audit_status',
    selectArr: [
      { label: '待提审', value: 10 },
      { label: '待审核', value: 20 },
      { label: '待变更审核', value: 21 },
      { label: '待财务审核', value: 30 },
      { label: '已通过', value: 90 },
      { label: '已拒绝', value: 95 },
    ],
  },
  { label: '来源', value: null, type: 'select', key: 'source_type', selectArr: sourceOption, showSearch: true, filterOption },
  { label: '创建时间', value: null, type: 'range-picker', key: 'create_at', formKeys: ['start_time', 'end_time'], placeholder: ['创建开始时间', '创建结束时间'] },
])

const getDeptMember = async (dept_id = '') => {
  const res = await GetPurchaseByDeptSelect({ dept_id })
  formArr.value.find((v) => v.key === 'buyer_id')!.selectArr = res.data
}

onMounted(() => {
  getDeptMember()
  getSelectOptions()
  search()
})

const getSelectOptions = async () => {
  const res1 = await GetMRPSupplierGroupSelectByParent({})
  const res2 = await GetDeptSelect({})

  formArr.value.forEach((item) => {
    if (item.key === 'supplier_group_id') {
      item.selectArr = res1.data.map((v) => ({
        label: v.name,
        options: v.options,
      }))
    } else if (item.key === 'dept_id') {
      item.selectArr = res2.data
    }
  })
}
const updateRef = ref()
const detailRef = ref()
const handleAdd = () => {
  updateRef.value.open()
}

const handleDetail = (row) => {
  detailRef.value.open(row.audit_id, true)
}

const handleAudit = (row) => {
  detailRef.value.open(row.audit_id, true, true)
}

const handleEdit = async (row) => {
  updateRef.value.open(row.audit_id, true)
}

const rightOperateList = ref([
  {
    label: '查看',
    show: 43102,
    onClick: ({ row }) => {
      handleDetail(row)
    },
  },
  {
    label: '编辑',
    show: ({ row }) => {
      return btnPermission.value[43104] && [10, 95].includes(row.audit_status)
    },
    onClick: ({ row }) => {
      handleEdit(row)
    },
  },
  {
    label: '审核',
    show: ({ row }) => {
      return (btnPermission.value[43103] && [20, 21].includes(row.audit_status)) || (btnPermission.value[43105] && [30].includes(row.audit_status))
    },
    onClick: ({ row }) => {
      handleAudit(row)
    },
  },
])

defineExpose({
  search,
})
</script>

<style lang="scss" scoped>
//
</style>
