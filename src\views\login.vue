<template>
  <div class="loginMainBox">
    <div class="loginBox">
      <div class="titleBox">
        <img src="../assets/image/2024_9_6_logo2.png" alt="" />
        <div>
          <div>欢迎登录</div>
          <div>西月SRM系统</div>
        </div>
      </div>
      <div class="formBox">
        <div class="formItem">
          <div class="label">帐号</div>
          <a-input size="large" v-model:value="username" placeholder="请输入账号" @keydown.enter="tapLogin" />
        </div>
        <div class="formItem">
          <div class="label">密码</div>
          <a-input-password size="large" v-model:value="password" placeholder="请输入密码" @keydown.enter="tapLogin" />
        </div>
      </div>
      <div class="operationBox">
        <a-checkbox v-model:checked="remember"><span class="label">记住密码</span></a-checkbox>
        <a-checkbox v-model:checked="autoLogin"><span class="label">下次自动登录</span></a-checkbox>
      </div>
      <a-button @click="tapLogin" :loading="loginLoading" class="login" :class="{ loading: loginLoading }">登录</a-button>
    </div>
    <div class="beian">
      <span>© 广东云跨易网络科技有限公司 版权所有</span>
      <div class="line"></div>
      <a href="https://beian.miit.gov.cn/" target="_blank">粤ICP备2024317142号-1</a>
    </div>
  </div>
  <ConfirmModal ref="confirmModalRef" />
</template>
<script lang="ts" setup>
import { message } from 'ant-design-vue/es'
import { onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'

import ConfirmModal from '@/components/ConfirmModal.vue'

import { Login } from '@/servers/User'

const router = useRouter()
console.log(import.meta.env, '-----------env--------')
const remember = ref(true)
const autoLogin = ref(true)
const username = ref('')
const password = ref('')
const loginLoading = ref(false)

const confirmModalEl = useTemplateRef('confirmModalRef')
onMounted(() => {
  if (localStorage.getItem('rememberUserPsd') == 'true' || !localStorage.getItem('rememberUserPsd')) {
    remember.value = true
  } else {
    remember.value = false
  }
  if (localStorage.getItem('autoLogin') == 'true' || !localStorage.getItem('rememberUserPsd')) {
    autoLogin.value = true
  } else {
    autoLogin.value = false
  }
  try {
    username.value = localStorage.getItem('userAccount') || ''
    password.value = JSON.parse(localStorage.getItem('userPsd') || '')
  } catch (error) {
    console.log(error, '-----------error--------')
  }
})
const tapLogin = () => {
  login()
}

// 登录接口
const login = () => {
  if (loginLoading.value) return
  const data = {
    userName: username.value,
    password: password.value,
  }
  loginLoading.value = true
  Login(data)
    .then((res) => {
      loginLoading.value = false
      if (res.success) {
        if (!localStorage.getItem('screeningObj')) {
          localStorage.setItem('screeningObj', JSON.stringify({}))
        }
        localStorage.setItem('rememberUserPsd', String(remember.value))
        localStorage.setItem('autoLogin', String(autoLogin.value))
        if (remember.value) {
          localStorage.setItem('userPsd', JSON.stringify(password.value))
          localStorage.setItem('userAccount', username.value)
        } else {
          localStorage.setItem('userPsd', '')
          localStorage.setItem('userAccount', '')
        }
        localStorage.setItem('edition', import.meta.env.VITE_APP_VERSION)
        localStorage.setItem('userData', JSON.stringify(res.data))

        const menuList = res.data.permissions_infos
        router.push(menuList[0]?.children?.length > 0 ? menuList[0].children[0].path : menuList[0].path)
        setTimeout(() => {
          window.parent.postMessage(
            {
              type: 'LOGIN',
            },
            'https://up.westmonth.cn',
          )
        }, 300)
      } else {
        message.error(res.message)
      }
    })
    .catch(() => {
      loginLoading.value = false
      confirmModalEl.value?.confirm({
        title: '无法登录系统',
        type: 'warning',
        width: 540,
        confirmBtn: false,
        cancelText: '关闭',
        content: [
          { text: '帐号或密码错误，请检查您输入的帐号和密码是否正确' },
          {
            text: '如果您已输入了正确的帐号密码仍无法登录，可能是',
            subText: ['您的密码已更新或忘记了新密码', '您的帐号没有权限访问该系统', '您的帐号已被停用，无法访问该系统', '如有疑问，请与我司对接的人员联系，或联系系统管理员'],
          },
        ],
      })
    })
}
</script>
<style lang="scss" scoped>
.loginMainBox {
  position: relative;
  width: 100%;
  min-width: 1180px;
  height: 100vh;
  background-color: #fff;
  background-image: url('../assets/image/login-bg.webp');
  background-size: cover;

  .loginBox {
    position: absolute;
    top: 0;
    right: 360px;
    bottom: 0;
    width: 480px;
    height: 490px;
    padding: 60px;
    margin: auto;
    background: rgb(255 255 255 / 90%);
    border-radius: 10px;
    box-shadow: 0 0 20px 0 rgb(0 0 0 / 10%);

    .titleBox {
      display: flex;
      gap: 16px;
      align-items: center;
      margin-bottom: 40px;
      font-size: 18px;
      font-weight: 400;
      line-height: 30px;
      color: #1a1a1a;

      img {
        width: 60px;
        height: 60px;
      }
    }

    .formBox {
      display: flex;
      flex-direction: column;
      gap: 30px;
      margin-bottom: 20px;

      .label {
        margin-bottom: 8px;
        font-size: 12px;
        color: #333;
      }
    }

    ::v-deep(.operationBox) {
      display: flex;
      justify-content: space-between;
      margin-bottom: 40px;

      .label {
        font-size: 12px;
      }
    }

    .login {
      width: 100%;
      height: 40px;
      font-size: 16px;
      color: #fff;
      text-align: center;
      background: linear-gradient(90deg, #8974ff 4%, rgb(64 158 255 / 0%) 50%, #80beff 96%);
      background-color: #1890ff;
      border-radius: 4px;
      transition: all 0.3s;
    }

    .loading {
      background-color: #1677ff;
    }
  }

  .beian {
    position: absolute;
    right: 0;
    bottom: 30px;
    left: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: auto;
    font-size: 12px;
    color: #999;

    .line {
      position: relative;
      top: 1px;
      width: 1px;
      height: 10px;
      margin: 0 12px;
      background: #999;
    }

    a {
      color: #999;
      text-decoration: none;
    }
  }
}

::v-deep(.ant-checkbox-inner) {
  width: 16px;
  height: 16px;

  &::after {
    left: 4px;
  }
}
</style>
