import { GetLabelStatusCount, GetMenuStatusCountList } from '@/servers/BusinessCommon'

import { PageTypeEnum } from '@/enums/tableEnum'

// type PageTypeEnumValue = `${PageTypeEnum}`
// type PageTypeEnumKey = keyof typeof PageTypeEnum

export const useLabelStatus = (pageType: PageTypeEnum | PageTypeEnum[]) => {
  const labelStatusCountMap = ref<Record<string, number>>({})
  const refresh = () => {
    GetLabelStatusCount({ labelStatusType: pageType }).then((res) => {
      labelStatusCountMap.value = res.data
    })
  }

  const menuStatusCountMap = ref<Record<string, number>>({})
  const getMenuCount = () => {
    GetMenuStatusCountList({
      dataPermissions: pageType,
    }).then((res) => {
      menuStatusCountMap.value = res.data
    })
  }

  return {
    labelStatusRefresh: refresh,
    labelStatusCountMap,
    getMenuLabelCount: getMenuCount,
    menuStatusCountMap,
  }
}
