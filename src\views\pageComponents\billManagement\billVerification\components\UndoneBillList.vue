<template>
  <div class="flex flex-col h-full">
    <Form ref="formRef" v-model:form="formArr" :page-type="PageTypeEnum.billVerificationUnDoneBillList" @search="onSearch" @resetForm="onReset" @setting="tableRef?.showTableSetting()" />

    <BaseTable
      ref="tableRef"
      v-model:form="formArr"
      :page-type="PageTypeEnum.billVerificationUnDoneBillList"
      :isCheckbox="true"
      :getList="getUnreconciledList"
      :pageSizeOptions="['20', '50', '100', '250', '500', '1000', '2000', '3000']"
      :totalField="['total_purchase_amount', 'refund_amount', 'prepayment_amount', 'inv_amount']"
      @loadData="onLoadData"
      :keyField="'purchase_number'"
      :checkCb="handleTableSelectionChange"
    >
      <!-- 采购单编号跳转模板 -->
      <template #purchase_number="{ row }">
        <span class="link" @click="handlePurchaseOrderClick(row)">
          {{ row.purchase_number }}
        </span>
      </template>

      <!-- 退库申请单号跳转模板 -->
      <template #return_application_number="{ row }">
        <span v-if="row.return_application_number && row.return_application_number.includes(',')">
          <!-- 多个单号时，每个单号都可以单独点击 -->
          <span v-for="(number, index) in row.return_application_number.split(',').map((n) => n.trim())" :key="index" class="link" @click="handleReturnApplicationClick(row, number.trim(), index)">
            {{ number.trim() }}{{ index < row.return_application_number.split(',').length - 1 ? ', ' : '' }}
          </span>
        </span>
        <span v-else-if="row.return_application_number" class="link" @click="handleReturnApplicationClick(row)">
          {{ row.return_application_number }}
        </span>
        <span v-else>-</span>
      </template>

      <!-- 应付单号跳转模板 -->
      <template #bill_payable_ids="{ row }">
        <span v-if="row.bill_payable_number && row.bill_payable_number.includes(',')">
          <!-- 多个单号时，每个单号都可以单独点击 -->
          <span v-for="(number, index) in row.bill_payable_number.split(',').map((n) => n.trim())" :key="index" class="link" @click="handleBillPayableClick(row, number.trim(), index)">
            {{ number.trim() }}{{ index < row.bill_payable_number.split(',').length - 1 ? ', ' : '' }}
          </span>
        </span>
        <span v-else-if="row.bill_payable_number" class="link" @click="handleBillPayableClick(row)">
          {{ row.bill_payable_number }}
        </span>
        <span v-else-if="row.bill_payable_ids && Array.isArray(row.bill_payable_ids) && row.bill_payable_ids.length > 0" class="link" @click="handleBillPayableClick(row)">
          {{ row.bill_payable_ids.join(', ') }}
        </span>
        <span v-else>-</span>
      </template>

      <!-- 采购入库数量模板 -->
      <template #purchase_inbound_quantity="{ row }">
        <span v-if="row.is_saled === true">-</span>
        <span v-else>{{ row.purchase_inbound_quantity }}</span>
      </template>

      <!-- 未交付数量模板 -->
      <template #undelivered_quantity="{ row }">
        <span v-if="row.is_saled === true">-</span>
        <span v-else>{{ row.undelivered_quantity }}</span>
      </template>

      <!-- 采购总金额模板 -->
      <template #total_purchase_amount="{ row }">
        <span v-if="row.is_saled === true">-</span>
        <span v-else>{{ row.total_purchase_amount }}</span>
      </template>

      <!-- 预付金额模板 -->
      <template #prepayment_amount="{ row }">
        <span v-if="row.is_saled === true">-</span>
        <span v-else>{{ row.prepayment_amount }}</span>
      </template>

      <template #left-btn>
        <a-button type="primary" @click="handleBatchVerification" :disabled="!hasSelectedItems" class="ml-2">批量对账</a-button>
        <!-- <a-button type="primary" @click="handleGenerateBill">批量对账</a-button> -->
        <span class="ml-4 text-sm">
          待核对金额：
          <span class="text-red-500 font-bold text-base">{{ pendingAmount }}</span>
        </span>
      </template>
      <template #right-btn>
        <a-dropdown placement="bottomLeft" class="ml-auto">
          <template #overlay>
            <a-menu @click="onExportTypeChange">
              <a-menu-item v-for="item in exportOptions" :key="item.value">{{ item.label }}</a-menu-item>
            </a-menu>
          </template>
          <a-button>
            导出
            <DownOutlined />
          </a-button>
        </a-dropdown>
      </template>
    </BaseTable>

    <!-- 退库申请单详情抽屉 -->
    <Application ref="applicationRef" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, inject, watch } from 'vue'
import { DownOutlined } from '@ant-design/icons-vue'
import { message, Modal } from 'ant-design-vue'

import Form from '@/components/Form.vue'
import BaseTable from '@/components/BaseTable.vue'
import eventBus from '@/utils/eventBus'
import { checkFormParams } from '@/utils/index'
import { usePage } from '@/hook/usePage'

import Application from '@/views/pageComponents/PurchaseReturnApplication/components/Application.vue'

import { unBillFormArr, exportOptions, BillVerificationExportTypeEnum } from './BillList.data'

import { GetBillCheckListByUnreconciled, ExportBillCheckListByUnreconciled } from '@/servers/billVerification'

import { PageTypeEnum } from '@/enums/tableEnum'

// 处理表格选择变化 - 实现按采购单分组选择
const handleTableSelectionChange = (currentSelectedRows: any[], otherSelectedRows: any[]) => {
  console.log('=== handleTableSelectionChange 触发 ===')
  console.log('当前页选中:', currentSelectedRows.length)
  console.log('其他页选中:', otherSelectedRows.length)

  // 获取当前表格数据
  const currentTableData = tableRef.value?.tableData || []
  console.log('当前表格数据数量:', currentTableData.length)

  // 处理当前页选中的数据
  let finalSelection: any[] = []

  // 遍历当前页选中的行，按采购单分组处理
  currentSelectedRows.forEach((row) => {
    if (row.purchase_number) {
      console.log('处理采购单:', row.purchase_number)

      // 获取当前采购单下的所有明细行
      const samePurchaseRows = currentTableData.filter((item) => item.purchase_number === row.purchase_number)
      console.log('相同采购单的行数:', samePurchaseRows.length)
      console.log(
        '相同采购单的明细:',
        samePurchaseRows.map((item) => ({ detail_id: item.detail_id, purchase_number: item.purchase_number })),
      )

      // 将该采购单下的所有明细行添加到最终选择中
      samePurchaseRows.forEach((detailRow) => {
        if (detailRow.detail_id) {
          const exists = finalSelection.some((item) => item.detail_id === detailRow.detail_id)
          if (!exists) {
            finalSelection.push(detailRow)
            console.log('添加明细到选择:', detailRow.detail_id)
          }
        }
      })
    }
  })

  // 添加其他页选中的数据（如果有的话）
  finalSelection = [...finalSelection, ...otherSelectedRows]

  // 更新表格的选择状态
  if (tableRef.value) {
    tableRef.value.checkItemsArr = finalSelection
  }

  console.log('最终选择状态:', finalSelection)
  console.log('=== handleTableSelectionChange 结束 ===')
}

// 使用usePage hook
const { pushPage } = usePage()

// 退库申请单抽屉引用
const applicationRef = useTemplateRef('applicationRef')

// 跳转函数
// 跳转到采购详情页面
const handlePurchaseOrderClick = (row: any) => {
  // 使用 purchase_order_id 字段跳转
  if (row.id) {
    pushPage(`/purchaseOrderLook/${row.id}`)
  } else {
    message.warning('采购单ID不存在')
  }
}

// 打开退库申请详情抽屉
const handleReturnApplicationClick = (row: any, returnNumber?: string, index?: number) => {
  // 如果传入了具体的单号和索引，使用对应的ID
  if (returnNumber !== undefined && index !== undefined && row.return_application_ids && Array.isArray(row.return_application_ids)) {
    // 根据索引获取对应的ID
    const targetId = row.return_application_ids[index]
    if (targetId) {
      applicationRef.value?.open('view', targetId)
      return
    }
  }

  // 如果传入了具体的单号但没有索引，或者索引无效
  if (returnNumber) {
    // 尝试在ID数组中查找匹配的单号（如果后端返回的数据结构支持）
    if (row.return_application_ids && Array.isArray(row.return_application_ids) && row.return_application_ids.length > 0) {
      // 暂时使用第一个ID
      applicationRef.value?.open('view', row.return_application_ids[0])
      return
    }
  }

  // 如果没有传入具体单号，使用默认逻辑
  if (row.return_application_ids && Array.isArray(row.return_application_ids) && row.return_application_ids.length > 0) {
    // 如果有多个ID，使用第一个
    applicationRef.value?.open('view', row.return_application_ids[0])
  } else if (row.return_application_id) {
    // 兼容单个ID字段
    applicationRef.value?.open('view', row.return_application_id)
  } else {
    message.warning('退库申请ID不存在')
  }
}

// 跳转到应付单详情页面
const handleBillPayableClick = (row: any, billNumber?: string, index?: number) => {
  // 如果传入了具体的单号和索引，使用对应的ID
  if (billNumber !== undefined && index !== undefined && row.bill_payable_ids && Array.isArray(row.bill_payable_ids)) {
    // 根据索引获取对应的ID
    const targetId = row.bill_payable_ids[index]
    if (targetId) {
      pushPage(`/billPayable/view/${targetId}`)
      return
    }
  }

  // 如果传入了具体的单号但没有索引，或者索引无效
  if (billNumber) {
    // 尝试在ID数组中查找匹配的单号（如果后端返回的数据结构支持）
    if (row.bill_payable_ids && Array.isArray(row.bill_payable_ids) && row.bill_payable_ids.length > 0) {
      // 暂时使用第一个ID
      pushPage(`/billPayable/view/${row.bill_payable_ids[0]}`)
      return
    }
  }

  // 如果没有传入具体单号，使用默认逻辑
  if (row.bill_payable_ids && Array.isArray(row.bill_payable_ids) && row.bill_payable_ids.length > 0) {
    // 如果有多个ID，使用第一个
    pushPage(`/billPayable/view/${row.bill_payable_ids[0]}`)
  } else if (row.bill_payable_id) {
    // 兼容单个ID字段
    pushPage(`/billPayable/view/${row.bill_payable_id}`)
  } else {
    message.warning('应付单ID不存在')
  }
}

const formRef = ref()
const tableRef = ref()
const pendingAmount = ref('0.00') // 待核对金额

const formArr = unBillFormArr

// 计算是否有选中的项目
const hasSelectedItems = computed(() => {
  const selectedItems = tableRef.value?.checkItemsArr || []
  return selectedItems.length > 0
})

// 计算待核对金额
const calculatePendingAmount = (data: any[]) => {
  if (!data || data.length === 0) {
    pendingAmount.value = '0.00'
    return
  }

  // 直接累加当页所有数据的采购应付金额，不做过滤
  const total = data.reduce((sum, item) => {
    const amount = Number(item.inv_amount) || 0
    return sum + amount
  }, 0)

  // 格式化为两位小数
  pendingAmount.value = total.roundNext(2)
}

// 获取未对账列表数据
const getUnreconciledList = async (params: any) => {
  try {
    const response = await GetBillCheckListByUnreconciled(params)
    return response
  } catch (error) {
    console.error('获取未对账列表失败:', error)
    message.error('获取未对账列表失败')
    return { data: { data: [], total: 0 } }
  }
}

// 数据加载完成后的回调
const onLoadData = (data: any[]) => {
  calculatePendingAmount(data)
}

const onSearch = () => {
  // 触发表格重新加载数据
  tableRef.value?.search()
}

const onReset = () => {
  // 重置后触发搜索
  tableRef.value?.search()
}

// 批量对账处理
const handleBatchVerification = () => {
  const selectedItems = tableRef.value?.checkItemsArr || []

  if (selectedItems.length === 0) {
    message.warning('请先勾选要生成账单的采购单')
    return
  }

  // 检查订单状态，只有"已完成"状态才能对账
  const invalidStatusItems: string[] = []
  // 检查是否可以创建账单，只有 is_create_bill 为 true 的才能对账
  const invalidBillItems: string[] = []

  selectedItems.forEach((item) => {
    // 检查订单状态，只有"已完成"状态才能对账
    if (item.purchase_order_status !== 2) {
      invalidStatusItems.push(item.purchase_number)
    }

    // 检查是否可以创建账单（检查所有状态的采购单）
    if (item.is_create_bill === false) {
      invalidBillItems.push(item.purchase_number)
    }
  })

  // 结算周期限制已由后端卡控，前端不再检查

  // 显示错误弹窗，不重复订单号，不自动复制
  const showErrorModal = (title: string, errorMessage: string) => {
    Modal.error({
      title,
      content: `${errorMessage}\n\n提示：请手动复制订单号`,
      okText: '确定',
      width: 500,
    })
  }

  // 如果有订单状态不是"已完成"的采购单，显示弹窗提示
  if (invalidStatusItems.length > 0) {
    const invalidStatusPurchaseNumbers = invalidStatusItems.join('、')
    const statusErrorMessage = `包含采购单${invalidStatusPurchaseNumbers}订单状态不是"已完成"，请重新选择`
    showErrorModal('订单状态限制', statusErrorMessage)
    return
  }

  // 如果有未生成订单的采购单，显示弹窗提示
  if (invalidBillItems.length > 0) {
    const invalidBillPurchaseNumbers = invalidBillItems.join('、')
    const billErrorMessage = `包含采购单${invalidBillPurchaseNumbers}未生成应付单，请重新选择`
    showErrorModal('订单生成限制', billErrorMessage)
    return
  }

  // 检查是否勾选了不同的供应商
  const uniqueSuppliers = new Set()
  selectedItems.forEach((item) => {
    if (item.supplier_id) {
      uniqueSuppliers.add(item.supplier_id)
    }
  })

  if (uniqueSuppliers.size > 1) {
    // 如果勾选了不同的供应商，显示提示并阻止继续
    const supplierNames = Array.from(uniqueSuppliers).map((id) => {
      const item = selectedItems.find((item) => item.supplier_id === id)
      return item?.supplier_name || '未知供应商'
    })

    Modal.warning({
      title: '供应商不一致',
      content: `您勾选了不同供应商的采购单：${supplierNames.join('、')}。\n\n请按供应商分别进行批量对账，以确保对账的准确性。`,
      okText: '确定',
      onOk() {
        // 用户确认后，弹窗关闭，不继续执行批量对账
      },
    })
    return
  }

  // 检查是否有历史单据未对账
  checkHistoricalUnverifiedBills(selectedItems)
}

// 检查是否有历史单据未对账
const checkHistoricalUnverifiedBills = (selectedItems: any[]) => {
  // 获取选中的供应商子公司ID
  const selectedCompanySupplierId = selectedItems[0]?.company_supplier_id

  if (!selectedCompanySupplierId) {
    // 如果没有子公司信息，直接执行批量对账
    executeBatchVerification(selectedItems)
    return
  }

  // 获取选中采购单中最早的采购时间
  const selectedPurchaseTimes = selectedItems
    .map((item) => new Date(item.purchase_time))
    .filter((time) => !Number.isNaN(time.getTime()))
    .sort((a, b) => a.getTime() - b.getTime())

  if (selectedPurchaseTimes.length === 0) {
    // 如果没有有效的采购时间，直接执行批量对账
    executeBatchVerification(selectedItems)
    return
  }

  const earliestSelectedTime = selectedPurchaseTimes[0]

  // 检查当前表格数据中是否有未勾选的历史单据
  const currentTableData = tableRef.value?.tableData || []
  const hasHistoricalUnverified = currentTableData.some((item: any) => {
    // 检查是否是同一供应商子公司
    if (item.company_supplier_id !== selectedCompanySupplierId) {
      return false
    }

    // 检查是否未被勾选
    const isSelected = selectedItems.some((selected) => selected.detail_id === item.detail_id)
    if (isSelected) {
      return false
    }

    // 检查采购时间是否早于选中的最早时间
    const itemPurchaseTime = new Date(item.purchase_time)
    if (Number.isNaN(itemPurchaseTime.getTime())) {
      return false
    }

    return itemPurchaseTime < earliestSelectedTime
  })

  if (hasHistoricalUnverified) {
    // 显示二次确认提示
    Modal.confirm({
      title: '二次确认',
      content: '有历史单据未对账，确认继续吗？',
      okText: '确认继续',
      cancelText: '取消',
      onOk() {
        executeBatchVerification(selectedItems)
      },
      onCancel() {
        // 用户取消，不做任何操作
      },
    })
  } else {
    // 没有历史单据未对账，直接执行批量对账
    executeBatchVerification(selectedItems)
  }
}

// 执行批量对账
const executeBatchVerification = (selectedItems: any[]) => {
  // 获取选中的detail_ids
  const detailIds = selectedItems.map((item) => {
    const detailId = item.detail_id
    // 确保返回数字ID
    return typeof detailId === 'string' ? parseInt(detailId) : detailId
  })

  // 获取供应商信息（假设所有选中项都是同一供应商）
  const supplierId = selectedItems[0]?.supplier_id
  const supplierName = selectedItems[0]?.supplier_name

  // 检测是否存在多个子公司
  const uniqueCompanySuppliers = new Set()
  selectedItems.forEach((item) => {
    if (item.company_supplier_id) {
      uniqueCompanySuppliers.add(item.company_supplier_id)
    }
  })

  let companySupplierId: string | null = null
  let companySupplierName: string | null = null

  if (uniqueCompanySuppliers.size === 0) {
    // 没有子公司
    companySupplierId = null
    companySupplierName = null
  } else if (uniqueCompanySuppliers.size === 1) {
    // 只有一个子公司
    companySupplierId = selectedItems[0]?.company_supplier_id || null
    companySupplierName = selectedItems[0]?.company_supplier_name || null
  } else {
    // 存在多个子公司
    companySupplierId = 'multiple'
    companySupplierName = '该供应商存在多个子公司'
  }

  // 构建批量对账数据
  const batchData = {
    detailIds,
    supplierId,
    supplierName,
    companySupplierId,
    companySupplierName,
    timestamp: Date.now(), // 添加时间戳确保数据唯一性
  }

  // 生成唯一的批量对账标识
  const batchId = Date.now().toString()

  // 将数据编码到URL参数中
  const encodedBatchData = encodeURIComponent(JSON.stringify(batchData))

  // 跳转到生成账单页面，通过路由参数传递数据
  // 使用 pushPage 替代 router.push，确保页面生命周期管理
  pushPage(`/billVerification/generateBill/${batchId}?batchData=${encodedBatchData}`, { source: true })
}

// 获取modal实例
const modal = inject('modal') as any

// 导出相关
const onExportTypeChange = ({ key }: { key: any }) => {
  const exportType = parseInt(key.toString())
  const count = tableRef.value?.checkItemsArr?.length || 0

  // 判断是否为带图的导出
  const isExportImg = exportType > 10
  const baseExportType = isExportImg ? exportType - 10 : exportType

  if (baseExportType === BillVerificationExportTypeEnum.根据勾选导出) {
    if (!count) {
      message.info('请勾选数据')
      return
    }
  }

  // 构建导出参数
  let ids: string[] = []
  let searchListParam: any = {}

  if (baseExportType === BillVerificationExportTypeEnum.根据勾选导出) {
    ids = tableRef.value?.checkItemsArr?.map((item: any) => item.id) || []
  } else if (baseExportType === BillVerificationExportTypeEnum.根据筛选结果导出) {
    // 使用 checkFormParams 处理表单参数
    searchListParam = checkFormParams({ formArr: formArr.value, obj: { status: 'unverified' } })
  }

  const params = {
    exportType: baseExportType,
    ids,
    searchListParam,
    type: 1, // 未对账列表
    isExportImg, // 是否带图
  }

  // 显示确认对话框
  modal.open({
    title: '是否确定导出未对账列表数据?',
    onOk() {
      return new Promise<void>((resolve) => {
        ExportBillCheckListByUnreconciled(params)
          .then(({ data }) => {
            eventBus.emit('downLoadId', {
              id: data as number,
              resolve,
              download: true,
            })
          })
          .catch((error) => {
            console.error('导出失败:', error)
            message.error('导出失败，请重试')
            resolve()
          })
      })
    },
  })
}

// 监听表格数据变化，重新计算待核对金额
watch(
  () => tableRef.value?.tableData,
  (newData) => {
    if (newData && Array.isArray(newData)) {
      calculatePendingAmount(newData)
    }
  },
  { deep: true, immediate: true },
)

// 暴露方法给父组件
defineExpose({
  onSearch,
  onReset,
})
</script>

<style scoped>
.text-red-500 {
  color: #ff4d4f;
}

.font-bold {
  font-weight: bold;
}

.text-base {
  font-size: 16px;
}
</style>
