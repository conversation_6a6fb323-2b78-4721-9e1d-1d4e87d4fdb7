<template>
  <vxe-table
    :border="true"
    ref="tableRef"
    size="mini"
    :row-config="{ keyField: keyField || '_X_ROW_KEY', isHover: true, height: 40 }"
    :custom-config="{ mode: 'popup' }"
    :data="data"
    :show-overflow="true"
    :show-header-overflow="true"
    :show-footer-overflow="true"
    :column-config="{ resizable: true }"
    class="tableBoxwidth"
    :checkbox-config="{ reserve: true }"
    @checkbox-all="selectChangeEvent"
    @checkbox-change="selectChangeEvent"
    min-height="0"
    stripe
    v-bind="$attrs"
    :row-style="rowStyle"
  >
    <vxe-column v-if="isCheckbox" type="checkbox" field="checkbox" width="45" fixed="left" align="center"></vxe-column>

    <slot name="column">
      <template v-for="i in tableKey" :key="i.field">
        <vxe-column v-bind="i">
          <template v-if="$slots[String(i.field)]" #default="attr">
            <slot :name="i.field" v-bind="attr" :item="i" />
          </template>
        </vxe-column>
      </template>
    </slot>
  </vxe-table>
</template>

<script setup lang="ts">
import { VxeColumnProps, VxeTableInstance } from 'vxe-table'

const tableRef = ref<VxeTableInstance>()

const props = withDefaults(
  defineProps<{
    data: any[]
    tableKey: VxeColumnProps[] | any[]
    keyField?: string
    isCheckbox?: boolean
    type?: number
  }>(),
  {
    data: () => [],
    tableKey: () => [],
    isCheckbox: false,
  },
)
const checkItemsArr = ref([] as any[])

const selectChangeEvent = () => {
  const $table = tableRef.value
  // 当前页选中的数据
  const currentSelectedRows = $table?.getCheckboxRecords() || []
  // 其他页选中的数据
  const otherSelectedRows = $table?.getCheckboxReserveRecords() || []
  checkItemsArr.value = [...currentSelectedRows, ...otherSelectedRows]
}

const clearCheckbox = () => {
  checkItemsArr.value = []
}
const colors = ref(['white', '#f0f4ff']) // 两种交替颜色

const rowStyle = ({ row }) => {
  const name = props.type == 1 ? row.sku_name : row.material_name
  const arr = Array.from(new Set(props.data.map((item) => item[props.type == 1 ? 'sku_name' : 'material_name'])))
  const index = arr.findIndex((item) => item === name) % 2

  return {
    backgroundColor: colors.value[index],
  }
}

defineExpose({ checkItemsArr, tableRef, clearCheckbox })
</script>
<style lang="scss" scoped>
.tableBox {
  position: relative;
  flex: 1;
  border-bottom: 1px solid #ddd;

  .toolbarBtn {
    position: absolute;
    right: 0;
    bottom: 100%;
    padding: 0;
    padding-bottom: 0.6em;
    margin-block: -5px;
  }

  .box {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;

    // overflow-y: scroll;
    .editbtn {
      color: #1890ff;
      cursor: pointer;
    }

    .movesea {
      margin-left: 20px;
      color: #1890ff;
      cursor: pointer;
    }
  }
}

.required {
  color: #f56c6c;
}

.required-text {
  margin-right: 4px;
}
</style>
