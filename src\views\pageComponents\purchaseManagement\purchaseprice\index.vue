<template>
  <div class="flex flex-col h-full main">
    <Form ref="formRef" v-model:form="formArr" :page-type="PageTypeEnum.PurchasePriceList" @search="tableRef?.search()" @setting="tableRef?.showTableSetting()">
      <template #header>
        <StatusTabs v-model:status="status" :options="purchaseAuditstatusOption" :count-map="labelStatusCountMap" @change="tableRef?.search()" />
      </template>
    </Form>
    <BaseTable ref="tableRef" :page-type="PageTypeEnum.PurchasePriceList" v-model:form="formArr" :get-list="GetPurchasePriceList" :isCheckbox="true" :form-format="formatData">
      <template #left-btn>
        <a-button v-if="btnPermission[81005] || btnPermission[81006]" @click="handleShowAuditModal(true)">批量审核通过</a-button>
        <a-button v-if="btnPermission[81005] || btnPermission[81006]" @click="handleShowAuditModal(false)">批量审核拒绝</a-button>
      </template>
      <template #right-btn>
        <a-button @click="handleShowAdd" type="primary" v-if="btnPermission[81002]">添加价目表</a-button>
      </template>
      <template #is_default="{ row }">
        <span>{{ row.is_default ? '是' : '否' }}</span>
      </template>
      <template #price_type="{ row }">
        <span>{{ PurchasePriceTypeMap[row.price_type] }}</span>
      </template>

      <template #fix_option="{ row, column }">
        <RightOperate :list="rightOperateList" :row="row" :column="column" />
      </template>
    </BaseTable>
    <price-drawer ref="priceDrawerRef" @query="tableRef?.search()" />
    <audit-modal ref="auditModalRef" @audit="handleSubmitAudit" />
    <adjust-price-drawer ref="adjustPriceDrawerRef" />
  </div>
</template>

<script setup lang="ts">
import { message } from 'ant-design-vue'

import { DetailTypeEnum } from '@/common/enum'
import { PurchasePriceTypeMap } from '@/common/map'
import { purchaseAuditstatusOption } from '@/common/options'

import AuditModal from '@/views/pageComponents/purchaseManagement/components/AuditModal.vue'
import AdjustPriceDrawer from '@/views/pageComponents/purchaseManagement/purchaseAdjustPrice/components/AdjustPriceDrawer.vue'

import PriceDrawer from './components/PriceDrawer.vue'

import { GetPurchasePriceList, BatchAuditPurchasePrice } from '@/servers/Purchaseprice'
import { GetPageSuppliersSelect, GetPageMRPSupplierCompanySelect } from '@/servers/BusinessCommon'

import { PageTypeEnum } from '@/enums/tableEnum'

const priceDrawerEl = useTemplateRef('priceDrawerRef')
const auditModalEl = useTemplateRef('auditModalRef')
const adjustPriceDrawerEl = useTemplateRef('adjustPriceDrawerRef')

const { btnPermission } = usePermission()
const { labelStatusCountMap, labelStatusRefresh } = useLabelStatus(PageTypeEnum.PurchasePriceList)

const tableRef = ref()
const formRef = ref()

const status = ref('')

const formArr = ref([
  { label: '单据编号', value: '', type: 'inputDlg', key: 'number' },
  { label: '价目表名称', value: '', type: 'input', key: 'name' },
  { label: '商品名称', value: '', type: 'input', key: 'material_name' },
  { label: '商品编号/聚水潭编号', value: '', type: 'inputDlg', key: 'k3_jst_key' },
  {
    label: '供应商',
    value: null,
    type: 'select-supplier',
    key: 'supplier_id',
    mode: 'single',
    linkage: 'company_supplier_id',
    api: GetPageSuppliersSelect,
  },
  {
    label: '供应商子公司',
    value: undefined,
    type: 'select-supplier',
    key: 'company_supplier_id',
    mode: 'single',
    api: GetPageMRPSupplierCompanySelect,
    apiParams: { is_contains_srs: true },
  },
  { label: '时间', value: null, type: 'range-picker', key: 'create_at', formKeys: ['create_time', 'end_time'], placeholder: ['创建开始时间', '创建结束时间'] },
])

useSearchForm(formArr)

// 添加价目表
const handleShowAdd = () => {
  priceDrawerEl.value?.open(DetailTypeEnum.ADD)
}
// 显示详情
const handleShowDetail = (id: number, type: DetailTypeEnum) => {
  priceDrawerEl.value?.open(type, id)
}
// 显示审核模态框
const handleShowAuditModal = (isPass: boolean) => {
  if (tableRef.value?.checkItemsArr.length === 0) {
    message.warning('请选择要审核的价目表')
    return
  }
  if (tableRef.value?.checkItemsArr.some((i) => ![20, 30].includes(i.audit_status))) {
    message.warning('请选择正确的需审核的价目表')
    return
  }
  let haveAlike = false
  let noPreAudit = false
  tableRef.value?.checkItemsArr.forEach((i) => {
    if (i.audit_status === 20 && !btnPermission.value[81005]) {
      noPreAudit = true
    }
    if (i.audit_status === 30 && !btnPermission.value[81006]) {
      noPreAudit = true
    }
    tableRef.value?.checkItemsArr.forEach((j) => {
      if (i.audit_status !== j.audit_status) {
        haveAlike = true
      }
    })
  })
  if (haveAlike) {
    message.warning('请选择相同状态的单据进行批量审核操作')
    return
  }
  if (noPreAudit) {
    message.warning('您没有该状态的审核权限')
    return
  }
  auditModalEl.value?.open(isPass)
}
// 提交审核
const handleSubmitAudit = async (form: any) => {
  try {
    await BatchAuditPurchasePrice({ ...form, ids: tableRef.value?.checkItemsArr.map((i) => i.id) })
    message.success('审核成功')
    auditModalEl.value?.close()
    tableRef.value?.search()
  } catch (_) {
    auditModalEl.value?.close()
  }
}
// 申请调价
const handleShowAdjustPrice = (id: number) => {
  adjustPriceDrawerEl.value?.applyOpen(id)
}

const formatData = (data: any) => {
  labelStatusRefresh()
  return {
    ...data,
    audit_status: status.value,
  }
}

const rightOperateList = ref([
  {
    label: '查看',
    show: 81001,
    onClick: ({ row }) => {
      handleShowDetail(row.id, DetailTypeEnum.VIEW)
    },
  },
  {
    label: '审核',
    show: ({ row }) => {
      return [20, 30].includes(row.audit_status) && ((row.audit_status === 20 && btnPermission.value[81005]) || (row.audit_status === 30 && btnPermission.value[81006]))
    },
    onClick: ({ row }) => {
      handleShowDetail(row.id, DetailTypeEnum.AUDIT)
    },
  },
  {
    label: '编辑',
    show: ({ row }) => {
      return [10, 95].includes(row.audit_status) && row.is_update && btnPermission.value[81003]
    },
    onClick: ({ row }) => {
      handleShowDetail(row.id, DetailTypeEnum.EDIT)
    },
  },
  {
    label: '申请调价',
    show: ({ row }) => {
      return row.audit_status === 90 && btnPermission.value[81004]
    },
    onClick: ({ row }) => {
      handleShowAdjustPrice(row.id)
    },
  },
])
</script>

<style scoped lang="scss"></style>
