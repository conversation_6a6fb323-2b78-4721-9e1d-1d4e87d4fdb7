import { type WatchHandle } from 'vue'

export const useSearchForm = (formArr) => {
  const formSelectMap = ref<Record<string, string | number>>({})
  const formWatcherMap = ref<Record<string, WatchHandle>>({})

  const getItemKey = ({ key, name, value }) => key || name || value

  const toCamelCase = (str: string): string => {
    return str.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase())
  }

  const itemMode = async (item, { key }, type?) => {
    const formatter = (f) => ({
      value: `${f.key || f.value}`,
      label: f.label || f.value,
    })
    const formData = item.api instanceof Function ? formArr.value.reduce((acc, cur) => ({ ...acc, [getItemKey(cur)]: cur.value }), {}) : {}
    const res = await item.api(formData)
    if (!type) {
      formSelectMap.value[`${toCamelCase(key)}`] = (res.data?.list || res.data || []).reduce(
        (acc, cur) => ({
          ...acc,
          [`${getItemKey(cur)}`]: cur.label || cur.value,
        }),
        {},
      )
    }
    item.selectArr = [...(item.selectInitArr || []), ...(res.data?.list || res.data || []).map(item?.formatter || formatter)]
  }

  const initForm = async () => {
    for (const item of formArr.value) {
      const itemKey = getItemKey(item)
      if (item.api && ['select'].includes(item.type)) {
        await itemMode(item, { key: itemKey })
      }
      if (item.api && ['cascader'].includes(item.type)) {
        await itemMode(item, { key: itemKey }, 'cascader')
      }
      if (item.linkage) {
        formWatcherMap.value[itemKey] = watch(
          () => item.value,
          () => {
            const target = formArr.value.find((f) => f.key === item.linkage)
            target.value = target.type === 'select-supplier' ? undefined : null
            if (!['select-supplier'].includes(target.type)) {
              itemMode(target, { key: getItemKey(target) }, 'change')
            }
          },
        )
      }
    }
  }

  onMounted(() => {
    initForm()
  })

  onBeforeUnmount(() => {
    for (const watcher of Object.values(formWatcherMap.value)) {
      watcher()
    }
  })

  return {
    formSelectMap,
    initForm,
  }
}
