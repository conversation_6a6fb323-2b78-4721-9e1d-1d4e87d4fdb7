<template>
  <a-drawer title="选择商品" width="85vw" :visible="visible" @close="handleClose" :maskClosable="false" destroyOnClose>
    <div class="flex h-full overflow-auto">
      <div class="h-full overflow-auto w65vw">
        <a-space class="mb-12">
          <!-- <a-input placeholder="商品编号/聚水潭编号" v-model:value="queryParams.product_sku_code" :maxlength="200" allowClear /> -->
          <a-input-group compact class="!flex">
            <a-input v-model:value="queryParams.product_sku_code" placeholder="商品编码/聚水潭编码" allow-clear @blur="queryParams.product_sku_code = `${queryParams.product_sku_code || ''}`.trim()" />
            <a-button @click="showInputDlg({ label: '商品编码' })" class="!flex items-center justify-center !w-40px">
              <template #icon>
                <span class="iconfont icon-chazhao_find text-14px c-#000"></span>
              </template>
            </a-button>
          </a-input-group>
          <a-input placeholder="商品名称" v-model:value="queryParams.product_name" :maxlength="200" allowClear />
          <a-input placeholder="供应商名称" v-model:value="queryParams.k3_supplier_number" :maxlength="200" allowClear />
          <a-button type="primary" @click="handleSearch">查询</a-button>
        </a-space>
        <vxe-table
          v-if="leftVisible"
          :border="true"
          ref="productTableRef"
          size="mini"
          :row-config="{ isHover: true, keyField: 'sku_id', height: 40 }"
          :custom-config="{ mode: 'popup' }"
          :data="data"
          :show-overflow="true"
          :show-header-overflow="true"
          :show-footer-overflow="true"
          :column-config="{ resizable: true }"
          class="tableBoxwidth"
          :checkbox-config="{ reserve: true }"
          min-height="0"
          stripe
          v-bind="$attrs"
          @checkbox-all="selectChangeEvent"
          @checkbox-change="selectChangeEvent"
        >
          <vxe-column type="checkbox" field="checkbox" width="50" fixed="left"></vxe-column>
          <slot name="column">
            <template v-for="i in tableKey" :key="i.field">
              <vxe-column v-bind="i">
                <template #default="{ row }">
                  <EasyImage v-if="i.field === 'image_url'" :src="row.image_url"></EasyImage>
                  <span v-else-if="i.field === 'category'">{{ row.product_category_name || productTypeMap[row.category] }}</span>
                  <span v-else>{{ row[i.field] }}</span>
                </template>
              </vxe-column>
            </template>
          </slot>
        </vxe-table>
        <div class="paginationBox">
          <div class="pagination">
            <a-pagination
              show-quick-jumper
              :total="total"
              show-size-changer
              v-model:current="queryParams.page"
              v-model:page-size="queryParams.pageSize"
              :page-size-options="['20', '30', '40', '50']"
              @change="handlePageChange"
              size="small"
            >
              <template #buildOptionText="props">
                <span>{{ props.value }}条/页</span>
              </template>
            </a-pagination>
          </div>
          <div class="totalBox">
            <div class="text">总数:</div>
            <div class="total">{{ total }}</div>
          </div>
        </div>
      </div>
      <div class="flex-1 overflow-y-auto p-16 text-black">
        <div class="flex mb-12 h30px line-height-30px h-30px">
          <div class="text-16px">已选{{ selectProductList.length }}个商品</div>
          <a-button class="ml-auto" type="primary" @click="cleanCheck">清空</a-button>
        </div>
        <vxe-table
          v-if="leftVisible"
          :border="true"
          ref="selectTableRef"
          size="mini"
          :row-config="{ isHover: true, keyField: 'sku_id', height: 40 }"
          :custom-config="{ mode: 'popup' }"
          :data="selectProductList"
          :show-overflow="true"
          :show-header-overflow="true"
          :show-footer-overflow="true"
          :column-config="{ resizable: true }"
          class="tableBoxwidth"
          :checkbox-config="{ reserve: true }"
          min-height="0"
          stripe
          v-bind="$attrs"
        >
          <vxe-column type="seq" width="50" fixed="left"></vxe-column>
          <slot name="column">
            <template v-for="i in selectTableKey" :key="i.field">
              <vxe-column v-bind="i">
                <template v-if="$slots[String(i.field)]" #default="attr">
                  <slot :name="i.field" v-bind="attr" :item="i" />
                </template>
              </vxe-column>
            </template>
          </slot>
        </vxe-table>
      </div>
    </div>
    <template #footer>
      <a-space>
        <a-button type="primary" @click="handleSelectProduct">确定</a-button>
        <a-button @click="handleClose">取消</a-button>
      </a-space>
    </template>
    <a-modal v-model:open="isShowInputDlg" title="批量输入" okText="确定" @ok="closeInputDlg" @cancel="isShowInputDlg = false" style="top: 200px">
      <a-form-item
        :label="inputLabel"
        :label-col="{
          style: {
            width: '130px',
          },
        }"
      >
        <a-textarea v-model:value="inputText" :placeholder="`多个${inputLabel}，以逗号分隔或每行一个${inputLabel}`" :rows="10" />
      </a-form-item>
      <template #footer>
        <a-button style="float: left" @click="inputText = ''">清空</a-button>
        <a-button @click="isShowInputDlg = false">取消</a-button>
        <a-button type="primary" @click="closeInputDlg">确认</a-button>
      </template>
    </a-modal>
  </a-drawer>
</template>

<script setup lang="ts">
import { VxeTableInstance } from 'vxe-table'

import { productTypeMap } from '@/common/map'

import { GetK3SkuInfo } from '@/servers/BusinessCommon'

const productTableEl = useTemplateRef<VxeTableInstance>('productTableRef')

const emits = defineEmits(['selectProduct'])
const visible = ref(false)
const leftVisible = ref(false)

const data = ref([])

const total = ref(0)

const selectProductList = ref<any[]>([])

const queryParams = ref<any>({
  page: 1,
  pageSize: 15,
})

const tableKey = ref([
  {
    field: 'category',
    title: '商品分类',
  },
  {
    field: 'image_url',
    title: '商品主图',
  },
  {
    field: 'sku_id',
    title: '商品编码',
  },
  {
    field: 'sku_name',
    title: '商品名称',
  },
  {
    field: 'type_specification',
    title: '规格型号',
  },
  {
    field: 'style_code',
    title: '款式编码',
  },
  {
    field: 'material_name',
    title: '材质',
  },
  {
    field: 'conversion_value',
    title: '换算值',
  },
  {
    field: 'conversion_formula',
    title: '换算公式',
  },
  {
    field: 'valuation_unit',
    title: '采购单位',
  },
  {
    field: 'packing_qty',
    title: '标准装箱数',
  },
  {
    field: 'jst_sku_id',
    title: '聚水潭编号',
  },
  {
    field: 'company_supplier_name',
    title: '默认供应商',
  },
])

const selectTableKey = ref([
  {
    field: 'sku_id',
    title: '商品编码',
  },
  {
    field: 'sku_name',
    title: '商品名称',
  },
])

// 打开
const open = () => {
  cleanCheck()
  visible.value = true
  leftVisible.value = true
}
// 关闭
const handleClose = () => {
  visible.value = false
  queryParams.value = {
    page: 1,
    pageSize: 15,
  }
}

// 批量输入
const isShowInputDlg = ref(false)
const inputLabel = ref(null)
const inputText = ref('')
const showInputDlg = (item) => {
  isShowInputDlg.value = true
  inputText.value = queryParams.value.product_sku_code
  inputLabel.value = item.label
}
const closeInputDlg = () => {
  queryParams.value.product_sku_code = dealStr(inputText.value)
  isShowInputDlg.value = false
}
const dealStr = (val) => {
  if (val) {
    val = val.replace(/\n/g, ',')
    val = val.replace(/，/g, ',')
    val = val.replace(/;/g, ',')
    val = val.replace(/；/g, ',')
    let arr = []
    let str = ''
    arr = val.split(',')
    arr.forEach((it: string) => {
      if (it) {
        str += `${it.trim()},`
      }
    })
    str = str.slice(0, -1)
    return str
  }
}

// 获取商品列表
const getProductList = async () => {
  for (const key in queryParams.value) {
    if (!queryParams.value[key]) {
      queryParams.value[key] = null
    }
  }
  const res = await GetK3SkuInfo(queryParams.value)
  data.value = res.data.list
  total.value = res.data.total
  setTimeout(() => {
    if (selectProductList.value.length == 0) {
      const $table = productTableEl.value
      $table?.clearCheckboxRow()
    }
  }, 20)
}
// 分页
const handlePageChange = (page: number, pageSize: number) => {
  queryParams.value.page = page
  queryParams.value.pageSize = pageSize
  getProductList()
}

// 查询
const handleSearch = () => {
  queryParams.value.page = 1
  getProductList()
}
// 选中事件
const selectChangeEvent = () => {
  const $table = productTableEl.value
  // 当前页选中的数据
  const currentSelectedRows = $table?.getCheckboxRecords() || []
  // 其他页选中的数据
  const otherSelectedRows = $table?.getCheckboxReserveRecords() || []
  selectProductList.value = [...currentSelectedRows, ...otherSelectedRows]
}

const cleanCheck = async () => {
  selectProductList.value = []
  leftVisible.value = false
  await getProductList()
  leftVisible.value = true
}
// 确定
const handleSelectProduct = () => {
  emits('selectProduct', selectProductList.value)
  handleClose()
}

defineExpose({
  open,
})
</script>

<style scoped lang="scss">
.paginationBox {
  display: flex;
  align-items: center;
  margin-top: 0.83rem;

  .totalBox {
    display: flex;
    align-items: flex-end;
    margin-left: 20px;
    color: #000;

    .text {
      margin-right: 8px;
      font-size: 14px;
    }

    .total {
      font-size: 16px;
      color: #1890ff;
    }
  }
}
</style>
