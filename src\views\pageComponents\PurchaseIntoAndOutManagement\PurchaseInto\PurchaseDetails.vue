<template>
  <a-drawer v-model:open="show" width="700px" title="查看采购入库单" @close="handleClose">
    <div v-if="loading" class="w-full flex h-full justify-center items-center">
      <a-spin tip="加载中..."></a-spin>
    </div>
    <div v-else-if="!loading && formData">
      <div class="drawer-title">基本信息</div>
      <div class="w-full">
        <a-form :model="formData" :label-col="{ style: { width: '100px' } }">
          <a-row>
            <a-col :span="12">
              <a-form-item label="入库单号">
                {{ formData.io_id }}
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="预约单号">
                {{ formData.merge_so_id }}
              </a-form-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="12">
              <a-form-item label="采购单号">
                {{ formData.purchase_order_numbers && formData.purchase_order_numbers.length > 0 ? formData.purchase_order_numbers.join('，') : '' }}
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="供应商子公司">
                {{ formData.supplier_name }}
              </a-form-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="12">
              <a-form-item label="采购日期">
                {{ formData.po_date }}
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="入库日期">
                {{ formData.io_date }}
              </a-form-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="12">
              <a-form-item label="相隔时间">
                {{ formData.interval_time }}
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="修改时间">
                {{ formData.modified }}
              </a-form-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="12">
              <a-form-item label="仓库名称">
                {{ formData.warehouse }}
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="进仓类型">
                {{ formData.type }}
              </a-form-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="12">
              <a-form-item label="仓库编号">
                {{ formData.wh_id }}
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="分仓编号">
                {{ formData.wms_co_id }}
              </a-form-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="12">
              <a-form-item label="物流单号">
                {{ formData.l_id }}
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="制单人">
                {{ formData.creator_name }}
              </a-form-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="12">
              <a-form-item label="备注">
                {{ formData.remark }}
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </div>
      <div class="drawer-title">明细信息</div>
      <div class="w-full">
        <a-table :pagination="false" :scroll="{ x: 1000 }" bordered :data-source="tableData" :columns="tableCoulmns"></a-table>
      </div>
    </div>
    <template #footer>
      <a-button @click="handleClose">关闭</a-button>
    </template>
  </a-drawer>
</template>

<script lang="ts" setup>
import { OaErpapiPurchaseDetail } from '@/servers/JushuitanOrder'

const emits = defineEmits(['close'])

const formData = ref()

const loading = ref(false)

const tableData = ref([])

const tableCoulmns = [
  { title: '入库子单号', dataIndex: 'ioi_id', key: 'ioi_id', width: 80 },
  { title: '商品编码', dataIndex: 'sku_id', key: 'sku_id', width: 120 },
  { title: '款式编码', dataIndex: 'i_id', key: 'i_id', width: 100 },
  { title: '商品名称', dataIndex: 'name', key: 'name', width: 100 },
  { title: '颜色及规格', dataIndex: 'properties_value', key: 'properties_value', width: 100 },
  { title: '金额', dataIndex: 'cost_amount', key: 'cost_amount', width: 100 },
  { title: '单价', dataIndex: 'cost_price', key: 'cost_price', width: 100 },
  { title: '入库数量', dataIndex: 'qty', key: 'qty', width: 100 },
  { title: '单位', dataIndex: 'unit', key: 'unit', width: 100 },
  { title: '明细备注', dataIndex: 'remark', key: 'remark', width: 150 },
]

const handleClose = () => {
  emits('close')
}
//
const show = ref(false)
const open = async (data) => {
  show.value = true
  loading.value = true
  const res = await OaErpapiPurchaseDetail({ id: data.id }).finally(() => {
    loading.value = false
  })
  formData.value = res.data
  tableData.value = res.data.purchase_items
}

defineExpose({
  open,
})
</script>

<style lang="scss" scoped>
//
</style>
