<template>
  <a-modal v-model:open="visible" title="批量修改备注" @ok="handleOk" @cancel="handleCancel">
    <a-form ref="formRef" :model="formState" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
      <a-form-item label="修改类型" name="type">
        <a-radio-group v-model:value="formState.type">
          <a-radio :value="1">覆盖</a-radio>
          <a-radio :value="2">追加</a-radio>
        </a-radio-group>
      </a-form-item>
      <a-form-item label="备注文案" name="remark" :rules="[{ required: true, message: '请输入备注' }]">
        <a-textarea v-model:value="formState.remark" placeholder="请输入备注文案" :rows="4" :maxlength="500" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { message } from 'ant-design-vue'

import { BatchUpdatePurchaseOrderRemark } from '@/servers/PurchaseManage'

const emit = defineEmits(['success'])

const visible = ref(false)
const formRef = ref()
const formState = reactive({
  type: 1,
  remark: '',
})
const purchaseOrderIds = ref<number[]>([])

const open = (ids: number[]) => {
  purchaseOrderIds.value = ids
  formState.type = 1
  formState.remark = ''
  if (formRef.value) {
    formRef.value.clearValidate()
  }
  visible.value = true
}

const handleOk = async () => {
  try {
    await formRef.value.validate()
    const params = {
      ids: purchaseOrderIds.value,
      ...formState,
    }
    const res = await BatchUpdatePurchaseOrderRemark(params)
    if (res.success) {
      message.success('批量修改备注成功')
      emit('success')
      handleCancel()
    } else {
      message.error(res.message)
    }
  } catch (_error) {
    // validation failed
  }
}

const handleCancel = () => {
  visible.value = false
}

defineExpose({ open })
</script>
