<template>
  <div class="main">
    <!-- 过滤器 -->
    <Form ref="formRef" v-model:form="formArr" @search="search" @setting="tableRef?.showTableSetting()" :pageType="PageTypeEnum.NoticeManage" />

    <BaseTable ref="tableRef" :isCheckbox="true" :pageType="PageTypeEnum.NoticeManage" :getList="getListFn" v-model:form="formArr" :auto-search="false">
      <template #left-btn>
        <a-button id="noticeBatchSend" v-if="btnPermission[61002]" @click="beforeBatchSend">批量发布</a-button>
      </template>
      <template #right-btn>
        <a-button id="noticeIncrease" v-if="btnPermission[61001]" type="primary" @click="increase">新建通知</a-button>
      </template>
      <template #notice_status="{ row }">
        <span>{{ row.notice_status == 1 ? '草稿' : row.notice_status == 2 ? '待发布' : row.notice_status == 3 ? '已发布' : '--' }}</span>
      </template>
      <template #scope="{ row }">
        <span>{{ row.scope || '--' }}</span>
      </template>
      <template #publish_at="{ row }">
        <span>{{ row.publish_at || '--' }}</span>
      </template>
      <template #plan_publish_time="{ row }">
        <span>{{ row.plan_publish_time || '--' }}</span>
      </template>
      <template #create_at="{ row }">
        <span>{{ row.create_at || '--' }}</span>
      </template>
      <template #update_at="{ row }">
        <span>{{ row.update_at || '--' }}</span>
      </template>
      <template #fix_option="{ row, column }">
        <RightOperate :list="rightOperateList" :row="row" :column="column" />
      </template>
    </BaseTable>

    <!-- 确认弹窗 -->
    <a-modal :zIndex="10000" v-model:open="modalData.isShow" :title="modalData.title">
      <div class="modalContent">{{ modalData.content }}</div>
      <template #footer>
        <a-button v-if="modalData.isConfirmBtn" :danger="modalData.okType === 'danger'" type="primary" style="margin-right: 10px" @click="modalData.okFn">{{ modalData.confirmBtnText }}</a-button>
        <a-button v-if="modalData.isCancelBtn" @click="modalData.isShow = false">取消</a-button>
      </template>
    </a-modal>
    <!-- 新增 编辑 -->
    <a-drawer
      v-model:open="drawerVisible"
      @afterVisibleChange="formRef?.clearValidate()"
      width="50vw"
      :title="drawerStatus === 1 ? '新建通知' : '编辑通知'"
      placement="right"
      :maskClosable="false"
      :footer-style="{ textAlign: 'left' }"
    >
      <LoadingOutlined v-show="drawerLoading" class="loadingIcon" />
      <a-form v-if="!drawerLoading" ref="formRef" :model="formData">
        <a-form-item
          label="通知标题"
          name="title"
          :rules="[
            { required: true, message: '请输入通知标题' },
            { max: 200, message: '输入内容不可超过200字符' },
          ]"
        >
          <a-input id="title" v-model:value="formData.title" placeholder="请输入通知标题"></a-input>
        </a-form-item>
        <a-form-item
          label="通知内容"
          name="content"
          :rules="[
            { required: true, message: '请输入通知内容' },
            { max: 5000, message: '输入内容不可超过5000字符' },
          ]"
        >
          <a-textarea id="content" v-model:value="formData.content" placeholder="请输入通知内容" :rows="4" />
        </a-form-item>
        <a-form-item label="通知类型" name="type" :rules="[{ required: true, message: '请选择通知类型' }]">
          <a-select id="type" v-model:value="formData.type" disabled class="w240" :options="noticeTypeOption" placeholder="请选择通知类型"></a-select>
        </a-form-item>
        <a-form-item label="通知范围" name="scope" :rules="[{ required: true, message: '请选择通知范围' }]">
          <a-select id="scope" v-model:value="formData.scope" disabled class="w240" :options="scopeOption" placeholder="请选择通知范围"></a-select>
        </a-form-item>
        <div style="display: flex; gap: 12px; align-items: center">
          <a-form-item label="发布计划" name="publish_type" :rules="[{ required: true, message: '请选择发布计划' }]">
            <a-radio-group id="publishType" @change="formData.plan_publish_time = null" v-model:value="formData.publish_type">
              <a-radio :id="`publishType${item.value}`" v-for="(item, index) in publishTypeOption" :key="index" :value="item.value">{{ item.label }}</a-radio>
            </a-radio-group>
          </a-form-item>
          <a-form-item label="计划发布时间" name="plan_publish_time">
            <a-date-picker
              @change="checkTime"
              :disabled-date="disabledDate"
              id="planPublishTime"
              :disabled="formData.publish_type == 1"
              :valueFormat="'YYYY-MM-DD HH:mm'"
              format="YYYY-MM-DD HH:mm"
              :show-time="{ format: 'HH:mm' }"
              v-model:value="formData.plan_publish_time"
              placeholder="请选择计划发布时间"
            />
          </a-form-item>
        </div>
        <a-form-item label="过期时间" name="exp_time">
          <a-date-picker
            :disabled-date="(current) => disabledDate2(current, formData.plan_publish_time)"
            id="expTime"
            class="w240"
            :valueFormat="'YYYY-MM-DD HH:mm'"
            format="YYYY-MM-DD HH:mm"
            :show-time="{ format: 'HH:mm' }"
            v-model:value="formData.exp_time"
            placeholder="请选择过期时间"
          />
        </a-form-item>
      </a-form>
      <template #footer>
        <a-button
          id="noticeFormComfirmAndSend"
          style="margin-right: 10px"
          type="primary"
          @click="
            () => {
              formData.save_type = 1
              drawerComfirm()
            }
          "
        >
          保存并发布
        </a-button>
        <a-button
          id="noticeFormComfirmAndSave"
          style="margin-right: 10px"
          @click="
            () => {
              formData.save_type = 2
              drawerComfirm()
            }
          "
        >
          保存草稿
        </a-button>
        <a-button id="noticeFormCancel" @click="drawerVisible = false">取消</a-button>
      </template>
    </a-drawer>
    <!-- 查看 -->
    <detail-drawer ref="detailDrawerRef" />
  </div>
</template>
<script lang="ts" setup>
import { LoadingOutlined } from '@ant-design/icons-vue'
import { onMounted, ref, reactive } from 'vue'
import { message } from 'ant-design-vue'

import Form from '@/components/Form.vue'
import BaseTable from '@/components/BaseTable.vue'
import RightOperate from '@/components/EasyTable/RightOperate.vue'
import eventBus from '@/utils/eventBus'

import DetailDrawer from './components/DetailDrawer.vue'

import { AddNotify, EditNotify, BatchPublishNotify, DeleteNotify, GetNotifyList, RevocationNotify, GetDropsSource, SaveAndPublish, GetNotice } from '@/servers/Notice'

import { PageTypeEnum } from '@/enums/tableEnum'

const tableRef = ref()
const formRef = ref()

const noticeTypeOption = ref([{ label: '系统通知', value: 1 }])
const scopeOption = ref([{ label: '全员', value: 1 }])
const publishTypeOption = ref([
  { label: '立即发布', value: 1 },
  { label: '定时发布', value: 2 },
])

const { btnPermission } = usePermission()

const getListFn = ref(GetNotifyList)

const rightOperateList = ref([
  {
    label: '查看',
    show: 61005,
    onClick: ({ row }) => {
      detail(row)
    },
  },
  {
    label: '撤回',
    show: ({ row }) => {
      return btnPermission.value[61002] && ['待发布', '已发布'].includes(row.status) && row.status !== '已发布'
    },
    onClick: ({ row }) => {
      noticeWithdraw(row)
    },
  },
  {
    label: '发布',
    show: ({ row }) => {
      return btnPermission.value[61002] && row.status === '草稿'
    },
    onClick: ({ row }) => {
      beforeSend(row)
    },
  },
  {
    label: '编辑',
    show: ({ row }) => {
      return btnPermission.value[61003] && !['待发布', '已发布'].includes(row.status)
    },
    onClick: ({ row }) => {
      edit(row)
    },
  },
  {
    label: '删除',
    show: ({ row }) => {
      return btnPermission.value[61004] && !['待发布', '已发布'].includes(row.status)
    },
    onClick: ({ row }) => {
      del(row)
    },
  },
])

const formArr: any = ref([
  {
    label: '搜索编码',
    value: null,
    type: 'input',
    key: 'no',
  },
  {
    label: '搜索标题',
    value: null,
    type: 'input',
    key: 'title',
  },
  {
    label: '搜索内容',
    value: null,
    type: 'input',
    key: 'content',
  },
  {
    label: '发布时间',
    value: null,
    type: 'range-picker',
    selectArr: [],
    key: 'create_at',
    formKeys: ['published_start', 'published_end'],
    placeholder: ['发布开始时间', '发布结束时间'],
  },
  {
    label: '创建时间',
    value: null,
    type: 'range-picker',
    selectArr: [],
    key: 'update_at',
    formKeys: ['created_start', 'created_end'],
    placeholder: ['创建开始时间', '创建结束时间'],
  },
  {
    label: '修改时间',
    value: null,
    type: 'range-picker',
    selectArr: [],
    key: 'update_at',
    formKeys: ['modified_start', 'modified_end'],
    placeholder: ['修改开始时间', '修改结束时间'],
  },
  {
    label: '状态',
    value: null,
    type: 'select',
    key: 'status',
    isShow: true,
    isQuicks: true,
  },
])
// 确认框数据
const modalData = reactive({
  isShow: false,
  isConfirmBtn: true,
  isCancelBtn: true,
  confirmBtnText: '确定',
  okType: 'primary',
  title: '',
  content: '',
  okFn: () => {
    modalData.isShow = false
  },
})
// 查看
const detailDrawerRef = ref<any>(null)
// 新增 编辑
const drawerLoading = ref(false)
const drawerVisible = ref(false)
const drawerStatus = ref(1)
const formData = ref<any>({
  id: '',
  save_type: null,
  title: null,
  content: null,
  type: null,
  scope: null,
  publish_type: null,
  plan_publish_time: null,
  exp_time: null,
})
const oldStatus = ref<any>(null)

const search = () => tableRef.value.search()

onMounted(() => {
  getSelect()
  search()
  initScreening()
})
onActivated(() => {})
const checkTime = () => {
  const time1 = formData.value.plan_publish_time
  const time2 = formData.value.exp_time
  if (time1 && time2) {
    if (time1 > time2) {
      message.error('计划发布时间不可在过期时间之后')
      formData.value.exp_time = null
    }
  }
}
const disabledDate = (current) => {
  if (!current) return false
  const currentDate = new Date(current)
  const now = new Date()
  now.setHours(0, 0, 0, 1)
  return currentDate.getTime() < now.getTime()
}

const disabledDate2 = (current, A = null) => {
  if (!current) return false
  console.log(A)
  // 如果 A 存在，则解析为 Date，否则使用当前时间
  const referenceDate = A ? new Date(A) : new Date()
  referenceDate.setHours(0, 0, 0, 1) // 重置为当天的开始时间

  const currentDate = new Date(current)
  return currentDate.getTime() < referenceDate.getTime()
}

const initScreening = () => {
  const obj = JSON.parse(localStorage.getItem('screeningObj') || '{}')
  if (obj.notice) {
    const arr: any[] = []
    obj.notice.forEach((x) => {
      formArr.value.forEach((y) => {
        if (x.key === y.key) {
          y.isShow = x.isShow
          arr.push(y)
        }
      })
    })
    formArr.value = arr
  } else {
    formArr.value.forEach((item) => {
      item.isShow = true
    })
  }
}

// 新增
const increase = () => {
  drawerLoading.value = false
  formData.value = {
    id: '',
    save_type: null,
    title: null,
    content: null,
    type: 1,
    scope: 1,
    publish_type: 1,
    plan_publish_time: null,
    exp_time: null,
  }
  drawerStatus.value = 1
  drawerVisible.value = true
}
// 详情
const detail = (item) => {
  detailDrawerRef.value.open(item.id)
}

// 获取下拉选项
const getSelect = () => {
  GetDropsSource().then((res) => {
    const statusItem = formArr.value.find((x) => x.key === 'status')
    if (statusItem) {
      statusItem.selectArr = Object.entries(res.data.status).map(([key, value]) => ({ label: value, value: key }))
    }
  })
}

// 编辑
const edit = (item) => {
  formData.value = { type: 1, scope: 1, id: item.id }
  getNotifyInfo(item.id)
  // formData.value = item
  oldStatus.value = Number(item.status)
  drawerLoading.value = false
  drawerStatus.value = 2
  drawerVisible.value = true
}

const getNotifyInfo = async (id) => {
  const res = await GetNotice(id)
  const { title, content, publish_type, plan_publish_time, exp_time } = res.data
  const newPublishType = publishTypeOption.value.find((x) => x.label === publish_type)?.value
  formData.value = { ...formData.value, title, content, publish_type: newPublishType, plan_publish_time, exp_time }
}
// 新增 编辑提交
const drawerComfirm = async () => {
  try {
    await formRef.value?.validateFields()
    let fn
    if (formData.value.save_type === 1) {
      fn = SaveAndPublish
    } else {
      fn = drawerStatus.value === 1 ? AddNotify : EditNotify
    }
    fn(formData.value).then(() => {
      if (formData.value.save_type === 1) {
        eventBus.emit('getNotice')
      }
      let msg = ''
      msg += `${drawerStatus.value === 1 ? '新增' : '修改'}`
      if (formData.value.save_type === 1 && formData.value.publish_type === 2) msg += `通知成功，按照计划时间：${formData.value.plan_publish_time}发布`
      else msg += '并保存成功'
      message.success(msg)
      drawerVisible.value = false
      search()
    })
  } catch (errorInfo) {
    console.log('Failed:', errorInfo)
  }
}
// 删除
const del = (item) => {
  modalData.title = '删除通知'
  modalData.content = `此操作不可恢复，确定要删除该通知吗？`
  modalData.confirmBtnText = '确定'
  modalData.isCancelBtn = true
  modalData.okType = 'danger'
  modalData.okFn = () => {
    DeleteNotify({ id: item.id })
      .then(() => {
        modalData.isShow = false
        message.success('删除成功')
        search()
      })
      .catch(() => {
        modalData.isShow = false
      })
  }
  modalData.isShow = true
}
// 批量发布
const batchSend = (obj, type) => {
  BatchPublishNotify(obj).then(() => {
    eventBus.emit('getNotice')
    message.success(`${type === 1 ? '' : '批量'}发布成功`)
    search()
    modalData.isShow = false
  })
}
// 批量发布
const beforeBatchSend = () => {
  if (tableRef.value.checkItemsArr.length === 0) {
    message.info('请勾选需要的通知')
  } else {
    const arr = tableRef.value.checkItemsArr.map((obj) => obj.id)
    batchSend({ ids: arr }, 2)
  }
}
const beforeSend = (item) => {
  batchSend({ ids: [item.id] }, 1)
}
const noticeWithdraw = (item) => {
  RevocationNotify({ id: item.id }).then(() => {
    message.success('撤回成功')
    search()
  })
}
</script>

<style lang="scss" scoped>
.main {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.modalContent {
  font-size: 14px;
  word-break: break-word;
  white-space: pre-wrap;
}

::v-deep(.ant-form-item) {
  align-items: flex-start;

  .ant-form-item-label {
    display: flex;
    justify-content: flex-end;
    width: 120px;
    min-width: 120px;
    margin-right: 30px;

    label {
      font-size: 15px;

      &::after {
        display: none !important;
      }
    }
  }
}

.w240 {
  width: 240px;
}

.description {
  padding-left: 20px;
  font-size: 12px;
  color: rgb(0 0 0 / 50%);
  white-space: nowrap;
}

.loadingIcon {
  font-size: 30px;
  color: #1890ff;
}

.TagFilter {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-top: 8px;

  .item {
    margin-right: 20px;
  }
}
</style>
