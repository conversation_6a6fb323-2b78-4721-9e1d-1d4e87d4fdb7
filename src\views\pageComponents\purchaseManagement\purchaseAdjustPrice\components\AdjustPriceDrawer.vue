<template>
  <a-drawer :maskClosable="false" title="采购调价表" :width="showAuditRecord ? '1750px' : '1400px'" :visible="visible" @close="handleClose" :bodyStyle="{ padding: '0' }" destroyOnClose>
    <template #extra>
      <a-button @click="handleShowAuditRecord" v-if="[DetailTypeEnum.VIEW, DetailTypeEnum.AUDIT].includes(viewType) && btnPermission[82008]">审核记录</a-button>
    </template>
    <div class="flex h-full">
      <div class="flex flex-col w-1400 p-16">
        <div class="drawer-title">基本信息</div>
        <a-form :label-col="{ style: { width: '140px' } }" :model="form" ref="formRef" :rules="rules">
          <a-row>
            <a-col :span="12">
              <a-form-item label="单据编号" name="number">
                <span v-if="[DetailTypeEnum.VIEW, DetailTypeEnum.AUDIT].includes(viewType)">{{ form.number }}</span>
                <a-input v-else v-model:value="form.number" :disabled="true" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="调价表名称" name="name">
                <span v-if="[DetailTypeEnum.VIEW, DetailTypeEnum.AUDIT].includes(viewType)">{{ form.name }}</span>
                <a-input v-else v-model:value="form.name" placeholder="请输入" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="12">
              <a-form-item label="价目表" name="price_list_id">
                <span v-if="[DetailTypeEnum.VIEW, DetailTypeEnum.AUDIT, DetailTypeEnum.EDIT].includes(viewType)">{{ form.price_list_name }}</span>
                <a-select v-else v-model:value="form.price_list_id" allowClear placeholder="请选择" :filter-option="false" @change="handleChangePriceList">
                  <a-select-option v-for="item in priceListOption" :key="item.value" :value="item.value">{{ item.label }}</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="调价原因" name="reason_type">
                <span v-if="[DetailTypeEnum.VIEW, DetailTypeEnum.AUDIT].includes(viewType)">{{ adjustPriceReasonMap[form.reason_type] }}</span>
                <a-select v-else v-model:value="form.reason_type" allowClear placeholder="请选择">
                  <a-select-option v-for="item in adjustPriceReasonOption" :key="item.value" :value="item.value">{{ item.label }}</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="12">
              <a-form-item label="币别" name="currency_type">
                <span v-if="[DetailTypeEnum.VIEW, DetailTypeEnum.AUDIT].includes(viewType)">{{ currencyMap[form.currency_type] }}</span>
                <a-select v-else v-model:value="form.currency_type" allowClear placeholder="请选择">
                  <a-select-option v-for="item in currencyOption" :key="item.value" :value="item.value">{{ item.label }}</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="定价员" name="rater_name">
                <span v-if="[DetailTypeEnum.VIEW, DetailTypeEnum.AUDIT].includes(viewType)">{{ form.rater_name }}</span>
                <a-select v-else v-model:value="form.rater_name" allowClear placeholder="请选择">
                  <a-select-option v-for="item in userInfoOption" :key="item.value" :value="item.value">{{ item.label }}</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="24">
              <a-form-item label="备注" name="remark" :label-col="{ style: { width: '140px' } }">
                <a-textarea v-model:value="form.remark" placeholder="" :auto-size="{ minRows: 3, maxRows: 5 }" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="12">
              <a-form-item label="是否含税" name="is_contain_tax">
                <span v-if="[DetailTypeEnum.VIEW, DetailTypeEnum.AUDIT].includes(viewType)">{{ form.is_contain_tax ? '是' : '否' }}</span>
                <a-radio-group v-else v-model:value="form.is_contain_tax" @change="setTaxRate">
                  <a-radio :value="true">是</a-radio>
                  <a-radio :value="false">否</a-radio>
                </a-radio-group>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="状态">
                <span>{{ form.status || '待发布' }}</span>
              </a-form-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="12">
              <a-form-item label="附件" name="attachments">
                <Upload v-model:value="form.attachments" v-if="[DetailTypeEnum.EDIT, DetailTypeEnum.ADD].includes(viewType)" :accept="'.jpg,.png,.pdf'" :module="UploadFileModuleEnum.PurchaseOrder" />
                <FileModal :list="form.files" v-else />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
        <div class="drawer-title">明细信息</div>
        <a-tabs v-model:activeKey="active" type="card">
          <a-tab-pane key="1" tab="单品价目"></a-tab-pane>
          <a-tab-pane key="2" tab="材质价目"></a-tab-pane>
        </a-tabs>
        <template v-if="active === '1'">
          <a-flex justify="space-between" class="mb-8" v-if="[DetailTypeEnum.EDIT, DetailTypeEnum.ADD].includes(viewType)">
            <a-space>
              <a-button @click="handleDownloadTemplate(false)">下载导入模板</a-button>
              <a-button @click="handleDownloadTemplate(true)">下载新商品导入模板</a-button>
              <a-upload :showUploadList="false" :before-upload="(file) => handleBeforeUpload(file, false)">
                <a-button>导入</a-button>
              </a-upload>
              <a-upload :showUploadList="false" :before-upload="(file) => handleBeforeUpload(file, true)">
                <a-button>导入新商品</a-button>
              </a-upload>
            </a-space>
            <a-button class="ml-auto mr-5px" @click="handleShowEditPriceList">修改价目表</a-button>
            <a-button type="primary" @click="handleAddProduct">新增商品</a-button>
          </a-flex>

          <a-flex justify="space-between" v-if="[DetailTypeEnum.VIEW, DetailTypeEnum.AUDIT].includes(viewType)">
            <a-space class="mb-12" :size="16" v-if="[DetailTypeEnum.VIEW, DetailTypeEnum.AUDIT].includes(viewType)">
              <a-input placeholder="商品编码/聚水潭编码" v-model:value="queryForm.sku_id" :maxlength="200" allowClear />
              <a-select placeholder="价格趋势" v-model:value="queryForm.price_trend" :options="trendOptions" allowClear />
              <a-button type="primary" @click="getProductList">查询</a-button>
            </a-space>
          </a-flex>

          <div class="flex flex-1 overflow-hidden" v-if="[DetailTypeEnum.EDIT, DetailTypeEnum.ADD].includes(viewType)">
            <div class="w200px mr-5px flex flex-col">
              <div class="bg-#EAECEE mb-10">
                <a-input-search v-model:value="productName" placeholder="根据商品编号搜索" @input="filterTag(productName)" />
              </div>
              <div class="flex-1 overflow-auto">
                <div
                  v-for="(item, index) in leftTagList"
                  :key="index"
                  class="h100px bg-#f2f2f2 mt-10px text-#3D3D3D pl-10px pr-10px overflow-hidden cursor-pointer"
                  :class="{ isClick: select_sku_id == item.sku_id, isRed: item.isRed }"
                  style="text-overflow: ellipsis; white-space: nowrap; border: 1px solid #c6e2ff"
                  @click="changeSkuid(item.sku_id)"
                >
                  <div class="h20px">{{ item.sku_id }}</div>
                  <div class="h20px">{{ item.jst_sku_id }}</div>
                  <div class="h20px">{{ item.sku_name }}</div>
                  <div class="h20px">
                    <span>{{ item.type_specification }}</span>
                    <span class="ml-10px">{{ item.valuation_unit }}</span>
                  </div>
                  <div class="h20px">{{ productTypeMap[item.category] }}</div>
                </div>
              </div>
            </div>
            <div class="flex flex-col overflow-hidden flex-1">
              <price-table ref="priceTableRef" :table-key="tableColumns" :data="filterProductList" :type="1" :rowHeight="50" :minHeight="230" height="100%" :showPagination="false">
                <template #data_status="{ row }">
                  <span>{{ PurchaseAdjustStatusMap[row.data_status] }}</span>
                </template>
                <template #process_type="{ row }" v-if="[DetailTypeEnum.EDIT, DetailTypeEnum.ADD].includes(viewType)">
                  <a-select
                    v-model:value="row.process_type"
                    :options="machiningTypeList"
                    @change="checkLeftList()"
                    class="w-full"
                    placeholder="请选择加工方式"
                    :disabled="row.data_status == 3 || row.data_status == 4"
                  />
                </template>
                <template #str_adjust_material_price="{ row }" v-if="[DetailTypeEnum.EDIT, DetailTypeEnum.ADD].includes(viewType)">
                  <div class="text-center">
                    <span class="miniNumber">{{ row.str_material_price }}</span>
                    <div>
                      <a-input-number
                        size="small"
                        v-model:value="row.str_adjust_material_price"
                        :controls="false"
                        :min="0.0"
                        :max="99999"
                        step="0.00000000"
                        :precision="8"
                        stringMode
                        :class="{ isRed: row.isPriceRed }"
                        @change="(checkLeftList(), setPrice())"
                        :disabled="row.data_status == 4"
                      />
                    </div>
                  </div>
                </template>
                <template #adjust_quantity_from="{ row }" v-if="[DetailTypeEnum.EDIT, DetailTypeEnum.ADD].includes(viewType)">
                  <div class="text-center">
                    <span class="miniNumber">{{ row.quantity_from }}</span>
                    <div>
                      <a-input-number
                        size="small"
                        v-model:value="row.adjust_quantity_from"
                        :controls="false"
                        :min="0"
                        :max="999999999"
                        :parser="(value) => value.replace('.', '')"
                        :class="{ isRed: row.isFromRed }"
                        @change="checkLeftList()"
                        :disabled="row.data_status == 4"
                      />
                    </div>
                  </div>
                </template>
                <template #adjust_quantity_to="{ row }" v-if="[DetailTypeEnum.EDIT, DetailTypeEnum.ADD].includes(viewType)">
                  <div class="text-center">
                    <span class="miniNumber">{{ row.quantity_to }}</span>
                    <div>
                      <a-input-number
                        size="small"
                        v-model:value="row.adjust_quantity_to"
                        :controls="false"
                        :min="0"
                        :max="999999999"
                        :parser="(value) => value.replace('.', '')"
                        :class="{ isRed: row.isToRed }"
                        @change="checkLeftList()"
                        :disabled="row.data_status == 4"
                      />
                    </div>
                  </div>
                </template>
                <template #str_adjust_process_fee="{ row }" v-if="[DetailTypeEnum.EDIT, DetailTypeEnum.ADD].includes(viewType)">
                  <div class="text-center">
                    <span class="miniNumber">{{ row.str_process_fee }}</span>
                    <div>
                      <a-input-number
                        size="small"
                        :controls="false"
                        v-model:value="row.str_adjust_process_fee"
                        :min="0.0"
                        :max="99999"
                        step="0.00000000"
                        :precision="8"
                        stringMode
                        @change="setPrice()"
                        :disabled="row.data_status == 4"
                      />
                    </div>
                  </div>
                </template>
                <template #adjust_tax_rate_id="{ row }" v-if="[DetailTypeEnum.EDIT, DetailTypeEnum.ADD].includes(viewType)">
                  <div class="text-center">
                    <span class="miniNumber">{{ taxRateOption.find((v) => v.value == row.tax_rate_id)?.label }}</span>
                    <div>
                      <a-select
                        v-model:value="row.adjust_tax_rate_id"
                        :options="taxRateOption"
                        @change="setPrice()"
                        class="w-full"
                        placeholder="请选择税率"
                        :disabled="!form.is_contain_tax || row.data_status == 4"
                      />
                    </div>
                  </div>
                </template>
                <template #str_adjust_price="{ row }" v-if="[DetailTypeEnum.EDIT, DetailTypeEnum.ADD].includes(viewType)">
                  <div class="text-center">
                    <span class="miniNumber">{{ row.str_price }}</span>
                    <span class="miniNumber text-red">{{ row.str_adjust_price }}</span>
                  </div>
                </template>
                <template #str_adjust_total_rate_price="{ row }" v-if="[DetailTypeEnum.EDIT, DetailTypeEnum.ADD].includes(viewType)">
                  <div class="text-center">
                    <span class="miniNumber">{{ row.str_total_rate_price }}</span>
                    <span class="miniNumber text-red">{{ row.str_adjust_total_rate_price }}</span>
                  </div>
                </template>
                <template #take_effect_time="{ row }" v-if="[DetailTypeEnum.EDIT, DetailTypeEnum.ADD].includes(viewType)">
                  <a-date-picker
                    v-if="[DetailTypeEnum.EDIT, DetailTypeEnum.ADD].includes(viewType)"
                    show-time
                    v-model:value="row.take_effect_time"
                    valueFormat="YYYY-MM-DD HH:mm:ss"
                    :disabled-date="disabledDate"
                    @change="checkTime(row, 'start')"
                    :disabled="row.data_status == 4"
                  />
                </template>
                <template #lose_efficacy_time="{ row }" v-if="[DetailTypeEnum.EDIT, DetailTypeEnum.ADD].includes(viewType)">
                  <a-date-picker
                    v-if="[DetailTypeEnum.EDIT, DetailTypeEnum.ADD].includes(viewType)"
                    show-time
                    v-model:value="row.lose_efficacy_time"
                    valueFormat="YYYY-MM-DD HH:mm:ss"
                    :disabled-date="(current) => disabledDate2(current, row.take_effect_time)"
                    @change="checkTime(row, 'end')"
                    :disabled="row.data_status == 4"
                  />
                </template>
                <template #cost_num_from="{ row }">
                  <span>{{ (row.adjust_quantity_from * row.conversion_value || 0).roundNext(5) }}</span>
                </template>
                <template #cost_num_to="{ row }">
                  <span>{{ (row.adjust_quantity_to * row.conversion_value || 0).roundNext(5) }}</span>
                </template>
                <template #operate="{ row }" v-if="[DetailTypeEnum.EDIT, DetailTypeEnum.ADD].includes(viewType)">
                  <a-button class="mr-5px" @click="addSetting(row)" v-if="row.data_status != 4">新增价格</a-button>
                  <a-button @click="delSetting(row)" v-if="row.data_status != 4">删除</a-button>
                </template>
              </price-table>
            </div>
          </div>

          <div class="flex flex-col overflow-hidden flex-1" v-else>
            <price-table
              ref="priceTableRef"
              :table-key="tableColumnsCache"
              :data="selectProductList"
              :type="1"
              :rowHeight="50"
              keyField="id"
              :showPagination="true"
              :currentPage="productPage"
              :pageSize="productPageSize"
              :total="productTotal"
              :minHeight="230"
              height="100%"
              @pageChange="handleProductPageChange"
              @pageSizeChange="handleProductPageSizeChange"
            >
              <template #category="{ row }">
                {{ productTypeMap[row.category] }}
              </template>
              <template #data_status="{ row }">
                <span>{{ PurchaseAdjustStatusMap[row.data_status] }}</span>
              </template>
              <template #image_url="{ row }">
                <EasyImage :src="row.image_url"></EasyImage>
              </template>
              <template #process_type="{ row }">
                <span>{{ machiningTypeList.find((v) => v.value == row.process_type)?.label }}</span>
              </template>
              <template #adjust_tax_rate_id="{ row }">
                <div class="text-center">
                  <span class="miniNumber">{{ taxRateOption.find((v) => v.value == row.tax_rate_id)?.label }}</span>
                  <span class="miniNumber text-red">{{ taxRateOption.find((v) => v.value == row.adjust_tax_rate_id)?.label }}</span>
                </div>
              </template>
              <template #price_trend="{ row }">
                <div v-if="row.price_trend == 1">
                  <arrow-up-outlined class="text-red" />
                  <span class="text-red">涨价</span>
                </div>
                <div v-if="row.price_trend == 2">
                  <span>价优</span>
                </div>
              </template>
              <template #adjust_quantity_from="{ row }">
                <div class="text-center">
                  <span class="miniNumber">{{ row.quantity_from }}</span>
                  <span class="miniNumber text-red">{{ row.adjust_quantity_from }}</span>
                </div>
              </template>
              <template #adjust_quantity_to="{ row }">
                <div class="text-center">
                  <span class="miniNumber">{{ row.quantity_to }}</span>
                  <span class="miniNumber text-red">{{ row.adjust_quantity_to }}</span>
                </div>
              </template>
              <template #str_adjust_material_price="{ row }">
                <div class="text-center">
                  <span class="miniNumber">{{ row.str_material_price }}</span>
                  <span class="miniNumber text-red">{{ row.str_adjust_material_price }}</span>
                </div>
              </template>
              <template #str_adjust_process_fee="{ row }">
                <div class="text-center">
                  <span class="miniNumber">{{ row.str_process_fee }}</span>
                  <span class="miniNumber text-red">{{ row.str_adjust_process_fee }}</span>
                </div>
              </template>
              <template #str_adjust_price="{ row }">
                <div class="text-center">
                  <span class="miniNumber">{{ row.str_price }}</span>
                  <span class="miniNumber text-red">{{ row.str_adjust_price }}</span>
                </div>
              </template>
              <template #str_adjust_total_rate_price="{ row }">
                <div class="text-center">
                  <span class="miniNumber">{{ row.str_total_rate_price }}</span>
                  <span class="miniNumber text-red">{{ row.str_adjust_total_rate_price }}</span>
                </div>
              </template>
              <template #cost_num_from="{ row }">
                <span>{{ (row.adjust_quantity_from * row.conversion_value || 0).roundNext(5) }}</span>
              </template>
              <template #cost_num_to="{ row }">
                <span>{{ (row.adjust_quantity_to * row.conversion_value || 0).roundNext(5) }}</span>
              </template>
            </price-table>
          </div>
        </template>

        <template v-if="active === '2'">
          <a-flex justify="space-between" class="mb-8" v-if="[DetailTypeEnum.EDIT, DetailTypeEnum.ADD].includes(viewType)">
            <a-space>
              <a-button @click="handleMaterialTemplate">下载导入模板</a-button>
              <a-upload :showUploadList="false" :before-upload="handleMaterialUpload">
                <a-button>导入</a-button>
              </a-upload>
            </a-space>
            <a-button class="ml-auto mr-5px" @click="handleShowMaterialPriceList">修改价目表</a-button>
            <a-button type="primary" @click="handleShowAddMaterial">新增材质</a-button>
          </a-flex>
          <a-flex justify="space-between" v-if="[DetailTypeEnum.VIEW, DetailTypeEnum.AUDIT].includes(viewType)">
            <a-space class="mb-12" :size="16" v-if="[DetailTypeEnum.VIEW, DetailTypeEnum.AUDIT].includes(viewType)">
              <a-input placeholder="材质名称" v-model:value="materialForm.material_name" :maxlength="200" />
              <a-select placeholder="价格趋势" v-model:value="materialForm.price_trend" :options="trendOptions" allowClear />
              <a-button type="primary" @click="getMaterialList">查询</a-button>
            </a-space>
          </a-flex>
          <div class="flex flex-1 overflow-hidden" v-if="[DetailTypeEnum.EDIT, DetailTypeEnum.ADD].includes(viewType)">
            <div class="w200px mr-5px">
              <div class="bg-#EAECEE">
                <a-input-search v-model:value="materialName" placeholder="根据材质名称搜索" @input="filterMaterialTag(materialName)" />
              </div>
              <div class="h-full overflow-auto">
                <template v-for="(item, index) in materialTagList" :key="index">
                  <div
                    class="h23px bg-#f2f2f2 mt-10px text-#3D3D3D pl-10px pr-10px overflow-hidden cursor-pointer"
                    :class="{ isClick: select_material_name == item.material_name, isRed: item.isRed }"
                    style="text-overflow: ellipsis; white-space: nowrap; border: 1px solid #c6e2ff"
                    @click="changeMaterialid(item.material_name)"
                  >
                    <div class="h23px">{{ item.material_name }}</div>
                  </div>
                </template>
              </div>
            </div>
            <price-table
              ref="materialTableRef"
              :table-key="materialColumns"
              :data="filterMaterialList"
              :isCheckbox="[DetailTypeEnum.VIEW, DetailTypeEnum.AUDIT].includes(viewType)"
              :type="2"
              :rowHeight="50"
              :showPagination="false"
            >
              <template #data_status="{ row }">
                <span>{{ PurchaseAdjustStatusMap[row.data_status] }}</span>
              </template>
              <template #process_type="{ row }" v-if="[DetailTypeEnum.EDIT, DetailTypeEnum.ADD].includes(viewType)">
                <a-select
                  v-model:value="row.process_type"
                  :options="machiningTypeList"
                  @change="checkLeftMaterialList()"
                  class="w-full"
                  placeholder="请选择加工方式"
                  :disabled="row.data_status == 3 || row.data_status == 4"
                />
              </template>
              <template #adjust_quantity_from="{ row }" v-if="[DetailTypeEnum.EDIT, DetailTypeEnum.ADD].includes(viewType)">
                <span class="miniNumber">{{ row.quantity_from }}</span>
                <div>
                  <a-input-number
                    size="small"
                    :controls="false"
                    v-model:value="row.adjust_quantity_from"
                    :min="0"
                    :max="99999999"
                    :parser="(value) => value.replace('.', '')"
                    :class="{ isRed: row.isFromRed }"
                    @change="checkLeftMaterialList()"
                    :disabled="row.data_status == 4"
                  />
                </div>
              </template>
              <template #adjust_quantity_to="{ row }" v-if="[DetailTypeEnum.EDIT, DetailTypeEnum.ADD].includes(viewType)">
                <span class="miniNumber">{{ row.quantity_to }}</span>
                <a-input-number
                  size="small"
                  :controls="false"
                  v-model:value="row.adjust_quantity_to"
                  :min="0"
                  :max="99999999"
                  :parser="(value) => value.replace('.', '')"
                  :class="{ isRed: row.isToRed }"
                  @change="checkLeftMaterialList()"
                  :disabled="row.data_status == 4"
                />
              </template>
              <template #str_adjust_material_price="{ row }" v-if="[DetailTypeEnum.EDIT, DetailTypeEnum.ADD].includes(viewType)">
                <span class="miniNumber">{{ row.str_material_price }}</span>
                <a-input-number
                  size="small"
                  :controls="false"
                  v-model:value="row.str_adjust_material_price"
                  :min="0.0"
                  :max="99999"
                  step="0.00000000"
                  :precision="8"
                  stringMode
                  :class="{ isRed: row.isPriceRed }"
                  @change="(checkLeftMaterialList(), setPrice())"
                  :disabled="row.data_status == 4"
                />
              </template>
              <template #str_adjust_process_fee="{ row }" v-if="[DetailTypeEnum.EDIT, DetailTypeEnum.ADD].includes(viewType)">
                <div class="text-center">
                  <span class="miniNumber">{{ row.str_process_fee }}</span>
                  <div>
                    <a-input-number
                      size="small"
                      :controls="false"
                      v-model:value="row.str_adjust_process_fee"
                      :min="0.0"
                      :max="99999"
                      step="0.00000000"
                      :precision="8"
                      stringMode
                      @change="setPrice()"
                      :disabled="row.data_status == 4"
                    />
                  </div>
                </div>
              </template>
              <template #adjust_tax_rate_id="{ row }" v-if="[DetailTypeEnum.EDIT, DetailTypeEnum.ADD].includes(viewType)">
                <div class="text-center">
                  <span class="miniNumber">{{ taxRateOption.find((v) => v.value == row.tax_rate_id)?.label }}</span>
                  <div>
                    <a-select
                      v-model:value="row.adjust_tax_rate_id"
                      :options="taxRateOption"
                      @change="setPrice()"
                      class="w-full"
                      placeholder="请选择税率"
                      :disabled="!form.is_contain_tax || row.data_status == 4"
                    />
                  </div>
                </div>
              </template>
              <template #str_adjust_price="{ row }" v-if="[DetailTypeEnum.EDIT, DetailTypeEnum.ADD].includes(viewType)">
                <div class="text-center">
                  <span class="miniNumber">{{ row.str_price }}</span>
                  <span class="miniNumber">{{ row.str_adjust_price }}</span>
                </div>
              </template>
              <template #str_adjust_total_rate_price="{ row }" v-if="[DetailTypeEnum.EDIT, DetailTypeEnum.ADD].includes(viewType)">
                <div class="text-center">
                  <span class="miniNumber">{{ row.str_total_rate_price }}</span>
                  <span class="miniNumber text-red">{{ row.str_adjust_total_rate_price }}</span>
                </div>
              </template>
              <template #take_effect_time="{ row }" v-if="[DetailTypeEnum.EDIT, DetailTypeEnum.ADD].includes(viewType)">
                <a-date-picker
                  show-time
                  v-model:value="row.take_effect_time"
                  valueFormat="YYYY-MM-DD HH:mm:ss"
                  :disabled-date="disabledDate"
                  @change="checkTime(row, 'start')"
                  :disabled="row.data_status == 4"
                />
              </template>
              <template #lose_efficacy_time="{ row }" v-if="[DetailTypeEnum.EDIT, DetailTypeEnum.ADD].includes(viewType)">
                <a-date-picker
                  show-time
                  v-model:value="row.lose_efficacy_time"
                  valueFormat="YYYY-MM-DD HH:mm:ss"
                  :disabled-date="(current) => disabledDate2(current, row.take_effect_time)"
                  @change="checkTime(row, 'end')"
                  :disabled="row.data_status == 4"
                />
              </template>
              <template #operate="{ row }" v-if="[DetailTypeEnum.EDIT, DetailTypeEnum.ADD].includes(viewType)">
                <a-button class="mr-5px" @click="addMaterialSetting(row)" v-if="row.data_status != 4">新增价格</a-button>
                <a-button @click="delMaterialSetting(row)" v-if="row.data_status != 4">删除</a-button>
              </template>
            </price-table>
          </div>
          <div class="flex flex-1 overflow-hidden flex-col" v-else>
            <price-table
              ref="materialTableRef"
              :table-key="materialColumnsCache"
              :data="selectMaterialList"
              :isCheckbox="[DetailTypeEnum.VIEW, DetailTypeEnum.AUDIT].includes(viewType)"
              :type="2"
              :rowHeight="50"
              :showPagination="true"
              :currentPage="materialPage"
              :pageSize="materialPageSize"
              :total="materialTotal"
              height="100%"
              :minHeight="240"
              @pageChange="handleMaterialPageChange"
              @pageSizeChange="handleMaterialPageSizeChange"
            >
              <template #category="{ row }">
                {{ productTypeMap[row.category] }}
              </template>
              <template #data_status="{ row }">
                <span>{{ PurchaseAdjustStatusMap[row.data_status] }}</span>
              </template>
              <template #image_url="{ row }">
                <EasyImage :src="row.image_url"></EasyImage>
              </template>
              <template #process_type="{ row }">
                <span>{{ machiningTypeList.find((v) => v.value == row.process_type)?.label }}</span>
              </template>
              <template #adjust_tax_rate_id="{ row }">
                <div class="text-center">
                  <span class="miniNumber">{{ taxRateOption.find((v) => v.value == row.tax_rate_id)?.label }}</span>
                  <span class="miniNumber text-red">{{ taxRateOption.find((v) => v.value == row.adjust_tax_rate_id)?.label }}</span>
                </div>
              </template>
              <template #price_trend="{ row }">
                <div v-if="row.price_trend == 1">
                  <arrow-up-outlined class="text-red" />
                  <span class="text-red">涨价</span>
                </div>
                <div v-if="row.price_trend == 2">
                  <span>价优</span>
                </div>
              </template>
              <template #adjust_quantity_from="{ row }">
                <div class="text-center">
                  <span class="miniNumber">{{ row.quantity_from }}</span>
                  <span class="miniNumber text-red">{{ row.adjust_quantity_from }}</span>
                </div>
              </template>
              <template #adjust_quantity_to="{ row }">
                <div class="text-center">
                  <span class="miniNumber">{{ row.quantity_to }}</span>
                  <span class="miniNumber text-red">{{ row.adjust_quantity_to }}</span>
                </div>
              </template>
              <template #str_adjust_material_price="{ row }">
                <div class="text-center">
                  <span class="miniNumber">{{ row.str_material_price }}</span>
                  <span class="miniNumber text-red">{{ row.str_adjust_material_price }}</span>
                </div>
              </template>
              <template #str_adjust_process_fee="{ row }">
                <div class="text-center">
                  <span class="miniNumber">{{ row.str_process_fee }}</span>
                  <span class="miniNumber text-red">{{ row.str_adjust_process_fee }}</span>
                </div>
              </template>
              <template #str_adjust_price="{ row }">
                <div class="text-center">
                  <span class="miniNumber">{{ row.str_price }}</span>
                  <span class="miniNumber text-red">{{ row.str_adjust_price }}</span>
                </div>
              </template>
              <template #str_adjust_total_rate_price="{ row }">
                <div class="text-center">
                  <span class="miniNumber">{{ row.str_total_rate_price }}</span>
                  <span class="miniNumber text-red">{{ row.str_adjust_total_rate_price }}</span>
                </div>
              </template>
            </price-table>
          </div>
        </template>
      </div>
      <div class="p-16 bg-#f2f2f2 overflow-auto w-350" v-if="showAuditRecord">
        <a-timeline class="ml-16" v-if="auditRecordList.length > 0">
          <a-timeline-item v-for="item in auditRecordList" :key="item.id">
            <div class="timeline-title">
              {{ item.audit_time }}
            </div>
            <div class="timeline-content">
              {{ item.message }}
            </div>
          </a-timeline-item>
        </a-timeline>
        <a-empty v-else class="c-#333" description="暂无审核记录" />
      </div>
    </div>
    <template #footer>
      <a-space :size="16" v-if="[DetailTypeEnum.EDIT, DetailTypeEnum.ADD].includes(viewType)">
        <a-button type="primary" @click="handleSubmitAudit(true)">提交审核</a-button>
        <a-button @click="handleSubmitAudit(false)">保存暂不提交</a-button>
        <a-button @click="handleClose">取消</a-button>
      </a-space>
      <a-space :size="16" v-else-if="viewType === DetailTypeEnum.AUDIT">
        <a-button type="primary" @click="handleAudit(true)">审核通过</a-button>
        <a-button @click="handleAudit(false)">审核拒绝</a-button>
        <a-button @click="handleClose">取消</a-button>
      </a-space>
      <a-space :size="16" v-else-if="viewType === DetailTypeEnum.VIEW">
        <a-button @click="handleClose">取消</a-button>
      </a-space>
    </template>
    <product-drawer ref="productDrawerRef" @selectProduct="(list) => handleSelectProduct(list, 2)"></product-drawer>
    <material-drawer ref="materialDrawerRef" @select-material="(list) => handleSelectMaterial(list, 2)" />
    <audit-modal ref="auditModalRef" @audit="handleFinishAudit" />
    <exist-price-table ref="existPriceTableRef" @selectProduct="(list) => selectExistProduct(list, 3)" />
    <material-price-table ref="materialPriceTableRef" @selectProduct="(list) => selectExistMaterial(list, 3)" />
  </a-drawer>
</template>

<script setup lang="ts">
import { message, Modal } from 'ant-design-vue'
import { ArrowUpOutlined } from '@ant-design/icons-vue'

import { DetailTypeEnum } from '@/common/enum'
import { download, isEmpty } from '@/utils'
import { adjustPriceReasonOption, currencyOption } from '@/common/options'
import { PurchaseAdjustStatusMap, currencyMap, adjustPriceReasonMap, productTypeMap } from '@/common/map'
import eventBus from '@/utils/eventBus'

import AuditModal from '@/views/pageComponents/purchaseManagement/components/AuditModal.vue'
import PriceTable from '@/views/pageComponents/purchaseManagement/purchaseAdjustPrice/components/PriceTable.vue'
import ExistPriceTable from '@/views/pageComponents/purchaseManagement/purchaseAdjustPrice/components/ExistPriceTable.vue'
import MaterialPriceTable from '@/views/pageComponents/purchaseManagement/purchaseAdjustPrice/components/ExisMaterialTable.vue'
import MaterialDrawer from '@/views/pageComponents/purchaseManagement/components/MaterialDrawer.vue'

import ProductDrawer from '../../components/ProductDrawer.vue'

import { GetAuditRecord, GetPLMMachiningType, GetTaxRateSelect } from '@/servers/BusinessCommon'
import {
  GetPurchaseAdjustPrice,
  GetPurchaseAdjustPriceDetail,
  DownloadImportTemplate,
  BatchAuditPurchaseAdjustPrice,
  AddPurchaseAdjustPrice,
  UpdatePurchaseAdjustPrice,
  GetPurchasePriceSelects,
  PurchaseAdjustImportK3SkuInfo,
  PurchaseAdjustImportNewK3SkuInfo,
  PurchaseAdjustImportMaterialInfo,
  GetPurchaseAdjustPriceMaterialDetail,
} from '@/servers/PurchaseAdjustPrice'
import { DownloadImportTemplate as DownloadImportTemplate2 } from '@/servers/Purchaseprice'

const emits = defineEmits(['query'])
const active = ref('1')
const { btnPermission } = usePermission()
const priceTableRef = ref()
const materialTableRef = ref()
const productDrawerRef = ref()
const materialDrawerRef = ref()
const auditModalRef = ref()
const formRef = ref()
const existPriceTableRef = ref()
const materialPriceTableRef = ref()

const showAuditRecord = ref(false)
const visible = ref(false)

const viewType = ref<DetailTypeEnum>(DetailTypeEnum.VIEW)

const form = ref<any>({
  currency_type: 1,
  is_contain_tax: false,
  attachments: [],
  files: [],
})
const queryForm = ref({
  sku_id: '',
  price_trend: undefined,
})

const materialForm = ref({
  material_name: '',
  price_trend: undefined,
})

const trendOptions = ref<any[]>([
  { label: '涨价', value: 1 },
  { label: '价优', value: 2 },
])

const selectProductList = ref<any[]>([])
const selectMaterialList = ref<any[]>([])
const selectProductCache = ref<any[]>([])
const selectMaterialCache = ref<any[]>([])
const leftTagListCache = ref<any[]>([])
const materialTagListCache = ref<any[]>([])
const leftTagList = ref<any[]>([])
const materialTagList = ref<any[]>([])
const select_sku_id = ref()
const select_material_name = ref()

// 单品价目分页相关
const productPage = ref(1)
const productPageSize = ref(20)
const productTotal = ref(0)

// 材质价目分页相关
const materialPage = ref(1)
const materialPageSize = ref(20)
const materialTotal = ref(0)

const filterProductList = computed(() =>
  selectProductList.value
    .filter((v) => v.sku_id == select_sku_id.value)
    .sort((a, b) => {
      if (a.data_status === 4 && b.data_status !== 4) {
        return 1 // a 放在后面
      }
      if (a.data_status !== 4 && b.data_status === 4) {
        return -1 // b 放在后面
      }
      return 0 // 保持原顺序
    }),
)
const filterMaterialList = computed(() =>
  selectMaterialList.value
    .filter((v) => v.material_name == select_material_name.value)
    .sort((a, b) => {
      if (a.data_status === 4 && b.data_status !== 4) {
        return 1 // a 放在后面
      }
      if (a.data_status !== 4 && b.data_status === 4) {
        return -1 // b 放在后面
      }
      return 0 // 保持原顺序
    }),
)
const auditRecordList = ref<any[]>([])
const productName = ref('')
const materialName = ref('')
const resArr = ref<any[]>([])
// 价目表选项
const priceListOption = ref<any[]>([])

const userData = JSON.parse(localStorage.getItem('userData') || '')
const userInfoOption = ref<any[]>([])

const rules = ref({
  name: [
    { required: true, message: '请输入调价表名称' },
    { max: 100, message: '调价表名称最多100个字符' },
  ],
  price_list_id: [{ required: true, message: '请选择价目表' }],
  reason_type: [{ required: true, message: '请选择调价原因' }],
  currency_type: [{ required: true, message: '请选择币别' }],
  rater_name: [{ required: true, message: '请选择定价员' }],
  is_contain_tax: [{ required: true, message: '请选择是否含税' }],
})

const tableColumns = ref([
  { title: '类型', field: 'data_status', width: 80 },
  { title: '加工方式', field: 'process_type', width: 120 },
  { title: '采购单位', field: 'valuation_unit', width: 120 },
  { title: '采购数量（从）', field: 'adjust_quantity_from', width: 120 },
  { title: '采购数量（至）', field: 'adjust_quantity_to', width: 120 },
  { title: '换算值', field: 'conversion_value', width: 120 },
  {
    title: '成本数量（从）',
    field: 'cost_num_from',
    width: 120,
  },
  {
    title: '成本数量（至）',
    field: 'cost_num_to',
    width: 120,
  },
  { title: '材料单价', field: 'str_adjust_material_price', width: 120 },
  { title: '加工费', field: 'str_adjust_process_fee', width: 120 },
  { title: '合计单价', field: 'str_adjust_price', width: 120 },
  { title: '税率', field: 'adjust_tax_rate_id', width: 120 },
  { title: '合计含税单价', field: 'str_adjust_total_rate_price', width: 120 },
  { title: '生效时间', field: 'take_effect_time', width: 140 },
  { title: '失效时间', field: 'lose_efficacy_time', width: 140 },
  { title: '操作', field: 'operate', width: 160, fixed: 'right' },
])
const tableColumnsCache = ref([
  { title: '商品主图', field: 'image_url', width: 80 },
  { title: '商品编码', field: 'sku_id', width: 120 },
  { title: '聚水潭编码', field: 'jst_sku_id', width: 120 },
  { title: '商品名称', field: 'sku_name', width: 120 },
  { title: '规格型号', field: 'type_specification', width: 120 },
  { title: '类型', field: 'data_status', width: 80 },
  { title: '加工方式', field: 'process_type', width: 120 },
  { title: '采购单位', field: 'valuation_unit', width: 120 },
  { title: '商品分类', field: 'category', width: 120 },
  { title: '采购数量（从）', field: 'adjust_quantity_from', width: 120 },
  { title: '采购数量（至）', field: 'adjust_quantity_to', width: 120 },
  { title: '换算值', field: 'conversion_value', width: 120 },
  {
    title: '成本数量（从）',
    field: 'cost_num_from',
    width: 120,
  },
  {
    title: '成本数量（至）',
    field: 'cost_num_to',
    width: 120,
  },
  { title: '材料单价', field: 'str_adjust_material_price', width: 120 },
  { title: '加工费', field: 'str_adjust_process_fee', width: 120 },
  { title: '合计单价', field: 'str_adjust_price', width: 120 },
  { title: '税率', field: 'adjust_tax_rate_id', width: 120 },
  { title: '合计含税单价', field: 'str_adjust_total_rate_price', width: 120 },
  { title: '价格趋势', field: 'price_trend', width: 120 },
  { title: '生效时间', field: 'take_effect_time', width: 140 },
  { title: '失效时间', field: 'lose_efficacy_time', width: 140 },
])

// 材质价目
const materialColumns = ref([
  { title: '类型', field: 'data_status', width: 80 },
  { title: '加工方式', field: 'process_type', width: 120 },
  { title: '数量（从）', field: 'adjust_quantity_from', width: 120 },
  { title: '数量（至）', field: 'adjust_quantity_to', width: 120 },
  { title: '材料单价', field: 'str_adjust_material_price', width: 120 },
  { title: '加工费', field: 'str_adjust_process_fee', width: 120 },
  { title: '合计单价', field: 'str_adjust_price', width: 120 },
  { title: '税率', field: 'adjust_tax_rate_id', width: 120 },
  { title: '合计含税单价', field: 'str_adjust_total_rate_price', width: 120 },
  { title: '生效时间', field: 'take_effect_time', width: 140 },
  { title: '失效时间', field: 'lose_efficacy_time', width: 140 },
  { title: '操作', field: 'operate', width: 160 },
])

const materialColumnsCache = ref([
  { title: '类型', field: 'data_status', width: 80 },
  { title: '加工方式', field: 'process_type', width: 120 },
  { title: '材质', field: 'material_name', width: 120 },
  { title: '数量（从）', field: 'adjust_quantity_from', width: 120 },
  { title: '数量（至）', field: 'adjust_quantity_to', width: 120 },
  { title: '材料单价', field: 'str_adjust_material_price', width: 120 },
  { title: '加工费', field: 'str_adjust_process_fee', width: 120 },
  { title: '合计单价', field: 'str_adjust_price', width: 120 },
  { title: '税率', field: 'adjust_tax_rate_id', width: 120 },
  { title: '合计含税单价', field: 'str_adjust_total_rate_price', width: 120 },
  { title: '价格趋势', field: 'price_trend', width: 120 },
  { title: '生效时间', field: 'take_effect_time', width: 140 },
  { title: '失效时间', field: 'lose_efficacy_time', width: 140 },
])
const auditStatus = ref(0)
const saveId = ref()
const machiningTypeList = ref<any>([])

const taxRateOption = ref<any>([])
const disabledDate = (current) => {
  if (!current) return false
  const currentDate = new Date(current)
  const now = new Date()
  now.setHours(0, 0, 0, 1)
  return currentDate.getTime() < now.getTime()
}

const disabledDate2 = (current, A = null) => {
  if (!current) return false
  // 如果 A 存在，则解析为 Date，否则使用当前时间
  const referenceDate = A ? new Date(A) : new Date()
  referenceDate.setHours(0, 0, 0, 1) // 重置为当天的开始时间

  const currentDate = new Date(current)
  return currentDate.getTime() < referenceDate.getTime()
}

const checkTime = (row, type) => {
  const time1 = row.take_effect_time
  const time2 = row.lose_efficacy_time
  if (time1 && time2) {
    if (time1 > time2) {
      message.error('生效时间不可在失效时间之后')
      if (type == 'start') {
        row.take_effect_time = null
      } else {
        row.lose_efficacy_time = null
      }
    }
  }
}

// 去重
const uniqueData = (data, key) => {
  const uniqueArr = data.filter((item, index) => data.findIndex((i) => i[key] === item[key]) === index)
  return uniqueArr
}

watch(
  () => selectProductList.value.length,
  () => {
    productName.value = ''
    leftTagListCache.value = uniqueData(selectProductList.value, 'sku_id')
    checkLeftList()
  },
  { deep: true },
)

watch(
  () => leftTagListCache.value.length,
  () => {
    filterTag(productName.value)
  },
)

watch(
  () => selectMaterialList.value.length,
  () => {
    materialName.value = ''
    materialTagListCache.value = uniqueData(selectMaterialList.value, 'material_id')
    checkLeftMaterialList()
  },
  { deep: true },
)

watch(
  () => materialTagListCache.value.length,
  () => {
    if (materialTagListCache.value?.length > 0) {
      filterMaterialTag(materialName.value)
    } else {
      materialTagList.value = []
    }
  },
)

const filterTag = (value) => {
  leftTagList.value = leftTagListCache.value.filter((item) => item.sku_id.indexOf(value) >= 0)
  if (leftTagList.value.length > 0) {
    select_sku_id.value = leftTagList.value[0].sku_id
  }
}

const filterMaterialTag = (value) => {
  materialTagList.value = materialTagListCache.value.filter((item) => item.material_name.indexOf(value) >= 0)
  if (materialTagList.value.length > 0) {
    select_material_name.value = materialTagList.value[0].material_name
  }
}
// 打开
const open = async (type: DetailTypeEnum, id?: number, status?: string, newStatus?: number) => {
  active.value = '1'
  saveId.value = id
  auditStatus.value = newStatus || 0
  formRef.value?.resetFields()
  userInfoOption.value = [{ label: userData.display_name, value: userData.display_name }]
  if (type === DetailTypeEnum.ADD) {
    form.value.rater_name = userInfoOption.value[0].value
  }

  await getPriceList()
  await getMachiningType()
  await getTaxRateList()
  visible.value = true
  viewType.value = type
  if (typeof id === 'number') {
    await getPriceDetail(id)
  } else {
    // 如果是新增模式，默认调用单品价目接口
    if (active.value === '1') {
      await getProductListByPage(1)
    }
  }
  if (status) {
    form.value.status = status
  }
  // 显示审核记录
  if ([DetailTypeEnum.VIEW, DetailTypeEnum.AUDIT].includes(viewType.value) && btnPermission.value[82008]) {
    showAuditRecord.value = localStorage.getItem('showAuditRecord') === '1'
    if (showAuditRecord.value) {
      getAuditRecord()
    }
  } else {
    showAuditRecord.value = false
  }
}
// 申请打开
const applyOpen = async (id: number, list1?: any[], list2?: any[]) => {
  userInfoOption.value = [{ label: userData.display_name, value: userData.display_name }]
  form.value.rater_name = userInfoOption.value[0].value
  await getPriceList()
  await getMachiningType()
  await getTaxRateList()
  form.value.price_list_id = id
  visible.value = true
  viewType.value = DetailTypeEnum.ADD
  selectProductList.value = list1 || []
  selectMaterialList.value = list2 || []
  form.value.is_contain_tax = [...selectProductList.value, ...selectMaterialList.value].some((v) => v.adjust_tax_rate_id > 0)
}
// 获取价目表详情
const getPriceDetail = async (id: number) => {
  const infoRes = await GetPurchaseAdjustPrice({ id })
  if (infoRes.data?.price_list_id) {
    infoRes.data.price_list_name = priceListOption.value.find((item) => item.value === infoRes.data.price_list_id)?.label
  }

  if (infoRes.data?.rater_name) {
    userInfoOption.value = [{ label: infoRes.data.rater_name, value: infoRes.data.rater_name }]
  }

  form.value = infoRes.data
  form.value.attachments = form.value.files
  form.value.id = id

  // 根据当前active tab加载对应数据
  if (active.value === '1') {
    selectProductCache.value = await getProductListByPage(1)
  } else {
    selectMaterialCache.value = await getMaterialListByPage(1)
  }

  form.value.is_contain_tax = [...selectProductList.value, ...selectMaterialList.value].some((v) => v.adjust_tax_rate_id > 0)
}

// 监听active变化，切换时重新加载数据
watch(active, async (newActive) => {
  if (form.value.id) {
    if (newActive === '1') {
      selectProductCache.value = await getProductListByPage(1)
    } else {
      selectMaterialCache.value = await getMaterialListByPage(1)
    }
  }
})

// 处理单品价目分页
const handleProductPageChange = async (page: number) => {
  await getProductListByPage(page)
}

// 处理单品价目分页大小变化
const handleProductPageSizeChange = async (current: number, size: number) => {
  productPageSize.value = size
  await getProductListByPage(1)
}

// 处理材质价目分页
const handleMaterialPageChange = async (page: number) => {
  await getMaterialListByPage(page)
}

// 处理材质价目分页大小变化
const handleMaterialPageSizeChange = async (current: number, size: number) => {
  materialPageSize.value = size
  await getMaterialListByPage(1)
}

const getProductList = async () => {
  if ([DetailTypeEnum.VIEW, DetailTypeEnum.AUDIT].includes(viewType.value)) {
    // 在查看/审核模式下，需要重新调用API获取数据
    const { data } = await GetPurchaseAdjustPriceDetail({
      id: form.value.id,
      page: 1,
      pageSize: productPageSize.value,
      sortField: '',
      sortType: '',
      ...queryForm.value,
    })
    if (data.list) {
      productTotal.value = data.total
      productPage.value = 1
      selectProductList.value = data.list.map((v, index) => ({
        ...v,
        iid: index + v.sku_id,
      }))
    }
    return
  }
  let arr = selectProductCache.value
  if (queryForm.value.price_trend) {
    arr = arr.filter((v) => v.price_trend == queryForm.value.price_trend)
  }
  if (queryForm.value.sku_id) {
    arr = arr.filter((v) => v.sku_id.indexOf(queryForm.value.sku_id) > -1 || v.jst_sku_id.indexOf(queryForm.value.sku_id) > -1)
  }
  selectProductList.value = arr
}

const getMaterialList = async () => {
  if ([DetailTypeEnum.VIEW, DetailTypeEnum.AUDIT].includes(viewType.value)) {
    // 在查看/审核模式下，需要重新调用API获取数据
    const { data } = await GetPurchaseAdjustPriceMaterialDetail({
      id: form.value.id,
      page: 1,
      pageSize: materialPageSize.value,
      sortField: '',
      sortType: '',
      ...materialForm.value,
    })
    if (data.list) {
      materialPage.value = 1
      materialTotal.value = data.total
      selectMaterialList.value = data.list.map((v, index) => ({
        ...v,
        iid: index + v.material_id,
      }))
    }
    return
  }
  let arr = selectMaterialCache.value
  if (materialForm.value.price_trend) {
    arr = arr.filter((v) => v.price_trend == materialForm.value.price_trend)
  }
  selectMaterialList.value = arr.filter((v) => v.material_name.indexOf(materialForm.value.material_name) > -1)
}

// 下载(新商品/普通)导入模板
const handleDownloadTemplate = (isNew: boolean) => {
  const api = isNew ? DownloadImportTemplate2 : DownloadImportTemplate
  api(false).then((res) => {
    download(res, `采购调价表${isNew ? '新商品' : '单品'}导入模板.xlsx`)
  })
}

const handleMaterialTemplate = () => {
  DownloadImportTemplate(true).then((res) => {
    download(res, '采购调价表材质导入模板.xlsx')
  })
}

// 显示修改价目表
const handleShowEditPriceList = () => {
  if (form.value?.price_list_id) {
    existPriceTableRef.value?.open(form.value.price_list_id, [])
  } else {
    message.warning('请先选择价目表')
  }
}

const handleShowMaterialPriceList = () => {
  if (form.value?.price_list_id) {
    materialPriceTableRef.value?.open(form.value.price_list_id, [])
  } else {
    message.warning('请先选择价目表')
  }
}

// 显示审核记录
const handleShowAuditRecord = () => {
  showAuditRecord.value = !showAuditRecord.value
  localStorage.setItem('showAuditRecord', showAuditRecord.value ? '1' : '0')
  if (showAuditRecord.value) {
    getAuditRecord()
  }
}
// 获取审核记录
const getAuditRecord = async () => {
  const res = await GetAuditRecord({ id: form.value.id, type: 2 })
  auditRecordList.value = res.data
}
const handleAddProduct = () => {
  if (!form.value.price_list_id) {
    message.info('请先选择价目表!')
    return
  }
  productDrawerRef.value?.open(selectProductList.value.map((item) => item.sku_id))
}

const handleShowAddMaterial = () => {
  if (!form.value.price_list_id) {
    message.info('请先选择价目表!')
    return
  }
  materialDrawerRef.value?.open(selectMaterialList.value.map((i) => i.material_id))
}
// 关闭
const handleClose = () => {
  visible.value = false
  form.value = {
    is_contain_tax: false,
    currency_type: 1,
    attachments: [],
    files: [],
  }
  queryForm.value = {
    sku_id: '',
    price_trend: undefined,
  }
  materialForm.value = {
    material_name: '',
    price_trend: undefined,
  }
  selectProductList.value = []
  selectMaterialList.value = []
}

// 获取价目表列表
const getPriceList = async () => {
  const res = await GetPurchasePriceSelects()
  priceListOption.value = res.data.map((item: any) => ({ label: item.value, value: item.key }))
}

// 选中商品
const handleSelectProduct = (list: any[], status?: number) => {
  const newList = list.map((i) => {
    return {
      product_id: i.id,
      price_detail_id: null,
      quantity_from: i.quantity_from || null,
      quantity_to: i.quantity_to || null,
      adjust_quantity_from: i.quantity_from || null,
      adjust_quantity_to: i.quantity_to || null,
      process_type: machiningTypeList.value[0].value,
      str_material_price: i.str_material_price || null,
      str_adjust_material_price: i.str_material_price || null,
      str_process_fee: i.str_process_fee || null,
      str_adjust_process_fee: i.str_process_fee || '0',
      str_price: i.str_price || null,
      str_adjust_price: i.str_price || null,
      tax_rate_id: i.tax_rate_id || null,
      adjust_tax_rate_id: taxRateOption.value[0].value,
      str_total_rate_price: i.str_total_rate_price || null,
      str_adjust_total_rate_price: i.str_total_rate_price || null,
      take_effect_time: i.take_effect_time || null,
      lose_efficacy_time: i.lose_efficacy_time || null,
      data_status: status || (selectProductList.value.find((item) => item.sku_id === i.sku_id || item.sku_id === i.sku_id) ? 3 : 2),
      id: 0,
      isFromRed: false,
      isToRed: false,
      isPriceRed: false,
      ...i,
      conversion_value: i.conversion_value || 1,
    }
  })

  selectProductList.value = [...selectProductList.value, ...newList]
  selectProductList.value.forEach((v, index) => {
    v.iid = index + v.sku_id
  })
}

const selectExistProduct = (list: any[], status?: number) => {
  const newList = list.map((i) => {
    const { id, ...args } = i
    console.log(id)
    return {
      price_detail_id: i.id,
      quantity_from: i.quantity_from || null,
      quantity_to: i.quantity_to || null,
      adjust_quantity_from: i.quantity_from || null,
      adjust_quantity_to: i.quantity_to || null,
      process_type: i.process_type || machiningTypeList.value[0].value,
      str_material_price: i.str_material_price || null,
      str_adjust_material_price: i.str_material_price || null,
      str_process_fee: i.str_process_fee || '0',
      str_adjust_process_fee: i.str_process_fee || '0',
      str_price: i.str_price || null,
      str_adjust_price: i.str_price || null,
      tax_rate_id: i.tax_rate_id || null,
      adjust_tax_rate_id: i.tax_rate_id || taxRateOption.value[0].value,
      str_total_rate_price: i.str_total_rate_price || null,
      str_adjust_total_rate_price: i.str_total_rate_price || null,
      take_effect_time: i.take_effect_time || null,
      lose_efficacy_time: i.lose_efficacy_time || null,
      data_status: status || (selectProductList.value.find((item) => item.sku_id === i.sku_id || item.sku_id === i.sku_id) ? 3 : 2),
      id: 0,
      isFromRed: false,
      isToRed: false,
      isPriceRed: false,
      ...args,
    }
  })

  selectProductList.value = [...selectProductList.value, ...newList]
  selectProductList.value = uniqueData(selectProductList.value, 'price_detail_id')
  selectProductList.value.forEach((v, index) => {
    v.iid = index + v.sku_id
  })
  form.value.is_contain_tax = [...selectProductList.value, ...selectMaterialList.value].some((v) => v.adjust_tax_rate_id > 0)
}

const handleSelectMaterial = (list: any[], status?: number) => {
  const newList = list.map((i) => {
    const { id, ...args } = i
    console.log(id)
    return {
      price_detail_id: null,
      material_id: Number(i.value),
      material_name: i.label,
      quantity_from: i.quantity_from || null,
      quantity_to: i.quantity_to || null,
      adjust_quantity_from: i.quantity_from || null,
      adjust_quantity_to: i.quantity_to || null,
      process_type: machiningTypeList.value[0].value,
      str_material_price: i.str_material_price || null,
      str_adjust_material_price: i.str_material_price || null,
      str_process_fee: i.str_process_fee || null,
      str_adjust_process_fee: i.str_process_fee || '0',
      str_price: i.str_price || null,
      str_adjust_price: i.str_price || null,
      tax_rate_id: i.tax_rate_id || null,
      adjust_tax_rate_id: taxRateOption.value[0].value,
      str_total_rate_price: i.str_total_rate_price || null,
      str_adjust_total_rate_price: i.str_total_rate_price || null,
      take_effect_time: i.take_effect_time || null,
      lose_efficacy_time: i.lose_efficacy_time || null,
      isFromRed: false,
      isToRed: false,
      isPriceRed: false,
      data_status: status || (selectMaterialList.value.find((item) => item.material_id === i.material_id || item.material_id === i.material_id) ? 3 : 2),
      ...args,
    }
  })

  selectMaterialList.value = [...selectMaterialList.value, ...newList]
  selectMaterialList.value.forEach((v, index) => {
    v.iid = index + v.material_id
  })
}

const selectExistMaterial = (list: any[], status?: number) => {
  const newList = list.map((i) => {
    const { id, ...args } = i
    console.log(id)

    return {
      price_detail_id: i.id,
      quantity_from: i.quantity_from || null,
      quantity_to: i.quantity_to || null,
      adjust_quantity_from: i.quantity_from || null,
      adjust_quantity_to: i.quantity_to || null,
      process_type: i.process_type || machiningTypeList.value[0].value,
      str_material_price: i.str_material_price || null,
      str_adjust_material_price: i.str_material_price || null,
      str_process_fee: i.str_process_fee || '0',
      str_adjust_process_fee: i.str_process_fee || '0',
      str_price: i.str_price || null,
      str_adjust_price: i.str_price || null,
      tax_rate_id: i.tax_rate_id || null,
      adjust_tax_rate_id: i.tax_rate_id || taxRateOption.value[0].value,
      str_total_rate_price: i.str_total_rate_price || null,
      str_adjust_total_rate_price: i.str_total_rate_price || null,
      take_effect_time: i.take_effect_time || null,
      lose_efficacy_time: i.lose_efficacy_time || null,
      data_status: status || (selectMaterialList.value.find((item) => item.material_id === i.material_id || item.material_id === i.material_id) ? 3 : 2),
      id: 0,
      isFromRed: false,
      isToRed: false,
      isPriceRed: false,
      ...args,
    }
  })

  selectMaterialList.value = [...selectMaterialList.value, ...newList]
  selectMaterialList.value = uniqueData(selectMaterialList.value, 'price_detail_id')
  selectMaterialList.value.forEach((v, index) => {
    v.iid = index + v.material_id
  })
  form.value.is_contain_tax = [...selectProductList.value, ...selectMaterialList.value].some((v) => v.adjust_tax_rate_id)
}

// 价目表选择
const handleChangePriceList = () => {
  selectProductList.value = []
  selectMaterialList.value = []
}

const checkProductList = () => {
  leftTagListCache.value.forEach((item) => {
    const arr = selectProductList.value.filter((v) => v.sku_id == item.sku_id)
    const res = validateIntervals(arr, '商品', 'sku_name')
    resArr.value.push(res)
  })
  const failArr = resArr.value.filter((item) => !item.isValid)
  return failArr.length == 0
}

const checkMaterialList = () => {
  materialTagListCache.value.forEach((item) => {
    const arr = selectMaterialList.value.filter((v) => v.material_id == item.material_id)
    const res = validateIntervals(arr, '材质', 'material_name')
    resArr.value.push(res)
  })
  const failArr = resArr.value.filter((item) => !item.isValid)
  return failArr.length == 0
}

// 提交审核
const handleSubmitAudit = async (is_pass: boolean) => {
  try {
    message.destroy()
    await formRef.value?.validate()
    if (selectProductList.value.length == 0 && selectMaterialList.value.length == 0) {
      message.info('请至少选择一个单品或者材质')
      return
    }
    resArr.value = []
    const isPassProduct = checkProductList()
    const isPassMaterial = checkMaterialList()
    const errorLen = resArr.value.filter((v) => !v?.isValid).length
    if (resArr.value?.length > 0 && errorLen > 0) {
      resArr.value.forEach((item) => {
        if (!item?.isValid) {
          message.error(item.message, 10)
        }
      })
      return
    }

    if (isPassProduct || isPassMaterial) {
      const productArr: any[] = []
      selectProductList.value.forEach((i) => {
        if (!i.str_adjust_material_price && i.str_adjust_material_price !== 0) {
          i.str_adjust_material_price = i.str_material_price || 0
        }
        i.id = i.id || 0
        i.adjust_price_list_id = form.value.id || 0
        i.str_material_price = i.str_material_price || null
        if (!saveId.value) {
          delete i.id
          delete i.adjust_price_list_id
        }
        delete i.iid
        if (i.str_total_rate_price && i.str_adjust_total_rate_price && i.str_total_rate_price != i.str_adjust_total_rate_price) {
          i.price_trend = +i.str_total_rate_price < +i.str_adjust_total_rate_price ? 1 : 2
        } else {
          i.price_trend = null
        }
        productArr.push(i)
      })
      const materialArr: any[] = []
      selectMaterialList.value.forEach((i) => {
        if (!i.str_adjust_material_price && i.str_adjust_material_price !== 0) {
          i.str_adjust_material_price = i.str_material_price || 0
        }
        i.id = i.id || 0
        i.adjust_price_list_id = form.value.id || 0
        i.str_material_price = i.str_material_price || null
        if (!saveId.value) {
          delete i.id
          delete i.adjust_price_list_id
        }
        delete i.iid
        if (i.str_total_rate_price && i.str_adjust_total_rate_price && i.str_total_rate_price != i.str_adjust_total_rate_price) {
          i.price_trend = +i.str_total_rate_price < +i.str_adjust_total_rate_price ? 1 : 2
        } else {
          i.price_trend = null
        }
        materialArr.push(i)
      })
      const submitForm = {
        is_pass,
        ...form.value,
        details: productArr,
        material_details: materialArr,
      }
      if ([DetailTypeEnum.EDIT, DetailTypeEnum.ADD].includes(viewType.value)) {
        submitForm.attachments = submitForm.attachments?.map((i) => i.id)
      }
      console.log('submitForm', submitForm)
      // if (submitForm) return

      const fn = form.value.id ? UpdatePurchaseAdjustPrice : AddPurchaseAdjustPrice
      fn(submitForm).then(() => {
        message.success('提交成功')
        handleClose()
        emits('query')
      })
    }
  } catch (err) {
    console.log(err)
  }
}
// 审核
const handleAudit = (is_pass: boolean) => {
  if (auditStatus.value === PurchaseOrderAuditStatusEnum.待一级审核 && !btnPermission.value[82004]) {
    message.error('您没有该状态的审核权限')
    return
  }
  if (auditStatus.value === PurchaseOrderAuditStatusEnum.待二级审核 && !btnPermission.value[82005]) {
    message.error('您没有该状态的审核权限')
    return
  }
  if (auditStatus.value === PurchaseOrderAuditStatusEnum.待三级审核 && !btnPermission.value[82006]) {
    message.error('您没有该状态的审核权限')
    return
  }
  if (auditStatus.value === PurchaseOrderAuditStatusEnum.待四级审核 && !btnPermission.value[82007]) {
    message.error('您没有该状态的审核权限')
    return
  }
  auditModalRef.value?.open(is_pass)
}

// 审核完成
const handleFinishAudit = async (auditForm: any) => {
  await BatchAuditPurchaseAdjustPrice({ ...auditForm, ids: [form.value.id] })
  message.success('审核成功')
  auditModalRef.value?.close()
  handleClose()
  emits('query')
}

// 导入金蝶商品明细
const handleBeforeUpload = (file: any, isNew: boolean) => {
  if (!form.value.price_list_id) {
    message.error('请先选择价目表')
    return false
  }
  const api = isNew ? PurchaseAdjustImportNewK3SkuInfo : PurchaseAdjustImportK3SkuInfo
  const formdata = new FormData()
  formdata.append('file', file)
  api(formdata, form.value.price_list_id).then((res) => {
    const list = res.data.datas
    const downLoadId = res.data.error_download_id
    // const errorNum = res.data.errors.length
    // 过滤掉已经存在的商品
    const newList = list.filter((i) => !selectProductList.value.some((j) => j.sku_id === i.sku_id))
    // const isHaveShop = list.filter((i) => selectProductList.value.some((j) => j.sku_id === i.sku_id))
    // isHaveShop = selectProductList.value.map((i) => {
    //   const item = isHaveShop.find((j) => j.sku_id === i.sku_id)
    //   return {
    //     ...i,
    //     adjust_quantity_from: item.adjust_quantity_from,
    //     adjust_quantity_to: item.adjust_quantity_to,
    //     str_adjust_material_price: item.str_adjust_material_price,
    //     take_effect_time: item.take_effect_time,
    //     lose_efficacy_time: item.lose_efficacy_time,
    //   }
    // })
    selectProductList.value = [...selectProductList.value, ...newList]
    selectProductList.value = selectProductList.value.map((v, index) => {
      v.iid = index + v.sku_id
      return v
    })
    Modal.info({
      title: '提示',
      // content: h('div', {}, [h('p', `共导入${list.length + errorNum}条数据，已成功导入${newList.length + isHaveShop.length}条数据，${errorNum ? `导入失败${errorNum}条数据` : ''}`)]),
      content: h('div', {}, res.data.errors?.[0] || `共导入${list.length}条数据，已成功导入${list.length}条数据`),
      okText: downLoadId ? '下载失败原因' : '确认',
      onOk: () => {
        return new Promise((resolve) => {
          if (downLoadId) {
            eventBus.emit('downLoadId', {
              id: downLoadId as number,
              resolve,
              download: true,
            })
          } else {
            resolve(true)
          }
        })
      },
    })
  })

  return false
}

const handleMaterialUpload = (file: any) => {
  if (!form.value.price_list_id) {
    message.error('请先选择价目表')
    return false
  }

  const formdata = new FormData()
  formdata.append('file', file)
  PurchaseAdjustImportMaterialInfo(formdata, form.value.price_list_id).then((res) => {
    const list = res.data.datas
    const errorNum = res.data.errors.length
    const downLoadId = res.data.error_download_id
    // 过滤掉已经存在的商品
    const newList = list.filter((i) => !selectMaterialList.value.some((j) => j.material_id === i.material_id))
    let isHaveShop = list.filter((i) => selectMaterialList.value.some((j) => j.material_id === i.material_id))
    isHaveShop = selectMaterialList.value.map((i) => {
      const item = isHaveShop.find((j) => j.material_id === i.material_id)
      return {
        ...i,
        adjust_quantity_from: item.adjust_quantity_from,
        adjust_quantity_to: item.adjust_quantity_to,
        str_adjust_material_price: item.str_adjust_material_price,
        take_effect_time: item.take_effect_time,
        lose_efficacy_time: item.lose_efficacy_time,
      }
    })
    selectMaterialList.value = [...selectMaterialList.value, ...newList]
    selectMaterialList.value = selectMaterialList.value.map((v, index) => {
      v.iid = index + v.material_id
      return v
    })
    Modal.info({
      title: '提示',
      content: h('div', {}, [h('p', `共导入${list.length + errorNum}条数据，已成功导入${newList.length + isHaveShop.length}条数据，${errorNum ? `导入失败${errorNum}条数据` : ''}`)]),
      okText: downLoadId ? '下载失败原因' : '确认',
      onOk: () => {
        return new Promise((resolve) => {
          if (downLoadId) {
            eventBus.emit('downLoadId', {
              id: downLoadId as number,
              resolve,
              download: true,
            })
          } else {
            resolve(true)
          }
        })
      },
    })
  })

  return false
}

const addSetting = (row) => {
  const idx = selectProductList.value.findIndex((v) => v.iid == row.iid)
  const obj = {
    ...selectProductList.value[idx],
    id: 0,
    adjust_quantity_from: null,
    adjust_quantity_to: null,
    quantity_from: null,
    quantity_to: null,
    str_material_price: null,
    str_adjust_material_price: null,
    take_effect_time: null,
    lose_efficacy_time: null,
    data_status: 2,
    iid: Math.random(),
    isFromRed: false,
    isToRed: false,
    isPriceRed: false,
  }
  selectProductList.value.splice(idx + 1, 0, obj)
}

const addMaterialSetting = (row) => {
  const idx = selectMaterialList.value.findIndex((v) => v.iid == row.iid)
  const obj = {
    ...selectMaterialList.value[idx],
    id: 0,
    adjust_quantity_from: null,
    adjust_quantity_to: null,
    quantity_from: null,
    quantity_to: null,
    str_material_price: null,
    str_adjust_material_price: null,
    take_effect_time: null,
    lose_efficacy_time: null,
    data_status: 2,
    iid: Math.random(),
    isFromRed: false,
    isToRed: false,
    isPriceRed: false,
  }
  selectMaterialList.value.splice(idx + 1, 0, obj)
  console.log(selectMaterialList.value, 'selectMaterialList')
}

const delSetting = (row) => {
  const idx = selectProductList.value.findIndex((v) => v.iid == row.iid)
  if (row.data_status == 3) {
    row.data_status = 4
  } else {
    selectProductList.value.splice(idx, 1)
    console.log(selectProductList.value, 'selectProductList')
  }
}

const delMaterialSetting = (row) => {
  const idx = selectMaterialList.value.findIndex((v) => v.iid == row.iid)
  if (row.data_status == 3) {
    row.data_status = 4
  } else {
    selectMaterialList.value.splice(idx, 1)
    console.log(selectMaterialList.value, 'selectMaterialList')
  }
}

const changeSkuid = (val) => {
  select_sku_id.value = val
}

const changeMaterialid = (val) => {
  select_material_name.value = val
}

const validateBasicFields = (interval, title, key, line) => {
  // 检查空值情况
  if (isEmpty(interval.adjust_quantity_from, '') && isEmpty(interval.adjust_quantity_to, '')) {
    interval.isFromRed = true
    interval.isToRed = true
    return { isValid: false, message: `${title}${interval[key]}第${line}行：采购数量（从）和采购数量（至）不能同时为空` }
  }
  if (isEmpty(interval.str_adjust_material_price, '')) {
    interval.isPriceRed = true
    return { isValid: false, message: `${title}${interval[key]}第${line}行：材料单价不能为空` }
  }
  if (!isEmpty(interval.adjust_quantity_from, '') && !isEmpty(interval.adjust_quantity_to, '') && interval.adjust_quantity_from > interval.adjust_quantity_to) {
    interval.isFromRed = true
    interval.isToRed = true
    return { isValid: false, message: `${title}${interval[key]}第${line}行：采购数量（从）必须小于采购数量（至）` }
  }
  return { isValid: true }
}

const getConflictDetail = (current, next, arr, title, key, line1, line2) => {
  const currentMin = current.adjust_quantity_from === null ? -Infinity : current.adjust_quantity_from
  const currentMax = current.adjust_quantity_to === null ? Infinity : current.adjust_quantity_to
  const nextMin = next.adjust_quantity_from === null ? -Infinity : next.adjust_quantity_from
  const nextMax = next.adjust_quantity_to === null ? Infinity : next.adjust_quantity_to

  if (currentMax === nextMin) {
    return `${title}${arr[0][key]}第${line1}行的采购数量（至） ${currentMax} 与 第${line2}行的采购数量（从） ${nextMin} 重合`
  }
  if (nextMax === currentMin) {
    return `${title}${arr[0][key]}第${line2}行的采购数量（至） ${nextMax} 与 第${line1}行的采购数量（从） ${currentMin} 重合`
  }
  if (currentMin <= nextMin && currentMax >= nextMax) {
    return `${title}${arr[0][key]}第${line1}行区间完全包含第${line2}行区间`
  }
  if (nextMin <= currentMin && nextMax >= currentMax) {
    return `${title}${arr[0][key]}第${line2}行区间完全包含第${line1}行区间`
  }
  return `${title}${arr[0][key]}第${line1}行区间与第${line2}行区间存在交叉重叠`
}

const validateIntervals = (intervals, title, key) => {
  intervals = intervals.filter((item) => item.data_status !== 4)
  // 基础字段校验
  for (let i = 0; i < intervals.length; i++) {
    const result = validateBasicFields(intervals[i], title, key, i + 1)
    if (!result.isValid) return result
  }

  // 按process_type分组处理
  const processTypes = [...new Set(intervals.map((item) => item.process_type))]

  for (const processType of processTypes) {
    const arr = intervals.filter((item) => item.process_type === processType)

    // 排序并保留原始行号
    const sorted = arr
      .map((item, index) => ({ ...item, originalIndex: index }))
      .sort((a, b) => {
        const aMin = a.adjust_quantity_from === null ? -Infinity : a.adjust_quantity_from
        const bMin = b.adjust_quantity_from === null ? -Infinity : b.adjust_quantity_from
        return aMin - bMin
      })

    // 重叠检查
    for (let i = 0; i < sorted.length - 1; i++) {
      const current = sorted[i]
      const currentMin = current.adjust_quantity_from === null ? -Infinity : current.adjust_quantity_from
      const currentMax = current.adjust_quantity_to === null ? Infinity : current.adjust_quantity_to

      for (let j = i + 1; j < sorted.length; j++) {
        const next = sorted[j]
        const nextMin = next.adjust_quantity_from === null ? -Infinity : next.adjust_quantity_from

        // 提前终止条件
        if (nextMin > currentMax) break

        const nextMax = next.adjust_quantity_to === null ? Infinity : next.adjust_quantity_to
        if (currentMax >= nextMin && nextMax >= currentMin) {
          const line1 = current.originalIndex + 1
          const line2 = next.originalIndex + 1

          arr[line1 - 1].isFromRed = true
          arr[line1 - 1].isToRed = true
          arr[line2 - 1].isFromRed = true
          arr[line2 - 1].isToRed = true

          return {
            isValid: false,
            message: `区间冲突：${getConflictDetail(current, next, arr, title, key, line1, line2)}`,
          }
        }
      }
    }
  }

  return { isValid: true, message: '区间校验通过' }
}

const checkLeftList = () => {
  selectProductList.value.map((i) => {
    i.isFromRed = false
    i.isToRed = false
    i.isPriceRed = false
    return i
  })
  leftTagListCache.value.forEach((item) => {
    item.isRed = false
    const arr = selectProductList.value.filter((v) => v.sku_id == item.sku_id)
    const res = validateIntervals(arr, '商品', 'sku_name')
    if (!res.isValid) {
      item.isRed = true
    }
  })
}

const setTaxRate = () => {
  if (!form.value.is_contain_tax) {
    const tax_tate_id = taxRateOption.value[0].value
    selectProductList.value.forEach((v) => {
      v.adjust_tax_rate_id = tax_tate_id
    })
    selectMaterialList.value.forEach((v) => {
      v.adjust_tax_rate_id = tax_tate_id
    })
  }
  setPrice()
}

const setPrice = () => {
  const calculatePrice = (item: any) => {
    const materialPrice = Number(item.str_adjust_material_price || 0)
    const processFee = Number(item.str_adjust_process_fee || 0)
    const price = materialPrice + processFee
    const taxRate = taxRateOption.value.find((i) => i.value == item.adjust_tax_rate_id)?.tax_rate || 0
    const taxAmount = (price * taxRate) / 100

    return {
      str_adjust_price: price.roundNext(8),
      str_adjust_total_rate_price: (price + taxAmount).roundNext(8),
    }
  }

  selectProductList.value.forEach((v) => Object.assign(v, calculatePrice(v)))
  selectMaterialList.value.forEach((v) => Object.assign(v, calculatePrice(v)))
}

const checkLeftMaterialList = () => {
  selectMaterialList.value.map((i) => {
    i.isFromRed = false
    i.isToRed = false
    i.isPriceRed = false
    return i
  })
  materialTagListCache.value.forEach((item) => {
    item.isRed = false
    const arr = selectMaterialList.value.filter((v) => v.material_id == item.material_id)
    const res = validateIntervals(arr, '材质', 'material_name')
    if (!res.isValid) {
      item.isRed = true
    }
  })
}
const getMachiningType = async () => {
  const res = await GetPLMMachiningType({})
  machiningTypeList.value = res.data.map((v) => ({
    label: v.label,
    value: Number(v.value),
  }))
}

const getTaxRateList = async () => {
  const res = await GetTaxRateSelect({})
  if (res.success) {
    taxRateOption.value = res.data
      .map((v) => {
        return {
          label: v.label,
          value: Number(v.value),
          tax_rate: v.tax_rate,
        }
      })
      .sort((a, b) => a.tax_rate - b.tax_rate)
  }
}

// 获取单品价目数据
const getProductListByPage = async (page: number = 1) => {
  const { data } = await GetPurchaseAdjustPriceDetail({
    id: form.value.id,
    page,
    pageSize: productPageSize.value,
    sortField: '',
    sortType: '',
  })
  if (data.list) {
    productTotal.value = data.total
    productPage.value = page
    selectProductList.value = data.list.map((v, index) => ({
      ...v,
      iid: index + v.sku_id,
    }))
  }
  return Promise.resolve(selectProductList.value)
}

// 获取材质价目数据
const getMaterialListByPage = async (page: number = 1) => {
  const { data } = await GetPurchaseAdjustPriceMaterialDetail({
    id: form.value.id,
    page,
    pageSize: materialPageSize.value,
    sortField: '',
    sortType: '',
  })
  if (data.list) {
    materialTotal.value = data.total
    materialPage.value = page
    selectMaterialList.value = data.list.map((v, index) => ({
      ...v,
      iid: index + v.material_id,
    }))
  }
  return Promise.resolve(selectMaterialList.value)
}

defineExpose({
  open,
  applyOpen,
})
</script>

<style scoped lang="scss">
.isClick {
  background-color: #c6e2ff;
}

.isRed {
  border-color: #ff4d4f !important;
  box-shadow: 0 0 0 2px rgb(255 77 79 / 20%) !important;
}

.miniNumber {
  display: block;
  height: 18px;
  line-height: 18px;
  text-align: center;
}

:deep .ant-input-number-sm input {
  color: red;
  text-align: center;
}
</style>
