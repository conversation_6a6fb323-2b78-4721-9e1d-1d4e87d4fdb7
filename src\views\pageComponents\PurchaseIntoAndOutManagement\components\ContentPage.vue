<template>
  <div class="flex flex-col h-full main boxStr">
    <Form ref="formRef" v-model:form="formArr" :page-type="pageType" @search="handleSearch" @setting="tableRef?.showTableSetting()"></Form>
    <BaseTable ref="tableRef" :page-type="pageType" v-model:form="formArr" :get-list="getList">
      <template #right-btn>
        <a-button v-if="btnPermission[syncCode]" type="primary" @click="handleSync()" :loading="syncLoading" :disabled="syncLoading">{{ syncLoading ? '同步中...' : '立即同步' }}</a-button>

        <a-dropdown v-if="exportCode ? btnPermission[exportCode] : true" placement="bottomLeft" class="ml-auto">
          <template #overlay>
            <a-menu @click="handleExport">
              <a-menu-item v-for="item in exportOptions" :key="item.value">{{ item.label }}</a-menu-item>
            </a-menu>
          </template>
          <a-button :disabled="exportLoading">
            导出
            <DownOutlined />
          </a-button>
        </a-dropdown>
      </template>
      <template #fix_option="{ row, column }">
        <RightOperate :list="rightOperateList" :row="row" :column="column" />
      </template>
    </BaseTable>
    <slot></slot>
  </div>
</template>

<script lang="ts" setup>
import { DownOutlined } from '@ant-design/icons-vue'

const emits = defineEmits(['details', 'sync', 'export'])

const props = defineProps<{
  pageType: number
  getList: any
  btnPermission: any
  syncCode: number
  syncLoading?: boolean
  exportCode?: number
}>()

const formArr = defineModel<Array<any>>('formArr', {
  type: Array<any>,
  default: () => [],
})

const tableRef = useTemplateRef<any>('tableRef')
const rightOperateList = ref([
  {
    label: '查看',
    onClick: ({ row }) => {
      handleDetails(row)
    },
  },
])

// 导出相关变量
const exportOptions = ref<any[]>(
  [
    // { label: '根据勾选导出', value: 1 },
    { label: '根据筛选结果导出', value: 2 },
    props.exportCode === 115002 && { label: '根据筛选结果导出明细', value: 4 },
    { label: '全部导出', value: 3 },
  ].filter(Boolean),
)
const exportType = ref()
const exportLoading = ref(false)

const handleSearch = () => {
  if (tableRef.value) tableRef.value.search()
}

const handleDetails = (row: any) => {
  emits('details', row)
}

// 同步数据
const handleSync = () => {
  emits('sync')
}

// 导出数据
const handleExport = ({ key }) => {
  emits('export', key)
}
defineExpose({
  search: handleSearch,
  tableRef,
  exportType,
  exportLoading,
  exportOptions,
})
</script>

<style lang="scss" scoped>
//
</style>
