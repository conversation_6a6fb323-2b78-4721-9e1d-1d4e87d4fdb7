<template>
  <div class="flex flex-col h-full main">
    <Form ref="formRef" v-model:form="formArr" :page-type="PageTypeEnum.ReservationList" @search="tableRef?.search()" @setting="tableRef?.showTableSetting()">
      <template #header>
        <StatusTabs v-model:status="status" :options="reservationStatusOption" :count-map="labelStatusCountMap" @change="tableRef?.search()" />
      </template>
    </Form>
    <BaseTable ref="tableRef" :page-type="PageTypeEnum.ReservationList" v-model:form="formArr" :get-list="List" :isCheckbox="true" :form-format="formatData">
      <template #left-btn>
        <a-button @click="Openreview('审核通过', '确认通过', true)">批量审核通过</a-button>
        <a-button @click="Openreview('审核拒绝', '确认拒绝', false)">批量审核拒绝</a-button>
      </template>
      <template #right-btn>
        <a-button type="primary" @click="addReservationOrder" v-if="btnPermission[111001]">添加预约入库单</a-button>
      </template>
      <template #fix_option="{ row, column }">
        <RightOperate :list="rightOperateList" :row="row" :column="column" />
      </template>
    </BaseTable>
    <review ref="reviewRef" @ClickReview="ClickReview"></review>
    <OperationLog ref="operationLogRef"></OperationLog>
  </div>
</template>
<script setup lang="ts">
import { message } from 'ant-design-vue'

import { reservationStatusOption } from '@/common/options'
import { useSearchForm } from '@/hook/useSearchForm'

import review from './components/review.vue'

import { List, BatchAudit, GetUserOptions, GetWarehouses, CancelBookingOrder, BatchCompletedBookingOrder } from '@/servers/ReservationApi'
import { GetPageSuppliersSelect, GetPageMRPSupplierCompanySelect } from '@/servers/BusinessCommon'

import { PageTypeEnum } from '@/enums/tableEnum'

const { btnPermission } = usePermission()
const { pushPage } = usePage({ refresh: () => tableRef.value.search() })
const { labelStatusCountMap, labelStatusRefresh } = useLabelStatus(PageTypeEnum.ReservationList)

const reviewRef = ref()
const tableRef = ref()
const formRef = ref()
const status = ref('')
const formArr = ref([
  { label: '采购单编号', value: '', type: 'inputDlg', key: 'purcharse_order_number' },
  { label: '预约入库单编号', value: '', type: 'inputDlg', key: 'number' },
  { label: '商品编码', value: '', type: 'inputDlg', key: 'product_number' },
  { label: '商品名称', value: '', type: 'input', key: 'product_name' },
  { label: 'SRS平台商品编码', value: '', type: 'input', key: 'srs_platform_prod_code' },
  {
    label: '供应商',
    value: null,
    type: 'select-supplier',
    key: 'supplier_id',
    mode: 'single',
    api: GetPageSuppliersSelect,
  },
  {
    label: '供应商子公司',
    type: 'select-supplier',
    key: 'company_supplier_id',
    api: GetPageMRPSupplierCompanySelect,
    apiParams: { is_contains_srs: true },
    mode: 'single',
  },
  {
    label: '选择仓库',
    value: null,
    type: 'select',
    key: 'warehouse_id',
    selectArr: [],
    api: GetWarehouses,
  },
  {
    label: '选择申请人',
    value: [],
    type: 'select',
    key: 'buyer_id',
    selectArr: [],
    multiple: true,
    showSearch: true,
    filterOption: (input, option) => option.label?.toLowerCase().includes(input.toLowerCase()),
    api: GetUserOptions,
  },
])

useSearchForm(formArr)

const formatData = (data: any) => {
  labelStatusRefresh()
  return {
    ...data,
    audit_status: status.value,
  }
}
// 审核订单
const Openreview = (text, submitText, ispass) => {
  if (tableRef.value.checkItemsArr.length > 0) {
    const audit_status = Array.from(
      new Set(
        tableRef.value.checkItemsArr
          .filter((v) => {
            return v.audit_status
          })
          .map((v) => {
            return v.audit_status
          }),
      ),
    )

    if (audit_status.length > 1) {
      message.error('请选择同一状态的审核数据')
      return
    }
    if (audit_status.length == 1 && audit_status[0] !== 20) {
      message.error('只能选择待审核的订单进行批量审核操作')
      return
    }

    if (btnPermission.value[111004]) {
      reviewRef.value.setReview(true, text, submitText, ispass)
    } else {
      message.error('您没有该状态的审核权限')
    }
  } else {
    message.error('请选择批量审核数据')
  }
}
const ClickReview = async (val, ispass) => {
  const arr: any = []
  tableRef.value.checkItemsArr.forEach((item) => {
    arr.push(item.id)
  })
  const params = {
    is_pass: ispass,
    audit_opinion: val,
    ids: arr,
  }
  const res = await BatchAudit(params)
  if (res.success) {
    message.success('操作成功')
    tableRef.value.search()
  } else {
    tableRef.value.search()
    message.error(res.message)
  }
}

// 添加采购订单
const addReservationOrder = () => {
  pushPage('/addReservation', { source: true })
}
const ReviewOrder = (row) => {
  pushPage(`/reviewReservation/${row.id}`, { source: true })
}
const LookOrder = (row) => {
  pushPage(`/lookReservation/${row.id}`, { source: true })
}
const EditOrder = (row) => {
  pushPage(`/editReservation/${row.id}`, { source: true })
}

const ModifyLogistics = (row) => {
  pushPage(`/modifyLogistics/${row.id}`, { source: true })
}

// 确认到货
const handleConfirmArrival = (row) => {
  pushPage(`/confirmArrival/${row.id}`, { source: true })
}

// 操作日志
const operationLogRef = ref()
const openLog = ({ id }) => {
  operationLogRef.value.open({
    params: {
      id,
      pageType: OpLogPageTypeEnum.预约入库,
    },
  })
}

const modal: any = inject('modal')
const handleCancelOrder = (row) => {
  modal.open({
    title: '确定要作废该订单吗？',
    content: '作废后不可恢复，是否继续？',
    async onOk() {
      await CancelBookingOrder([row.id])
      message.success('作废成功')
      tableRef.value.search()
    },
  })
}

// 完成订单
const handleCompleteOrder = (row) => {
  modal.open({
    title: '确定要完成该订单吗？',
    async onOk() {
      await BatchCompletedBookingOrder([row.id])
      message.success('操作成功')
      tableRef.value.search()
    },
  })
}

const rightOperateList = ref([
  {
    label: '查看',
    show: 111002,
    onClick: ({ row }) => {
      LookOrder(row)
    },
  },
  {
    label: '日志',
    // show: 84001,
    onClick: ({ row }) => {
      openLog(row)
    },
  },
  {
    label: '审核',
    show: ({ row }) => {
      return btnPermission.value[111004] && row.audit_status === BookingOrderAuditStatusEnum.待一级审核
    },
    onClick: ({ row }) => {
      ReviewOrder(row)
    },
  },
  {
    label: '编辑',
    show: ({ row }) => {
      return btnPermission.value[111003] && [BookingOrderAuditStatusEnum.待提审, BookingOrderAuditStatusEnum.已拒绝].includes(row.audit_status)
    },
    onClick: ({ row }) => {
      EditOrder(row)
    },
  },
  {
    label: ({ row }) => (row.tracking_status === '待发货' ? '发货' : '修改物流'),
    show: ({ row }) => {
      return btnPermission.value[111005] && [BookingOrderAuditStatusEnum.待提审, BookingOrderAuditStatusEnum.已通过].includes(row.audit_status)
    },
    onClick: ({ row }) => {
      ModifyLogistics(row)
    },
  },
  {
    label: '确认到货',
    show: ({ row }) => {
      return btnPermission.value[111007] && [BookingOrderAuditStatusEnum.已通过].includes(row.audit_status) && !row.actual_arrival_time
    },
    onClick: ({ row }) => {
      handleConfirmArrival(row)
    },
  },
  {
    label: '作废订单',
    show: ({ row }) => {
      return btnPermission.value[111008] && ![BookingOrderAuditStatusEnum.已完成, BookingOrderAuditStatusEnum.已作废].includes(row.audit_status)
    },
    onClick: ({ row }) => {
      handleCancelOrder(row)
    },
  },
  {
    label: '完成订单',
    show: ({ row }) => {
      return btnPermission.value[111010] && [BookingOrderAuditStatusEnum.已通过].includes(row.audit_status)
    },
    onClick: ({ row }) => {
      handleCompleteOrder(row)
    },
  },
])
</script>
