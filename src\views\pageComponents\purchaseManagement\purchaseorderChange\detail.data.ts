import dayjs from 'dayjs'

import { areaCity } from '@/utils/address'
import { options2Map, Enum2Options, Enum2Map } from '@/utils'

import { GetAliPurchaseAccountSelect, GetAliPurchaseAccountReceiveAddressSelect } from '@/servers/PurchaseAccount'
import { GetWarehouse } from '@/servers/WarehouseApi'
import { GetSupplierCompanyMessage } from '@/servers/Supplier'
import { GetWarehouses } from '@/servers/Purchaseapplyorder'
import { GetPurchaseByDeptSelect, GetPLMMachiningType } from '@/servers/BusinessCommon'

export const setFormItems = (formData, formRef, config, id) =>
  [
    { type: 'title', span: 24, title: '基本信息' },
    {
      span: 9,
      label: '变更单编号',
      type: 'input',
      name: 'change_number',
      disabled: true,
      placeholder: '自动生成',
    },
    { label: '采购单编号', name: 'number', span: 9, type: 'input', disabled: true, placeholder: '自动生成' },
    {
      label: '采购类型',
      name: 'type',
      span: 9,
      type: 'select',
      disabled: true,
      options: Enum2Options(PurchaseTypeEnum),
      textFormat: ({ item, value }) => {
        return options2Map(item.options)[value]
      },
      linkage: ['company_supplier_id', 'ali_purchase_account_list_id', 'ali_purchase_account_receive_address_id', 'settlement_type'],
      props: {
        onChange: (val) => {
          config.type && config.type(val)
        },
      },
    },
    {
      label: '加工方式',
      required: true,
      name: 'process_type',
      alias: 'str_process_type',
      dataType: 'string',
      span: 9,
      type: 'select',
      options: [{ label: '直采', value: '1' }],
      api: () => GetPLMMachiningType({}),
    },
    {
      label: '供应商子公司',
      name: 'company_supplier_id',
      alias: 'supplier_name',
      // disabled: true,
      span: 9,
      type: 'select',
      required: true,
      options: [],
      props: {
        open: false,
        onClick: (params) => {
          config.company_supplier_id_ck && config.company_supplier_id_ck(params)
        },
        onChange: (id) => {
          if (!id) return
          GetSupplierCompanyMessage({
            id,
          }).then(({ data }) => {
            formRef.value.changeValue({
              settlement_type: data.settlement_type,
              purchase_time: dayjs().format('YYYY-MM-DD'),
            })
            config.company_supplier_id && config.company_supplier_id()
          })
        },
      },
    },
    {
      label: '结算方式',
      name: 'settlement_type',
      span: 9,
      type: 'select',
      required: true,
      options: Enum2Options(SettlementTypeEnum),
      textFormat: ({ item, value }) => {
        return options2Map(item.options)[value]
      },
      linkageFn: ({ item, form }) => {
        const options = [...item.options]
        // 只有采购类型为1688线上采购订单(2)时才显示线上付款选项
        if (form.type !== 2) {
          options[4] = { ...options[4], hide: true }
        } else {
          options[4] = { ...options[4], hide: false }
        }
        item.options = options
      },
      props: {
        onChange: (val) => {
          // 触发预付比例字段的重新验证和状态更新
          if (config.settlementTypeChange) {
            config.settlementTypeChange(Number(val))
          }
        },
      },
    },
    {
      label: '预付比例',
      name: 'prepayment_ratio',
      span: 9,
      type: 'input',
      required: formData.value.settlement_type === 4,
      hide: false,
      defaultValue: 100,
      linkageFn: ({ item, form }) => {
        // 当采购类型为1688线上采购订单(2)或寄售订单(4)时隐藏预付比例字段
        item.hide = form.type === 2 || form.type === 4
      },
      props: {
        type: 'number',
        min: 0,
        max: 100,
        step: 1,
        addonAfter: '%',
      },
      placeholder: '请输入预付比例',
      rules: [
        {
          required: formData.value.settlement_type === 4,
          message: '结算方式为预付时，预付比例为必填项',
        },
        {
          validator: (_, value) => {
            if (!value) return Promise.resolve()
            const num = parseInt(value)
            if (Number.isNaN(num) || num !== parseFloat(value)) return Promise.reject('请输入整数')
            if (num < 0 || num > 100) return Promise.reject('预付比例必须在0-100之间')
            return Promise.resolve()
          },
        },
      ],
    },
    { label: '采购时间', name: 'purchase_time', disabled: true, span: 9, type: 'date', required: true, options: [] },
    {
      label: '采购员',
      name: 'buyer_id',
      alias: 'buyer_name',
      disabled: true,
      span: 9,
      type: 'select',
      required: true,
      options: [],
      api: () => GetPurchaseByDeptSelect(id ? {} : { dept_id: null, keyword: null, is_chilrd_query: true }),
      apiCallBack: ({ data }) => {
        const display_name = JSON.parse(localStorage.getItem('userData') as any).display_name
        const obj = data.find((item) => item.label.indexOf(display_name) >= 0)
        formData.value.buyer_id = obj.value
      },
    },
    {
      label: '商品类型',
      name: 'product_type',
      span: 9,
      type: 'select',
      required: true,
      options: [
        {
          label: '成品',
          value: 1,
        },
        {
          label: '半成品',
          value: 2,
        },
        {
          label: '原材料',
          value: 3,
        },
        {
          label: '包材',
          value: 4,
        },
      ],
      textFormat: ({ item, value }) => {
        return options2Map(item.options)[value]
      },
    },
    {
      label: '采购收料仓',
      name: 'warehourse_id',
      alias: 'warehourse_name',
      span: 9,
      type: 'select',
      required: true,
      disabled: true,
      options: [],
      api: GetWarehouses,
      props: {
        onChange: async (id) => {
          const { data } = await GetWarehouse({ id })
          await formRef.value.changeValue({
            consignee: data.consignee,
            shipments_address: data.shipments_address,
            phone_number: data.phone_number,
            province: data.province,
            city: data.city,
            area: data.area,
          })
        },
      },
    },
    // {
    //   label: '1688采购账号',
    //   name: 'ali_purchase_account_list_id',
    //   type: 'text',
    //   span: 9,
    //   alias: 'ali_purchase_account_list_name',
    //   linkageFn: ({ item, form }) => {
    //     item.hide = form.type !== 2
    //   },
    // },
    // {
    //   label: '1688收货地址',
    //   name: 'ali_purchase_account_receive_address_id',
    //   type: 'text',
    //   span: 9,
    //   alias: 'ali_purchase_account_receive_address_name',
    //   linkageFn: ({ item, form }) => {
    //     item.hide = form.type !== 2
    //   },
    // },
    {
      label: '1688采购账号',
      name: 'ali_purchase_account_list_id',
      alias: 'ali_purchase_account_list_name',
      span: 9,
      type: 'select',
      required: true,
      hide: true,
      options: [],
      linkage: 'ali_purchase_account_receive_address_id',
      linkageFn: ({ item, form }) => {
        item.hide = form.type !== 2
      },
      api: GetAliPurchaseAccountSelect,
    },
    {
      label: '1688收货地址',
      name: 'ali_purchase_account_receive_address_id',
      alias: 'ali_purchase_account_receive_address_name',
      span: 9,
      type: 'select',
      required: true,
      props: {
        showSearch: true,
      },
      hide: true,
      options: [],
      linkageFn: ({ item, form }) => {
        item.hide = form.type !== 2
      },
      api: ({ ali_purchase_account_list_id }) => GetAliPurchaseAccountReceiveAddressSelect({ id: ali_purchase_account_list_id || undefined }),
    },
    { label: '备注', name: 'remark', span: 18, type: 'input', options: [], props: { maxlength: 2000 } },
    { label: '采购单审核状态', name: 'audit_status', span: 9, type: 'text', textFormat: ({ value }) => PurchaseOrderAuditStatusEnum[value] },
    { label: '变更单审核状态', name: 'change_audit_status', span: 9, type: 'text', textFormat: ({ value }) => PurchaseOrderAuditStatusEnum[value] },
    { label: '供应商状态', name: 'supplier_type', span: 9, type: 'text', textFormat: ({ value }) => SupplierStatusEnum[value] },
    {
      label: '订单状态',
      name: 'order_status',
      span: 9,
      type: 'text',
      textFormat: ({ value }) => PurchaseOrderStatusEnum[value],
    },
    { label: '领料状态', name: 'received_status', span: 9, type: 'text', textFormat: ({ value }) => PurchaseOrderReceivedStatusEnum[value] },
    // { label: '标签', name: 'tags', span: 9, type: 'tags', options: [] },
    {
      label: '标签',
      name: 'tags',
      span: 18,
      type: 'input',
      textFormat: ({ value }) => {
        return (value || []).map((text) => h('div', { class: 'easy-tag default mb-5 mr-10 ml-0!' }, text))
      },
    },

    { type: 'title', span: 24, title: '收货地址' },
    {
      label: '收货人',
      name: 'consignee',
      span: 9,
      type: 'input',
      required: true,
    },
    { label: '联系电话', name: 'phone_number', span: 9, type: 'input', required: true },
    { type: 'empty', span: 5 },
    {
      label: '收货地址',
      name: 'province',
      span: 6,
      type: 'select',
      required: true,
      placeholder: '请选择省份',
      options: areaCity.map((f) => ({ ...f, value: f.name, label: f.name })),
      linkage: ['city', 'area'],
    },
    {
      label: '',
      name: 'city',
      span: 6,
      type: 'select',
      required: true,
      placeholder: '请选择城市',
      options: [],
      linkage: 'area',
      linkageFn: ({ option, item }) => {
        item.options = (option?.children || []).map((f) => ({ ...f, value: f.name, label: f.name }))
      },
    },
    {
      label: '',
      name: 'area',
      span: 6,
      type: 'select',
      required: true,
      placeholder: '请选择区域',
      options: [],
      linkageFn: ({ option, item, name }) => {
        if (name === 'city') {
          item.options = (option?.children || []).map((f) => ({ ...f, value: f.name, label: f.name }))
        }
      },
    },
    { label: '详细地址', name: 'shipments_address', span: 18, type: 'input', required: true, placeholder: '请选择区域' },

    // { type: 'title', span: 24, title: '明细信息' },
    { slot: 'details', span: 24 },
    ...(id
      ? [
          // { type: 'title', span: 24, title: '入库明细' },
          { slot: 'booking', span: 24 },
        ]
      : []),
  ] as EasyFormItemProps[]

export const setColumns = () => [
  {
    name: '商品主图',
    key: 'image_url',
    align: 'center',
    width: 100,
    is_show: true,
    cellRender: {
      name: 'image',
    },
  },
  { name: '商品名称', key: 'sku_name', align: 'center', width: 200, is_show: true },
  { name: '商品编号', key: 'k3_sku_id', align: 'center', width: 200, is_show: true },
  { name: '聚水潭编号', key: 'jst_sku_id', align: 'center', width: 200, is_show: true },
  { name: 'SRS平台商品编码', key: 'srs_platform_prod_code', width: 150, is_show: true },
  { name: '颜色规格', key: 'type_specification', align: 'center', width: 100, is_show: true },
  { name: '款式编码', key: 'style_code', align: 'center', width: 100, is_show: true },
  { name: '材质', key: 'material_name', align: 'center', width: 100, is_show: true },
  {
    name: '换算值',
    key: 'conversion_value',
    align: 'center',
    width: 120,
    is_show: true,
    slot: 'number',
    params: {
      precision: 5,
    },
  },
  { name: '换算公式', key: 'conversion_formula', align: 'center', width: 100, is_show: true },
  { name: '采购单位', key: 'valuation_unit', align: 'center', width: 100, is_show: true },
  { name: '面积', key: 'k3_reference_acreage', align: 'center', width: 130, is_show: true },
  {
    name: '标准装箱数',
    key: 'packing_qty',
    align: 'center',
    width: 120,
    is_show: true,
    slot: 'number',
  },
  { name: '上次采购价', key: 'last_purchase_price', align: 'center', width: 150, is_show: true, slot: 'upPice' },
  {
    name: '采购数量',
    key: 'purchase_quantity',
    align: 'center',
    width: 120,
    is_show: true,
    slot: 'number',
  },
  {
    name: '赠品数量',
    key: 'gift_purchase_quantity',
    align: 'center',
    width: 120,
    is_show: true,
    slot: 'number',
  },
  { name: '采购总数', key: 'total_purchase_quantity', align: 'center', width: 120, is_show: true },
  { name: '成本数量', key: 'cost_num', align: 'center', width: 120, is_show: true },
  { name: '成本单位', key: 'cost_unit', align: 'center', width: 120, is_show: true },
  { name: '系统单价', key: 'system_unit_price', align: 'center', width: 100, is_show: true },
  {
    name: '成本材料单价',
    key: 'material_price',
    align: 'center',
    width: 120,
    is_show: true,
    slot: 'number',
    params: {
      precision: 8,
    },
  },
  {
    name: '成本加工费',
    key: 'process_fee',
    align: 'center',
    width: 120,
    is_show: true,
    slot: 'number',
    params: {
      precision: 8,
    },
  },
  { name: '成本合计单价', key: 'unit_price', align: 'center', width: 120, is_show: true },
  { name: '金蝶采购价', key: 'k3_reference_price', align: 'center', width: 100, is_show: true },
  { name: '税率', key: 'tax_rate_id', align: 'center', width: 130, is_show: true },
  {
    name: '成本含税单价',
    key: 'tax_unit_price',
    align: 'center',
    width: 100,
    is_show: true,
    formatter: ({ row }) => {
      return row.tax_unit_price ? `${row.tax_unit_price}（￥/${row.cost_unit}）` : ''
    },
  },
  {
    name: '采购含税单价',
    key: 'purchase_tax_price',
    align: 'center',
    width: 100,
    is_show: true,
    formatter: ({ row }) => {
      return row.purchase_tax_price ? `${row.purchase_tax_price}（￥/${row.valuation_unit}）` : ''
    },
  },
  {
    name: '商品总金额',
    key: 'sum_price',
    align: 'center',
    width: 120,
    is_show: true,
    slot: 'number',
    params: {
      precision: 2,
    },
  },
  { name: '税额', key: 'tax', align: 'center', width: 100, is_show: true },
  { name: '商品含税总金额', key: 'tax_sum_price', align: 'center', width: 120, is_show: true },
  { name: '商品优化总金额', key: 'optimize_sum_price', align: 'center', width: 100, is_show: true },
  {
    name: '开模费',
    key: 'mould_fees',
    align: 'center',
    width: 120,
    is_show: true,
    slot: 'number',
    params: {
      precision: 2,
    },
  },
  {
    name: '其他费用',
    key: 'other_fees',
    align: 'center',
    width: 120,
    is_show: true,
    slot: 'number',
    params: {
      precision: 2,
    },
  },
  {
    name: '优惠金额',
    key: 'discount_amount',
    align: 'center',
    width: 120,
    is_show: true,
    slot: 'number',
    params: {
      precision: 2,
    },
  },
  { name: '采购总金额', key: 'total_purchase_amount', align: 'center', width: 100, is_show: true },
  { name: '预付金额', key: 'prepaid_amount', align: 'center', width: 100, is_show: true },
  { name: '协议到货日期', key: 'predict_delivery_date', align: 'center', width: 150, is_show: true },
  { name: '采购已预约入库', key: 'purchase_scheduled_quantity', align: 'center', width: 120, is_show: true },
  { name: '赠品已预约入库', key: 'gift_scheduled_quantity', align: 'center', width: 120, is_show: true },
  { name: '已预约入库总数量', key: 'total_scheduled_quantity', align: 'center', width: 120, is_show: true },
  { name: '采购入库数量', key: 'purchase_inbound_quantity', align: 'center', width: 120, is_show: true },
  { name: '赠品入库数量', key: 'gift_inbound_quantity', align: 'center', width: 120, is_show: true },
  { name: '实际入库总数量', key: 'total_actual_inbound', align: 'center', width: 120, is_show: true },
  { name: '成品缺货数量', key: 'total_stockout_quantity', align: 'center', width: 120, is_show: true },
  { name: '备注', key: 'remark', align: 'left', width: 300, is_show: true },
  // { name: '单价是否反写价目表', key: 'reverse_price', align: 'center', width: 100, is_show: true },
  { name: '操作', key: 'operation', align: 'center', width: 200, fixed: 'right', is_show: true },
]

export const setInnerColumns = [
  { title: '申请单商品序号', field: 'sort', align: 'center', span: 1 },
  { title: '申请单编号', field: 'apply_order_number', align: 'center' },
  { title: '计划单编号', field: 'plan_number', align: 'center' },
  {
    title: '成品计划单号',
    field: 'finished_external_ids',
    formatter: 'join',
  },
  {
    title: '成品申请单号',
    field: 'finished_numbers',
    formatter: 'join',
  },
  {
    title: '最短周转天数',
    field: 'shortest_turnover_days',
    align: 'center',
    formatter: 'infinity',
  },
  { title: '最短可售库存', field: 'shortest_saleable_inventory', align: 'center' },
  { title: '最长缺货天数', field: 'longest_stockout_days', align: 'center' },
  {
    title: '申请单日期',
    field: 'demand_time',
    align: 'center',
    style: {
      color: 'rgb(0 0 0 / 50%)',
      fontWeight: 'bold',
    },
  },
  {
    title: '预计交期',
    field: 'planned_arrival_time',
    align: 'center',
    style: {
      color: 'rgb(0 0 0 / 50%)',
      fontWeight: 'bold',
    },
  },
  { title: '待执行采购数量', field: 'await_purchase_quantity', align: 'center' },
  { title: '采购数量', field: 'quantity', align: 'center' },
  { title: '已预约入库数量', field: 'scheduled_quantity', align: 'center' },
  { title: '实际入库总数', field: 'actual_inbound', align: 'center' },
  { title: '成品缺货数量', field: 'stockout_quantity', align: 'center' },
]

export const setBookingColumns = [
  { title: '序号', field: 'key', type: 'seq', align: 'center', width: 60, fixed: 'left' },
  { title: '商品主图', field: 'image_url', align: 'center', width: 70, cellRender: { name: 'image' } },
  { title: '商品名称', field: 'sku_name', align: 'center', width: 200 },
  { title: '商品编码', field: 'k3_sku_id', align: 'center', width: 150 },
  { title: '聚水潭编号', field: 'jst_sku_id', align: 'center', width: 150 },
  { title: 'SRS平台商品编码', field: 'srs_platform_prod_code', width: 150 },
  { title: '颜色规格', field: 'type_specification', align: 'center', width: 150 },
  { title: '采购单位', field: 'valuation_unit', align: 'center', width: 100 },
  { title: '预约入库单编号', field: 'booking_number', align: 'center', width: 120 },
  { title: '收料仓库', field: 'warehouse_name', align: 'center', width: 120 },
  {
    title: '预约入库单状态',
    field: 'audit_status',
    align: 'center',
    width: 120,
    formatter: ({ row }) => Enum2Map(BookingOrderAuditStatusEnum)[row.audit_status],
  },
  { title: '采购本次预约数量', field: 'purchase_scheduled_quantity', align: 'center', width: 120 },
  { title: '赠品本次预约数量', field: 'gift_scheduled_quantity', align: 'center', width: 120 },
  { title: '本次预约入库总数量', field: 'scheduled_quantity', align: 'center', width: 140 },
  { title: '本次发货箱数', field: 'box_num', align: 'center', width: 100 },
  { title: '预约入库时间', field: 'create_at', align: 'center', width: 100 },
  { title: '物流公司', field: 'logistics_company', align: 'center', width: 100 },
  { title: '物流单号', field: 'tracking_number', align: 'center', width: 100 },
  { title: '预计到货时间', field: 'scheduled_arrival_time', align: 'center', width: 100 },
  { title: '入库单编号', field: 'io_id', align: 'center', width: 100 },
  { title: '入库状态', field: 'status_str', align: 'center', width: 100 },
  { title: '采购入库数量', field: 'purchase_quantity', align: 'center', width: 100 },
  { title: '赠品入库数量', field: 'gift_purchase_quantity', align: 'center', width: 100 },
  { title: '实际入库总数', field: 'total_actual_inbound', align: 'center', width: 100 },
  { title: '入库时间', field: 'io_date', align: 'center', width: 150 },
]

// 付款明细列配置
export const setPaymentColumns = [
  { key: 'key', title: '序号', width: 60, dataIndex: 'key', is_show: true },
  { key: 'payment_type', title: '付款类型', width: 100, dataIndex: 'payment_type', customRender: ({ text }) => PaymentTypeEnum[Number(text)] || text, is_show: true },
  { key: 'source_type', title: '类型', width: 100, dataIndex: 'source_type', customRender: ({ text }) => PaymentOrderSourceTypeEnum[Number(text)] || text, is_show: true },
  { key: 'payment_order_number', title: '付款单号', width: 150, dataIndex: 'payment_order_number', is_show: true },
  { key: 'payable_order_number', title: '应付单号', width: 150, dataIndex: 'payable_order_number', is_show: true },
  { key: 'prepayment_amount', title: '本次付款金额', width: 120, dataIndex: 'prepayment_amount', is_show: true },
  { key: 'deduct_amount', title: '扣款金额', width: 100, dataIndex: 'deduct_amount', is_show: true },
  { key: 'the_actual_amount', title: '本次实付金额', width: 120, dataIndex: 'the_actual_amount', is_show: true },
  {
    key: 'order_status',
    title: '状态',
    width: 100,
    dataIndex: 'order_status',
    customRender: ({ text }) => PaymentOrderStatusEnum[Number(text)] || text,
    is_show: true,
  },
  {
    key: 'payment_status',
    title: '打款状态',
    width: 100,
    dataIndex: 'payment_status',
    customRender: ({ text }) => PaymentStatusEnum[Number(text)] || text,
    is_show: true,
  },
  { key: 'expected_payment_date', title: '期望付款日期', width: 120, dataIndex: 'expected_payment_date', is_show: true },
]

// 退款明细列配置
export const setRefundColumns = [
  { key: 'key', title: '序号', width: 60, dataIndex: 'key', is_show: true },
  { key: 'image_url', title: '商品主图', width: 80, dataIndex: 'image_url', is_show: true },
  { key: 'sku_name', title: '商品名称', width: 200, dataIndex: 'sku_name', is_show: true },
  { key: 'sku_id', title: '商品编码', width: 200, dataIndex: 'sku_id', is_show: true },
  { key: 'jst_sku_id', title: '聚水潭商品编码', width: 200, dataIndex: 'jst_sku_id', is_show: true },
  { key: 'srs_platform_prod_code', title: 'SRS平台商品编码', width: 150, dataIndex: 'srs_platform_prod_code', is_show: true },
  { key: 'type_specification', title: '颜色规格', width: 100, dataIndex: 'type_specification', is_show: true },
  { key: 'total_purchase_quantity', title: '采购总数量', width: 100, dataIndex: 'total_purchase_quantity', is_show: true },
  { key: 'total_actual_inbound', title: '实际到货总数量', width: 120, dataIndex: 'total_actual_inbound', is_show: true },
  { key: 'purchase_return_application_number', title: '退库申请单号', width: 150, dataIndex: 'purchase_return_application_number', is_show: true },
  { key: 'jst_return_no', title: '退货单号', width: 150, dataIndex: 'jst_return_no', is_show: true },
  {
    key: 'application_type',
    title: '退库类型',
    width: 100,
    dataIndex: 'application_type',
    customRender: ({ text }) => PurchaseReturnApplicationTypeEnum[text] || text,
    is_show: true,
  },
  {
    key: 'return_reason_type',
    title: '退库原因',
    width: 150,
    dataIndex: 'return_reason_type',
    customRender: ({ text }) => ReturnReasonTypeEnum[text] || text,
    is_show: true,
  },
  { key: 'return_quantity', title: '退库申请数量', width: 100, dataIndex: 'return_quantity', is_show: true },
  { key: 'actual_return_quantity', title: '退库数量', width: 100, dataIndex: 'actual_return_quantity', is_show: true },
  { key: 'return_amount', title: '退款金额', width: 100, dataIndex: 'return_amount', is_show: true },
  { key: 'refund_warehouse_status', title: '退库状态', width: 100, dataIndex: 'refund_warehouse_status', customRender: ({ text }) => PurchaseOrderStatusEnum[Number(text)] || text, is_show: true },
]
