<template>
  <a-drawer title="选择材质" width="55vw" :visible="visible" @close="handleClose" :maskClosable="false" destroyOnClose>
    <div class="flex h-full overflow-auto">
      <div class="p-16px h-full overflow-auto w35vw">
        <a-space class="mb-12">
          <a-input placeholder="材质名称" v-model:value="queryParams.keyword" :maxlength="200" />
          <a-button type="primary" @click="handleSearch">查询</a-button>
        </a-space>
        <vxe-table
          v-if="leftVisible"
          :border="true"
          ref="productTableRef"
          size="mini"
          :row-config="{ isHover: true, keyField: 'value', height: 40 }"
          :custom-config="{ mode: 'popup' }"
          :data="data"
          :show-overflow="true"
          :show-header-overflow="true"
          :show-footer-overflow="true"
          :column-config="{ resizable: true }"
          class="tableBoxwidth"
          :checkbox-config="{ reserve: true }"
          min-height="0"
          stripe
          v-bind="$attrs"
          @checkbox-all="selectChangeEvent"
          @checkbox-change="selectChangeEvent"
        >
          <vxe-column type="checkbox" field="checkbox" width="50" fixed="left"></vxe-column>
          <slot name="column">
            <template v-for="i in tableKey" :key="i.field">
              <vxe-column v-bind="i">
                <template v-if="$slots[String(i.field)]" #default="attr">
                  <slot :name="i.field" v-bind="attr" :item="i" />
                </template>
              </vxe-column>
            </template>
          </slot>
        </vxe-table>
        <div class="paginationBox">
          <div class="pagination">
            <a-pagination
              show-quick-jumper
              :total="total"
              show-size-changer
              v-model:current="queryParams.page"
              v-model:page-size="queryParams.pageSize"
              :page-size-options="['10', '20', '30', '40', '50']"
              @change="handlePageChange"
              size="small"
            >
              <template #buildOptionText="props">
                <span>{{ props.value }}条/页</span>
              </template>
            </a-pagination>
          </div>
          <div class="totalBox">
            <div class="text">总数:</div>
            <div class="total">{{ total }}</div>
          </div>
        </div>
      </div>
      <div class="flex-1 overflow-y-auto p-16 text-black">
        <div class="flex mb-12 h30px line-height-30px h-30px">
          <div class="text-16px">已选{{ selectProductList.length }}个商品</div>
          <a-button class="ml-auto" type="primary" @click="cleanCheck">清空</a-button>
        </div>
        <vxe-table
          v-if="leftVisible"
          :border="true"
          ref="selectTableRef"
          size="mini"
          :row-config="{ isHover: true, keyField: 'value', height: 40 }"
          :custom-config="{ mode: 'popup' }"
          :data="selectProductList"
          :show-overflow="true"
          :show-header-overflow="true"
          :show-footer-overflow="true"
          :column-config="{ resizable: true }"
          class="tableBoxwidth"
          :checkbox-config="{ reserve: true }"
          min-height="0"
          stripe
          v-bind="$attrs"
        >
          <vxe-column type="seq" width="50" fixed="left"></vxe-column>
          <slot name="column">
            <template v-for="i in selectTableKey" :key="i.field">
              <vxe-column v-bind="i">
                <template v-if="$slots[String(i.field)]" #default="attr">
                  <slot :name="i.field" v-bind="attr" :item="i" />
                </template>
              </vxe-column>
            </template>
          </slot>
        </vxe-table>
      </div>
    </div>
    <template #footer>
      <a-space>
        <a-button type="primary" @click="handleSelectProduct">确定</a-button>
        <a-button @click="handleClose">取消</a-button>
      </a-space>
    </template>
  </a-drawer>
</template>

<script setup lang="ts">
import { VxeTableInstance } from 'vxe-table'

import { GetProductCaizhiSelect } from '@/servers/Purchaseprice'

const productTableEl = useTemplateRef<VxeTableInstance>('productTableRef')

const checkRowKeys = ref<any[]>([])

const emits = defineEmits(['selectMaterial'])

const visible = ref(false)
const leftVisible = ref(false)

const data = ref([])

const total = ref(0)

const selectProductList = ref<any[]>([])

const queryParams = ref<any>({
  page: 1,
  pageSize: 10,
})

const tableKey = ref([
  {
    field: 'label',
    title: '材质',
  },
])

const selectTableKey = ref([
  {
    field: 'label',
    title: '材质',
  },
])

// 打开
const open = (ids?: number[]) => {
  checkRowKeys.value = []
  if (ids instanceof Array && ids.length > 0) {
    checkRowKeys.value = ids
  }
  cleanCheck()
  visible.value = true
  leftVisible.value = true
}
// 关闭
const handleClose = () => {
  visible.value = false
  queryParams.value = {
    page: 1,
    pageSize: 10,
  }
}

// 获取商品列表
const getProductList = async () => {
  for (const key in queryParams.value) {
    if (!queryParams.value[key]) {
      queryParams.value[key] = null
    }
  }
  const res = await GetProductCaizhiSelect(queryParams.value)
  data.value = res.data.list
  total.value = res.data.total
  setTimeout(() => {
    if (selectProductList.value.length == 0) {
      const $table = productTableEl.value
      $table?.clearCheckboxRow()
    }
  }, 20)
}
// 分页
const handlePageChange = (page: number, pageSize: number) => {
  queryParams.value.page = page
  queryParams.value.pageSize = pageSize
  getProductList()
}

// 查询
const handleSearch = () => {
  queryParams.value.page = 1
  getProductList()
}
// 选中事件
const selectChangeEvent = () => {
  const $table = productTableEl.value
  // 当前页选中的数据
  const currentSelectedRows = $table?.getCheckboxRecords() || []
  // 其他页选中的数据
  const otherSelectedRows = $table?.getCheckboxReserveRecords() || []
  selectProductList.value = [...currentSelectedRows, ...otherSelectedRows]
}

const cleanCheck = async () => {
  selectProductList.value = []
  leftVisible.value = false
  await getProductList()
  leftVisible.value = true
}
// 确定
const handleSelectProduct = () => {
  emits('selectMaterial', selectProductList.value)
  handleClose()
}

defineExpose({
  open,
})
</script>

<style scoped lang="scss">
.paginationBox {
  display: flex;
  align-items: center;
  margin-top: 0.83rem;

  .totalBox {
    display: flex;
    align-items: flex-end;
    margin-left: 20px;
    color: #000;

    .text {
      margin-right: 8px;
      font-size: 14px;
    }

    .total {
      font-size: 16px;
      color: #1890ff;
    }
  }
}
</style>
