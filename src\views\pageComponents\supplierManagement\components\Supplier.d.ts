export interface SupplierInfo {
  id?: number // 数据 id
  supplier_id?: number | null // 供应商id
  supplier_name?: string // 供应商名称
  supplier_type: number // 供应商类型
  k3_supplier_id: number | null // 金蝶供应商id
  k3_supplier_number: number | null // 金蝶供应商编号
  purchase_nature: number | null // 采购性质
  supplier_group_id: number | null // 供应商分组
  supplier_group_name?: string // 供应商分组名称
  website?: string // 公司网址/1688店铺网址
  shop_name?: string // 1688店铺名字
  carriage_mode: number | null // 承运方式
  describes: string // 描述说明
  business_license_file_ids: number[] // 营业执照/资质证书等
  business_license_files?: {
    id: number
    name: string
    url: string
  }[] // 营业执照/资质证书等
  status: number // 供应商状态
  audit_status?: number // 审核状态
  audit_status_time?: string // 审核状态时间
  creator_id?: number // 创建人id
  creator_name?: string // 创建人名称
  create_at?: string // 创建时间
  audit_id?: number // 审核人id
  audit_name?: string // 审核人名称
  modifier_id?: number | null // 修改人id
  modifier_name?: string | null // 修改人名称
  modified_at?: string | null // 修改时间
  remark?: string | null // 备注

  settlement_type: number | null // 结算方式
  invoice_type: number | null // 发票类型
  default_tax_rate_id: number | null // 默认税率
  default_tax_rate?: string
  supplier_company_info_list: SubCompanyMessage[]
  supplier_contact_info_list: ContactMessage[]
  supplier_finance_info_list: FinancialMessage[]
  supplier_shipments_info_list: DeliveryMessage[]
  supplier_user_info_list: UserMessage[]
  is_review: boolean
  is_update_finance_info?: boolean
}

export interface SubCompanyMessage {
  id?: number // 数据id
  company_supplier_id: number | null // 子公司id
  company_supplier_name: string | null // 子公司名称
  supplier_id: number | null // 供应商id
  k3_supplier_id: number | null // 金蝶子公司id
  jst_supplier_id: number | null // 金蝶子公司id
  buyer_ids: number[] | null // 采购员id
  buyer_oa_id: number | null // 采购员oa id
  buyer_name?: string | null // 采购员名称
  data_status: number // 数据状态
  is_default?: boolean // 是否默认
  is_view?: boolean // 是否可编辑
  website?: string | null // 公司网址/1688店铺网址
  shop_name?: string | null // 1688店铺名字
  supplier_category?: string | null // 供应商分类
}

export interface ContactMessage {
  id: number // 联系人id
  type: number // 联系人类型
  supplier_name: string | null // 供应商名称
  supplier_id: number | null // 供应商id
  name: string // 联系人姓名
  job: string // 联系人职务
  phone_number: string // 联系电话
  mobile_phone_number: string // 手机号码
  fax_no: string // 传真号码
  email: string
  province: string | null // 省
  city: string | null // 市
  area: string | null // 区
  address: string // 地址
  is_default: boolean
  data_status: number // 数据状态
  is_view?: boolean // 是否可编辑
}

export interface FinancialMessage {
  id: number | null // 财务信息id
  type: number // 财务信息类型
  supplier_name: string | null // 供应商名称
  supplier_id: number | null // 供应商id
  account_type: number | null // 账户类型
  account_name: string // 账户名称
  collection_card_number: string // 收款卡号
  collection_bank: string // 收款银行
  collection_account_certificate_file_ids: number[] // 收款账户证书文件id
  collection_account_certificate_files?: {
    id: number
    name: string
    url: string
  }[] // 收款账户证书文件
  remark: string // 备注
  is_default: boolean // 是否默认
  data_status: number // 数据状态
  is_view?: boolean // 是否可编辑
}

export interface DeliveryMessage {
  id: number | null // 发货信息id
  type: number // 发货信息类型
  supplier_name: string | null // 供应商名称
  supplier_id: number | null // 供应商id
  province: string | null // 省
  city: string | null // 市
  area: string | null // 区
  shipments_address: string // 发货地址
  is_default: boolean // 是否默认
  data_status: number // 数据状态
  is_view?: boolean // 是否可编辑
}

export interface UserMessage {
  id: number | null // 用户信息id
  supplier_id: number | null // 供应商id
  supplier_name: string | null // 供应商名称
  real_name: string // 真实姓名
  user_name: string // 用户名
  password: string // 密码
  phone_number: string // 手机号码
  status: number // 状态
  data_status: number // 数据状态
  is_view?: boolean // 是否可编辑
}

// 付款单收款信息
export interface PaymentVoucherCollection {
  id?: number | null
  payment_type: number | null // 付款类型
  settlement_method: string | null // 结算方式
  expected_payment_date: string | null // 期望付款日期
  the_actual_amount: number // 本次实付金额supplier_subsidiary_id
  receipt_method: number | null // 收款方式
  account_type: number | null // 账户类型
  deduct_amount: number // 扣款金额
  receipt_account_name: string | null // 收款账户名称
  receipt_card_number: string // 收款账号
  remark: string | null // 备注
  supplier_subsidiary_name: string // 供应商子公司
  supplier_subsidiary_id: number | null // 供应商子公司ID
  // attachment?: {
  //   id: number
  //   name: string
  //   url: string
  // }[] // 附件
  // attachment_file_ids: number[]
  receipt_bank: string | null // 收款银行
  common_private_account: number | null // 付款账户类型
  payment_account_name: string | null // 付款账号名称
  payment_company: string | null // 付款公司
  payment_account: string | null // 付款账号
  payment_account_id: string | null // 付款账号ID
  payment_operator_id: number | null // 打款操作人ID
  payment_operator_name: string | null // 打款操作人
  treasury_payment_serial_number: string | null // 财资云付款流水号
  // cashier_attachment?: {
  //   id: number
  //   name: string
  //   url: string
  // }[]
  payment_order_detail: Array<any>
}
