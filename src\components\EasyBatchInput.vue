<template>
  <div class="easy-batch-input">
    <a-input :placeholder="realInputPlaceholder" v-model:value="model" v-bind="$attrs">
      <template #suffix>
        <span class="iconfont icon-chazhao_find text-14px c-#000 icon-suffix" @click="showDlg = true"></span>
      </template>
    </a-input>
    <a-modal v-model:open="showDlg" :title="`批量输入`" width="700px" @ok="handleOk" @cancel="showDlg = false">
      <div style="display: flex; align-items: flex-start">
        <div style="width: 120px; margin-right: 8px; font-size: 12px; line-height: 32px; text-align: right">{{ props.label }}：</div>
        <a-textarea v-model:value="batchText" :placeholder="realTextareaPlaceholder" :rows="rows" :maxlength="maxlength" style="flex: 1" />
      </div>
      <template #footer>
        <a-button @click="clearAll">清 空</a-button>
        <a-button @click="showDlg = false">取 消</a-button>
        <a-button type="primary" @click="handleOk">确 认</a-button>
      </template>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { watch, computed } from 'vue'

const model = defineModel<string>()
const props = defineProps({
  label: { type: String, default: '' },
  placeholder: { type: String, default: '' },
  inputPlaceholder: { type: String, default: '' },
  textareaPlaceholder: { type: String, default: '' },
  rows: { type: Number, default: 8 },
  maxlength: { type: Number, default: 2000 },
})
const showDlg = ref(false)
const batchText = ref('')

const realInputPlaceholder = computed(() => props.inputPlaceholder || props.placeholder || (props.label ? `请输入${props.label}` : '请输入'))

const realTextareaPlaceholder = computed(
  () => props.textareaPlaceholder || props.placeholder || (props.label ? `多个${props.label}，以逗号分隔或每行一个${props.label}` : '多个值，以逗号分隔或每行一个'),
)

watch(
  () => showDlg.value,
  (val) => {
    if (val) {
      batchText.value = (typeof model.value === 'string' ? model.value : '') || ''
    }
  },
)

function handleOk() {
  if (!batchText.value.trim()) {
    showDlg.value = false
    return
  }
  // 支持逗号、换行、中文逗号、分号等分隔
  const val = batchText.value
    .replace(/[，；;\n]/g, ',')
    .split(',')
    .map((s) => s.trim())
    .filter(Boolean)
    .join(',')
  model.value = val
  showDlg.value = false
  batchText.value = ''
}

function clearAll() {
  batchText.value = ''
  model.value = ''
}
</script>

<style scoped lang="scss">
.easy-batch-input :deep(.ant-input-affix-wrapper) {
  padding: 0 8px !important;
}
</style>
