<template>
  <div class="flex-1 overflow-hidden">
    <vxe-table
      :border="true"
      ref="tableRef"
      size="mini"
      :row-config="{ keyField: keyField || '_X_ROW_KEY', isHover: true, height: rowHeight }"
      :custom-config="{ mode: 'popup' }"
      :data="data"
      :show-overflow="true"
      :show-header-overflow="true"
      :show-footer-overflow="true"
      :column-config="{ resizable: true }"
      class="tableBox"
      :checkbox-config="{ reserve: true }"
      min-height="0"
      height="100%"
      stripe
      v-bind="$attrs"
      :row-style="rowStyle"
    >
      <slot name="column">
        <slot name="append"></slot>
        <template v-for="i in tableKey" :key="i.field">
          <vxe-column v-bind="i">
            <template v-if="$slots[String(i.field)]" #default="attr">
              <slot :name="i.field" v-bind="attr" :item="i" />
            </template>
          </vxe-column>
        </template>
      </slot>
    </vxe-table>
  </div>
  <!-- 分页器 -->
  <div v-if="showPagination" class="pagination-container flex justify-end mt-10">
    <slot name="pagination" :pagination="paginationConfig">
      <a-pagination v-bind="paginationConfig" @change="handlePageChange" @showSizeChange="handlePageSizeChange" />
    </slot>
  </div>
</template>

<script setup lang="ts">
import { VxeColumnProps, VxeTableInstance } from 'vxe-table'

const tableRef = ref<VxeTableInstance>()

const props = withDefaults(
  defineProps<{
    data: any[]
    tableKey: VxeColumnProps[] | any[]
    keyField?: string
    type?: number
    rowHeight?: number
    showPagination?: boolean
    currentPage?: number
    pageSize?: number
    total?: number
    paginationConfig?: Record<string, any>
  }>(),
  {
    data: () => [],
    tableKey: () => [],
    rowHeight: 40,
    type: 1,
    showPagination: true,
    currentPage: 1,
    pageSize: 20,
    total: 0,
  },
)

const emit = defineEmits<{
  pageChange: [page: number, pageSize: number]
  pageSizeChange: [current: number, size: number]
}>()

// 计算分页配置
const paginationConfig = computed(() => {
  const defaultConfig = {
    current: props.currentPage,
    pageSize: props.pageSize,
    total: props.total,
    showQuickJumper: true,
    showSizeChanger: true,
    pageSizeOptions: ['20', '50', '100', '250', '500'],
    size: 'small' as const,
    showTotal: (total: number) => `共 ${total} 条`,
  }

  return props.paginationConfig ? { ...defaultConfig, ...props.paginationConfig } : defaultConfig
})

const checkItemsArr = ref([] as any[])

const colors = ref(['white', '#f0f4ff']) // 两种交替颜色

const rowStyle = ({ row }) => {
  const name = props.type == 1 ? row.sku_name : row.material_name
  const arr = Array.from(new Set(props.data.map((item) => item[props.type == 1 ? 'sku_name' : 'material_name'])))
  const index = arr.findIndex((item) => item === name) % 2

  return {
    backgroundColor: colors.value[index],
  }
}

// 分页事件处理
const handlePageChange = (page: number, pageSize: number) => {
  emit('pageChange', page, pageSize)
}

const handlePageSizeChange = (current: number, size: number) => {
  emit('pageSizeChange', current, size)
}

defineExpose({ checkItemsArr, tableRef })
</script>
<style lang="scss" scoped>
.tableBox {
  position: relative;
  flex: 1;
  border-bottom: 1px solid #ddd;
}

.required {
  color: #f56c6c;
}

.required-text {
  margin-right: 4px;
}
</style>
