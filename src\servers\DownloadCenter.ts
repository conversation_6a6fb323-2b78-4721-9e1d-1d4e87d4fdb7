import { request } from './request'

// 下载状态
export enum DownloadTaskStateEnum {
  '未开始' = 0,
  '进行中' = 1,
  '完成' = 2,
  '失败' = 3,
  '过期' = 4,
}

// 获取下载任务列表
export const GetDownloadTaskList = (data) => {
  return request({ url: '/api/DownloadCenter/GetDownloadTaskList', data }, 'POST')
}

// 获取下载任务信息
export const GetDownloadTaskInfos = (data) => {
  return request({ url: '/api/DownloadCenter/GetDownloadTaskInfos', data }, 'POST')
}

// 获取下载任务预览
export const Preview = (data) => {
  return request({ url: '/api/DownloadCenter/Preview', data }, 'GET')
}

// 获取当前登录用户未下载文件的任务数
export const GetUserNotDownloadTaskNum = () => {
  return request({ url: '/api/DownloadCenter/GetUserNotDownloadTaskNum' }, 'GET')
}

// 下载文件by taskId
export const DownloadFile = (data) => {
  return request({ url: `/api/DownloadCenter/DownloadFile?taskId=${data.taskId}`, data }, 'GET')
}
