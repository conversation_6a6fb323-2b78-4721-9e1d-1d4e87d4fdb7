import { defineStore } from 'pinia'

export const useBillVerificationStore = defineStore('billVerification', () => {
  // 状态 - 从 localStorage 初始化或使用默认值
  const selectedDetailIds = ref<number[]>([])
  const supplierId = ref<string | null>(null)
  const supplierName = ref<string | null>(null)
  const companySupplierId = ref<string | null>(null)
  const companySupplierName = ref<string | null>(null)

  // 当前会话ID
  const currentSessionId = ref<string | null>(null)

  // 初始化状态（从localStorage恢复）
  const initFromStorage = (sessionId?: string) => {
    currentSessionId.value = sessionId || null
    const prefix = sessionId ? `billVerification_${sessionId}_` : 'billVerification_'

    selectedDetailIds.value = JSON.parse(localStorage.getItem(`${prefix}selectedDetailIds`) || '[]')
    supplierId.value = localStorage.getItem(`${prefix}supplierId`) || null
    supplierName.value = localStorage.getItem(`${prefix}supplierName`) || null
    companySupplierId.value = localStorage.getItem(`${prefix}companySupplierId`) || null
    companySupplierName.value = localStorage.getItem(`${prefix}companySupplierName`) || null
  }

  // 设置选中的采购明细
  const setSelectedDetails = (
    detailIds: number[],
    supplierInfo: {
      supplierId: string
      supplierName: string
      companySupplierId: string | null
      companySupplierName: string | null
    },
    sessionId?: string,
  ) => {
    selectedDetailIds.value = detailIds
    supplierId.value = supplierInfo.supplierId
    supplierName.value = supplierInfo.supplierName
    companySupplierId.value = supplierInfo.companySupplierId
    companySupplierName.value = supplierInfo.companySupplierName

    // 保存到 localStorage
    const prefix = sessionId ? `billVerification_${sessionId}_` : 'billVerification_'
    localStorage.setItem(`${prefix}selectedDetailIds`, JSON.stringify(detailIds))
    localStorage.setItem(`${prefix}supplierId`, supplierInfo.supplierId)
    localStorage.setItem(`${prefix}supplierName`, supplierInfo.supplierName)
    if (supplierInfo.companySupplierId) {
      localStorage.setItem(`${prefix}companySupplierId`, supplierInfo.companySupplierId)
    } else {
      localStorage.removeItem(`${prefix}companySupplierId`)
    }
    if (supplierInfo.companySupplierName) {
      localStorage.setItem(`${prefix}companySupplierName`, supplierInfo.companySupplierName)
    } else {
      localStorage.removeItem(`${prefix}companySupplierName`)
    }
  }

  // 清除状态
  const clearState = (sessionId?: string) => {
    selectedDetailIds.value = []
    supplierId.value = null
    supplierName.value = null
    companySupplierId.value = null
    companySupplierName.value = null

    // 清除 localStorage
    const prefix = sessionId ? `billVerification_${sessionId}_` : 'billVerification_'
    localStorage.removeItem(`${prefix}selectedDetailIds`)
    localStorage.removeItem(`${prefix}supplierId`)
    localStorage.removeItem(`${prefix}supplierName`)
    localStorage.removeItem(`${prefix}companySupplierId`)
    localStorage.removeItem(`${prefix}companySupplierName`)
  }

  // 强制重置状态（不恢复localStorage数据）
  const forceReset = () => {
    selectedDetailIds.value = []
    supplierId.value = null
    supplierName.value = null
    companySupplierId.value = null
    companySupplierName.value = null
    currentSessionId.value = null

    // 清理所有相关的localStorage数据
    const keys = Object.keys(localStorage)
    keys.forEach((key) => {
      if (key.startsWith('billVerification_')) {
        localStorage.removeItem(key)
      }
    })
  }

  // 获取供应商信息（用于表单）
  const getFormSupplierInfo = computed(() => ({
    supplier_id: supplierId.value,
    supplier_name: supplierName.value,
    company_supplier_id: companySupplierId.value,
    company_supplier_name: companySupplierName.value,
    supplier_id_alias: supplierName.value,
    company_supplier_id_alias: companySupplierName.value,
  }))

  // 是否有选中的数据
  const hasSelectedData = computed(() => selectedDetailIds.value.length > 0)

  return {
    // 状态
    selectedDetailIds,
    supplierId,
    supplierName,
    companySupplierId,
    companySupplierName,

    // 方法
    setSelectedDetails,
    clearState,
    forceReset,
    initFromStorage,

    // 计算属性
    getFormSupplierInfo,
    hasSelectedData,
  }
})
