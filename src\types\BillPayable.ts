// 审核状态
export enum BillPayableOrderAuditEnum {
  待提审 = 10,
  已通过 = 90,
  已拒绝 = 95,
}

// 结算状态
export enum BillPayableOrderSettlementEnum {
  // 未付款 = 1,
  // 部分付款 = 2,
  // 已付款 = 3,
  未对账 = 0,
  未结算 = 1,
  部分结算 = 2,
  已结算 = 3,
}

// 开票类型
export enum InvoiceTypeEnum {
  增值税专用发票 = 1,
  普通发票 = 2,
  不开发票 = 3,
}

// 供应商应付类型
export enum SupplierLiquidateOrderTypeEnum {
  采购入库 = 1,
  采购退库 = 2,
}

// 对账状态
export enum ReconciliationStatusEnum {
  未对账 = 1,
  已对账 = 2,
}

// 应付单状态
export enum BillPayableOrderStatusEnum {
  未生成 = 1,
  已生成 = 2,
}
