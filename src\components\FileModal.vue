<template>
  <div class="w-full">
    <a-button size="small" type="link" danger v-if="isReload" @click="handleReload" :loading="loading">
      <div class="text-xs">重新加载</div>
    </a-button>
    <a-button class="w-full overflow-hidden" size="small" type="link" v-else-if="list?.length" @click="handleClick">
      <div class="flex items-center text-xs w-full">
        {{ text ? text : '查看附件' }}
        <span class="search-icon ml-5" v-if="!text"></span>
      </div>
    </a-button>
    <a-button size="small" type="link" disabled v-else>
      <div class="text-xs">{{ text ? text : '无附件' }}</div>
    </a-button>

    <a-modal v-model:open="visible" title="附件" :bodyStyle="{ padding: 0 }" :footer="null">
      <div class="p-4 pt-0">
        <a-upload :fileList="fileList" @download="downloadFile" @preview="previewFile" :show-upload-list="{ showDownloadIcon: true, showRemoveIcon: false }"></a-upload>
      </div>
    </a-modal>

    <a-image
      class="!size-0"
      :style="{ display: 'none' }"
      :preview="{
        visible: imgVisible,
        onVisibleChange: setImgVisible,
      }"
      :src="preview"
    />
  </div>
</template>

<script lang="ts" setup>
import type { UploadFile } from 'ant-design-vue'
import { message } from 'ant-design-vue'

import { GetFileUrl, GetDownloadUrl } from '@/servers/Common'

interface FileList {
  id: string
  name: string
  url: string
  status: string
  oa_file_id?: string
  file_name?: string
  file_path?: string
  org_name?: string
}

const list = defineModel<FileList[]>('list', { required: true })
const text = defineModel<string>('text')
const props = defineProps({
  attr: {
    default: null as string | null,
  },
  from: {
    default: null as string | null,
  },
  customApi: {
    default: null as string | null,
  },
  customParam: {
    default: null as string | null,
  },
})

const isReload = computed(() => {
  if (props.attr && typeof props.attr === 'string') {
    if (props.attr.split(',').length !== list.value.length) {
      return true
    }

    let flag = false
    props.attr.split(',').forEach((v) => {
      if (
        !list.value
          .map((v) => v.oa_file_id)
          .join(',')
          .includes(v)
      ) {
        flag = true
      }
    })
    return flag
  }
  return false
})

const fileList = computed<UploadFile[]>(() => {
  if (props.from == 'OWM') {
    return list.value.map((item) => ({
      id: item.id,
      uid: item.id,
      name: item.file_name || item.name || '',
      url: item.file_path || '',
      status: 'done' as const,
    }))
  }
  return list.value.map((item) => ({
    id: item.id,
    uid: item.id,
    name: item.org_name || item.name || '',
    url: 'test',
    status: 'done' as const,
  }))
})

const visible = ref(false)
const handleClick = () => {
  visible.value = true
}

const preview = ref('')
const imgVisible = ref(false)
const setImgVisible = (value: boolean) => {
  imgVisible.value = value
}

const previewFile = async (file: UploadFile) => {
  if (file.url != 'test') {
    if (file.name?.endsWith('.png') || file.name?.endsWith('.jpg') || file.name?.endsWith('.jpeg')) {
      preview.value = file.url || ''
      nextTick(() => {
        setImgVisible(true)
      })
    } else {
      preview.value = file.url || ''
      window.open(preview.value, '_blank')
    }
    return
  }

  // 使用自定义API或默认API
  if (props.customApi) {
    try {
      const fileUrl = `${props.customApi}?${props.customParam || 'id'}=${file.uid}`

      // 对于自定义API，根据文件类型决定预览方式
      if (file.name?.endsWith('.png') || file.name?.endsWith('.jpg') || file.name?.endsWith('.jpeg')) {
        // 图片文件直接在当前页面预览
        preview.value = fileUrl
        nextTick(() => {
          setImgVisible(true)
        })
      } else {
        // 其他文件类型在新窗口打开
        window.open(fileUrl, '_blank')
      }
    } catch (error) {
      console.error('Custom API error:', error)
      message.error('预览文件失败')
    }
  } else {
    try {
      let res: any = {}
      if (/(mp4|mp3|avi|mov|wmv|flv|webm|mkv|m4v|3gp|rmvb|wav|aac|ogg|flac|wma|m4a)$/i.test(file.name)) {
        res = await GetDownloadUrl({ fileId: file.uid })
      } else {
        res = await GetFileUrl({ fileId: file.uid })
      }
      const fileUrl = res?.data

      if (file.name?.endsWith('.png') || file.name?.endsWith('.jpg') || file.name?.endsWith('.jpeg')) {
        preview.value = fileUrl
        nextTick(() => {
          setImgVisible(true)
        })
      } else {
        preview.value = fileUrl
        window.open(preview.value, '_blank')
      }
    } catch (error) {
      console.error('GetFileUrl error:', error)
      message.error('预览文件失败')
    }
  }
}

const downloadFile = async (file: UploadFile) => {
  if (file.url != 'test') {
    preview.value = file.url || ''
    window.open(preview.value, '_blank')
    return
  }

  // 使用自定义API或默认API
  if (props.customApi) {
    try {
      const fileUrl = `${props.customApi}?${props.customParam || 'id'}=${file.uid}`

      // 对于自定义API，根据文件类型决定处理方式
      if (file.name?.endsWith('.png') || file.name?.endsWith('.jpg') || file.name?.endsWith('.jpeg')) {
        // 图片文件直接在当前页面预览
        preview.value = fileUrl
        nextTick(() => {
          setImgVisible(true)
        })
      } else {
        // 其他文件类型在新窗口打开
        window.open(fileUrl, '_blank')
      }
    } catch (error) {
      console.error('Custom API error:', error)
      message.error('下载文件失败')
    }
  } else {
    try {
      let res: any = {}
      if (/(\.mp4|\.mp3|\.avi|\.mov|\.wmv|\.flv|\.webm|\.mkv|\.m4v|\.3gp|\.rmvb|\.wav|\.aac|\.ogg|\.flac|\.wma|\.m4a)$/.test(file.name)) {
        res = await GetDownloadUrl({ fileId: file.uid })
      } else {
        res = await GetFileUrl({ fileId: file.uid })
      }
      const fileUrl = res.data
      window.open(fileUrl, '_blank')
    } catch (error) {
      console.error('GetFileUrl error:', error)
      message.error('下载文件失败')
    }
  }
}

const loading = ref(false)

const handleReload = () => {
  loading.value = true

  // 这里需要根据实际的重新加载API来实现
  // 暂时使用GetFileUrl作为示例
  Promise.all(
    props.attr?.split(',').map(async (fileId) => {
      try {
        const res = await GetFileUrl({ fileId })
        return res.data
      } catch (error) {
        console.error('Reload file error:', error)
        return null
      }
    }) || [],
  )
    .then((results) => {
      const validResults = results.filter(Boolean)
      if (validResults.length > 0) {
        // 这里需要根据实际的数据结构来更新list
        message.success('重新加载成功')
      } else {
        message.error('重新加载失败')
      }
    })
    .finally(() => {
      loading.value = false
    })
}
</script>

<style lang="scss" scoped>
:deep(.ant-upload-list-item-container) {
  height: auto !important;
}

:deep(.ant-upload-list) {
  padding: 8px 0;
}

:deep(.ant-upload-list-item) {
  padding: 8px;
  margin-top: 8px;
  border-radius: 4px;
  transition: all 0.3s;

  &:hover {
    background-color: #f5f5f5;
  }

  &:first-child {
    margin-top: 0;
  }
}

.search-icon {
  display: block;
  width: 14px;
  height: 14px;
  background-image: url('data:image/svg+xml;base64,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');
  background-size: cover;
}
</style>
