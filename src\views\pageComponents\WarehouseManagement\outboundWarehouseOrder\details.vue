<template>
  <div>
    <EasyForm v-if="type" ref="formRef" :mode="mode" :formItems="formItems" v-model:form="formData">
      <!-- 成品明细 -->
      <template #final>
        <div class="flex items-center justify-end" v-if="['edit', 'new'].includes(type)">
          <a-button class="mr-16" @click="handleGenerateMaterialByBom">根据Bom生成原料</a-button>
          <a-button type="primary" @click="handleAddProduct">添加商品</a-button>
        </div>
        <BaseTable ref="finalTableRef" v-bind="finalTableConfig">
          <template #this_processing_num="{ row }" v-if="['edit', 'new'].includes(type)">
            <a-input-number class="w-full" :max="row.total_purchase_quantity" v-model:value="row.this_processing_num" :min="0" :precision="0" @change="handleChangeThisProcessingNum(row)" />
          </template>
          <template #remark="{ row }" v-if="['edit', 'new'].includes(type)">
            <a-input class="w-full" v-model:value="row.remark" :maxlength="300" placeholder="请输入备注" />
          </template>
          <template #operation="{ row }" v-if="['edit', 'new'].includes(type)">
            <a-button type="link" @click="handleDelete(row, 'final')">删除</a-button>
          </template>
        </BaseTable>
      </template>
      <!-- 原料明细 -->
      <template #material>
        <BaseTable ref="materialTableRef" v-bind="materialTableConfig">
          <template #finished_product_number="{ row, rowIndex }">
            <div class="flex flex-col justify-center items-center text-center">
              <span class="mb-5">{{ row.finished_product_number }}</span>
              <a-button v-if="['edit', 'new'].includes(type)" class="mb-5" type="default" size="small" @click="handleAddMaterial(rowIndex)">添加物料</a-button>
            </div>
          </template>
          <!-- 本次领料数量 -->
          <template #material_quantity="{ row }" v-if="['edit', 'new'].includes(type)">
            <a-input-number v-if="row?.sku_id" class="w-full" v-model:value="row.material_quantity" :min="0" :max="row.actual_inventory_num" :precision="0" />
          </template>
          <template #operation="{ row }" v-if="['edit', 'new'].includes(type)">
            <a-button type="link" @click="handleDelete(row, 'material')">删除</a-button>
          </template>
        </BaseTable>
      </template>
    </EasyForm>
    <div class="submit-row">
      <template v-if="['edit', 'new'].includes(type)">
        <a-button type="primary" :loading="submitLoading" class="ml mr" @click="handleSubmit(true)">提交审核</a-button>
        <a-button type="default" :loading="submitLoading" class="ml mr" @click="handleSubmit(false)">保存暂不提交</a-button>
      </template>
      <template v-else-if="type === 'review'">
        <a-button class="ml mr" @click="reviewType(1)">审核通过</a-button>
        <a-button class="ml mr" @click="reviewType(2)">审核拒绝</a-button>
      </template>
      <a-button class="ml mr" v-else @click="() => closePage()">关闭</a-button>
      <a-button v-if="formData.id" type="primary" class="ml mr" @click="reviewType">审核记录</a-button>
    </div>
    <SelectProduct ref="selectProductRef" @confirm="onAddProduct" />
    <SelectOrder ref="selectOrderRef" :syncConfirm="onAddOrder" />
    <SelectMaterial ref="selectMaterialRef" @confirm="onAddMaterial" />
    <reviewTimer ref="reviewTimerRef"></reviewTimer>
    <AuditConfirm ref="auditConfirmRef" @audit="onAuditConfirm"></AuditConfirm>
  </div>
</template>

<script lang="ts" setup>
import { message } from 'ant-design-vue'

import { Enum2Map } from '@/utils'

import reviewTimer from '@/views/pageComponents/purchaseManagement/components/purchaseorderManagementComponents/reviewTimer.vue'

import { GetWarehouses } from '@/servers/Purchaseapplyorder'
import {
  GetOutboundWarehouseBySkuList,
  GetOutboundWarehouseByPurchaseOrderList,
  GetOutboundRawMaterialBomList,
  GetOutboundWarehouseByMaterialList,
  AddOutboundWarehouseOrder,
  GetOutboundWarehouseOrderDetail,
  UpdateOutboundWarehouseOrder,
  BatchAudit,
} from '@/servers/OutboundWarehouseOrder'
import { GetMRPSupplierCompanySelect, GetPurchaseByDeptSelect } from '@/servers/BusinessCommon'

enum CurDataStatusEnum {
  新增 = 1,
  更新 = 2,
  删除 = 3,
}

const type = ref()

const formRef = ref()
const route = useRoute()
const { closePage, setRefreshByPath } = usePage({
  initMethod: () => initMethod(),
  formRef,
})

const formData = ref<Record<string, any>>({
  purchase_order_numbers: [],
})
const formItems = ref<EasyFormItemProps[]>([
  { type: 'title', span: 24, title: '基本信息' },
  { label: '委外领料单编号', name: 'number', type: 'input', disabled: true, placeholder: ' ' },
  { label: '出库单编号', name: 'delivery_number', type: 'input', disabled: true, placeholder: ' ' },
  {
    label: '采购单编号',
    name: 'purchase_order_numbers',
    type: 'select',
    required: true,
    props: {
      open: false,
      mode: 'multiple',
      onClick: () => handleAddOrder(),
      onChange: () => {
        for (const item of $finalTable.value.getTableData().fullData) {
          handleDelete(item, 'final')
        }
      },
    },
    textFormat: ({ value }) => {
      return value.join(',')
    },
  },
  {
    label: '供应商子公司',
    name: 'company_supplier_id',
    alias: 'company_supplier_name',
    placeholder: ' ',
    type: 'select',
    dataType: 'string',
    required: true,
    api: GetMRPSupplierCompanySelect,
    disabled: true,
  },
  {
    label: '领料仓库',
    name: 'warehouse_id',
    alias: 'warehouse_name',
    dataType: 'string',
    type: 'select',
    required: true,
    api: GetWarehouses,
    props: {
      showSearch: true,
      onChange: (value) => {
        if (!value) return
        loadData([], $materialTable)
      },
    },
  },
  { label: '创建时间', name: 'create_at', type: 'text', placeholder: ' ' },
  {
    label: '创建人',
    name: 'creator_id',
    alias: 'creator',
    type: 'select',
    placeholder: ' ',
    disabled: true,
    api: GetPurchaseByDeptSelect,
    apiCallBack: (res) => {
      if (!formData.value.creator_id) {
        const display_name = JSON.parse(localStorage.getItem('userData') as any).display_name
        const obj = res.data.find((item) => item.label.indexOf(display_name) >= 0)
        formData.value.creator_id = obj?.value
      }
    },
  },
  { label: '状态', name: 'audit_status', type: 'text', textFormat: ({ value }) => Enum2Map(AuditStatusEnum)[value || AuditStatusEnum.待提审] },
  { label: '出库时间', name: 'delivery_time', type: 'text', hide: true },
  { label: '备注', name: 'remark', type: 'input', span: 24 },
  { type: 'title', span: 24, title: '成品明细' },
  { slot: 'final', span: 24 },
  { type: 'title', span: 24, title: '原料明细' },
  { slot: 'material', span: 24 },
])

// 添加采购单
const selectOrderRef = ref()
const handleAddOrder = () => {
  selectOrderRef.value.open(
    {
      keyField: 'purchase_order_number',
      search: ['采购单号', '供应商子公司'],
      left: ['采购单编码', '供应商名称', '采购类型', '采购员', '采购时间', '采购总数量', '备注'],
      right: ['序号', '采购单编码'],
      api: GetOutboundWarehouseByPurchaseOrderList,
    },
    formData.value?.purchase_order_numbers || [],
  )
}
const onAddOrder = async (list: any[]) => {
  console.log('onAddOrder')
  if (list.some((f) => f.company_supplier_id !== list[0].company_supplier_id)) {
    message.error('请选择相同供应商的采购单')
    return Promise.resolve(false)
  }
  const processWarehouseId = list.find((f) => !!f.process_warehouse_id)?.process_warehouse_id
  await formRef.value.changeValue(
    {
      purchase_order_numbers: list.map((f) => f.purchase_order_number),
      company_supplier_name: list[0].company_supplier_name,
      company_supplier_id: list[0].company_supplier_id,
      warehouse_id: processWarehouseId || list[0].warehouse_id,
    },
    undefined,
    true,
  )
  return Promise.resolve(true)
}

const $finalTable = computed(() => finalTableRef.value?.tableRef)
const $materialTable = computed(() => materialTableRef.value?.tableRef)

// 成品明细
const finalTableRef = ref()
const finalTableConfig = ref<Record<string, any>>({
  hideTop: true,
  hidePagination: true,
  autoSearch: false,
  boxCls: ['relative!', 'mt-10', 'mb-20'],
  maxHeight: 600,
  minHeight: 56,
  height: null,
  tableColumns: [
    { type: 'seq', title: '序号', width: 50, align: 'center' },
    { name: '商品主图', key: 'image_url', cellRender: { name: 'image' }, showOverflow: false, width: 75 },
    { name: '商品名称', key: 'sku_name' },
    { name: '款式编号', key: 'style_code' },
    { name: '商品编码', key: 'sku_id' },
    { name: '颜色规格', key: 'type_specification' },
    { name: '采购单编号', key: 'purchase_order_number', width: 130 },
    { name: '采购总数量', key: 'total_purchase_quantity', width: 130 },
    { name: '本次加工数量', key: 'this_processing_num', width: 130 },
    { name: '累计加工数量', key: 'total_processing_quantity', width: 130 },
    { name: '备注', key: 'remark' },
    { name: '操作', key: 'operation', width: 80 },
  ],
  keyField: '$type',
})

// 原料明细
const materialTableRef = ref()
const materialTableConfig = ref<Record<string, any>>({
  hideTop: true,
  hidePagination: true,
  autoSearch: false,
  boxCls: ['relative!', 'mt-10', 'mb-70'],
  maxHeight: 600,
  minHeight: 56,
  height: null,
  tableColumns: [
    { type: 'seq', title: '序号', width: 50, align: 'center' },
    { name: '成品商品编号', key: 'finished_product_number', showOverflow: false, width: 130, align: 'center' },
    { name: '采购单编号', key: 'purchase_order_number', width: 130 },
    { name: '商品主图', key: 'image_url', cellRender: { name: 'image' }, showOverflow: false, width: 66 },
    { name: '商品名称', key: 'sku_name' },
    { name: '商品编码', key: 'sku_id' },
    { name: '聚水潭编号', key: 'jst_sku_id' },
    { name: '颜色规格', key: 'type_specification' },
    { name: '单位用量', key: 'unit_dosage' },
    { name: '本次领料数量', key: 'material_quantity' },
    { name: '实际库存数', key: 'actual_inventory_num' },
    { name: '操作', key: 'operation', width: 80 },
  ],
  rowStyle: () => {
    return {
      backgroundColor: '#fff',
    }
  },
  rowConfig: {
    keyField: 'uuid',
    isHover: false,
  },
  cellConfig: {},
})

// 添加商品
const selectProductRef = ref()
const handleAddProduct = () => {
  const purchase_order_numbers = formData.value.purchase_order_numbers
  if (!purchase_order_numbers?.length) {
    message.warning('请先选择采购单')
    return
  }
  console.log(' $finalTable.value', $finalTable.value.getTableData().fullData)
  selectProductRef.value.open(
    {
      width: 1300,
      search: ['商品编号/聚水潭编号', '商品名称SKU'],
      left: ['采购单编号', '商品主图', '商品名称', '商品编码', '聚水潭编号', '颜色规格', '计价单位_成本', '采购数量'],
      right: ['商品名称', '商品编码', '采购单编号'],
      api: GetOutboundWarehouseBySkuList,
      params: { purchase_order_numbers },
      dataFormat: (data) => {
        return data.map((f) => ({ ...f, $type: `${f.purchase_order_number}-${f.sku_id}` }))
      },
      keyField: '$type',
    },
    $finalTable.value.getTableData().fullData,
  )
}

const loadData = async (data, tableRef) => {
  await tableRef.value.reloadData(data)
}

const onAddProduct = async (list: any[]) => {
  console.log('onAddProduct')
  const oldData = $finalTable.value.getTableData().fullData
  const result = list.map((f) => {
    const index = oldData.findIndex((f2) => f2.$type === f.$type)
    if (~index) {
      oldData[index].$has = true
      return oldData[index]
    }
    return { ...f, this_processing_num: f.total_purchase_quantity }
  })
  oldData
    .filter((f) => !f.$has)
    .map(async (item) => {
      await handleDelete(item, 'final')
    })
  loadData(result, $finalTable)
}

// 根据bom生成原料列表
const handleGenerateMaterialByBom = async () => {
  if (!formData.value.warehouse_id) {
    message.warning('请先选择领料仓库')
    return
  }
  const data = $finalTable.value.getTableData().fullData
  const res = await GetOutboundRawMaterialBomList({
    warehouse_id: formData.value.warehouse_id,
    param_list: data.map((f) => ({ finished_product_number: f.sku_id, purchase_order_number: f.purchase_order_number })),
  })
  flattenBomData(res.data, true, [{}])
}
// 原料明细组装(附加拍平)
const flattenBomData = async (data, isFlatten = true, defaultArr: any[] = []) => {
  let flattenData = data
  if (isFlatten) {
    flattenData = data.reduce((acc, cur) => {
      const { outbound_raw_material_bom_details, outbound_raw_material_details, ...rest } = cur
      const _materialBom = outbound_raw_material_bom_details || outbound_raw_material_details
      const details = _materialBom?.length ? _materialBom : defaultArr
      details.forEach((detail) => {
        acc.push({
          ...rest,
          ...detail,
        })
      })
      return acc
    }, [])
  }
  const result: any[] = []
  const mergeCells: any[] = []
  for (const [index, item] of flattenData.entries()) {
    item.$type = `${item.purchase_order_number}-${item.finished_product_number}`
    const uuid = `${item.$type}-${item.sku_id || ''}`
    const this_processing_num = $finalTable.value.getTableData().fullData.find((f) => f.$type === `${item.$type}`)?.this_processing_num
    const material_quantity = Math.min(Math.ceil(this_processing_num * item.unit_dosage), item.actual_inventory_num)

    const prev = index && flattenData[index - 1]
    if ((index && item.$type !== prev.$type) || index === 0) {
      Array.prototype.push.apply(mergeCells, [
        { row: index, col: 1, rowspan: 1, colspan: 1 },
        { row: index, col: 2, rowspan: 1, colspan: 1 },
      ])
    } else {
      mergeCells[mergeCells.length - 1].rowspan += 1
      mergeCells[mergeCells.length - 2].rowspan += 1
    }

    result.push({
      material_quantity, // 位置不能换 防止item.material_quantity被覆盖
      ...item,
      uuid,
    })
  }
  await loadData(result, $materialTable)
  setTimeout(() => {
    $materialTable.value.setMergeCells(mergeCells)
  })
  return Promise.resolve(JSON.parse(JSON.stringify(result)))
}

// 添加物料
const selectMaterialRef = ref()
const handleAddMaterial = (rowIndex) => {
  if (!formData.value.warehouse_id) {
    return message.error('请选择仓库')
  }
  const data = $materialTable.value.getTableData().fullData
  const start = data[rowIndex]
  const range = data.filter((f) => f.$type === start.$type)
  onAddMaterial.value = (list) => {
    const pushList = list.map((item) => {
      const found = range.find((f) => f.sku_id === item.sku_id) || {}
      return {
        ...item,
        ...found,
        finished_product_number: start.finished_product_number,
        purchase_order_number: start.purchase_order_number,
        id: found.id || undefined,
      }
    })
    data.splice(rowIndex, start.sku_id ? range.length : 1, ...pushList)
    console.log('pushList -> data:', data)
    flattenBomData(data, false)
  }
  selectMaterialRef.value.open(
    {
      api: GetOutboundWarehouseByMaterialList,
      params: {
        warehouse_id: formData.value.warehouse_id,
      },
      search: ['分仓仓库编码', '商品名称'],
      left: ['分仓仓库编码', '商品编码', '聚水潭编码', '商品名称', '规格型号', '计价单位', '实际库存数'],
      right: ['序号', '商品名称', '商品编码'],
    },
    range.filter((f) => f.sku_id),
  )
}
const onAddMaterial = ref()

// 本次加工数量变化
const handleChangeThisProcessingNum = (row) => {
  const this_processing_num = row.this_processing_num
  const data = $materialTable.value.getTableData().fullData
  for (const item of data) {
    if (item.$type === row.$type) {
      item.material_quantity = Math.min(Math.ceil(this_processing_num * item.unit_dosage), item.actual_inventory_num)
    }
  }
}

// 删除
const handleDelete = async (row: any, key) => {
  if (key === 'final') {
    $finalTable.value.remove(row)
  } else {
    $materialTable.value.remove(row)
  }
  await nextTick()
  if (key === 'final') {
    const data = $materialTable.value.getTableData().fullData
    for (const item of data) {
      if (item.$type === row.$type) {
        handleDelete(item, 'material')
      }
    }
  } else {
    const data = $materialTable.value.getTableData().fullData
    const isDelete = !data.some((f) => f.$type === row.$type)
    if (isDelete) {
      const deleteRow = $finalTable.value.getTableData().fullData.find((f) => f.$type === row.$type)
      if (deleteRow) {
        handleDelete(deleteRow, 'final')
      }
    }
  }
}

// 审核
const reviewTimerRef = ref()
const reviewType = (val) => {
  if (val == 1) {
    auditConfirmRef.value.open(true)
  } else if (val === 2) {
    auditConfirmRef.value.open(false)
  } else {
    reviewTimerRef.value.setModel(true, formData.value.id, 7)
  }
}
const auditConfirmRef = ref()
const onAuditConfirm = async (data) => {
  const { audit_opinion, is_pass } = data
  const params = {
    is_pass,
    audit_opinion,
    ids: [formData.value.id],
  }
  const res = await BatchAudit(params)
  if (res.success) {
    message.success('操作成功')
    setRefreshByPath('/outboundWarehouseOrder')
    closePage()
  } else {
    message.error(res.message)
  }
}

// 成品明细对比 赋值data_status
const finishedDataComparison = (childData: any[]) => {
  const result: any[] = []
  const data = $finalTable.value.getTableData().fullData
  for (const item of data) {
    const found = cloneFinalData.value.find((f) => f.$type === item.$type)
    if (!found) {
      result.push({ ...item, data_status: CurDataStatusEnum.新增 })
    } else {
      found.$isHas = true
      const isEditRemark = found.remark !== item.remark
      const isEditProcessingNum = found.this_processing_num !== item.this_processing_num
      const isEditChild = childData.some((f) => f.$type === item.$type && [CurDataStatusEnum.新增, CurDataStatusEnum.更新, CurDataStatusEnum.删除].includes(f.data_status))
      result.push({
        ...item,
        id: found.id,
        outbound_warehouse_id: found.outbound_warehouse_id,
        data_status: [isEditRemark, isEditProcessingNum, isEditChild].includes(true) ? CurDataStatusEnum.更新 : undefined,
      })
    }
  }
  for (const item of cloneFinalData.value) {
    if (!item.$isHas) {
      result.push({ ...item, data_status: CurDataStatusEnum.删除 })
    }
  }
  return result
}
// 原料明细对比 赋值data_status
const materialDataComparison = () => {
  const result: any[] = []
  const data = $materialTable.value.getTableData().fullData
  for (const item of data) {
    const found = cloneMaterialData.value.find((f) => f.uuid === item.uuid)
    if (!found) {
      result.push({
        ...item,
        data_status: CurDataStatusEnum.新增,
      })
    } else {
      found.$isHas = true
      result.push({
        ...item,
        id: found.id,
        data_status: found.material_quantity !== item.material_quantity ? CurDataStatusEnum.更新 : undefined,
      })
    }
  }
  for (const item of cloneMaterialData.value) {
    if (!item.$isHas) {
      result.push({ ...item, data_status: CurDataStatusEnum.删除 })
    }
  }
  return result
}

// 提交
const submitLoading = ref(false)
const handleSubmit = async (is_pass) => {
  const formState = await formRef.value.validate()
  let finalData = $finalTable.value.getTableData().fullData
  let materialData = $materialTable.value.getTableData().fullData
  let flag = false
  if (!materialData.length) {
    return message.warning('请先根据Bom生成原料明细')
  }
  for (const [index, item] of materialData.entries()) {
    if (!item.sku_id) {
      message.warning(`原料明细 - 第${index + 1}行原料明细未添加物料`)
      flag = true
    }
    if (!item.material_quantity) {
      message.warning(`原料明细 - 第${index + 1}行领料数量为0，请删除`)
      flag = true
    }
  }
  if (flag) return
  if (type.value === 'edit') {
    materialData = materialDataComparison()
    finalData = finishedDataComparison(materialData)
  }
  const outbound_finished_details = finalData.map((item) => {
    const outbound_raw_material_details = materialData.filter((f) => f.$type === item.$type && f.sku_id)
    return {
      ...item,
      outbound_raw_material_details,
      id: /^(row)/.test(item.id) ? undefined : item.id,
    }
  })
  const params = {
    id: formData.value.id,
    is_pass,
    ...formState,
    outbound_finished_details,
  }
  console.log('params', params)
  // if (params) return
  const api = type.value === 'edit' ? UpdateOutboundWarehouseOrder : AddOutboundWarehouseOrder
  const res = await api(params, { loading: submitLoading })
  if (res.success) {
    message.success('提交成功')
    setRefreshByPath('/outboundWarehouseOrder')
    closePage()
  }
}

const mode = computed(() => {
  return ['edit', 'new'].includes(type.value) ? 'new' : 'detail'
})
const cloneFinalData = ref<Record<string, any>[]>([])
const cloneMaterialData = ref<Record<string, any>[]>([])
const initMethod = async () => {
  const {
    params: { id },
    meta: { code },
  } = route
  if (id) {
    type.value = code === 'outboundWarehouseOrderEdit' ? 'edit' : code === 'outboundWarehouseOrderReview' ? 'review' : 'detail'
    formData.value.id = id
    await nextTick()
    const res = await GetOutboundWarehouseOrderDetail({
      id,
      is_check: type.value === 'detail',
    })

    formRef.value.changeValue(res.data)
    const finalData = res.data.outboundFinishedDetails.map((f) => ({
      ...f,
      $type: `${f.purchase_order_number}-${f.sku_id}`,
      outbound_raw_material_details: f.outboundRawMaterialDetails,
      outboundRawMaterialDetails: undefined,
    }))
    await loadData(finalData, $finalTable)
    cloneFinalData.value = JSON.parse(JSON.stringify(finalData))
    cloneMaterialData.value = await flattenBomData(finalData)
  } else {
    type.value = 'new'
  }
  if (['review', 'detail'].includes(type.value)) {
    finalTableRef.value.setColumnByField('operation', { visible: false })
    materialTableRef.value.setColumnByField('operation', { visible: false })
  }
}
</script>

<style lang="scss" scoped></style>
