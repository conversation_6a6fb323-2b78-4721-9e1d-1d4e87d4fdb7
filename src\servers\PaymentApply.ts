// 付款申请单模块
import { request } from './request'
// 获取供应商列表
export const GetSupplierOptions = (data) => {
  return request({ url: '/api/SupplierFilter/GetSupplierOptions', data }, 'GET')
}
// 查询申请单列表
export const List = (data) => {
  return request({ url: '/api/PaymentApply/List', data })
}

// 查询申请单合计
export const Total = (data) => {
  return request({ url: '/api/PaymentApply/Total', data })
}

// 查询申请单状态汇总
export const Count = (data) => {
  return request({ url: '/api/PaymentApply/Count', data })
}
// 审核/批量审核通过
export const AuditPass = (data) => {
  return request({ url: '/api/PaymentApply/AuditPass', data })
}
// 审核/批量审核驳回
export const AuditReject = (data) => {
  return request({ url: '/api/PaymentApply/AuditReject', data })
}

// 删除申请单
export const DeleteApply = (data) => {
  return request({ url: '/api/PaymentApply/DeleteApply', data })
}

// 上传银行回单
export const UploadBankReceipt = (data) => {
  return request({ url: '/api/PaymentApply/UploadBankReceipt', data })
}

// 上传文件
export const Upload = (data) => {
  return request({ url: '/api/File/UploadOss', data, isFormData: true })
  // return request({ url: '/api/File/Upload', data, isFormData: true })
}

// 在线预览
export const Preview = (data) => {
  return request({ url: `/api/File/Preview?fileId=${data}` }, 'GET')
}

export const Export = (data) => {
  return request({ url: '/api/PaymentApply/Export', data, responseType: 'blob' })
}

// 获取申请部门或申请人
export const ApplicantFillterOptions = (type) => {
  return request({ url: `/api/PaymentApplyFilter/ApplicantFillterOptions?applicantOptionType=${type}` }, 'GET')
}

// 获取公户账号
export const PaymentAccountFillterOptions = (data) => {
  return request({ url: '/api/PaymentApplyFilter/PaymentAccountFillterOptions', data }, 'GET')
}

// 获取付款方式
export const PaymentMethodFillterOptions = (data) => {
  return request({ url: '/api/PaymentApplyFilter/PaymentMethodFillterOptions', data }, 'GET')
}

// 重新下载文件
export const ReDownloadFile = (data) => {
  return request({ url: `/api/PaymentApplyFilter/ReDownloadFile?id=${data.id}`, data }, 'GET')
}
