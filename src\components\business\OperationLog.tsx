import { Drawer, DrawerProps, Empty } from 'ant-design-vue'
import { LoadingOutlined } from '@ant-design/icons-vue'
import { VxeGrid } from 'vxe-table'
import { DynamicScroller, DynamicScrollerItem } from 'vue-virtual-scroller'
import 'vue-virtual-scroller/dist/vue-virtual-scroller.css'

import styles from './OperationLog.module.scss'

import { GetOpLogInfos } from '@/servers/BusinessCommon'

const LOG_STORAGE_KEY = 'operationLogUidMap'

export default defineComponent({
  name: 'OperationLog',
  props: {
    isDrawer: {
      type: Boolean,
      default: true,
    },
    visible: {
      type: Boolean,
      default: false,
    },
    uid: {
      type: String,
      default: '',
    },
    width: {
      type: Number,
      default: 640,
    },
    pageType: {
      type: Number as PropType<OpLogPageTypeEnum>,
      default: null,
    },
  },
  emits: ['update:visible', 'update:width'],
  setup(props, { emit }) {
    const router = useRouter()
    const uidMap: Record<string, any> = ref(JSON.parse(localStorage.getItem(LOG_STORAGE_KEY) || '{}'))
    const openFlag = ref(props.visible)
    const open = ({ params }) => {
      openFlag.value = true
      emit('update:visible', true)
      getLog(params)
    }
    const close = () => {
      openFlag.value = false
      emit('update:visible', false)
    }

    const loading = ref(true)
    const getLog = (params) => {
      GetOpLogInfos(params, { loading }).then((res) => {
        items.value = formatData(res.data)
      })
    }

    // 拖动宽度
    const moveEnable = ref(false)
    const handleDown = () => {
      moveEnable.value = true
      document.querySelectorAll<HTMLElement>('.ant-drawer-content-wrapper').forEach((f) => {
        if (f) {
          f.style.cssText = `${f.style.cssText || ''} transition: none;`
        }
      })
    }
    const handleUp = () => {
      moveEnable.value = false
      document.querySelectorAll<HTMLElement>('.ant-drawer-content-wrapper').forEach((f) => {
        f.style.cssText = (f.style.cssText || '').replace(/(transition: none;)/g, '')
      })
    }
    const handleMove = (event: MouseEvent) => {
      if (!moveEnable.value) return
      const _width = window.innerWidth - event.clientX
      emit('update:width', Math.max(300, _width))
    }

    const toggle = (val = false) => {
      openFlag.value = val
      emit('update:visible', val)
      // 更新storage
      if (props.uid) {
        uidMap.value[props.uid] = val
        localStorage.setItem(LOG_STORAGE_KEY, JSON.stringify(uidMap.value))
      }
      if (!props.isDrawer) {
        document.body[`${val ? 'add' : 'remove'}EventListener`]('mouseup', handleUp)
        document.body[`${val ? 'add' : 'remove'}EventListener`]('mousemove', handleMove as EventListener)
      }
    }
    // 初始化读取缓存
    if (uidMap.value?.[props.uid] && !props.isDrawer) {
      toggle(uidMap.value[props.uid])
    }

    watch(() => props.visible, toggle)

    const items = shallowRef([])
    // 数据组装
    const formatData = (data: Record<string, any>[]) => {
      let index = 1
      const result: any = []
      for (const item of data) {
        // for (const _ of Array.from({ length: 100 })) {
        result.push({
          id: index++,
          user_name: item.user_name,
          user_department: item.user_department,
          op_type: item.op_type,
          layer: 1,
        })
        // 组装二层
        for (const subItem of item.edits || []) {
          const { items, ...subProps } = subItem
          if (!items?.length) {
            result.push({
              ...subProps,
              op_type: item.op_type,
              id: index++,
              layer: 2,
            })
          } else {
            Array.prototype.push.apply(result, flattenTableData(subItem, index))
          }
        }
        // }
        // 组装二层 end
        result.push({
          id: index++,
          op_at: item.op_at,
          layer: 1,
        })
      }

      console.log('result', result)
      return result
    }
    const isEmpty = (value) => {
      return [undefined, null].includes(value)
    }
    const flattenTableData = (obj, index) => {
      const { items, ...other } = obj
      const unshiftList: any = []
      const childrenList: any = []
      const tableId = `${index}-table`
      const result = {
        ...other,
        id: tableId,
        layer: 3,
        gridOptions: {
          columns: [{ field: '$type', title: '', width: 140, align: 'center' }],
          data: [],
          border: true,
          minHeight: 30,
          maxHeight: Math.max(window.innerHeight - 600, 400),
          size: 'mini',
          columnConfig: { resizable: true },
        },
      }
      const typeMap = {}
      // 拼装头部 & 确定行的title
      for (const [index, item] of Object.entries(items as any[])) {
        result.gridOptions.columns.push({ field: item.fie_id + index, title: item.fie_id, minWidth: 100, slots: { default: 'number' }, align: 'center' })
        if (!isEmpty(item.new_val) || !isEmpty(item.old_val)) {
          unshiftList.push({
            ...item,
            layer: 2,
            id: `${tableId}-${item.fie_id}`,
            items: undefined,
          })
        }
        if (item.items?.length) {
          for (const subItems of item.items) {
            subItems.fie_id && (typeMap[subItems.fie_id] = { $type: subItems.name })
          }
        }
      }
      // 渲染行
      const tableData: any = []
      for (const [index, item] of Object.entries(items as any[])) {
        for (const subItem of item?.items || []) {
          if (typeMap[subItem.fie_id] && (!isEmpty(subItem.old_val) || !isEmpty(subItem.new_val))) {
            typeMap[subItem.fie_id] = { ...typeMap[subItem.fie_id], [item.fie_id + index]: [subItem.old_val, subItem.new_val] }
          }
          if (subItem.items?.length) {
            childrenList.push(
              flattenTableData(
                {
                  ...subItem,
                  fie_id: item.fie_id,
                },
                `${tableId}-1`,
              ),
            )
          }
        }
      }
      for (const row of Object.keys(typeMap)) {
        tableData.push(typeMap[row])
      }
      if (tableData?.length) {
        result.gridOptions.data = tableData
        return [...unshiftList, result, ...childrenList].flat()
      }
      return [...unshiftList, ...childrenList].flat()
    }

    const renderList = () => {
      if (loading.value || !items.value.length)
        return h(
          'div',
          { class: 'flex flex-col items-center justify-center flex-1' },
          loading.value
            ? [h(LoadingOutlined, { style: { fontSize: '20px', color: '#1890FF' } }), h('div', { class: 'mt-10' }, '数据获取中')]
            : [h(Empty, { image: Empty.PRESENTED_IMAGE_SIMPLE, description: '暂无数据' })],
        )
      // 大于规定条数据开启虚拟滚动
      if (items.value.length > 20) {
        return h(
          DynamicScroller,
          {
            items: items.value,
            minItemSize: 10,
            class: ['scroller', styles.scroller],
            updateInterval: 10,
          },
          {
            default: ({ item, index, active }) => {
              return h(
                DynamicScrollerItem,
                {
                  item,
                  watchData: true,
                  active,
                  dataIndex: index,
                  // sizeDependencies: [item.user_name],
                  dataActive: active,
                  class: styles['scroller-item'],
                },
                {
                  default: () => renderContent(item, index),
                },
              )
            },
          },
        )
      }
      return h(
        'div',
        { class: [styles.scroller, 'overflow-y-auto'] },
        items.value.map((item, index) =>
          h(
            'div',
            {
              class: styles['scroller-item'],
            },
            renderContent(item, index),
          ),
        ),
      )
    }

    const renderContent = (item: any, index: number) => {
      return h('div', { key: index }, [
        // 标点
        item.user_name && h('span', { class: styles['log-dot'] }),
        // 内容  第一层
        item.layer === 1 && [
          item.user_name && h('span', { class: 'text-15px mr-5' }, item.user_name),
          item.user_department && h('span', { class: 'text-12px text-#888' }, `（${item.user_department}）`),
          item.op_at && h('span', { class: 'text-#888' }, item.op_at),
        ],

        item.layer === 1 && item.op_type && h('div', { class: 'text-12px text-#888 mt-2' }, `${item.op_type}了 此记录`),
        // 内容 第二层
        item.layer === 2 &&
          h('div', { class: 'relative pl-30' }, [
            h('span', { class: [styles['log-dot'], styles.small] }),
            h('span', {}, item.name),
            h('span', {}, '：'),
            item.name
              ? item.isJump
                ? [
                    h(
                      'span',
                      {
                        class: ' link',
                        onClick: () => {
                          close()
                          router.push(`/purchaseOrderAlterLook/${item.new_val}`)
                        },
                      },
                      item.old_val,
                    ),
                  ]
                : [
                    // line
                    h('span', { class: 'line-through text-#aaa' }, item.old_val === null ? '空' : item.old_val || '空'),
                    h('span', { class: 'ml mr text-#777' }, '>'),
                    h('span', {}, item.new_val || '空'),
                  ]
              : item.order
                ? h('span', { class: 'link' }, item.order)
                : null,
          ]),
        // 表格
        item.gridOptions &&
          h('div', { class: 'ml-30 pb-15' }, [
            h('div', { class: 'mb-5 text-#000' }, `${item.fie_id || ''}  ${item.name}`),
            h(VxeGrid, item.gridOptions, {
              number: ({ row, column }: any) => [
                // line
                h('div', { class: 'line-through leading-13 mb-2' }, row[column.field]?.[0]),
                h('div', { class: 'leading-13' }, row[column.field]?.[1]),
              ],
            }),
          ]),
        // 页尾留白
        index === items.value.length - 1 && h('div', { class: 'h-30' }),
      ])
    }

    const renderVN = () => {
      const drawerProps: DrawerProps = {
        title: '操作日志',
        width: props.width,
        destroyOnClose: true,
        open: openFlag.value,
        placement: 'right',
        bodyStyle: {
          padding: 0,
          display: 'flex',
          flexDirection: 'column',
        },
        onClose: close,
        'onUpdate:open': toggle,
      }

      return props.isDrawer
        ? h(Drawer, drawerProps, {
            default: () => [
              // main content
              h('div', { class: ['h-20'] }),
              renderList(),
            ],
          })
        : h(
            'div',
            {
              class: ['h-full', styles['log-wrapper']],
              style: {
                'user-select': moveEnable.value ? 'none' : '',
                width: openFlag.value ? `${props.width}px` : 0,
                flex: openFlag.value ? `0 0 ${props.width}px` : 0,
                transition: !moveEnable.value ? 'all 0.2s' : '',
                transform: `translateX(${openFlag.value ? 0 : `${props.width}px`})`,
              },
            },
            openFlag.value
              ? [
                  // main content
                  h('div', { class: ['h-20'] }),
                  h('div', { class: styles['move-box'], onMousedown: handleDown }),
                  renderList(),
                ]
              : [],
          )
    }

    onUnmounted(() => {
      document.body.removeEventListener('mouseup', handleUp)
      document.body.removeEventListener('mousemove', handleMove)
    })

    return {
      open,
      close,
      renderVN,
    }
  },
  render() {
    return this.renderVN()
  },
})
