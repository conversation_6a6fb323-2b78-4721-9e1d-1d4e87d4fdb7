<template>
  <div class="flex flex-col h-full">
    <Form ref="formRef" v-model:form="formArr" :page-type="PageTypeEnum.billVerificationDoneBillList" @search="onSearch" @resetForm="onReset" @setting="tableRef?.showTableSetting()" />

    <BaseTable ref="tableRef" v-model:form="formArr" :page-type="PageTypeEnum.billVerificationDoneBillList" :isCheckbox="true" :getList="getBillList">
      <template #left-btn>
        <a-button type="primary" @click="handleBatchPayment" class="mr-2" :disabled="!hasSelectedItems" v-if="getTypeByStatus(currentStatus || '') === 2">批量请款</a-button>
      </template>
      <template #right-btn>
        <a-button type="primary" @click="handleOption(null, '新增')" class="mr-2" v-if="getTypeByStatus(currentStatus || '') === 2">新增账单</a-button>
        <a-dropdown placement="bottomLeft" class="ml-auto">
          <template #overlay>
            <a-menu @click="onExportTypeChange">
              <a-menu-item v-for="item in exportOptions" :key="item.value">{{ item.label }}</a-menu-item>
            </a-menu>
          </template>
          <a-button>
            导出
            <DownOutlined />
          </a-button>
        </a-dropdown>
      </template>

      <!-- 自定义操作列 - 等待打款状态时不显示操作列 -->
      <template #fix_option="{ row, column }">
        <RightOperate :list="rightOperateList" :row="row" :column="column" />
      </template>
    </BaseTable>

    <!-- 批量请款抽屉 -->
    <BatchPaymentOrder v-if="batchPaymentVisible" :key="batchPaymentKey" :bill-ids="batchPaymentBillIds" @close="handleBatchPaymentClose" @query="handleBatchPaymentQuery" />

    <!-- 操作日志组件 -->
    <OperationLog ref="operationLogRef"></OperationLog>
  </div>
</template>

<script setup lang="ts">
import { ref, inject, watch, computed } from 'vue'
import { DownOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'

import Form from '@/components/Form.vue'
import BaseTable from '@/components/BaseTable.vue'
import RightOperate from '@/components/EasyTable/RightOperate.vue'
import eventBus from '@/utils/eventBus'
import { checkFormParams } from '@/utils/index'
import { usePageStore } from '@/store/usePageStore'
import { usePage } from '@/hook/usePage'
import OperationLog from '@/components/business/OperationLog'
import { OpLogPageTypeEnum } from '@/types/enums'

import BatchPaymentOrder from '@/views/pageComponents/billManagement/billVerification/components/BatchPaymentOrder.vue'

import { billFormArr, exportOptions, BillVerificationExportTypeEnum, defaultBillStatus } from './BillList.data'

import { GetBillListResult, ExportBillList, DeleteBillOrder, CounterAuthorization } from '@/servers/billVerification'

import { PageTypeEnum } from '@/enums/tableEnum'

// 定义props接收父组件传递的状态
const props = defineProps<{
  currentStatus?: string
}>()

const formRef = ref()
const tableRef = ref()
const reverseAuditLoading = ref(false) // 反审核加载状态
const deleteLoading = ref(false) // 删除加载状态
const hasSelectedItems = ref(false) // 是否有勾选的数据

// 批量请款相关状态
const batchPaymentVisible = ref(false)
const batchPaymentBillIds = ref<number[]>([])
const batchPaymentKey = ref(0)

// 操作日志引用
const operationLogRef = ref()

const formArr = billFormArr

// 获取modal实例
const modal = inject('modal') as any

// 获取页面store实例
const pageStore = usePageStore()

// 使用 usePage hook
const { pushPage } = usePage({ formRef })

// 操作按钮配置
const rightOperateList = computed(() => [
  {
    label: '查看',
    onClick: ({ row }) => handleOption(row, '查看'),
  },
  {
    label: '编辑',
    show: ({ row }) => {
      // 待提审 + 未结算/部分结算：显示编辑按钮
      return row.audit_status_string === '待提审' && (row.settlement_status_string === '未结算' || row.settlement_status_string === '未对账')
    },
    onClick: ({ row }) => handleOption(row, '编辑'),
  },
  {
    label: '删除',
    show: ({ row }) => {
      // 待提审 + 未结算/部分结算：显示删除按钮
      return row.audit_status_string === '待提审' && (row.settlement_status_string === '未结算' || row.settlement_status_string === '未对账')
    },
    onClick: ({ row }) => handleOption(row, '删除'),
  },
  {
    label: '反审核',
    show: ({ row }) => {
      // 已通过 + 未结算/部分结算：显示反审核按钮
      return row.audit_status_string === '已通过' && (row.settlement_status_string === '未结算' || row.settlement_status_string === '未对账')
    },
    onClick: ({ row }) => handleOption(row, '反审核'),
  },
  {
    label: '日志',
    onClick: ({ row }) => handleOption(row, '日志'),
  },
])

// 获取已对账列表数据
const getBillList = async (params: any) => {
  try {
    // 根据当前状态动态设置参数
    const currentStatus = getCurrentStatus()
    const requestParams = {
      ...params,
      status: currentStatus,
      type: getTypeByStatus(currentStatus),
    }

    const response = await GetBillListResult(requestParams)

    return response
  } catch (error) {
    console.error('获取已对账列表失败:', error)
    message.error('获取已对账列表失败')
    return { data: { data: [], total: 0 } }
  }
}

// 获取当前状态
const getCurrentStatus = () => {
  // 从父组件传递的状态或者使用默认状态
  return props.currentStatus || defaultBillStatus
}

// 根据状态获取type参数
const getTypeByStatus = (status: string) => {
  switch (status) {
    case 'verified_pending': // 已对账待请款
      return 2
    case 'awaiting_payment': // 等待打款
      return 3
    case 'paid': // 已打款
      return 4
    case 'all': // 全部
      return 5
    default:
      return 2
  }
}

const onSearch = () => {
  // 触发表格重新加载数据
  tableRef.value?.search()
}

const onReset = () => {
  // 重置后触发搜索
  tableRef.value?.search()
}

// 统一的操作处理函数
const handleOption = async (row: any, key?: string) => {
  switch (key) {
    case '查看':
      try {
        pushPage(`/billVerification/generateBill/view/${row.id}`, { source: true })
      } catch (error) {
        console.error('获取账单详情失败:', error)
        message.error('获取账单详情失败')
      }
      break
    case '编辑':
      // 跳转到编辑页面，使用账单ID作为路由参数
      pushPage(`/billVerification/generateBill/edit/${row.id}`, { source: true })
      break
    case '删除':
      handleDelete(row)
      break
    case '反审核':
      handleReverseAudit(row)
      break
    case '日志':
      handleLog(row)
      break
    case '新增':
      // 临时禁用KeepAlive缓存
      pageStore.addByExclude('/billVerification/generateBill')

      // 跳转到新增页面
      pushPage('/billVerification/generateBill/new', { source: true })
      break
    // 其他情况
    default:
      break
  }
}

// 通用的确认弹窗函数
const showConfirmModal = (config: { title: string; content: string; okText?: string; okType?: string; onConfirm: () => Promise<void> }) => {
  modal.open({
    title: config.title,
    content: config.content,
    okText: config.okText || '确定',
    cancelText: '取消',
    okType: config.okType || 'default',
    async onOk() {
      await config.onConfirm()
    },
  })
}

// 通用的接口调用函数 - 使用 modal.open 显示确认弹窗
const callApiWithModal = async (apiCall: () => Promise<any>, loadingRef: any, successMessage: string, errorMessage: string) => {
  try {
    loadingRef.value = true
    const response = await apiCall()

    if (response.success) {
      message.success(successMessage)
      // 刷新列表
      tableRef.value?.search()
    } else {
      message.error(response.message || errorMessage)
    }
  } catch (error) {
    console.error(`${errorMessage}:`, error)
    message.error(errorMessage)
  } finally {
    loadingRef.value = false
  }
}

// 通用的危险操作确认弹窗
const showDangerConfirmModal = (title: string, content: string, onConfirm: () => Promise<void>) => {
  modal.open({
    title,
    content,
    okText: '确定',
    cancelText: '取消',
    okType: 'danger',
    async onOk() {
      await onConfirm()
    },
  })
}

const handleDelete = (row: any) => {
  showDangerConfirmModal('确认删除', `确定要删除账单 "${row.number}" 吗？删除后无法恢复！`, async () => {
    await callApiWithModal(() => DeleteBillOrder({ id: row.id }), deleteLoading, '删除成功', '删除失败')
  })
}

const handleReverseAudit = (row: any) => {
  showConfirmModal({
    title: '确认反审核',
    content: `确定要反审核账单 "${row.number}" 吗？`,
    onConfirm: async () => {
      await callApiWithModal(() => CounterAuthorization({ id: row.id }), reverseAuditLoading, '反审核成功', '反审核失败')
    },
  })
}

const handleLog = (row: any) => {
  // 打开操作日志组件
  operationLogRef.value.open({
    params: {
      id: row.id,
      pageType: OpLogPageTypeEnum.账单对账,
    },
  })
}

// 导出相关
const onExportTypeChange = ({ key }: { key: any }) => {
  const exportType = parseInt(key.toString())
  const count = tableRef.value?.checkItemsArr?.length || 0

  // 判断是否为带图的导出
  const isExportImg = exportType > 10
  const baseExportType = isExportImg ? exportType - 10 : exportType

  if (baseExportType === BillVerificationExportTypeEnum.根据勾选导出) {
    if (!count) {
      message.info('请勾选数据')
      return
    }
  }

  // 构建导出参数
  let ids: string[] = []
  let searchListParam: any = {}

  if (baseExportType === BillVerificationExportTypeEnum.根据勾选导出) {
    ids = tableRef.value?.checkItemsArr?.map((item: any) => item.id) || []
  } else if (baseExportType === BillVerificationExportTypeEnum.根据筛选结果导出) {
    // 使用 checkFormParams 处理表单参数
    searchListParam = checkFormParams({ formArr: formArr.value, obj: { status: defaultBillStatus } })
  }

  const params = {
    exportType: baseExportType,
    ids,
    searchListParam,
    type: 2, // 固定值，因为 defaultBillStatus 总是 'verified_pending'
    isExportImg, // 是否带图
  }

  // 显示确认对话框
  modal.open({
    title: '是否确定导出已对账列表数据?',
    async onOk() {
      try {
        const { data } = await ExportBillList(params)
        eventBus.emit('downLoadId', {
          id: data as number,
          resolve: () => {},
          download: true,
        })
      } catch (error) {
        console.error('导出失败:', error)
        message.error('导出失败，请重试')
      }
    },
  })
}

// 监听勾选状态变化
watch(
  () => tableRef.value?.checkItemsArr?.length,
  (newLength) => {
    hasSelectedItems.value = !!newLength
  },
  { immediate: true },
)

// 监听状态变化，自动刷新列表
watch(
  () => props.currentStatus,
  (newStatus) => {
    if (newStatus && tableRef.value) {
      tableRef.value.search()
    }
  },
  { immediate: true },
)

const handleBatchPayment = () => {
  // 获取勾选的账单ID列表
  const selectedBillIds = tableRef.value?.checkItemsArr?.map((item: any) => item.id) || []

  // 校验供应商一致性
  if (selectedBillIds.length > 0) {
    const selectedBills = tableRef.value?.checkItemsArr || []
    const firstBill = selectedBills[0]

    // 检查所有勾选的账单是否来自同一个供应商
    const hasSameSupplier = selectedBills.every((bill: any) => {
      return bill.supplier_name === firstBill.supplier_name
    })

    if (!hasSameSupplier) {
      message.warning('相同供应商的账单才可批量请款')
      return
    }
  }

  // 设置批量请款数据并显示抽屉
  batchPaymentBillIds.value = selectedBillIds
  batchPaymentKey.value++ // 重置key，强制重新创建组件
  batchPaymentVisible.value = true
}

// 批量请款抽屉关闭处理
const handleBatchPaymentClose = () => {
  batchPaymentVisible.value = false
  batchPaymentBillIds.value = []
  batchPaymentKey.value++ // 重置key
}

// 批量请款查询处理
const handleBatchPaymentQuery = () => {
  // 刷新列表
  tableRef.value?.search()
}

// 暴露方法给父组件
defineExpose({
  onSearch,
  onReset,
})
</script>

<style scoped>
.text-red-500 {
  color: #ff4d4f;
}

.font-bold {
  font-weight: bold;
}

.text-base {
  font-size: 16px;
}
</style>
