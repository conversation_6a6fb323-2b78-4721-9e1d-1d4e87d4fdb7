<template>
  <div class="">
    <EasyForm v-if="formItems.length > 0" ref="formRef" :mode="mode" :formItems="formItems" v-model:form="formData">
      <template #details>
        <div class="mb-20" v-if="mode === 'new'">
          <a-button class="mr" @click="handleAddPro" type="primary">添加商品</a-button>
          <a-button class="ml mr" @click="handleDownloadTemplate">下载导入模板</a-button>
          <a-upload :showUploadList="false" :before-upload="handleImportPro" :disabled="!formData.purchase_order_numbers?.length">
            <a-button class="ml mr" @click="!formData.purchase_order_numbers?.length && message.warn('请选择采购单')">导入商品</a-button>
          </a-upload>
        </div>
        <div class="detail mb-20">
          <a-table
            :columns="columns"
            :data-source="tableData"
            :defaultExpandAllRows="true"
            :pagination="false"
            :scroll="{ x: 1750 }"
            :rowKey="(record) => record.id || record.purcharse_order_id + record.k3_sku_id"
            bordered
          >
            <template #bodyCell="{ column, record, index }">
              <template v-if="column.key === 'key'">{{ (productPage - 1) * productPageSize + (index + 1) }}</template>
              <template v-if="column.key === 'pic'">
                <EasyImage :hidePreview="true" :src="record[column.dataIndex as string]" />
              </template>
              <template v-if="column.key === 'purchaseAllNum' && mode === 'new'">
                <a-input-number v-model:value="record[column.dataIndex as string]" style="width: 80px" @change="changePrice(column.dataIndex, record)" :min="0" step="1" :precision="0" />
              </template>
              <template v-if="column.key === 'purchaseAllNum3' && mode === 'new'">
                <a-input-number v-model:value="record[column.dataIndex as string]" style="width: 80px" @change="changePrice(column.dataIndex, record)" :min="0" step="1" :precision="0" :max="99999" />
              </template>
              <template v-if="column.key === 'purchaseAllNum2' && mode === 'new'">
                <a-input v-model:value="record[column.dataIndex as string]" :maxlength="200" />
              </template>
              <template v-if="column.dataIndex === 'attachments'">
                <Upload
                  v-model:value="record[column.dataIndex]"
                  v-if="mode === 'new'"
                  :accept="'.jpg,.png,.bmp,.jpeg,.webp,.mp4,.mov,.avi,.wmv,.flv,.webm'"
                  :module="UploadFileModuleEnum.ReservationOrder"
                />
                <FileModal :list="record[column.dataIndex]" v-else />
              </template>
              <template v-if="column.key === 'operation' && mode === 'new'">
                <a-button @click="deleteItem(record)">删除</a-button>
              </template>
            </template>
            <template #headerCell="{ column }">
              <template v-if="(column as any).tooltip">
                {{ column.title }}
                <a-tooltip>
                  <template #title>{{ (column as any).tooltip }}</template>
                  <InfoCircleOutlined></InfoCircleOutlined>
                </a-tooltip>
              </template>
            </template>
            <template #expandedRowRender="{ record }">
              <a-table v-if="record.bookingOrderDetailItems.length > 0" :columns="innerColumns" :data-source="record.bookingOrderDetailItems" :pagination="false">
                <template #bodyCell="{ column, record, index }">
                  <template v-if="column.key === 'operation' && mode === 'new'">
                    <a-button @click="deleteChildItem(record, index)">删除</a-button>
                  </template>
                </template>
                <template #headerCell="{ column }">
                  <template v-if="(column as any).tooltip">
                    {{ column.title }}
                    <a-tooltip>
                      <template #title>{{ (column as any).tooltip }}</template>
                      <InfoCircleOutlined></InfoCircleOutlined>
                    </a-tooltip>
                  </template>
                </template>
              </a-table>
            </template>
          </a-table>

          <div class="flex justify-end mt-10" v-if="mode !== 'new'">
            <a-pagination
              v-model:current="productPage"
              v-model:page-size="productPageSize"
              show-quick-jumper
              :total="productTotal"
              show-size-changer
              :page-size-options="['20', '50', '100', '250', '500']"
              @change="getBookingOrderDetailListByPage"
              size="small"
              :showTotal="(total) => `共 ${total} 条`"
            >
              <template #buildOptionText="props">
                <span>{{ props.value }}条/页</span>
              </template>
            </a-pagination>
          </div>
        </div>
      </template>
    </EasyForm>
    <div class="mb-50">&nbsp;</div>
    <div class="submit-row">
      <template v-if="type == 'new' || type == 'edit'">
        <a-button class="ml mr" type="primary" @click="handleSubmit(true)">提交审核</a-button>
        <a-button class="ml mr" @click="handleSubmit(false)">保存暂不提交</a-button>
      </template>
      <template v-if="type === 'review'">
        <a-button class="ml mr" @click="reviewType(1)">审核通过</a-button>
        <a-button class="ml mr" @click="reviewType(2)">审核拒绝</a-button>
      </template>
      <a-button v-if="type === 'delivery'" type="primary" class="ml mr" @click="handleDelivery">提交</a-button>
      <a-button v-if="type === 'confirmArrival'" type="primary" class="ml mr" @click="handleConfirmArrival">确认到货</a-button>
      <a-button v-if="type === 'detail'" class="ml mr" @click="() => closePage()">关闭</a-button>
      <a-button v-if="formData.id && btnPermission[111006]" class="ml mr" @click="reviewType(6)">审核记录</a-button>
    </div>
    <purchaseOrderDlg ref="purchaseOrderDlgRef" @setValue="setFormValue">
      <template #product_type="{ row }">
        <span>{{ productTypeMap[row.product_type] }}</span>
      </template>
    </purchaseOrderDlg>
    <reviewTimer ref="reviewTimerRef"></reviewTimer>
    <SelectProduct ref="selectProductRef" :syncConfirm="onAddProduct"></SelectProduct>
    <AuditConfirm ref="auditConfirmRef" @audit="onAuditConfirm" />
  </div>
</template>

<script lang="ts" setup>
import { message } from 'ant-design-vue'
import { debounce } from 'lodash'
import { InfoCircleOutlined } from '@ant-design/icons-vue'
import dayjs from 'dayjs'

import Upload from '@/components/Upload.vue'
import { productTypeMap } from '@/common/map'
import { download, isEmpty } from '@/utils/index'

import reviewTimer from './components/reviewTimer.vue'
import purchaseOrderDlg from './components/purchaseOrderDlg.vue'
import { setFormItems, setColumns, setInnerColumns } from './detail.data'

import {
  GetBookingPurchaseOrderDetail,
  ChangeModel,
  AddBookingOrder,
  GetBookingOrderDetail,
  Edit,
  UpdateLogisticsInfo,
  BatchAudit,
  ConfirmTheArrival,
  CheckBookingOrderStatus,
  GetBookingOrderDetailList,
  ImportProductInfo,
  DownloadImportTemplate,
} from '@/servers/ReservationApi'

const router = useRouter()
const formRef = ref()
const { closePage, setRefreshByPath } = usePage({ initMethod: () => initMethod(), formRef })
const { btnPermission } = usePermission()

const formData = ref<Record<string, any>>({
  scheduled_arrival_time: dayjs().add(30, 'm').format('YYYY-MM-DD HH:mm:ss'),
})

const purchaseOrderDlgRef = ref()

const formItems = ref<EasyFormItemProps[]>([])

const type = ref('new')

const tableData = ref<any[]>([])
const columns = ref<any[]>([])
const innerColumns = ref<any[]>([])
const setFormValue = async (val, callback) => {
  await CheckBookingOrderStatus({
    purchase_order_numbers: val.map((item) => item.number),
    booking_order_id: formData.value.id,
  })
  callback()

  formRef.value.changeValue({
    company_supplier_id: `${val[0].company_supplier_id || ''}`,
    product_type: val[0].product_type,
    warehouse_id: `${val[0].warehouse_id || ''}`,
    purchase_order_numbers: val.map((item) => item.number),
  })
  nextTick(() => {
    tableData.value = []
    message.info('采购单编号已改变，请重新添加商品并填写商品预约数量')
  })
}

const selectProductRef = ref()
const handleAddPro = () => {
  const rows = tableData.value.map((f) => f.bookingOrderDetailItems.map((sub) => ({ $uuid: `${sub.k3_sku_id}-${sub.purchase_order_number}` }))).flat(1)

  if (!formData.value?.purchase_order_numbers?.length) return message.error('请选择采购单')
  selectProductRef.value.open(
    {
      search: ['商品名称SKU', '商品编号', '聚水潭商品编号', '采购单编号'],
      left: ['商品主图', '商品名称', '商品编码K3', '聚水潭编号', '颜色规格', '计价单位', '采购单编号', '采购总数', '已预约入库数量'],
      right: ['商品名称', '商品编码K3', '采购单编号'],
      width: 1300,
      api: GetBookingPurchaseOrderDetail,
      params: {
        numbers: formData.value.purchase_order_numbers,
        booking_order_id: formData.value.id,
      },
      keyField: '$uuid',
      dataFormat: (data) => {
        return data.map((f) => ({ ...f, $uuid: `${f.k3_sku_id}-${f.purchase_order_number}` }))
      },
    },
    rows,
  )
}
const onAddProduct = async (rows) => {
  console.log('onAddProduct', rows)
  try {
    let k3_sku_ids = rows.map((item) => item.k3_sku_id)
    k3_sku_ids = Array.from(new Set(k3_sku_ids))
    let numbersArr = rows.map((item) => item.purchase_order_number)
    numbersArr = Array.from(new Set(numbersArr))
    const res = await ChangeModel({
      k3_sku_ids,
      numbers: numbersArr,
      page: 1,
      pageSize: 9999,
      booking_order_id: formData.value.id,
    })
    const newData = (res.data || []).map((f) => {
      const { scheduled_quantity, attachments } = tableData.value.find((item) => item.k3_sku_id === f.k3_sku_id) || {}
      f.scheduled_quantity = scheduled_quantity || f.scheduled_quantity || f.total_purchase_quantity - f.total_purchase_scheduled_quantity
      f.attachments = attachments

      f.bookingOrderDetailItems = f.bookingOrderDetailItems.map((item) => ({
        ...item,
        $this_purchase_scheduled_quantity: item.purchase_scheduled_quantity,
        $this_gift_scheduled_quantity: item.gift_scheduled_quantity,
        $this_scheduled_quantity: item.scheduled_quantity,
      }))
      InputPrice('scheduled_quantity', f)
      return f
    })

    tableData.value = newData

    return Promise.resolve(true)
  } catch (error) {
    console.error('添加商品时发生错误:', error)
    message.error('添加商品失败')
    return Promise.reject(error)
  }
}
// 导入商品
const handleImportPro = async (file: File) => {
  const formdata = new FormData()
  formdata.append('form_file', file)
  formData.value.id && formdata.append('booking_order_id', formData.value.id)
  for (const number of formData.value.purchase_order_numbers) {
    formdata.append('numbers', number)
  }
  const { data } = await ImportProductInfo(formdata)
  if (!data.datas?.length) {
    message.success('导入的数据不包含在所选采购单下')
    return
  }
  const ids = data.datas.map((f) => f.k3_sku_id)
  tableData.value = [
    ...tableData.value.filter((f) => !ids.includes(f.k3_sku_id)),
    ...data.datas.map((f) => {
      f.bookingOrderDetailItems = f.bookingOrderDetailItems.map((item) => ({
        ...item,
        $this_purchase_scheduled_quantity: item.purchase_scheduled_quantity,
        $this_gift_scheduled_quantity: item.gift_scheduled_quantity,
        $this_scheduled_quantity: item.scheduled_quantity,
      }))
      InputPrice('scheduled_quantity', f)
      return f
    }),
  ]
}
// 下载导入模板
const handleDownloadTemplate = () => {
  DownloadImportTemplate().then((res) => {
    download(res, '商品导入模板.xlsx')
  })
}

const deleteItem = (item) => {
  tableData.value = tableData.value.filter((f) => f.k3_sku_id != item.k3_sku_id)
}

const deleteChildItem = (item, index) => {
  if (item.scheduled_quantity > 0) {
    message.info('当前存在本次预约入库数量不可以删除')
    return
  }
  tableData.value.forEach((f) => {
    if (f.parentkey == item.key) {
      f.bookingOrderDetailItems.splice(index, 1)
    }
  })

  // 如果采购单全部删除，同步删除该商品
  tableData.value = tableData.value.filter((f) => f.bookingOrderDetailItems.length)
}

// 输入价格
const InputPrice = (dataIndex, item) => {
  console.log('InputPrice')
  if (!Number.isNaN(item[dataIndex])) {
    if (dataIndex == 'packing_qty' && item.packing_qty > 0) {
      item.box_num = Math.ceil(item.scheduled_quantity / item.packing_qty)
      item.remaining_quantity = item.scheduled_quantity - item.packing_qty * Math.floor(item.scheduled_quantity / item.packing_qty)
    }
    if (dataIndex == 'scheduled_quantity' && item.scheduled_quantity != null) {
      let count = item[dataIndex]
      item.bookingOrderDetailItems.forEach((subItem, subIndex) => {
        // 可分配的本次最大入库数量
        // const maxSQ = subItem.total_purchase_quantity - subItem.total_purchase_scheduled_quantity + subItem.$this_scheduled_quantity
        // 可分配的本次最大采购数量
        const maxPSQ = subItem.purchase_quantity - subItem.purchase_scheduled_qty + subItem.$this_purchase_scheduled_quantity
        // 可分配的本次最大赠品数量
        const maxGSQ = subItem.gift_purchase_quantity - subItem.gift_purchase_scheduled_quantity + subItem.$this_gift_scheduled_quantity

        // 计算得到本次采购预约数量
        subItem.purchase_scheduled_quantity = Math.min(maxPSQ, count, subItem.purchase_quantity)
        count -= subItem.purchase_scheduled_quantity

        // 计算得到赠品本次预约数量
        const isLast = subIndex === item.bookingOrderDetailItems.length - 1
        if (isLast) {
          subItem.gift_scheduled_quantity = count
        } else {
          subItem.gift_scheduled_quantity = Math.min(maxGSQ, count)
          count -= subItem.gift_scheduled_quantity
        }

        // 计算本次预约入库数量
        subItem.scheduled_quantity = subItem.gift_scheduled_quantity + subItem.purchase_scheduled_quantity
      })

      if (item.packing_qty < 0 || !item.packing_qty) {
        item.packing_qty = 0
        return
      }
      if (item.scheduled_quantity > item.total_purchase_quantity - item.total_purchase_scheduled_quantity) {
        item.scheduled_quantity = Math.max(item.scheduled_quantity, 0)
      }
      // debugger
      item.box_num = Math.ceil(item.scheduled_quantity / item.packing_qty)
      item.remaining_quantity = item.scheduled_quantity - item.packing_qty * Math.floor(item.scheduled_quantity / item.packing_qty)
    }
  }
}
const changePrice = debounce(InputPrice, 300)

// 审核
const reviewTimerRef = ref()
const auditConfirmRef = ref()
const reviewType = (val) => {
  if (val == 1) {
    auditConfirmRef.value.open(true)
  } else if (val === 2) {
    auditConfirmRef.value.open(false)
  } else {
    reviewTimerRef.value.setModel(true, formData.value.id)
  }
}

const onAuditConfirm = async (data) => {
  const { audit_opinion, is_pass } = data
  const params = {
    is_pass,
    audit_opinion,
    ids: [formData.value.id],
  }
  const res = await BatchAudit(params)
  if (res.success) {
    message.success('操作成功')
    setRefreshByPath('/bookingOrder')
    closePage()
  } else {
    message.error(res.message)
  }
}

// 提交
const handleSubmit = async (is_pass) => {
  const formState = await formRef.value.validate(undefined, !is_pass ? ['shipping_method', 'logistics_company_id', 'tracking_number', 'contact_person', 'contact_phone'] : [])
  const now = new Date().getTime()
  const scheduled_arrival_time = new Date(formState.scheduled_arrival_time).getTime()
  if (type.value == 'new' && now > scheduled_arrival_time) return message.info('预约入库时间不能小于当前时间,请重新选择')
  if (tableData.value.length == 0) return message.info('请选择采购商品')
  let errMsg = ''
  tableData.value.forEach((item) => {
    if (isEmpty(item.packing_qty, { rest: [0] }) || isEmpty(item.scheduled_quantity, { rest: [0] })) {
      errMsg = '标准装箱数和本次预约入库数量不能为0'
    }
    if (!item.attachments?.length) {
      errMsg = '请上传回货图片'
    }
  })
  if (errMsg) return message.warn(errMsg)
  const params = {
    id: formData.value.id,
    ...formState,
    is_pass,
    bookingOrderDetailList: tableData.value.map((f) => ({ ...f, attachments: (f.attachments || []).map((f) => f.id) })),
  }
  const api = !formData.value.id ? AddBookingOrder : Edit
  const res = await api(params)
  if (res.success == true) {
    message.success('提交成功')
    setRefreshByPath('/bookingOrder')
    closePage()
  } else {
    message.error(res.message)
  }
}

// 发货
const handleDelivery = async () => {
  const formState = await formRef.value.validate()
  // if (!formState.logistics_company_id) return message.error('请选择物流公司')
  const params = {
    id: formData.value.id,
    ...formState,
  }
  const res = await UpdateLogisticsInfo(params)
  if (res.success == true) {
    message.success('提交成功')
    setRefreshByPath('/bookingOrder')
    closePage()
  } else {
    message.error(res.message)
  }
}

// 确认到货
const handleConfirmArrival = async () => {
  const formState = await formRef.value.validate()
  const params = {
    booking_order_id: formData.value.id,
    ...formState,
    file_ids: formData.value.fileIds.map((item) => item.id),
  }
  const res = await ConfirmTheArrival(params)
  if (res.success == true) {
    message.success('确认到货成功')
    setRefreshByPath('/bookingOrder')
    closePage()
  } else {
    message.error(res.message)
  }
}

const productTotal = ref(0)
const productPage = ref(1)
const productPageSize = ref(20)
const getBookingOrderDetailListByPage = async (page) => {
  const { data } = await GetBookingOrderDetailList({
    booking_order_id: formData.value.id,
    page,
    pageSize: mode.value === 'new' ? 9999 : productPageSize.value,
  })
  if (data.list) {
    productTotal.value = data.total
    productPage.value = page
    tableData.value = data.list.map((f) => ({
      ...f,
      attachments: f.files || [],
      files: undefined,
      $scheduled_quantity: f.scheduled_quantity,
      bookingOrderDetailItems: f.bookingOrderDetailItems.map((item) => ({
        ...item,
        $this_purchase_scheduled_quantity: item.purchase_scheduled_quantity,
        $this_gift_scheduled_quantity: item.gift_scheduled_quantity,
        $this_scheduled_quantity: item.scheduled_quantity,
      })),
    }))
  }
}

const mode = computed(() => {
  const name = router.currentRoute.value.name as string
  return /(编辑|新增)$/.test(name) ? 'new' : 'detail'
})
const initMethod = async () => {
  const name = router.currentRoute.value.name as string
  type.value = /新增$/.test(name) ? 'new' : /编辑$/.test(name) ? 'edit' : /发货$/.test(name) ? 'delivery' : /审核$/.test(name) ? 'review' : 'detail'

  formItems.value = setFormItems(formData, purchaseOrderDlgRef, formRef)
  await nextTick()
  const query = router.currentRoute.value.query
  if (query?.uc) {
    const values = JSON.parse(localStorage.getItem('srm_booking_order_create') || '{}')[query.uc as string]
    if (!values) return message.error('预约单不存在, 请重新生成')
    formRef.value.changeValue(values)
  }

  formData.value.id = router.currentRoute.value.params.id

  if (formData.value.id) {
    const res = await GetBookingOrderDetail({ id: formData.value.id, is_check: !['new', 'edit'].includes(type.value) })
    formData.value.audit_status = res.data.audit_status
    await formRef.value.changeValue({
      ...res.data,
      logistics_company_id: res.data.logistics_company_id || undefined,
    })
    await formRef.value.changeValue('shipping_method', res.data.shipping_method, true)
    formRef.value.changeItem('jst_poi_id', { hide: false })
    await getBookingOrderDetailListByPage(1)

    // 可以发货
    if (type.value === 'delivery') {
      formRef.value.changeItem('shipping_method', { isDetail: false })
      formRef.value.changeItem('logistics_company_id', { isDetail: false })
      formRef.value.changeItem('tracking_number', { isDetail: false })
      formRef.value.changeItem('contact_person', { isDetail: false })
      formRef.value.changeItem('contact_phone', { isDetail: false })
    }

    // 已通过 & 可以确认到货
    if (formData.value.audit_status === AuditStatusEnum.已通过 && /(确认到货|查看)$/.test(name)) {
      const isDetail = !!res.data.actual_arrival_time || /查看$/.test(name)
      if (/确认到货$/.test(name)) {
        type.value = 'confirmArrival'
      }
      formRef.value.changeItem('到货信息', { hide: false })
      formRef.value.changeItem('actual_arrival_time', { hide: false, isDetail })
      formRef.value.changeItem('fileIds', { hide: false, isDetail })
    }
  }

  columns.value = [...setColumns, ...(mode.value === 'detail' ? [] : [{ key: 'operation', title: '操作', width: 100 }])]

  innerColumns.value = [...setInnerColumns, ...(mode.value === 'detail' ? [] : [{ key: 'operation', title: '操作' }])]
}
</script>
