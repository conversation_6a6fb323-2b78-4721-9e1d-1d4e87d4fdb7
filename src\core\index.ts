/**
 * 银行家舍入法（四舍六入五成双）
 * 如果舍去的数字正好是 5：最后保留的数字是偶数，舍入到偶数（向下），最后保留的数字是奇数，舍入到奇数（向上）
 * @param digits number
 * @returns string
 */
// eslint-disable-next-line no-extend-native
Number.prototype.bankRound = function bankRound(digits) {
  // 检查 digits 是否在有效范围内
  if (digits < 0 || digits > 20) {
    throw new RangeError('Digits out of range')
  }

  if (Number.isNaN(Number(this))) {
    throw new Error('Invalid number')
  }

  // 处理特殊情况
  if (this === 0) {
    return `0${digits > 0 ? `.${'0'.repeat(digits)}` : ''}`
  }

  // 获取整数部分和小数部分
  const [integerPart, decimalPart] = new Intl.NumberFormat('en-US', { minimumFractionDigits: digits, maximumFractionDigits: digits + 2 }).format(this as number).split('.')
  // 如果小数部分不存在，直接返回格式化的整数部分
  if (!decimalPart) {
    return `${integerPart}${digits > 0 ? `.${'0'.repeat(digits)}` : ''}`
  }
  // 位数不足则补零
  const decimalPartPadZeros = decimalPart.length < digits ? decimalPart.padEnd(digits, '0') : decimalPart
  // 计算要保留的小数位数
  const decimal = decimalPartPadZeros.slice(0, digits)
  let increment = 0
  // 四舍五入
  if (Number(decimalPartPadZeros[digits]) >= 5) {
    increment = 1
    // 如果前一位是偶数，向下舍入
    if (decimalPartPadZeros[digits - 1] && decimalPartPadZeros[digits] === '5' && Number(decimalPartPadZeros[digits - 1]) % 2 === 0) {
      increment = 0
    }
  }
  // console.log('padExit', padExit, decimal, this, typeof decimalPartPadZeros[digits])
  // 如果小数位数为0，则返回整数部分
  if (decimal === '' && digits === 0) return `${Number(integerPart) + increment}`

  const decimalOfBank = Number(decimal) + increment

  const decimalResult = decimalOfBank >= 10 ** (digits - 1) ? `${decimalOfBank}` : `${decimalOfBank}`.padStart(digits, '0')
  // console.log('decimalResult', decimalResult)
  return `${integerPart}.${decimalResult}`
}
// console.log('1 结果：', (1).bankRound(1))
// console.log('1 结果：', (1).bankRound(2))
// console.log('1.1 结果：', (1.1).bankRound(2))
// console.log('1.01 结果：', (1.01).bankRound(2))
// console.log('1.055 结果：', (1.055).bankRound(2))
// console.log('1.065 结果：', (1.065).bankRound(2))
// console.log('1.000015 结果：', (1.000015).bankRound(2))
// console.log('1.2 结果：', (1.2).bankRound(2))
// console.log('1.11111 结果：', (1.11111).bankRound(2))
// console.log('0.********* 结果：', (0.*********).bankRound(8))

/**
 * 四舍五入
 */
// eslint-disable-next-line no-extend-native
Number.prototype.roundNext = function roundNext(digits: number): string {
  // 检查 digits 是否在有效范围内
  if (digits < 0 || digits > 20) {
    throw new RangeError('Digits out of range')
  }

  if (Number.isNaN(Number(this))) {
    throw new Error('Invalid number')
  }

  const formatter = new Intl.NumberFormat('en-US', {
    minimumFractionDigits: digits,
    maximumFractionDigits: digits,
  })
  return formatter.format(this as number)
}
