<template>
  <div class="main">
    <div class="cardBox">
      <div class="card">
        <div class="title">操作界面水印</div>
        <a-button :disabled="!btnPermission[33001]" @click="OperationInterfaceWatermarkRef.open()">设置</a-button>
        <a-button :disabled="!btnPermission[33001]" @click="log(1)">日志</a-button>
        <div class="description">配置后将生效于所有操作界面。</div>
      </div>
    </div>
    <OperationInterfaceWatermark :rolesoptions="rolesOptions" ref="OperationInterfaceWatermarkRef" />
    <!-- 日志 -->
    <log-drawer ref="logDrawerRef" />
  </div>
</template>

<script setup lang="ts">
import LogDrawer from './components/LogDrawer.vue'
import OperationInterfaceWatermark from './components/OperationInterfaceWatermark.vue'

import { GetRoleSelectOption } from '@/servers/WaterMark'

const logDrawerRef = ref()
const rolesOptions = ref([])
const OperationInterfaceWatermarkRef = ref()
const { btnPermission } = usePermission()
const log = (page) => {
  logDrawerRef.value.open(page)
}
onMounted(() => {
  setTimeout(() => {
    if (btnPermission.value[33001]) {
      GetRoleSelectOption({ scope: 1 }).then((res) => {
        res.data.forEach((x) => {
          x.label = x.role_name
          x.value = x.role_id
        })
        rolesOptions.value = res.data
      })
    }
  }, 500)
})
</script>

<style lang="scss" scoped>
.cardBox {
  border: 1px solid #dcdcdc;
  border-radius: 4px;

  .card {
    display: flex;
    gap: 12px;
    align-items: center;
    padding: 12px 24px;
    font-size: 12px;
    color: #000;

    .title {
      margin-right: 40px;
    }

    .description {
      margin-left: 10px;
      color: rgb(0 0 0 / 50%);
    }
  }
}
</style>
