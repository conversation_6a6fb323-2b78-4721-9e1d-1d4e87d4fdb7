<template>
  <div class="main">
    <!-- 过滤器 -->
    <div v-show="!showFilter" class="formBox">
      <Form ref="formRef" v-model:form="formArr" :page-type="PageTypeEnum.SupplierOrganization" @search="search" @setting="tableRef?.showTableSetting()"></Form>
    </div>
    <!-- 表格 -->
    <BaseTable ref="tableRef" v-model:form="formArr" @pageChange="search()" :page-type="PageTypeEnum.SupplierOrganization" :get-list="GetListFn" :auto-search="false">
      <template #source_type="{ row }">
        <span>{{ getSource(row.source_type) }}</span>
      </template>
      <template #create_at="{ row }">
        <span>{{ row.create_at ? row.create_at.slice(0, 16) : '' }}</span>
      </template>
      <template #update_at="{ row }">
        <span>{{ row.update_at ? row.update_at.slice(0, 16) : '' }}</span>
      </template>
      <template #status="{ row }">
        {{ row.status === 0 ? '停用' : '启用' }}
      </template>
      <template #fix_option="{ row, column }">
        <!-- <a-button class="ml mr" id="clientListsDepartment" :disabled="!btnPermission[22102]" @click="viewDepartment(row)">部门</a-button> -->
        <!-- <a-button class="ml mr" id="clientListsDetail" :disabled="!btnPermission[22101]" @click="detail(row)">查看</a-button> -->
        <RightOperate :list="rightOperateList" :row="row" :column="column" />
      </template>
    </BaseTable>
    <!-- 确认弹窗 -->
    <a-modal :zIndex="10000" v-model:open="modalData.isShow" :title="modalData.title">
      <div class="modalContent">{{ modalData.content }}</div>
      <template #footer>
        <a-button v-if="modalData.isConfirmBtn" :danger="modalData.okType === 'danger'" type="primary" style="margin-right: 10px" @click="modalData.okFn">{{ modalData.confirmBtnText }}</a-button>
        <a-button v-if="modalData.isCancelBtn" @click="modalData.isShow = false">取消</a-button>
      </template>
    </a-modal>

    <!-- 部门 -->
    <department-drawer ref="departmentDrawerRef" />
    <!-- 查看 -->
    <detail-drawer ref="detailDrawerRef" />
  </div>
</template>
<script lang="ts" setup>
import { onMounted, ref, reactive } from 'vue'

import Form from '@/components/Form.vue'
import BaseTable from '@/components/BaseTable.vue'
import { Enum2Options } from '@/utils'

import DetailDrawer from './components/DetailDrawer.vue'
import DepartmentDrawer from './components/DepartmentDrawer.vue'

import { GetEnum } from '@/servers/Common'
import { GetSupplierOrganizationalList } from '@/servers/SupplierOrganizational'

import { PageTypeEnum } from '@/enums/tableEnum'

const enumData: any = ref([])

const GetListFn = ref(GetSupplierOrganizationalList)

const showFilter = ref(false)

const sourceOptions = ref(Enum2Options(SupplierSourceTypeEnum))

const formRef = ref()
const tableRef = ref()

const search = () => tableRef.value.search()

const formArr: any = ref([
  {
    label: '搜索供应商编号',
    value: null,
    type: 'input',
    key: 'supplier_id',
  },
  {
    label: '搜索供应商名称',
    value: null,
    type: 'input',
    key: 'supplier_name',
  },
  {
    label: '状态',
    value: null,
    type: 'select',
    selectArr: [],
    key: 'status',
  },
  {
    label: '来源',
    value: null,
    type: 'select',
    selectArr: sourceOptions,
    key: 'source_type',
  },
  {
    label: '创建',
    value: null,
    type: 'range-picker',
    key: 'create_at',
    formKeys: ['created_start_time', 'created_end_time'],
    placeholder: ['创建开始时间', '创建结束时间'],
  },
  {
    label: '修改',
    value: null,
    type: 'range-picker',
    key: 'update_at',
    formKeys: ['modified_start_time', 'modified_end_time'],
    placeholder: ['修改开始时间', '修改结束时间'],
  },
])

const rightOperateList = ref([
  {
    label: '部门',
    show: 22102,
    onClick: ({ row }) => {
      viewDepartment(row)
    },
  },
  {
    label: '查看',
    show: 22101,
    onClick: ({ row }) => {
      detail(row)
    },
  },
])

// 确认框数据
const modalData = reactive({
  isShow: false,
  isConfirmBtn: true,
  isCancelBtn: true,
  confirmBtnText: '确定',
  okType: 'primary',
  title: '',
  content: '',
  okFn: () => {
    modalData.isShow = false
  },
})
// 部门
const departmentDrawerRef = ref<any>(null)
// 查看
const detailDrawerRef = ref<any>(null)

onMounted(() => {
  getEnum()
  search()
  initScreening()
})

const getSource = (source) => {
  const option: any = sourceOptions.value.find((e: any) => e.value == source)
  return option ? option.label : ''
}

const initScreening = () => {
  const obj = JSON.parse(localStorage.getItem('screeningObj') || '{}')
  if (obj.clientLists) {
    const arr: any = []
    obj.clientLists.forEach((x) => {
      formArr.value.forEach((y: any) => {
        if (x.key === y.key) {
          y.isShow = x.isShow
          arr.push(y)
        }
      })
    })
    formArr.value = arr
  } else {
    formArr.value.forEach((item) => {
      item.isShow = true
    })
  }
}

// 部门
const viewDepartment = (item) => {
  departmentDrawerRef.value.open(item)
}
// 详情
const detail = (item) => {
  detailDrawerRef.value.open(item.supplier_id, sourceOptions.value)
}
// 获取所有枚举选项
const getEnum = () => {
  GetEnum().then((res) => {
    enumData.value = res.data
    formArr.value.forEach((item) => {
      if (item.key == 'status') {
        item.selectArr = enumData.value.common.status
      }
      if (item.key == 'scope') {
        item.selectArr = enumData.value.role.scope
      }
    })
  })
}
</script>

<style lang="scss" scoped>
.main {
  display: flex;
  flex-direction: column;
  height: 100%;

  .formBox {
    .operatingAreaBox {
      display: flex;
      align-items: center;
      height: 32px;

      .tag {
        padding: 0 10px;
        cursor: pointer;
        user-select: none;
      }

      .btn {
        margin-right: 10px;
      }
    }
  }

  .btnBox {
    display: flex;
    align-items: center;
    justify-content: space-evenly;
  }
}

.modalContent {
  font-size: 14px;
  word-break: break-word;
  white-space: pre-wrap;
}

::v-deep(.ant-form-item) {
  align-items: flex-start;

  .ant-form-item-label {
    display: flex;
    justify-content: flex-end;
    width: 100px;
    min-width: 100px;
    margin-right: 30px;

    label {
      &::after {
        display: none !important;
      }
    }
  }
}
</style>
