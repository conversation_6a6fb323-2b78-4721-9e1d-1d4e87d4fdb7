import { request } from './request'

// 获取供应商组织架构列表
export const GetSupplierOrganizationalList = (data) => request({ url: '/api/SupplierOrganizational/GetSupplierOrganizationalList', data })

// 获取供应商信息
export const GetSupplierOrganizationalMessage = (data) => request({ url: '/api/SupplierOrganizational/GetSupplierOrganizationalMessage', data }, 'GET')

// 获取供应商部门列表
export const GetSupplierDepartmentList = (data) => request({ url: '/api/SupplierOrganizational/GetSupplierDepartmentList', data }, 'GET')

// 创建或更新供应商部门
export const CreateOrUpdateSupplierDepartment = (data) => request({ url: '/api/SupplierOrganizational/CreateOrUpdateSupplierDepartment', data })

// 删除供应商部门
export const DeleteSupplierDepartment = (data) => request({ url: '/api/SupplierOrganizational/DeleteSupplierDepartment', data }, 'GET')
