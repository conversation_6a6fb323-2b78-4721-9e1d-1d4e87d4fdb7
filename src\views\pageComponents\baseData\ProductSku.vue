<!-- 商品库列表 -->
<template>
  <div class="flex flex-col h-full main">
    <Form ref="formRef" v-model:form="formArr" :page-type="PageTypeEnum.ProductSku" @search="handleSearch" @setting="tableRef?.showTableSetting()">
      <template #labelTop>
        <a-radio-group class="mb-20px" v-model:value="tagsSearchType">
          <a-radio value="1">包含任意一个标签</a-radio>
          <a-radio value="2">同时包含指定标签</a-radio>
        </a-radio-group>
      </template>
    </Form>
    <BaseTable ref="tableRef" :page-type="PageTypeEnum.ProductSku" v-model:form="formArr" :get-list="GetProductSkuList" :form-format="formFormat" :isIndex="true">
      <template #right-btn>
        <a-button v-if="syncBtnFlag && btnPermission[120101]" type="primary" @click="syncSku()" :loading="syncLoading" :disabled="syncLoading">同步商品{{ syncLoading ? '中...' : '' }}</a-button>
      </template>
      <template #category="{ row }">
        <span>{{ formSelectMap?.productType?.[`${row.category}`] || row.category }}</span>
      </template>
    </BaseTable>
  </div>
</template>
<script lang="ts" setup>
import { message } from 'ant-design-vue'

import { GetProductSkuList, GetProductCategorySelect, GetProductLabelSelect, SyncProductSkuInfo, GetProductSkuInfoSyncState } from '@/servers/BusinessCommon'

import { PageTypeEnum } from '@/enums/tableEnum'

const { btnPermission } = usePermission()

const formRef = ref()
const tableRef = ref()
const tagsSearchType = ref<'1' | '2' | null>('1')
const formArr = ref([
  { label: '商品编码/聚水潭编码', value: '', type: 'input', key: 'product_sku_code' },
  { label: '商品名称', value: '', type: 'input', key: 'product_name' },
  { label: '规格型号', value: '', type: 'input', key: 'type_specification' },
  { label: '款式编码', value: '', type: 'input', key: 'style_code' },
  { label: '材质', value: '', type: 'input', key: 'material' },
  { label: '默认供应商名称', value: '', type: 'input', key: 'supplier_name' },
  {
    label: '商品分类',
    value: null,
    type: 'select',
    selectArr: [],
    key: 'product_type',
    api: GetProductCategorySelect,
  },
  { label: 'SRS平台商品编码', value: '', type: 'input', key: 'srs_platform_prod_code' },
  { label: 'SRS供应商商品编码', value: '', type: 'input', key: 'srs_supplier_prod_code' },
  {
    label: '商品标签',
    value: [],
    multiple: true,
    type: 'supplier',
    selectArr: [],
    key: 'product_tags',
    topSlot: 'labelTop',
    api: GetProductLabelSelect,
    keyword: 'keyword',
    pageSize: 9999,
    formatter: (item) => ({
      label: item.label,
      value: item.value,
    }),
    onClear: () => {
      tagsSearchType.value = null
    },
    onBeforeConfirm: (value) => {
      let msg = ''
      if (!tagsSearchType.value) msg = '请选择包含标签方式'
      if (!value?.length) msg = '请选择商品标签'
      if (msg) {
        message.warn(msg)
        return false
      }
    },
  },
])

const { formSelectMap } = useSearchForm(formArr)

onActivated(() => {
  getSyncSkuStatus()
})

onDeactivated(() => syncSkuClose())

const formFormat = (data) => ({
  ...data,
  tags_search_type: tagsSearchType.value,
})

const handleSearch = () => {
  if (tableRef.value) tableRef.value.search()
}

const syncLoading = ref(false)
const timer = ref()
const syncSkuClose = () => {
  clearInterval(timer.value)
  timer.value = null
}
const syncSku = async () => {
  syncLoading.value = true
  await SyncProductSkuInfo()
  await getSyncSkuStatus()
}

const syncBtnFlag = ref(false)
const getSyncSkuStatus = () => {
  syncSkuClose()
  const fn = () => {
    GetProductSkuInfoSyncState()
      .then((res) => {
        if (res.data) {
          if (syncLoading.value) message.success('同步完成')
          syncSkuClose()
        }
        syncLoading.value = !res.data
      })
      .catch(() => {
        syncLoading.value = true
      })
      .finally(() => {
        syncBtnFlag.value = true
      })
  }
  fn()
  timer.value = setInterval(fn, 1000 * 8)
}
</script>
