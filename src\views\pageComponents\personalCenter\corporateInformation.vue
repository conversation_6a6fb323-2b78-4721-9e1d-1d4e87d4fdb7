<template>
  <div class="main !p-0 c-#333">
    <div class="px-12 py-8 flex justify-between items-center bg-[#f8f8f9] min-h-24px">
      <span class="text-lg font-bold">{{ statusText }}</span>
      <a-button type="primary" size="small" @click="handleEdit" :disabled="isHidden" v-if="btnPermission[71001]">
        <template #icon>
          <EditOutlined />
        </template>
        编辑修改
      </a-button>
    </div>
    <div class="flex flex-1 update-drawer flex-col px-12">
      <a-tabs size="small" :active-key="activeKey" @change="tabChange">
        <a-tab-pane v-for="item in tabList" :key="item.id" :tab="item.name"></a-tab-pane>
      </a-tabs>
      <div class="flex-1 overflow-y-auto h-full box-border relative">
        <div v-if="data" class="absolute scroll-auto pb-16px w-full pr-16px box-border">
          <div class="title" id="baseInfo">基本信息</div>
          <a-descriptions :column="3" bordered size="small" :labelStyle="{ width: px2(220) + 'px' }" :contentStyle="{ width: px2(300) + 'px' }">
            <a-descriptions-item label="供应商名称">{{ data.supplier_name }}</a-descriptions-item>
            <a-descriptions-item label="供应商编码">{{ data.supplier_id }}</a-descriptions-item>
            <a-descriptions-item label="金蝶供应商编码">{{ data.k3_supplier_number }}</a-descriptions-item>
            <a-descriptions-item label="供应商类型">{{ supplierTypeMap[data.supplier_type] }}</a-descriptions-item>
            <a-descriptions-item label="采购性质">{{ purchaseNatureMap[data.purchase_nature!] }}</a-descriptions-item>
            <a-descriptions-item label="供应商分组">{{ data.supplier_group_name }}</a-descriptions-item>
            <a-descriptions-item label="承运方式">{{ carriageModeMap[data.carriage_mode!] }}</a-descriptions-item>
            <!-- <a-descriptions-item label="公司网址/1688店铺网址">{{ data.website }}</a-descriptions-item>
            <a-descriptions-item label="1688店铺名字">{{ data.shop_name }}</a-descriptions-item> -->
            <a-descriptions-item label="申请时间">{{ data.create_at }}</a-descriptions-item>
            <a-descriptions-item label="供应商状态">{{ statusMap[data.status] }}</a-descriptions-item>
            <a-descriptions-item label="描述说明" :span="3">{{ data.describes }}</a-descriptions-item>
            <a-descriptions-item label="营业执照/资质证书等" :span="3">
              <FileModal :list="data.business_license_files" />
            </a-descriptions-item>
          </a-descriptions>

          <div class="title" id="subsidiariesInfo">子公司信息</div>
          <SimpleTable :tableKey="companyTableKeys" :data="data.supplier_company_info_list" :row-style="rowStyle">
            <template #buyer_name="{ row }">
              <span>{{ row.buyer_name }}</span>
            </template>
          </SimpleTable>

          <div class="title" id="contactInfo">联系信息</div>
          <SimpleTable :tableKey="contactTableKeys" :data="data.supplier_contact_info_list" :row-style="rowStyle">
            <template #address="{ row }">{{ row.province }} {{ row.city }} {{ row.area }} {{ row.address }}</template>
            <template #is_default="{ row }">
              <a-checkbox v-model:checked="row.is_default" disabled />
            </template>
          </SimpleTable>

          <div class="title" id="financialInfo">财务信息</div>
          <div class="flex mb-4 text-xs">
            <div>结算方式: {{ settlementTypeMap[data.settlement_type!] }}</div>
            <div class="ml-20">发票类型: {{ invoiceTypeMap[data.invoice_type!] }}</div>
            <div class="ml-20">默认税率: {{ data.default_tax_rate }}%</div>
          </div>
          <SimpleTable :tableKey="financialTableKeys" :data="data.supplier_finance_info_list" :row-style="rowStyle">
            <template #collection_account_certificate_files="{ row }">
              <FileModal :list="row.collection_account_certificate_files" />
            </template>
            <template #settlement_type>
              {{ settlementTypeMap[data.settlement_type!] }}
            </template>
            <template #payment_method="{ row }">
              {{ paymentMethodMap[row.payment_method] }}
            </template>
            <template #account_type="{ row }">
              {{ accountTypeMap[row.account_type] }}
            </template>
            <template #is_default="{ row }">
              <a-checkbox v-model:checked="row.is_default" disabled />
            </template>
          </SimpleTable>

          <div class="title" id="deliveryInfo">发货信息</div>
          <SimpleTable :tableKey="deliveryTableKeys" :data="data.supplier_shipments_info_list" :row-style="rowStyle">
            <template #shipments_address="{ row }">{{ row.province }} {{ row.city }} {{ row.area }} {{ row.shipments_address }}</template>

            <template #is_default="{ row }">
              <a-checkbox v-model:checked="row.is_default" disabled />
            </template>
          </SimpleTable>

          <div class="title" id="userInfo">用户信息</div>
          <SimpleTable :tableKey="userTableKeys" :data="data.supplier_user_info_list" :row-style="rowStyle">
            <template #status="{ row }">
              {{ row.status == 1 ? '启用' : '禁用' }}
            </template>
          </SimpleTable>

          <!-- <div class="title" id="otherInfo">其他信息</div>
          <SimpleTable :tableKey="otherTableKeys" :data="[data]">
            <template #audit_status="{ row }">
              {{ auditStatusMap[row.audit_status] }}
            </template>
          </SimpleTable> -->
        </div>
      </div>
    </div>
    <SupplierUpdate ref="supplierUpdateRef" @refresh="getInfo" />
  </div>
</template>

<script lang="ts" setup>
import { EditOutlined } from '@ant-design/icons-vue'

import { px2 } from '@/utils'
import { statusMap, accountTypeMap, carriageModeMap, invoiceTypeMap, purchaseNatureMap, settlementTypeMap, supplierTypeMap, paymentMethodMap } from '@/common/map'

import SupplierUpdate from '@/views/pageComponents/supplierManagement/components/SupplierUpdate.vue'

import { IsCheckProcess } from '@/servers/Supplier'
import { GetSupplierMessage, GetSupplierAuditStatus } from '@/servers/SupplierIndividual'

const activeKey = ref('baseInfo')
const scrollFlag = ref(false)
const tabList = ref([
  { id: 'baseInfo', name: '基本信息' },
  { id: 'subsidiariesInfo', name: '子公司信息' },
  { id: 'contactInfo', name: '联系信息' },
  { id: 'financialInfo', name: '财务信息' },
  { id: 'deliveryInfo', name: '发货信息' },
  { id: 'userInfo', name: '用户信息' },
  // { id: 'otherInfo', name: '其他信息' },
])

const { btnPermission } = usePermission()

const isHidden = ref(false)

const supplierUpdateRef = ref()

const data = ref<any>({})

const statusText = ref('')

const tabChange = (key) => {
  activeKey.value = key
  // 滚动到对应id
  const element = document.querySelector(`.update-drawer #${key}`)
  if (element) {
    element.scrollIntoView({ behavior: 'smooth' })
    scrollFlag.value = true
    setTimeout(() => {
      scrollFlag.value = false
    }, 1000)
  }
}
// 获取信息
const getInfo = async () => {
  try {
    const res = await GetSupplierMessage()
    data.value = res.data
    const id = data.value?.supplier_id
    isHidden.value = (await IsCheckProcess({ id })).data
    getStatus()
  } catch (err) {
    console.log(err)
    isHidden.value = true
  }
}

// 获取状态
const getStatus = async () => {
  const res = await GetSupplierAuditStatus()
  statusText.value = res.data
}

const companyTableKeys = [
  { type: 'seq', title: '序号', width: px2(80) },
  { title: '子公司供应商编号', field: 'company_supplier_id' },
  { title: '供应商编号(聚水潭)', field: 'jst_supplier_id' },
  {
    title: '供应商子公司名称',
    field: 'company_supplier_name',
    dataType: 'input',
    required: true,
  },
  { title: '对接采购员', field: 'buyer_name', required: true },
  { title: '公司网址/1688店铺网址', field: 'website' },
  { title: '1688店铺名字', field: 'shop_name' },
]

// 联系信息表格
const contactTableKeys = [
  { type: 'seq', title: '序号', width: px2(80) },
  { title: '供应商名称/子公司名称', field: 'company_supplier_name', required: true },
  {
    title: '联系人姓名',
    field: 'name',
    dataType: 'input',
    required: true,
  },
  {
    title: '职务',
    field: 'job',
    dataType: 'input',
  },
  {
    title: '电话',
    field: 'phone_number',
    dataType: 'input',
  },
  {
    title: '手机',
    field: 'mobile_phone_number',
    dataType: 'input',
    required: true,
  },
  {
    title: '传真',
    field: 'fax_no',
    dataType: 'input',
  },
  {
    title: '邮箱',
    field: 'email',
    dataType: 'input',
  },
  {
    title: '联系地址',
    field: 'address',
    dataType: 'input',
    required: true,
  },
  { title: '设为默认', field: 'is_default' },
]

//  财务信息
const financialTableKeys = [
  { type: 'seq', title: '序号', width: px2(80) },
  { title: '供应商名称/子公司名称', field: 'company_supplier_name' },
  {
    title: '收款方式',
    field: 'payment_method',
    dataType: 'select',
    options: [
      { label: '银行', value: 1 },
      { label: '支付宝', value: 2 },
      { label: '微信', value: 3 },
      { label: '现金', value: 4 },
    ],
  },
  {
    title: '账户名称',
    field: 'account_name',
    dataType: 'input',
  },
  {
    title: '账户类型',
    field: 'account_type',
    dataType: 'select',
    options: [
      { label: '公户', value: 1 },
      { label: '私户', value: 2 },
    ],
  },
  {
    title: '收款卡号',
    field: 'collection_card_number',
    dataType: 'input',
  },
  {
    title: '收款银行支行',
    field: 'collection_bank',
    dataType: 'input',
  },
  { title: '收款账号盖章凭证', field: 'collection_account_certificate_files' },
  {
    title: '备注',
    field: 'remark',
    dataType: 'input',
  },
  {
    title: '设为默认',
    field: 'is_default',
  },
]

// 发货信息
const deliveryTableKeys = [
  { type: 'seq', title: '序号', width: px2(80) },
  { title: '供应商名称/子公司名称', field: 'company_supplier_name', required: true },
  { title: '发货地址', field: 'shipments_address', required: true },
  { title: '设为默认', field: 'is_default' },
]

// 用户信息
const userTableKeys = [
  { type: 'seq', title: '序号', width: px2(80) },
  {
    title: '姓名',
    field: 'real_name',
    dataType: 'input',
  },
  {
    title: '联系方式',
    field: 'phone_number',
    dataType: 'input',
  },
  {
    title: '账号',
    field: 'user_name',
    dataType: 'input',
  },
  {
    title: '密码',
    field: 'password',
    dataType: 'input',
  },
  { title: '状态', field: 'status' },
]

// 其他信息
// const otherTableKeys = [
//   { type: 'seq', title: '序号', width: px2(50) },
//   { title: '创建人', field: 'creator_name' },
//   { title: '申请日期', field: 'create_at' },
//   { title: '最近修改人', field: 'modifier_name' },
//   { title: '最近修改时间', field: 'modified_at' },
//   { title: '最近审核人', field: 'auditor_name' },
//   { title: '最近审核日期', field: 'audit_status_time' },
//   { title: '审核状态', field: 'audit_status' },
//   { title: '备注', field: 'remark' },
// ]

const rowStyle = ({ row }) => {
  if (!data.value?.id) return {}
  if ([2, 3].includes(row.data_status)) return { color: 'red' }
  if (row.data_status === 4) return { textDecoration: 'line-through' }

  return {}
}

const handleEdit = async () => {
  supplierUpdateRef.value.open(data.value?.supplier_id, false, true)
}

onMounted(() => {
  getInfo()
})
</script>

<style lang="scss" scoped>
.title {
  @apply py-8 px-16 bg-#f5f7fe flex items-center text-sm font-bold my-8 rounded;
}

.main {
  :deep(.ant-tabs-nav) {
    margin-bottom: 0;
  }
}
</style>
