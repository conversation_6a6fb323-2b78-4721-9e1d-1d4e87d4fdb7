// 超出行数省略表示
@for $i from 1 through 3 {
  .line#{$i} {
    overflow: hidden;
    text-overflow: ellipsis;

    @if $i == 1 {
      white-space: nowrap;
    } @else {
      display: -webkit-box;
      -webkit-line-clamp: $i;
      -webkit-box-orient: vertical;
    }
  }
}

body,
html {
  overflow: hidden;
  font-family: var(--vxe-ui-font-family);
  font-size: 12px;
  line-height: initial;
  color: rgb(0 0 0 / 85%);
}

.bold {
  font-weight: bold;
}

.main {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 0 40px 20px;
}

.btnBox {
  display: flex;
  gap: 10px;
  align-items: flex-start; /* 交叉轴方向顶部对齐 */
  justify-content: flex-start; /* 主轴方向左对齐 */
}

.success-color {
  color: #00b42a;
}

.error-color {
  color: #eb1237;
}

.color {
  color: #1890ff;
}

.warn-color {
  color: #ff8d1a;
}

.drawer-title {
  height: 36px;
  padding-left: 20px;
  margin-bottom: 16px;
  font-size: 14px;
  line-height: 36px;
  color: #111;
  background-color: #f4f9fe;
}

.easy-button-dot {
  position: absolute;
  top: 0;
  right: 0;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 16px;
  height: 16px;
  padding: 4px 6px;
  font-size: 10px;
  line-height: 1;
  color: #fff;
  text-decoration: none;
  text-shadow: none;
  text-transform: uppercase;
  background-color: #ff4d4f;
  border-radius: 16px;
  transform: translate(50%, -50%) scale(0.9);
  transform-origin: center bottom;

  &.inline-dot {
    position: relative;
    display: inline-flex;
    transform: scale(0.965);
    transform-origin: center center;
  }
}

.easy-tag {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-height: 24px;
  padding: 0 10px;
  color: #666;
  white-space: nowrap;
  cursor: pointer;
  background-color: #f0f0f0;
  border-radius: 4px;

  &.no-border {
    height: 20px;
    min-height: 20px !important;
    padding: 0 4px;
    border: none !important;
    border-radius: 2px;
  }

  &.default {
    color: #0f1521;
    background-color: #edf2fa;
    border-color: #0f1521;
  }

  &.tip {
    color: #5530fc;
    background-color: #ece8ff;
    border-color: #5530fc;
  }

  &--0,
  &--1,
  &.info {
    color: #2a82e4;
    background-color: #e8f0ff;
    border-color: #2a82e4;
  }

  // 待N级审核
  &--20,
  &--30,
  &--40,
  &--50,
  &.audit {
    color: #ac33c1;
    background-color: #f9e9fb;
    border-color: #ac33c1;
  }

  &.disable {
    color: #00baad;
    background-color: transparent;
    border-color: transparent;
  }

  &--3,
  &--10,
  &.wait {
    color: #ff8d1a;
    background-color: #fff9f1;
    border-color: #ff8d1a;
  }

  &--4,
  &.warn {
    color: #ff5733;
    background-color: #fff0ed;
    border-color: #fff0ed;
  }

  &--2,
  &--90,
  &--100,
  &.success {
    color: #8dbd29;
    background-color: #f6fff2;
    border-color: #8dbd29;
  }

  &--95,
  &.error {
    color: #eb1237;
    background-color: #fff0ed;
    border-color: #eb1237;
  }

  &.current {
    color: #fff;
    background-color: #1890ff;
  }

  &.cursor-default {
    cursor: default;

    &:hover {
      opacity: 1 !important;
    }
  }

  &:hover {
    opacity: 0.8;
  }

  & + .easy-tag {
    margin-left: 10px;
  }
}

// 文字样式 eg: ● 等待中
.easy-label-status {
  position: relative;
  display: inline-flex;
  align-items: center;
  padding-left: 10px;
  font-size: 12px;
  line-height: 20px;

  &::before {
    position: absolute;
    left: 0;
    display: inline-block;
    font-family: Arial, sans-serif !important;
    font-size: 12px;
    line-height: 0;
    vertical-align: top;
    content: '●';
  }

  &.success {
    color: #00b42a;
  }

  &.error {
    color: #ff4d4f;
  }

  &.wait,
  &.warn,
  &.audit {
    color: #ff8d1a;
  }

  &.info {
    color: #1890ff;
  }

  &.disabled {
    color: #999;
  }

  &.default {
    color: #1890ff;
  }
}

.easy-ripple-button {
  position: relative;
  height: 24px;
  padding: 0 8px;
  font-size: 12px;
  color: #333;
  border: 1px solid #dcdee0;
  border-radius: 8px;
}

// 单选的btn-tab样式
.easy-tabs-single {
  display: flex;
  overflow: hidden;
  border-radius: 4px;

  &-btn {
    position: relative;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    height: 26px;
    padding: 0 8px;
    color: #666;
    border: 1px #dcdee0 solid;

    &:first-child {
      border-radius: 4px 0 0 4px;
    }

    &.big {
      height: 28px;
      padding: 0 24px;
    }

    & + .easy-tabs-single-btn {
      margin-left: -1px;
    }

    &:last-child {
      border-radius: 0 4px 4px 0;

      &::after {
        display: none !important;
      }
    }

    &::after {
      position: absolute;
      top: 0;
      right: 0;
      bottom: 0;
      z-index: 1;
      display: none;
      width: 1px;
      content: '';
      background: $color;
    }

    &:hover {
      cursor: pointer;
      border-color: $color;

      &::after {
        display: block;
      }
    }

    &.current {
      color: #fff;
      background-color: $color;
      border-color: $color;

      .count {
        color: #fff;
      }
    }

    .count {
      margin-left: 4px;
      color: #ff4d4f;

      &::before {
        content: '+';
      }
    }
  }
}

// 多选打勾btn样式
.easy-checkbox-btn {
  position: relative;
  display: inline-flex;
  align-items: center;
  height: 26px;
  padding: 3px 8px;
  margin-right: 4px;
  overflow: hidden;
  font-size: 12px;
  color: #666;
  background: #fff;
  border: 1px solid #dcdee0;
  border-radius: 4px;
  transition: 0.3s all;

  &:hover {
    color: $color;
    cursor: pointer;
    border-color: $color;
  }

  // & + .easy-checkbox-btn {
  //   margin-left: 4px;
  // }

  .count {
    margin-left: 2px;
    color: #ff4d4f;

    &::before {
      content: '+';
    }
  }

  &.active {
    color: $color;
    background: #fff;
    border: 1px solid $color;

    &::before {
      position: absolute;
      top: -2px;
      right: -7px;
      display: block;
      width: 0;
      height: 0;
      content: '';
      border-right: 10px solid transparent;
      border-bottom: 10px solid $color;
      border-left: 10px solid transparent;
      transition: all 0.3s;
      transform: rotate(45deg);
    }

    &::after {
      position: absolute;
      top: -2px;
      right: 0;
      box-sizing: border-box;
      display: table;
      width: 4px;
      height: 9px;
      content: '';
      border: 1px solid #fff;
      border-inline-start: 0;
      border-top: 0;
      transform: rotate(45deg) scale(1);
    }
  }
}

.submit-row {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 10;
  padding-left: 180px;
  border-top: 1px solid #dedede;

  @apply flex items-center justify-center w-full h-60 bg-white;
}

.link {
  @include link;

  &:not([class*='easy'], [class*='icon']):hover {
    text-decoration: underline;
  }
}

.link-default {
  @include link($font-default);

  &:not([class*='easy']):hover {
    text-decoration: underline;
  }
}

.text-green {
  color: #008000;
}

.image-list-dropdown.ant-dropdown {
  .ant-dropdown-arrow {
    z-index: 100;

    &::before {
      position: absolute;
      z-index: 201;
      width: 0;
      height: 0;
      clip-path: none;
      content: '';
      background: transparent;
      border: 6px transparent solid;
      border-radius: 0;
    }

    &::after {
      position: absolute;
      z-index: 200;
      width: 0;
      height: 0;
      clip-path: none;
      content: '';
      background: transparent;
      border: 4px transparent solid;
      border-radius: 0;
      box-shadow: none;
      transform: none;
    }
  }

  &-placement-bottomLeft {
    .ant-dropdown-arrow {
      transform: translate(-10px, -15px);

      &::before {
        bottom: -6px;
        left: 5px;
        border-bottom: 6px #f8f8f9 solid;
        border-left: 6px #f8f8f9 solid;
      }

      &::after {
        bottom: 0;
        left: 0;
        border-bottom: 4px $color solid;
        border-left: 4px $color solid;
      }
    }

    .image-list-dropdown-menu {
      border-radius: 0 4px 4px;
    }
  }

  &-placement-bottomRight {
    .ant-dropdown-arrow {
      transform: translate(10px, -15px);

      &::before {
        right: 5px;
        bottom: -6px;
        left: auto;
        border-right: 6px #f8f8f9 solid;
        border-bottom: 6px #f8f8f9 solid;
      }

      &::after {
        bottom: 0;
        left: 0;
        border-right: 4px $color solid;
        border-bottom: 4px $color solid;
      }
    }

    .image-list-dropdown-menu {
      border-radius: 4px 0 4px 4px;
    }
  }

  &-placement-topLeft {
    .ant-dropdown-arrow {
      transform: translate(-10px, 8px);

      &::before {
        bottom: 2px;
        left: 5px;
        border-top: 6px #fff solid;
        border-left: 6px #fff solid;
      }

      &::after {
        bottom: 0;
        left: 0;
        border-top: 4px $color solid;
        border-left: 4px $color solid;
      }
    }

    .image-list-dropdown-menu {
      border-radius: 4px 4px 4px 0;
    }
  }

  &-placement-topRight {
    .ant-dropdown-arrow {
      transform: translate(10px, 8px);

      &::before {
        bottom: 2px;
        left: -1px;
        border-top: 6px #fff solid;
        border-right: 6px #fff solid;
      }

      &::after {
        bottom: 0;
        left: 0;
        border-top: 4px $color solid;
        border-right: 4px $color solid;
      }
    }

    .image-list-dropdown-menu {
      border-radius: 4px 4px 0;
    }
  }

  .image-list-dropdown-menu {
    background: #fff;
    border: 1px #1890ff solid;
  }
}
