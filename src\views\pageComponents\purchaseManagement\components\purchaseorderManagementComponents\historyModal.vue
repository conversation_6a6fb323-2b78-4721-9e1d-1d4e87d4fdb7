<template>
  <a-drawer v-model:open="open" title="历史价格" placement="right" width="1000" :bodyStyle="{ padding: '0' }" destroyOnClose>
    <div class="pageContent">
      <div class="pageContentBox">
        <div class="proBtn mt-20">
          <a-button class="mr-10" :type="activeTab === 'srm' ? 'primary' : 'default'" @click="handleTabChange('srm')">SRM历史价格</a-button>
          <a-button :type="activeTab === 'archive' ? 'primary' : 'default'" @click="handleTabChange('archive')">历史归档价格</a-button>
        </div>
        <div class="proInfo">
          <div class="proInfoBox">
            <span>商品编号：</span>
            {{ proItem.k3_sku_id }}
          </div>
          <div class="proInfoBox">
            <span>商品名称：</span>
            {{ proItem.sku_name }}
          </div>
        </div>
        <div class="pricebBox">
          <div class="priceTitle">
            价格信息
            <span>本次采购价格：{{ proItem.unit_price }}</span>
          </div>
          <div class="priceTable">
            <div class="priceItem" v-for="(item, index) in pricetableData" :key="index">
              <div class="priceLable">{{ item.label }}</div>
              <div class="priceContent">{{ priceState[item.key] }}</div>
            </div>
          </div>
        </div>
        <div class="tableBox">
          <a-table :columns="columns" :data-source="tabledata" :pagination="false"></a-table>
          <div class="pageBox">
            <a-pagination v-model:current="current" v-model:page-size="pageSize" :total="total" :show-total="(total) => `共${total}条`" show-quick-jumper @change="onChange" />
          </div>
        </div>
      </div>

      <div class="footerBox">
        <a-button
          @click="
            () => {
              open = false
            }
          "
        >
          确认
        </a-button>
      </div>
    </div>
  </a-drawer>
</template>
<script setup lang="ts">
import { GetPurchaseOrderHistoricalPriceInfo, GetHistoricalPriceList, GetHistoricalArchivePriceInfo, GetHistoricalArchivePriceList } from '@/servers/PurchaseManage'

const open = ref(false)
const activeTab = ref('srm')
const proItem = ref()
const pricetableData = ref([
  {
    label: '供应商上次单价',
    key: 'supplier_last_purchase_price',
  },
  {
    label: '供应商历史平均采购价',
    key: 'supplier_historical_average_price',
  },
  {
    label: '供应商历史最高采购价',
    key: 'supplier_historical_max_price',
  },
  {
    label: '历史最低采购价',
    key: 'historical_min_price',
  },
  {
    label: '历史平均采购价',
    key: 'historical_average_price',
  },
  {
    label: '历史最高采购价',
    key: 'historical_max_price',
  },
])
const priceState = ref({
  k3_sku_id: '',
  sku_name: '',
})
const total = ref(0)
const columns = ref([
  {
    title: '采购单号',
    dataIndex: 'number',
    key: 'number',
  },
  {
    title: '采购时间',
    dataIndex: 'purchase_time',
    key: 'purchase_time',
    width: 160,
  },
  {
    title: '供应商',
    dataIndex: 'company_supplier_name',
    key: 'company_supplier_name',
    ellipsis: true,
  },
  {
    title: '数量',
    dataIndex: 'total_purchase_quantity',
    key: 'total_purchase_quantity',
    ellipsis: true,
  },
  {
    title: '材料单价',
    dataIndex: 'material_price',
    key: 'material_price',
    ellipsis: true,
  },
  {
    title: '加工费',
    dataIndex: 'process_fee',
    key: 'process_fee',
    ellipsis: true,
  },
  {
    title: '合计单价',
    dataIndex: 'unit_price',
    key: 'unit_price',
    ellipsis: true,
  },
  {
    title: '含税单价',
    dataIndex: 'tax_unit_price',
    key: 'tax_unit_price',
    ellipsis: true,
  },
  {
    title: '采购单备注',
    dataIndex: 'remark',
    key: 'remark',
    ellipsis: true,
  },
])

const tabledata = ref([])
const current = ref<number>(1)
const pageSize = ref<number>(20)
const company_supplier_id = ref()

const baseParams = ref({})
const setModel = async (bol, item, id, params = {}) => {
  open.value = bol
  if (open.value) {
    activeTab.value = 'srm'
    proItem.value = item
    company_supplier_id.value = id
    baseParams.value = {
      company_supplier_id: id,
      k3_sku_id: proItem.value.k3_sku_id,
      ...params,
    }
    const res = await GetPurchaseOrderHistoricalPriceInfo(baseParams.value)
    // console.log('res', res)
    if (res.success) {
      priceState.value = res.data
    }
    current.value = 1
    await getTabledata()
  }
}
const getTabledata = async () => {
  const params = {
    page: current.value,
    pageSize: pageSize.value,
    sortField: null,
    sortType: 'desc',
    ...baseParams.value,
  }

  let res
  if (activeTab.value === 'srm') {
    res = await GetHistoricalPriceList(params)
  } else {
    res = await GetHistoricalArchivePriceList(params)
  }

  if (res.success) {
    tabledata.value = res.data.list
    total.value = res.data.total
  }
}
const onChange = async (pageNumber: number) => {
  console.log('Page: ', pageNumber, pageSize.value)
  current.value = pageNumber
  await getTabledata()
}
const handleTabChange = async (tab: string) => {
  activeTab.value = tab
  console.log('切换到:', tab)

  // 根据tab切换调用不同的接口获取价格信息
  if (activeTab.value === 'srm') {
    const res = await GetPurchaseOrderHistoricalPriceInfo(baseParams.value)
    if (res.success) {
      priceState.value = res.data
    }
  } else {
    const res = await GetHistoricalArchivePriceInfo(baseParams.value)
    if (res.success) {
      priceState.value = res.data
    }
  }

  // 重新获取表格数据
  current.value = 1
  await getTabledata()
}
defineExpose({ setModel })
</script>
<style scoped lang="scss">
.pageContent {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  height: 100%;
  font-size: 12px;
  color: #000;

  .pageContentBox {
    width: 100%;
    height: calc(100vh - 120px);
    padding-right: 24px;
    padding-left: 24px;
    overflow: hidden;
    overflow-y: auto;

    .proInfo {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      margin-top: 24px;

      .proInfoBox {
        margin-right: 60px;
      }
    }

    .pricebBox {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      justify-content: flex-start;
      width: 100%;
      margin-top: 20px;

      .priceTitle {
        display: flex;
        flex-direction: row;
        align-items: flex-end;
        justify-content: flex-start;
        margin-bottom: 20px;
        font-size: 16px;

        span {
          margin-left: 20px;
          font-size: 12px;
        }
      }

      .priceTable {
        display: flex;
        flex-wrap: wrap;
        align-items: flex-start;
        justify-content: flex-start;
        width: 100%;
        margin-bottom: 20px;

        .priceItem {
          display: flex;
          align-items: flex-start;
          justify-content: flex-start;
          width: 33%;
          height: 30px;
          font-size: 12px;

          .priceLable {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 50%;
            height: 100%;
            background: #e2e2e2;
            border: 1px solid #696969;
          }

          .priceContent {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 50%;
            height: 100%;
            border: 1px solid #696969;
          }
        }
      }
    }

    .tableBox {
      width: 100%;

      .pageBox {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        width: 100%;
        margin-top: 20px;
      }
    }
  }

  .footerBox {
    position: absolute;
    bottom: 0;
    left: 0;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    width: 100%;
    height: 60px;
    padding-left: 24px;
    background: #fff;
    border-top: 1px solid #dedede;
  }
}
</style>
