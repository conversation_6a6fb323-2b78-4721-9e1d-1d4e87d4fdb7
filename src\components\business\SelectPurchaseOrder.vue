<template>
  <a-drawer title="选择采购单" width="85vw" :visible="visible" @close="handleClose" :maskClosable="false" destroyOnClose>
    <div class="flex h-full overflow-auto">
      <div class="p-16px h-full overflow-auto w65vw">
        <a-space class="mb-12">
          <a-input v-for="item in searchFormList" :key="item.name" :placeholder="item.placeholder" v-model:value="queryParams[item.name]" :maxlength="200" allowClear />
          <a-select v-for="item in selectFormList" :key="item.name" v-model:value="queryParams[item.name]" :placeholder="item.placeholder" allowClear>
            <a-select-option v-for="option in item.options" :key="option.value" :value="option.value">
              {{ option.label }}
            </a-select-option>
          </a-select>
          <a-button type="primary" @click="handleSearch">查询</a-button>
          <a-button @click="handleReset">重置</a-button>
        </a-space>
        <vxe-table
          v-if="leftVisible"
          :border="true"
          ref="productTableRef"
          size="mini"
          :row-config="{ isHover: true, keyField: tableKeyField, height: 40 }"
          :custom-config="{ mode: 'popup' }"
          :data="data"
          :show-overflow="true"
          :show-header-overflow="true"
          :show-footer-overflow="true"
          :column-config="{ resizable: true }"
          class="tableBoxwidth"
          :checkbox-config="{
            checkField: 'checked',
            trigger: 'row',
            checkMethod: checkMethod,
          }"
          min-height="0"
          stripe
          v-bind="$attrs"
          @checkbox-all="selectChangeEvent"
          @checkbox-change="selectChangeEvent"
        >
          <vxe-column type="checkbox" width="50" fixed="left"></vxe-column>
          <slot name="column">
            <template v-for="i in tableKey" :key="i.field">
              <vxe-column v-bind="i">
                <template #default="{ row }">
                  <span>{{ row[i.field] }}</span>
                </template>
              </vxe-column>
            </template>
          </slot>
        </vxe-table>
        <div class="paginationBox">
          <div class="pagination">
            <a-pagination
              show-quick-jumper
              :total="total"
              show-size-changer
              v-model:current="queryParams.page"
              v-model:page-size="queryParams.pageSize"
              :page-size-options="['10', '20', '30', '40', '50']"
              @change="handlePageChange"
              size="small"
            >
              <template #buildOptionText="props">
                <span>{{ props.value }}条/页</span>
              </template>
            </a-pagination>
          </div>
          <div class="totalBox">
            <div class="text">总数:</div>
            <div class="total">{{ total }}</div>
          </div>
        </div>
      </div>
      <div class="flex-1 overflow-y-auto p-16 text-black">
        <div class="flex mb-12 h30px line-height-30px h-30px">
          <div class="text-16px">已选{{ selectProductList.length }}个{{ titleText }}</div>
          <a-button class="ml-auto" type="primary" @click="cleanCheck">清空</a-button>
        </div>
        <vxe-table
          v-if="leftVisible"
          :border="true"
          ref="selectTableRef"
          size="mini"
          :row-config="{ isHover: true, keyField: tableKeyField, height: 40 }"
          :custom-config="{ mode: 'popup' }"
          :data="selectProductList"
          :show-overflow="true"
          :show-header-overflow="true"
          :show-footer-overflow="true"
          :column-config="{ resizable: true }"
          class="tableBoxwidth"
          min-height="0"
          stripe
          v-bind="$attrs"
        >
          <vxe-column type="seq" title="序号" width="60" fixed="left"></vxe-column>
          <template v-for="i in selectTableKey" :key="i.field">
            <vxe-column v-bind="i">
              <template #default="{ row }">
                <div class="flex items-center justify-between">
                  <span>{{ row[i.field] }}</span>
                  <a-button type="link" class="ml-2" @click="handleUnselectRow(row)">取消选中</a-button>
                </div>
              </template>
            </vxe-column>
          </template>
        </vxe-table>
      </div>
    </div>
    <template #footer>
      <a-space>
        <a-button type="primary" @click="handleSelectProduct" :disabled="hasConflict">确定</a-button>
        <a-button @click="handleClose">取消</a-button>
      </a-space>
    </template>
  </a-drawer>
</template>

<script setup lang="ts">
import { VxeTableInstance } from 'vxe-table'
import { ref, computed } from 'vue'
import { message } from 'ant-design-vue'

interface DataItem {
  id: number
  [key: string]: any
}

const productTableEl = useTemplateRef<VxeTableInstance>('productTableRef')

const emits = defineEmits(['selectData', 'updateData'])

const visible = ref(false)
const leftVisible = ref(false)

const data = ref<DataItem[]>([])
const total = ref(0)
const selectProductList = ref<DataItem[]>([])

// 配置参数
const config = ref<any>({})
const titleText = ref('数据')
const tableKeyField = ref('id')
const checkMethod = ref<((params: { $table: any; row: any }) => boolean) | undefined>(undefined)
const conflictCheck = ref<((data: any[]) => boolean) | undefined>(undefined)

const queryParams = ref<any>({
  page: 1,
  pageSize: 10,
  sortField: null,
  sortType: null,
})

const searchFormList = ref<any[]>([])
const selectFormList = ref<any[]>([])
const tableKey = ref<any[]>([])
const selectTableKey = ref<any[]>([])

// 添加临时选中列表
const tempSelectedList = ref<any[]>([])
// 添加一个变量来存储初始选中状态
const initialSelection = ref<any[]>([])

// 判断当前临时选中数据是否存在冲突
const hasConflict = computed(() => {
  if (!conflictCheck.value || tempSelectedList.value.length <= 1) return false
  return conflictCheck.value(tempSelectedList.value)
})

// 修改 selectChangeEvent 函数
const selectChangeEvent = () => {
  const $table = productTableEl.value
  if (!$table) return

  // 获取当前页面选中的行
  const currentPageSelectedRows = $table.getCheckboxRecords() || []

  // 找出新选中的行（当前选中但不在临时选中列表中的）
  const newlySelectedRows = currentPageSelectedRows.filter((row) => !tempSelectedList.value.some((selected) => selected[tableKeyField.value] === row[tableKeyField.value]))

  // 如果有新选中的行，且已有选中的数据，需要校验冲突
  if (newlySelectedRows.length > 0 && tempSelectedList.value.length > 0 && conflictCheck.value) {
    const testSelection = [...tempSelectedList.value, ...newlySelectedRows]
    if (conflictCheck.value(testSelection)) {
      message.warning(`选择的数据存在冲突，请重新选择`)
      // 只取消新选中的行
      newlySelectedRows.forEach((row) => {
        $table.setCheckboxRow(row, false)
      })
      return
    }
  }

  // 更新临时选中列表
  tempSelectedList.value = [
    ...tempSelectedList.value.filter((selected) => !data.value.some((row) => row[tableKeyField.value] === selected[tableKeyField.value])),
    ...currentPageSelectedRows.map((row) => ({ ...row })),
  ]

  // 更新当前页数据的选择状态
  const currentPageIds = new Set(currentPageSelectedRows.map((row) => row[tableKeyField.value]))
  data.value = data.value.map((row) => ({
    ...row,
    checked: currentPageIds.has(row[tableKeyField.value]),
  }))

  // 更新显示列表（右侧面板）
  selectProductList.value = JSON.parse(JSON.stringify(tempSelectedList.value))
}

// open方法初始化临时选中列表
const open = (configOptions: any, selectedData: any[] = []) => {
  console.log('配置选项', configOptions)
  console.log('已选数据', selectedData)

  // 保存配置
  config.value = configOptions
  titleText.value = configOptions.title || '数据'
  tableKeyField.value = configOptions.keyField || 'id'
  checkMethod.value = configOptions.checkMethod || undefined
  conflictCheck.value = configOptions.conflictCheck || undefined

  // 先重置查询条件
  queryParams.value = {
    page: 1,
    pageSize: 10,
    sortField: null,
    sortType: null,
  }

  // 设置搜索表单
  searchFormList.value = configOptions.search || []
  selectFormList.value = configOptions.select || []

  // 设置表格列配置
  tableKey.value = configOptions.left || []
  selectTableKey.value = configOptions.right || []

  const keyField = configOptions.keyField || 'id'
  // 统一 keyField 类型
  initialSelection.value = selectedData.map((item) => ({
    ...item,
    [keyField]: String(item[keyField]),
  }))
  tempSelectedList.value = JSON.parse(JSON.stringify(initialSelection.value))
  selectProductList.value = JSON.parse(JSON.stringify(initialSelection.value))
  data.value = []

  // 设置查询参数（覆盖重置后的默认值）
  if (configOptions.params) {
    queryParams.value = {
      ...queryParams.value,
      ...configOptions.params,
    }
  }

  visible.value = true
  leftVisible.value = true

  // 获取列表并设置选中状态
  getDataList().then(() => {
    // 更新表格数据选中状态
    data.value = data.value.map((row) => ({
      ...row,
      checked: tempSelectedList.value.some((selected) => selected[tableKeyField.value] === row[tableKeyField.value]),
    }))

    // 确保表格选中状态与数据一致
    const $table = productTableEl.value
    if ($table) {
      data.value.forEach((row) => {
        if (row.checked) {
          $table.setCheckboxRow(row, true)
        }
      })
    }
  })
}

// 修改 handleSelectProduct 函数
const handleSelectProduct = () => {
  // 无论是否有选择，都触发事件并关闭抽屉
  emits('selectData', tempSelectedList.value, null, null)
  visible.value = false
  leftVisible.value = false
}

// 修改 handleClose 方法
const handleClose = () => {
  // 恢复初始选中状态
  tempSelectedList.value = JSON.parse(JSON.stringify(initialSelection.value))
  selectProductList.value = JSON.parse(JSON.stringify(initialSelection.value))

  // 只关闭抽屉
  visible.value = false
  leftVisible.value = false
}

// 获取数据列表
const getDataList = async () => {
  if (!config.value.api) {
    console.error('未配置API接口')
    return
  }

  try {
    const res = await config.value.api(queryParams.value)

    if (res.success && res.data) {
      // 统一 keyField 类型
      const keyField = tableKeyField.value
      const selectedKeys = new Set(selectProductList.value.map((item) => String(item[keyField])))
      const newList = res.data.list.map((row: any) => ({
        ...row,
        [keyField]: String(row[keyField]),
        checked: selectedKeys.has(String(row[keyField])),
      }))

      data.value = newList
      total.value = res.data.total

      // 设置选中状态
      const $table = productTableEl.value
      if ($table) {
        data.value.forEach((row) => {
          if (row.checked) {
            $table.setCheckboxRow(row, true)
          }
        })
      }
    } else {
      console.error('API返回数据格式错误:', res)
      data.value = []
      total.value = 0
    }
  } catch (error) {
    console.error('获取数据失败:', error)
    data.value = []
    total.value = 0
  }
}

// 分页
const handlePageChange = (page: number, pageSize: number) => {
  queryParams.value.page = page
  queryParams.value.pageSize = pageSize
  getDataList()
}

// 查询
const handleSearch = () => {
  // 构建查询参数
  const searchParams = {
    ...queryParams.value,
    page: 1,
  }

  // 移除空值和未定义的参数
  Object.keys(searchParams).forEach((key) => {
    if (searchParams[key] === null || searchParams[key] === undefined || searchParams[key] === '') {
      delete searchParams[key]
    }
  })

  // 更新查询参数
  queryParams.value = searchParams
  getDataList()
}

// 修改 handleUnselectRow 方法
const handleUnselectRow = (row: any) => {
  // 从临时选中列表中移除
  tempSelectedList.value = tempSelectedList.value.filter((item) => item[tableKeyField.value] !== row[tableKeyField.value])

  // 更新左侧表格的选中状态
  const $table = productTableEl.value
  if ($table) {
    const targetRow = data.value.find((item) => item[tableKeyField.value] === row[tableKeyField.value])
    if (targetRow) {
      $table.setCheckboxRow(targetRow, false)
    }
  }

  // 更新当前页数据的选中状态
  data.value = data.value.map((dataRow) => ({
    ...dataRow,
    checked: tempSelectedList.value.some((selected) => selected[tableKeyField.value] === dataRow[tableKeyField.value]),
  }))

  // 更新显示列表（但不触发父组件更新）
  selectProductList.value = JSON.parse(JSON.stringify(tempSelectedList.value))
}

// 修改 cleanCheck 方法
const cleanCheck = async () => {
  // 只清空临时选中列表，保持 initialSelection 不变
  tempSelectedList.value = []
  const $table = productTableEl.value
  if ($table) {
    $table.clearCheckboxRow()
  }

  // 更新当前页数据的选中状态
  data.value = data.value.map((row) => ({
    ...row,
    checked: false,
  }))

  // 更新显示列表（但不触发父组件更新）
  selectProductList.value = []
}

// 修改重置方法
const handleReset = () => {
  queryParams.value = {
    page: 1,
    pageSize: 10,
    sortField: null,
    sortType: null,
  }
  getDataList()
}

// 暴露open和close方法供父组件调用
defineExpose({
  open,
  close: () => {
    visible.value = false
    leftVisible.value = false
  },
})
</script>

<style scoped lang="scss">
.paginationBox {
  display: flex;
  align-items: center;
  margin-top: 0.83rem;

  .totalBox {
    display: flex;
    align-items: flex-end;
    margin-left: 20px;
    color: #000;

    .text {
      margin-right: 8px;
      font-size: 14px;
    }

    .total {
      font-size: 16px;
      color: #1890ff;
    }
  }
}
</style>
