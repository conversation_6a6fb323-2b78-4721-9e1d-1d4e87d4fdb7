<template>
  <a-modal v-model:open="isShow" :title="title" @ok="handleOk" :footer="null">
    <a-textarea v-model:value="reviewText" placeholder="请输入审核意见" :rows="4" :maxlength="200" />

    <a-flex justify="center" class="mt-16">
      <a-space :size="16">
        <a-button type="primary" @click="handleOk">{{ okText }}</a-button>
        <a-button @click="handleCancel">取消</a-button>
      </a-space>
    </a-flex>
  </a-modal>
</template>
<script setup lang="ts">
const isShow = ref(false)
const title = ref('')
const okText = ref('')
const reviewText = ref('')
const isispass = ref()
const emits = defineEmits(['ClickReview'])
const setReview = (bol, text, submitText, ispass) => {
  reviewText.value = ''
  isShow.value = bol
  title.value = text
  okText.value = submitText
  isispass.value = ispass
}
const handleOk = () => {
  isShow.value = false
  emits('ClickReview', reviewText.value, isispass.value)
}

const handleCancel = () => {
  isShow.value = false
  reviewText.value = ''
}

defineExpose({ setReview })
</script>
