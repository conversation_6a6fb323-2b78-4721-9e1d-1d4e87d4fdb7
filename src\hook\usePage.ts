import { usePageStore } from '@/store/usePageStore'
import type { NavItem } from '@/store/usePageStore'

type NavItemKeys = keyof NavItem

type UsePageParams = {
  formRef?: Ref<any>
  initMethod?: () => void
  refresh?: () => void
  onCloseBefore?: () => void | Promise<boolean>
}

type UsePageReturn = {
  pushPage: (path: string, params?: any) => void
  addNavPage: (nav: NavItem, params?: any) => void
  setRefreshByPath: (path: string) => void
  closePage: (path?: { to?: string; close?: string }) => Promise<void> | void
  setPageOptions: (params: any) => void
}

/**
 * 页面管理 hook
 *
 * @param {UsePageParams} params 参数
 * @property {Ref} params.formRef 表单实例 (不带value)
 * @property {Function} params.initMethod 初始化方法
 * @property {Function} params.refresh 当路由的fullPath存在refreshList进入页面立即执行该方法
 * @property {Function|Promise} params.onCloseBefore 关闭前回调
 *
 * @return {UsePageReturn} result 返回值
 * @property {Function} result.closePage 关闭页面
 * @property {Function} result.addNavPage 设置对象到头部菜单
 * @property {Function} result.setRefreshByPath 设置刷新的页面
 * @property {Function} result.pushPage 打开页面
 */
export const usePage = (params?: UsePageParams): UsePageReturn => {
  const { formRef, initMethod, refresh, onCloseBefore } = params || {}

  const router = useRouter()
  const pageStore = usePageStore()

  const addNavPage = (nav, params?: any) => {
    if (['/', '/401', '/404', '/login'].includes(nav.path)) return
    // 只有 KeepAlive 不为 false 的页面才添加到导航列表
    if (nav.meta?.KeepAlive === false) return
    const keys: NavItemKeys[] = ['path', 'fullPath', 'name', 'title', 'code', 'id', 'sourcePath']
    const navItem = {}
    for (const key of keys) {
      nav[key] && (navItem[key] = nav[key])
    }
    pageStore.setNavPage(navItem as NavItem, params)
  }
  const setRefreshByPath = (path) => {
    if (!path) return
    pageStore.addByRefresh(path)
  }

  const pushPage = async (path, config = {}) => {
    if (!path) return
    const { add, source } = { add: true, source: false, ...config }
    const res = router.resolve(path)
    if (add) {
      addNavPage({
        ...res,
        code: res.meta.code,
        title: res.name,
        sourcePath: source ? router.currentRoute.value.fullPath : undefined,
      })
    }
    router.push(path)
  }
  const closePage = async ({ to, close }: { to?: string; close?: string } = {}) => {
    const path = close || router.currentRoute.value.fullPath

    if (formRef) {
      formRef?.value?.resetFields && formRef.value.resetFields()
    }
    if (onCloseBefore) {
      const fn = onCloseBefore()
      if (fn instanceof Promise) {
        const res = await (fn as Promise<boolean>)
        if (!res) return
      }
    }

    const closeFn = () => {
      const { index, item } = pageStore.removeNavPage(path)
      const source = pageStore.navPageArr.find((f) => f.fullPath === (to || item.sourcePath))
      const switchPath = source || pageStore.navPageArr[index === 0 ? 0 : index - 1].path || '/'
      if (switchPath) {
        router.push((typeof switchPath !== 'string' && switchPath?.fullPath) || switchPath).then(() => {
          pageStore.addByExclude(path)
        })
      }
    }

    if (close) return closeFn()

    setTimeout(closeFn, 300)
  }

  const setPageOptions = (params) => {
    pageStore.setNavPage(
      {
        fullPath: router.currentRoute.value.fullPath,
        path: router.currentRoute.value.path,
        id: undefined,
      },
      params,
    )
  }

  onActivated(() => {
    const path = router.currentRoute.value.fullPath
    if (pageStore.hasRefresh(path)) {
      refresh && refresh()
      pageStore.removeByRefresh(path)
    }
    if (initMethod && !pageStore.hasInclude(path)) {
      initMethod()
    }
    pageStore.addByInclude(path)
  })

  return {
    pushPage,
    addNavPage,
    setRefreshByPath,
    closePage,
    setPageOptions,
  }
}
