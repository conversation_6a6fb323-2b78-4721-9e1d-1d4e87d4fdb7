<template>
  <div class="page">
    <div class="menuBox" :class="collapsed ? 'menuActive' : ''">
      <div class="titleBox">
        <div class="title">
          <img class="logo" src="../assets/image/2024_9_6_logo2.png" alt="" />
          <div class="text" v-show="!collapsed">SRM系统</div>
        </div>
      </div>
      <div class="box">
        <a-menu mode="inline" theme="dark" :inline-collapsed="collapsed" @click="tapMenu" v-model:selectedKeys="selectedMenuKeys" v-model:openKeys="openKeys">
          <template v-for="item in menuList" :key="item.path">
            <template v-if="!item.children">
              <a-menu-item :key="item.path">
                <template #icon>
                  <a-badge :dot="false">
                    <i :class="`iconfont ${'icon-' + iconfontArr[item.path]}`"></i>
                  </a-badge>
                </template>
                {{ item.name }}
              </a-menu-item>
            </template>
            <template v-else>
              <a-sub-menu :key="item.path">
                <template #icon>
                  <a-badge :dot="getDotCount(item.id)" :numberStyle="{ boxShadow: 'none' }">
                    <i :class="`iconfont ${'icon-' + iconfontArr[item.path]}`"></i>
                  </a-badge>
                </template>
                <template #title>
                  <span class="pl-3">{{ item.name }}</span>
                </template>
                <a-menu-item v-for="item2 in item.children" :key="item2.path">
                  <span class="pl-5 flex items-center">
                    {{ item2.name }}
                    <!-- 采购申请单特殊处理 有数量只显示红点 -->
                    <a-badge v-if="item2.id == 83000" class="ml-2" :numberStyle="{ boxShadow: 'none' }" :dot="!!menuStatusCountMap?.[menuCountMapKeys[item2.id]]" />
                    <span v-else-if="menuStatusCountMap?.[menuCountMapKeys[item2.id]]" class="easy-button-dot relative! -mt-10 ml-2 translate-px!">
                      {{ Math.min(menuStatusCountMap?.[menuCountMapKeys[item2.id]], 999) === 999 ? '999+' : menuStatusCountMap?.[menuCountMapKeys[item2.id]] }}
                    </span>
                  </span>
                </a-menu-item>
              </a-sub-menu>
            </template>
          </template>
        </a-menu>
      </div>
      <div class="version">
        <InfoCircleOutlined class="icon" />
        版本号：{{ version ? version : '--' }}
      </div>
    </div>
    <div class="mainBox" :class="collapsed ? 'active' : ''">
      <div class="topNav">
        <div class="navBox">
          <a-button class="toggleBtn" v-if="showToggleBtn" :class="collapsed ? 'active' : ''" type="primary" @click="toggleCollapsed">
            <MenuUnfoldOutlined v-if="collapsed" />
            <MenuFoldOutlined v-else />
          </a-button>
          <a-tabs
            :tabBarGutter="0"
            v-model:activeKey="navPagePath"
            tab-position="top"
            type="editable-card"
            size="small"
            hideAdd
            @edit="(targetKey) => closePage({ close: targetKey as string })"
            @tabClick="(path) => pushPage(path as string)"
          >
            <a-tab-pane v-for="item in pageStore.navPageArr" :key="item.fullPath" :tab="item.name" :closable="pageStore.navPageArr.length !== 1"></a-tab-pane>
          </a-tabs>
        </div>
        <div class="btnBox">
          <a-tooltip placement="bottom">
            <template #title>
              <span>下载中心</span>
            </template>
            <a-badge :dot="notDownloadCount > 0">
              <span class="iconfont icon-a-xiazaizhongxin_downloadcenter text-16px cursor-pointer text-#999 hover:text-#1890FF" @click="DownLoadCenterRef.open()" />
            </a-badge>
          </a-tooltip>
          <a-tooltip placement="bottom">
            <template #title>
              <span>帮助中心</span>
            </template>
            <span class="iconfont icon-bangzhuzhongxin_help ml-12 text-16px cursor-pointer text-#999 hover:text-#1890FF" @click="toDescribe" />
          </a-tooltip>
          <a-dropdown @openChange="(val) => (dropDownVisible = val)">
            <template #overlay>
              <div @click="dropDownVisible = false" class="userBox">
                <div class="nameBox">
                  <div class="nameImg"><img src="@/assets/image/name.png" /></div>
                  <div class="nameText">
                    <div class="textTop">{{ userData.display_name || '' }}</div>
                    <div class="textBot" v-show="userData.job">
                      <div class="label" style="display: flex">
                        岗
                        <div style="width: 24px"></div>
                        位：
                      </div>
                      <span>{{ userData.job }}</span>
                    </div>
                    <div class="textBot" v-show="userData.department">
                      <span class="label">所在部门：</span>
                      <span>{{ userData.department }}</span>
                    </div>
                    <div class="textBot" v-show="userData.company">
                      <span class="label">所属公司：</span>
                      <span>{{ userData.company }}</span>
                    </div>
                  </div>
                </div>
                <div class="tuiChu" @click="tuichu">
                  <i class="iconfont icon-tuichu"></i>
                  退出登录
                </div>
              </div>
            </template>

            <div :style="dropDownVisible ? 'color: #1890FF;' : ''" class="btnBoxMainText">
              {{ userData.display_name || '' }}
              <span class="btnBoxsubText" v-if="userData.job || userData.department">
                (
                <!-- 内部 -->
                <span>
                  <span v-if="userData.job">{{ userData.job }}</span>
                  <span v-if="userData.job && userData.department">|</span>
                  <span v-if="userData.department">{{ userData.department.length > 10 ? userData.department.slice(0, 16) + '...' : userData.department }}</span>
                </span>

                )
              </span>
            </div>
          </a-dropdown>
        </div>
      </div>
      <!-- <router-view></router-view> -->

      <router-view v-slot="{ Component }">
        <a-alert
          @close="isCloseNotice = true"
          v-if="noticeContent && !isCloseNotice"
          class="msgAlert"
          :message="noticeContent"
          type="warning"
          closable
          banner
          :style="{ '--duration': `${20 + noticeContent.length * 0.1}s` }"
        />
        <keep-alive :exclude="pageStore.exclude">
          <component :is="setComponentInstance(Component, $route?.fullPath)" v-if="route.meta.KeepAlive" :key="route.fullPath" />
        </keep-alive>
        <component v-if="!route.meta.KeepAlive" :is="setComponentInstance(Component, $route?.fullPath)" :key="route.path"></component>
      </router-view>
    </div>
    <a-modal v-model:open="isKeyDown" width="600px" title="批量输入">
      <a-textarea v-model:value="commodityCode" placeholder="多个商品编码，以逗号分隔或每行一个商品编码" :rows="4" />
      <template #footer>
        <a-button key="back" style="float: left">清空</a-button>
        <a-button key="back" @click="isKeyDown = false">取消</a-button>
        <a-button key="submit" type="primary" @click="isKeyDown = false">确认</a-button>
      </template>
    </a-modal>
    <!-- 下载中心 -->
    <DownLoadCenter ref="DownLoadCenterRef"></DownLoadCenter>

    <!-- 共享tooltip实例 -->
    <vxe-tooltip ref="tooltipRef" class="view-tooltip" :zIndex="1000" :enter-delay="0" enterable @mouseenter="mouseenterByTooltip" @mouseleave="mouseleaveByTooltip"></vxe-tooltip>
    <!-- 共享modal实例 -->
    <contextHolder />
  </div>
</template>

<script lang="ts" setup>
import { Modal, message } from 'ant-design-vue'
import { MenuFoldOutlined, MenuUnfoldOutlined, InfoCircleOutlined } from '@ant-design/icons-vue'
import { VxeTooltipInstance } from 'vxe-pc-ui'

import { beforLogout } from '@/utils'
import eventBus from '@/utils/eventBus'
import DownLoadCenter from '@/components/DownLoadCenter.vue'
import { usePageStore } from '@/store/usePageStore'

import { GetNewNotify } from '@/servers/Notice'
import { Info } from '@/servers/Common'
import { GetUserNotDownloadTaskNum } from '@/servers/DownloadCenter'

const router = useRouter()
const route = useRoute()
const { closePage, pushPage, addNavPage } = usePage()

const pageStore = usePageStore()

const menuList: any = ref([])
const userData: any = ref({})
const commodityCode = ref()
const isKeyDown = ref(false)
const navPagePath = ref('/')
const collapsed = ref(false)
const selectedMenuKeys = ref(['/'])
const openKeys: any = ref()
const version = ref('')
const isCloseNotice = ref(false)
const noticeContent = ref<any>(null)
const DownLoadCenterRef = ref()

const menuCountMapKeys = ref({
  130000: ['paymentOrder'],
  131000: 'paymentOrder',
  80000: ['purchasePrice', 'purchaseAdjustPrice', 'purchaseApply', 'purchaseOrder', 'purchaseChange'],
  81000: 'purchasePrice',
  82000: 'purchaseAdjustPrice',
  83000: 'purchaseApply',
  84000: 'purchaseOrder',
  85000: 'purchaseChange',
  40000: ['supplierAudit'],
  43000: 'supplierAudit',
  110000: ['bookingOrder', 'purchaseReturnApplication', 'outboundWarehouseOrder'],
  111000: 'bookingOrder',
  114000: 'outboundWarehouseOrder',
  118000: 'purchaseReturnApplication',
})
const { menuStatusCountMap, getMenuLabelCount } = useLabelStatus([13, 12, 14, 15, 16, 17, 24, 26, 28, 29])

const getDotCount = (id) => {
  if (!Object.keys(menuStatusCountMap.value || {})?.length) return false
  const keys = menuCountMapKeys.value[id]
  if (keys instanceof Array) {
    return !!keys.reduce((acc, cur) => acc + menuStatusCountMap.value[cur], 0)
  }
  return !!menuStatusCountMap.value?.[keys]
}

const iconfontArr = {
  '/paymentOrderManager': 'caiwuguanli',
  '/supplierModule': 'gongyingshangguanli',
  '/purchaseManager': 'caijiguanli',
  '/billManagement': 'zhangdanguanli',
  '/wareHouseManagement': 'cangkuguanli',
  '/baseData': 'jichuziliao',
  '/systemDocking': 'a-1688zhanhui',
  '/notifyManagement': 'tongzhiguanli',
  '/clientModule': 'yonghuguanli',
  '/systemManagement': 'xitongshezhi',
}

const dropDownVisible = ref(false)

const componentMap = new Map()
watch(
  () => pageStore.exclude,
  (arr) => {
    for (const path of componentMap.keys()) {
      if (arr.includes(path)) {
        componentMap.delete(path)
      }
    }
  },
)
const setComponentInstance = (component, path) => {
  if (componentMap.has(path)) {
    return componentMap.get(path)
  }
  const wrapper = {
    name: path,
    render() {
      return h(component, { class: 'boxStr' })
    },
  }
  componentMap.set(path, wrapper)
  return wrapper
}

const checkRouter = (path: string) => {
  let foundPath // null 没找到 1 第一层找到  string 二层的path

  menuList.value.forEach((item) => {
    if (item.path == path) {
      foundPath = 1
    } else if (item?.children?.length) {
      item.children.forEach((item2) => {
        if (item2.path == path) {
          foundPath = item.path
        }
      })
    }
    if (foundPath) {
      navPagePath.value = path
      selectedMenuKeys.value = [path]
      openKeys.value = typeof foundPath === 'string' ? [foundPath] : []
    }
  })
  // 当前页面是二级页面
  if (router.currentRoute.value.meta.isChildPage == true) {
    const { matched } = router.currentRoute.value
    const parent = matched[1]
    foundPath = 1
    navPagePath.value = path
    selectedMenuKeys.value = []
    openKeys.value = [parent.path]
    let isPresence = true

    pageStore.navPageArr.forEach((item2) => {
      if (item2.path == path) {
        isPresence = false
      }
    })
    if (isPresence) {
      const item = {
        id: null,
        code: router.currentRoute.value.meta.code,
        name: router.currentRoute.value.name,
        path,
        fullPath: router.currentRoute.value.fullPath,
        title: router.currentRoute.value.name,
      }
      pageStore.setNavPage(item)
    }
  }
  if (!foundPath) {
    const res = router.resolve(path)
    if (res.path === '/') {
      return initialization()
    }
    // old 401
    if (res) {
      router.push('/')
    }
  }
}

// 路由和侧导航栏初始化
const initialization = () => {
  const arr = JSON.parse(localStorage.getItem('navPageArr') || '[]')
  pageStore.initNavPage(arr)
  const userDataStr = localStorage.getItem('userData') || ''
  if (userDataStr) {
    userData.value = JSON.parse(userDataStr)
    menuList.value = [...userData.value.permissions_infos]

    const first = menuList.value[0]?.children?.[0] || menuList.value[0]
    if (!pageStore.navPageArr.length) {
      addNavPage(first, { fullPath: first.path })
    }
    if (route.path === '/') {
      setTimeout(() => {
        pushPage(first.path)
      })
    }
  }
}

const notDownloadCount = ref(0)
const getUserNotDownloadTaskNum = async () => {
  const { data } = await GetUserNotDownloadTaskNum()
  notDownloadCount.value = data
}

watch(
  () => pageStore.navPageArr,
  (arr) => {
    const fullPath = decodeURIComponent(router.currentRoute.value.fullPath)
    for (const nav of arr) {
      if (decodeURIComponent(nav.fullPath) === fullPath) {
        navPagePath.value = fullPath
        return
      }
    }
  },
  { immediate: true },
)
initialization()
watch(() => router.currentRoute.value.fullPath, checkRouter, { immediate: true })

// 退出登录
const tuichu = () => {
  beforLogout()
  pageStore.reset()
  router.push('/login')
}
// 点击菜单
const tapMenu = (e: any) => {
  pushPage(e.key)
}
// 菜单导航栏切换大小
const toggleCollapsed = () => {
  collapsed.value = !collapsed.value
}

const checkEdition = () => {
  Info().then((res) => {
    version.value = res.data.version
    const localVersion = localStorage.getItem('version') || version.value
    if (version.value != localVersion) {
      setTimeout(() => {
        window.location.reload()
      })
    }
    localStorage.setItem('version', version.value)
  })
}

let timer
const getNotice = () => {
  GetNewNotify().then((res) => {
    if (res.data && res.data !== noticeContent.value && isCloseNotice.value) {
      isCloseNotice.value = false
    }
    noticeContent.value = res.data || null
    timer = setTimeout(() => {
      getNotice()
    }, 60 * 1000)
  })
}

eventBus.on('getNotice', () => {
  if (timer) clearTimeout(timer)
  getNotice()
})

// 打开操作手册
const toDescribe = () => {
  const url = 'https://doc.weixin.qq.com/doc/w3_ASIA6wZ8ALICNiQQNQe0TRYeut3Jh?scode=AOIABQfcAGAl3W9oWWASIA6wZ8ALI'
  // const url = 'https://doc.weixin.qq.com/doc/w3_AesARgZIADwmVGET4iXQwqtqkf2F0?scode=AJMAwAeSADQdzI0BhCAesARgZIADw'
  window.open(url)
}

// tooltip
const tooltipRef = ref<VxeTooltipInstance>()
const tooltipTimer = ref()

const mouseenterByTooltip = () => {
  clearTimeout(tooltipTimer.value)
}
const mouseleaveByTooltip = (event: MouseEvent) => {
  closeTooltip(event, 300)
}
const openTooltip = ({ target, type }, content) => {
  if (type !== 'mousemove') {
    closeTooltip(true)
  }
  const $tooltip = tooltipRef.value as VxeTooltipInstance
  if ($tooltip) {
    $tooltip?.open(target, content)
  }
}
const closeTooltip = (immediateClose: boolean | Event, delay?: number) => {
  const closeFn = () => {
    const $tooltip = tooltipRef.value
    if ($tooltip) {
      $tooltip?.close()
    }
  }
  if (immediateClose === true) {
    clearTimeout(tooltipTimer.value)
    return closeFn()
  }
  tooltipTimer.value = setTimeout(closeFn, delay ?? 200)
}
provide('tooltip', { open: openTooltip, close: closeTooltip })

// modal
const modalRef = ref()
const [modal, contextHolder] = Modal.useModal()
const openModal = (options) => {
  const defaultOptions = {
    title: '',
    icon: () => {},
    content: '',
    onOk: () => {},
    onCancel() {},
  }
  modalRef.value = modal.confirm({
    ...defaultOptions,
    ...options,
  })
}

const editValue = ref()
const openModalByEdit = (options) => {
  return new Promise((openResolve) => {
    const { api, params, placeholder, comp, compProps, title, value, callback, ...modalOptions } = options
    editValue.value = value || undefined
    const _placeholder = placeholder || title.replace(/修改|批量/g, '')
    openModal({
      title,
      content: () => {
        return h(comp, {
          value: editValue.value,
          placeholder: _placeholder,
          'onUpdate:value': (val) => {
            editValue.value = val
          },
          style: {
            width: '100%',
            margin: '10px 0',
          },
          ...(compProps || {}),
        })
      },
      onOk: () => {
        return new Promise((resolve, reject) => {
          if (!editValue.value) {
            message.warn(`${_placeholder.replace(/请|输入|选择/g, '')}不能为空`)
            return reject('error')
          }
          api(params(editValue.value)).then(() => {
            message.success('修改成功')
            callback && callback({ value: editValue.value })
            resolve(true)
            openResolve(true)
          })
        })
      },
      okText: '保存',
      cancelText: '取消',
      width: 400,
      maskClosable: true,
      closable: true,
      ...modalOptions,
    })
  })
}
const updateModal = (options) => {
  modalRef.value && modalRef.value.update(options)
}
const closeModal = () => {
  modalRef.value && modalRef.value.destroy()
}
const destroyAllModal = () => {
  Modal.destroyAll()
}
provide('modal', {
  open: openModal,
  update: updateModal,
  close: closeModal,
  destroyAll: destroyAllModal,
  openEdit: openModalByEdit,
})

// btnPermission
const getBtnPermission = () => {
  const userData = localStorage.getItem('userData') as any
  const permissionInfo = JSON.parse(userData).permissions_infos
  const result = {}
  const deepFound = (arr: any[]) => {
    arr.forEach((f) => {
      if (f.btnList) {
        for (const btn of f.btnList) {
          result[btn.id] = true
          for (const btn2 of btn?.children || []) {
            result[btn2.id] = true
          }
        }
      } else {
        deepFound(f?.children || [])
      }
    })
  }

  deepFound(permissionInfo)

  provide('btnPermission', result)
}

router.beforeEach((to, from, next) => {
  if (to.name === '找不到页面') {
    pageStore.addByExclude(from.fullPath)
  } else {
    pageStore.removeByExclude(to.fullPath)
    addNavPage(to as any)
  }
  closeTooltip(true)
  destroyAllModal()
  next()
})

onMounted(() => {
  getNotice()
  getMenuLabelCount()
  getUserNotDownloadTaskNum()
  eventBus.on('changeDownLoadCount', getUserNotDownloadTaskNum)
  checkEdition()
  getBtnPermission()
  setInterval(checkEdition, 1000 * 60 * 10)
})

onUnmounted(() => {
  if (timer) clearTimeout(timer)
})

const showToggleBtn = computed(() => {
  return window.self === window.top
})
</script>
<style lang="scss" scoped>
.page {
  box-sizing: border-box;
  display: flex;
  min-width: 1200px;
  min-height: 100vh;

  :deep(.ant-menu-dark) {
    background-color: rgb(0 0 0 / 0%);

    .ant-menu-item {
      border-left: 3px solid transparent;
    }
  }

  :deep(.ant-menu-dark.ant-menu-inline .ant-menu-sub.ant-menu-inline) {
    background-color: rgb(0 0 0 / 10%);
  }

  :deep(.ant-menu-dark .ant-menu-item-selected) {
    color: #7ebcfc;
    background-color: rgb(64 158 255 / 15%);
    border-left: 3px solid #7ebcfc;
  }

  :deep(.ant-menu-dark:not(.ant-menu-horizontal) .ant-menu-submenu-title),
  :deep(.ant-menu-dark:not(.ant-menu-horizontal) .ant-menu-item:not(.ant-menu-item-selected)) {
    &:hover,
    &:active {
      color: #7ebcfc;
      background-color: rgb(64 158 255 / 15%);
    }
  }

  :deep(.ant-menu-inline .ant-menu-item),
  :deep(.ant-menu-vertical .ant-menu-item),
  :deep(.ant-menu-inline .ant-menu-submenu-title),
  :deep(.ant-menu-vertical .ant-menu-submenu-title) {
    width: 100%;
    margin-inline: 0;
    border-radius: 0;
  }

  :deep(.ant-menu-inline-collapsed) {
    width: 62px;
  }

  :deep(.ant-menu-inline > .ant-menu-item),
  :deep(.ant-menu-vertical > .ant-menu-item),
  :deep(.ant-menu-inline > .ant-menu-submenu > .ant-menu-submenu-title),
  :deep(.ant-menu-vertical > .ant-menu-submenu > .ant-menu-submenu-title) {
    height: 40px;
    line-height: 40px;
  }

  .menuBox {
    z-index: 100;
    width: 180px;
    background-color: #1f1f3f;

    // flex-shrink: 0;
    // transition: all 1s;
    &.menuActive {
      width: 62px;
    }

    .titleBox {
      position: relative;
      min-width: 100%;
      height: 50px;
      background-color: #1f1f3f;

      .title {
        display: flex;
        justify-content: center;

        // padding: 10px 0 10px 14px;
        padding-block: 10px;
        overflow: hidden;
        font-size: 18px;
        font-weight: bold;
        color: #fff;

        .logo {
          width: 30px;
          height: 30px;
        }

        .text {
          margin-left: 10px;
          line-height: 30px;
          text-wrap: nowrap;
        }
      }
    }

    .version {
      height: 34px;
      font-size: 11px;
      line-height: 34px;
      color: #999;
      text-align: center;
      user-select: none;
      background-color: #1f1f3f;

      .icon {
        margin-right: 4px;
      }
    }

    .box {
      flex: 1;
      scrollbar-width: none;
      height: calc(100vh - 85px);
      overflow-y: scroll;
      -ms-overflow-style: none;

      .iconfont {
        font-size: 20px;
        color: rgb(255 255 255 / 65%);
      }

      &::-webkit-scrollbar {
        width: 0;
        height: 0;
      }

      :deep(.ant-menu-title-content) {
        overflow: visible;
      }
    }
  }

  .mainBox {
    display: flex;
    flex-direction: column;
    width: calc(100% - 180px);
    height: 100vh;

    &.active {
      width: calc(100% - 62px);
    }

    .topNav {
      display: flex;
      align-items: center;
      justify-content: end;
      justify-content: space-between;
      width: 100%;
      height: 40px;
      padding: 0 20px 0 0;
      background: rgb(235 235 235);

      .navBox {
        position: relative;
        display: flex;
        flex: 1;
        align-items: flex-end;
        height: 100%;
        margin-right: 24px;
        overflow: hidden;

        .toggleBtn {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 35px;
          height: 30px;
          padding: 0;
          color: #999;
          cursor: pointer;
          background-color: rgb(235 235 235);
          border-radius: 0;
        }

        :deep(.ant-tabs) {
          width: 100%;
          line-height: 14px;
        }

        :deep(.ant-tabs-nav) {
          margin-bottom: 0;

          &::before {
            display: none;
          }
        }

        :deep(.ant-tabs-nav-list) {
          background: linear-gradient(to bottom, rgb(235 235 235) 50%, white 50%);
        }

        :deep(.ant-tabs-tab) {
          position: relative;
          height: 32px;
          padding: 0 10px;
          font-size: 12px;
          color: #999;
          background-color: rgb(235 235 235);
          border: none;
          border-radius: 8px 8px 0 0;

          &::before {
            position: absolute;
            left: 0;
            width: 1px;
            height: 10px;
            content: '';
            background-color: #d9d9d9;
          }

          // top: -1px;
          .ant-tabs-tab-remove {
            padding: 0;
            padding-right: 4px;
            color: #999 !important;
          }

          .ant-tabs-tab-btn {
            transition: none;
          }
        }

        :deep(.ant-tabs-tab-with-remove) {
          .ant-tabs-tab-remove {
            color: white;
          }
        }

        :deep(.ant-tabs-tab-active) {
          position: relative;
          top: 0;
          color: #000;
          background-color: #fff;

          .ant-tabs-tab-remove {
            color: #000;
          }

          .ant-tabs-tab-btn {
            color: #000;
            text-shadow: none;
          }

          &::before {
            display: none;
          }
        }

        :deep(.ant-tabs-tab-active) + .ant-tabs-tab {
          border-bottom-left-radius: 8px;

          &::before {
            display: none;
          }
        }

        :deep(.ant-tabs-tab:has(+ .ant-tabs-tab-active)) {
          border-bottom-right-radius: 8px;
        }

        :deep(.anticon-ellipsis) {
          color: #fff;
        }

        :deep(.ant-tabs-nav-more) {
          position: relative;
          top: -1px;
          padding: 6px 8px;
          background: rgb(235 235 235);

          .anticon-ellipsis {
            color: #999;
          }
        }
      }

      .btnBox {
        display: flex;
        align-items: center;

        .btnBoxMainText {
          padding: 12px;
          color: #333;
          text-wrap: nowrap;
          cursor: pointer;
          transition: color 0.3s;

          .btnBoxsubText {
            padding-left: 8px;
            font-size: 12px;
          }
        }

        .btn {
          margin-left: 10px;
        }
      }
    }

    .msgAlert {
      padding: 4px 12px;
      overflow: hidden; /* 隐藏溢出内容 */
      font-size: 14px;
      white-space: nowrap; /* 不换行 */
      border: 1px solid #ffe58f;

      ::v-deep(.ant-alert-content) {
        position: relative;
        height: 20px;
        overflow: hidden;
      }

      ::v-deep(.ant-alert-message) {
        position: absolute;
        display: inline-block;
        animation: scroll var(--duration) linear infinite; /* 动画效果，56秒完成一次循环，速度更慢 */
      }
    }

    @keyframes scroll {
      0% {
        right: 0%; /* 开始位置在右侧 */
        transform: translateX(100%);
      }

      100% {
        right: 100%; /* 结束位置在左侧 */
      }
    }

    .boxStr {
      box-sizing: border-box;
      display: flex;
      flex: 1;
      flex-direction: column;
      width: 100%;
      padding: 12px;
      overflow: hidden scroll;

      &::-webkit-scrollbar {
        display: none;
        width: 0 !important;
        height: 0 !important;
        appearance: none;
        background: transparent;
      }
    }
  }
}

.userBox {
  box-sizing: border-box;
  width: 330px;
  margin-bottom: 10px;
  overflow: hidden;
  background-color: #fff;
  border-radius: 6px;
  box-shadow:
    0 3px 6px -4px #0000001f,
    0 6px 16px #00000014,
    0 9px 28px 8px #0000000d;

  .nameBox {
    box-sizing: border-box;
    display: flex;
    padding: 16px 20px;
    background: #fff;

    .nameImg {
      width: 44px;
      min-width: 44px;
      height: 44px;
      min-height: 44px;
      margin-right: 12px;
      overflow: hidden;

      // background-color: #000;
      border-radius: 50%;

      img {
        width: 100%;
        height: 100%;
      }
    }

    .nameText {
      .textTop {
        font-size: 14px;
        font-weight: 500;
        line-height: 25px;
        color: #111;
      }

      .textBot {
        display: flex;
        font-size: 12px;
        font-weight: 400;
        line-height: 18px;
        color: #999;

        .label {
          white-space: nowrap;
        }
      }
    }
  }

  .tuiChu {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    padding-left: 24px;
    color: #666;
    cursor: pointer;
    background: #fff;
    border-top: 1px solid #f0f0f0;
    transition: all 0.3s;

    i {
      margin-right: 12px;
    }
  }

  .tuiChu:hover {
    color: #1890ff;
    background-color: #eef2fa;
  }

  .editInfoBtn {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    padding-left: 24px;
    color: #666;
    cursor: pointer;
    background: #fff;
    border-top: 1px solid #f0f0f0;
    transition: all 0.3s;

    i {
      margin-right: 12px;
    }
  }

  .editInfoBtn:hover {
    color: #1890ff;
    background: #eef2fa;
  }
}
</style>

<style>
.ant-menu-dark.ant-menu-submenu > .ant-menu {
  margin-left: -12px;
  background-color: #2a2a48;
}

.ant-menu-dark:not(.ant-menu-horizontal) .ant-menu-item:not(.ant-menu-item-selected):active,
.ant-menu-dark:not(.ant-menu-horizontal) .ant-menu-item:not(.ant-menu-item-selected):hover {
  color: #7ebcfc;
  background-color: rgb(64 158 255 / 10%);
}

.ant-menu-submenu-popup .ant-menu-vertical .ant-menu-item,
.ant-menu-submenu-popup .ant-menu-vertical .ant-menu-submenu-title {
  width: 100%;
  margin-inline: 0;
  border-radius: 0;
}

.ant-menu-dark .ant-menu-item-selected {
  color: #7ebcfc;
  background-color: rgb(64 158 255 / 10%);
  border-left: 3px solid #7ebcfc;
}

.ant-alert-message {
  font-size: 12px;
}

.view-tooltip {
  .vxe-tooltip--content {
    max-width: 60vw;
    max-height: 40vh;
    overflow-y: auto;

    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: transparent;
      border-radius: 3px;
    }

    &:hover::-webkit-scrollbar-thumb {
      background: #666;
    }
  }
}
</style>
