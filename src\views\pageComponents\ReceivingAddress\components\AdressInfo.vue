<template>
  <a-drawer v-model:open="detailVisible" width="35vw" :title="'查看收货地址'" placement="right" :footer-style="{ textAlign: 'left' }">
    <div class="detailBox">
      <a-form v-if="adressInfo" ref="formRef" :model="adressInfo">
        <div class="drawer-title">基本信息</div>
        <a-form-item label="地址编号:" :rules="[{ required: true }]">
          <span class="detailValue">{{ adressInfo.number ? adressInfo.number : '--' }}</span>
        </a-form-item>
        <a-form-item label="所在部门:" :rules="[{ required: true }]">
          <span class="detailValue">{{ adressInfo.full_name ? adressInfo.full_name : '--' }}</span>
        </a-form-item>
        <a-form-item label="手机号码:" :rules="[{ required: true }]">
          <span class="detailValue">{{ adressInfo.mobile_phone ? adressInfo.mobile_phone : '--' }}</span>
        </a-form-item>
        <a-form-item label="收货地址:" :rules="[{ required: true }]">
          <span class="detailValue mr100px">{{ adressInfo.province ? adressInfo.province : '--' }}</span>
          <span class="detailValue mr100px">{{ adressInfo.city ? adressInfo.city : '--' }}</span>
          <span class="detailValue mr100px">{{ adressInfo.area ? adressInfo.area : '--' }}</span>
        </a-form-item>
        <a-form-item label="详细地址:" :rules="[{ required: true }]">
          <span class="detailValue">{{ adressInfo.address ? adressInfo.address : '--' }}</span>
        </a-form-item>
      </a-form>
    </div>
    <template #footer>
      <a-button @click="detailVisible = false">关闭</a-button>
    </template>
  </a-drawer>
</template>

<script lang="ts" setup>
import { message, type FormInstance } from 'ant-design-vue'
import { GetAliPurchaseAccountReceiveAddress } from '@/servers/PurchaseAccount'

const formRef = ref<FormInstance>()
const detailVisible = ref(false)
const adressInfo = ref<any>({})
const open = (row) => {
  adressInfo.value = null
  detailVisible.value = true
  GetAliPurchaseAccountReceiveAddress({ id: row.id }).then((res) => {
    if (res.success) {
      adressInfo.value = res.data
    } else {
      message.error(res.message)
    }
  })
}

defineExpose({
  open,
})
</script>

<style lang="scss" scoped>
::v-deep(.ant-form-item) {
  align-items: flex-start;

  .ant-form-item-label {
    display: flex;
    justify-content: flex-end;
    width: 110px;
    min-width: 110px;
    margin-right: 30px;

    label {
      &::after {
        display: none !important;
      }
    }
  }
}

.detailTitle {
  padding-left: 12px;
  margin-bottom: 20px;
  font-weight: bold;
  color: #000;
}

.w350 {
  width: 350px;
}

.w250 {
  width: 250px;
}

.w150 {
  width: 150px;
}

.w200 {
  width: 200px;
}

.description {
  padding-left: 20px;
  font-size: 12px;
  color: rgb(0 0 0 / 50%);
  white-space: nowrap;
}

.detailValueDescription {
  font-size: 12px;
  color: rgb(0 0 0 / 50%);
}

.detailBox {
  padding-top: 12px;

  .loadingIcon {
    font-size: 30px;
    color: #1890ff;
  }
}
</style>
