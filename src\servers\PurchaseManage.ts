import { request } from './request'
// 获取采购单列表
export const GetPurchaseOrderList = (data: any) => {
  return request({ url: '/api/PurchaseOrder/GetPurchaseOrderList', data })
}
// 添加采购单
export const AddPurchaseOrder = (data: any, config?) => {
  return request({ url: '/api/PurchaseOrder/AddPurchaseOrder', data, config })
}
// 批量审批
export const BatchAudit = (data: any) => {
  return request({ url: '/api/PurchaseOrder/BatchAudit', data })
}
// 开启或关闭采购单
export const OpenPurchaseOrder = (data: any) => {
  return request({ url: '/api/PurchaseOrder/OpenPurchaseOrder', data })
}
// ClosePurchaseOrder
export const ClosePurchaseOrder = (data: any) => {
  return request({ url: '/api/PurchaseOrder/ClosePurchaseOrder', data })
}
// 获取采购单详情
export const GetPurchaseOrderDetail = (data: any) => {
  return request({ url: '/api/PurchaseOrder/GetPurchaseOrderDetail', data })
}
// 修改采购单
export const UpdatePurchaseOrder = (data: any, config?) => {
  return request({ url: '/api/PurchaseOrder/UpdatePurchaseOrder', data, config })
}
// 添加采购变更单/api/PurchaseOrderChange/AddPurchaseOrderChange
export const AddPurchaseOrderChange = (data: any, config?) => {
  return request({ url: '/api/PurchaseOrderChange/AddPurchaseOrderChange', data, config })
}
// 供应商下拉框： api/Supplier/GetMRPSupplierCompanySelect
export const GetMRPSupplierCompanySelect = () => {
  return request({ url: '/api/Supplier/GetMRPSupplierCompanySelect' }, 'POST')
}

// 修改采购详情
export const UpdatePurchaseOrderDetail = (data: any, config?) => {
  return request({ url: '/api/PurchaseOrder/UpdatePurchaseOrderDetail', data, config })
}

// 所有供应商下拉框：
// export const GetMRPOfflineSupplierCompanySelect = () => {
//   return request({ url: '/api/Supplier/GetMRPOfflineSupplierCompanySelect' }, 'GET')
// }

// 获取未绑定供应商子公司下拉框
// export const GetNotMRPSupplierCompanySelect = () => {
//   return request({ url: '/api/Supplier/GetNotMRPSupplierCompanySelect' }, 'GET')
// }
// 申请人下拉框：api/PurchaseOrder/GetBuyers
export const GetBuyers = () => {
  return request({ url: '/api/PurchaseOrder/GetBuyers' }, 'GET')
}
// 申请单汇总
export const GetPurchaseApplyOrderSummary = (data: any) => {
  return request({ url: '/api/PurchaseApplyOrder/GetPurchaseApplyOrderSummary', data })
}
// 获取采购表商品信息 /api/PurchaseOrder/GetPurchaseOrderProductInfo
export const GetPurchaseOrderProductInfo = (data: any, config?: any) => {
  return request({ url: '/api/PurchaseOrder/GetPurchaseOrderProductInfo', data, timeout: 0, config })
}
// 获取我的采购单列表 /api/MyOrder/GetMyOrderList
export const GetMyOrderList = (data: any) => {
  return request({ url: '/api/MyOrder/GetMyOrderList', data })
}
// 确认订单/api/MyOrder/ConfirmOrder
export const ConfirmOrder = (data: any) => {
  return request({ url: '/api/MyOrder/ConfirmOrder', data }, 'GET')
}
// 批量确认订单 /api/MyOrder/BatchConfirmOrder
export const BatchConfirmOrder = (data: any) => {
  return request({ url: '/api/MyOrder/BatchConfirmOrder', data })
}
// 获取历史价格信息 /api/Purchase0rder/GetPurchaseOrderHistoricalPriceInfo
export const GetPurchaseOrderHistoricalPriceInfo = (data: any) => {
  return request({ url: '/api/PurchaseOrder/GetPurchaseOrderHistoricalPriceInfo', data })
}
// 获取历史价格列表 /api/PurchaseOrder/GetHistoricalPriceList
export const GetHistoricalPriceList = (data: any) => {
  return request({ url: '/api/PurchaseOrder/GetHistoricalPriceList', data })
}
// 获取历史价格归档信息 /api/PurchaseOrder/GetHistoricalArchivePriceInfo
export const GetHistoricalArchivePriceInfo = (data: any) => {
  return request({ url: '/api/PurchaseOrder/GetHistoricalArchivePriceInfo', data })
}
// 获取历史价格列表 /api/PurchaseOrder/GetHistoricalArchivePriceList
export const GetHistoricalArchivePriceList = (data: any) => {
  return request({ url: '/api/PurchaseOrder/GetHistoricalArchivePriceList', data })
}
// 作废采购单
export const CancelledPurchaseOrder = (data: any) => {
  return request({ url: '/api/PurchaseOrder/CancelledPurchaseOrder', data })
}
// 推单
export const RecommendOrder = (data: any, config?) => {
  return request({ url: '/api/PurchaseOrder/RecommendOrder', data, config })
}
// 反审核
export const CounterAuthorization = (data: any) => {
  return request({ url: `/api/PurchaseOrder/CounterAuthorization?id=${data.id}`, data }, 'GET')
}
// 获取1688库存
export const GetInventoryBy1688 = (data: any) => {
  return request({ url: '/api/PurchaseOrder/GetInventoryBy1688', data })
}
// 获取拍单子表列表
export const GetTakeOrderList = (data: any) => {
  return request({ url: `/api/PurchaseOrder/GetTakeOrderList?id=${data.id}`, data }, 'GET')
}
// 修改拍单子表订单类型
export const UpdateTakeOrderType = (data: any) => {
  return request({ url: `/api/PurchaseOrder/UpdateTakeOrderType`, data })
}
// 更新拍单子表信息
export const UpdateTakeOrderData = (data: any) => {
  return request({ url: `/api/PurchaseOrder/UpdateTakeOrderData?id=${data.id}`, data }, 'GET')
}

// 导出采购单商品明细
export enum PurchaseOrderExportTypeEnum {
  '根据勾选导出' = 1,
  '根据筛选结果导出' = 2,
  '全部导出' = 3,
  '根据勾选带图导出' = 4,
  '根据筛选结果带图导出' = 5,
  '全部带图导出' = 6,
}
export const ExportPurchaseOrderDetails = (data: any) => {
  return request({ url: `/api/PurchaseOrder/ExportPurchaseOrderDetails`, data })
}

/** 批量修改备注 */
export const BatchUpdatePurchaseOrderRemark = (data: any) => {
  return request({ url: '/api/PurchaseOrder/BatchUpdatePurchaseOrderRemark', data })
}

/** 批量修改采购单信息 */
export const BatchUpdatePurchaseOrderCompany = (data: any) => {
  return request({ url: '/api/PurchaseOrder/BatchUpdatePurchaseOrderCompany', data })
}

/** 获取采购单付款明细列表 */
export const GetPurchaseOrderPaymentOrderList = (data: any) => {
  return request({ url: '/api/PaymentOrder/GetPurchaseOrderPaymentOrderList', data }, 'GET')
}

/** 获取采购单退库明细列表 */
export const GetPurchaseOrderReturnApplyOrderList = (data: any) => {
  return request({ url: '/api/PurchaseReturnApplication/GetPurchaseOrderReturnApplyOrderList', data }, 'GET')
}

/** 获取采购单明细列表 */
export const GetPurchaseOrderDetailList = (data: any) => {
  return request({ url: '/api/PurchaseOrder/GetPurchaseOrderDetailList', data })
}

/** 获取采购单标签下拉框 */
export const GetPurchaseOrderTagsSelect = () => {
  return request({ url: '/api/BusinessCommon/GetPurchaseOrderTagsSelect' }, 'GET')
}

// 批量更新采购单预计发货时间
export const BatchUpdatePurchaseOrderPredictDeliveryDate = (data: any) => {
  return request({ url: '/api/PurchaseOrder/BatchUpdatePurchaseOrderPredictDeliveryDate', data })
}

// 更新采购单预计发货时间
export const UpdatePurchaseOrderPredictDeliveryDate = (data: any) => {
  return request({ url: '/api/PurchaseOrder/UpdatePurchaseOrderPredictDeliveryDate', data })
}

// 批量修改采购单商品备注
export const BatchUpdatePurchaseOrderDetailRemark = (data: any) => {
  return request({ url: '/api/PurchaseOrder/BatchUpdatePurchaseOrderDetailRemark', data })
}

// 查询要支付的列表数据
export const GetAliPayInfoList = (data: any) => {
  return request({ url: '/api/PurchaseOrder/GetAliPayInfoList', data })
}

// 1688批量支付
export const BatchAliPay = (data: any) => {
  return request({ url: '/api/PurchaseOrder/BatchAliPay', data })
}

// 执行手动完成订单
export const CompletedOrder = (data: any) => {
  return request({ url: '/api/PurchaseOrder/CompletedOrder', data })
}

// 获取采购单是否已关联应付单
export const CheckPurchaseOrderAllowPriceChange = (data: any) => {
  return request({ url: '/api/PurchaseOrder/CheckPurchaseOrderAllowPriceChange', data })
}

// 获取拆单商品列表
export const GetSplitPurchaseOrderApplyList = (data: any) => {
  return request({ url: '/api/PurchaseOrder/GetSplitPurchaseOrderApplyList', data }, 'GET')
}

// 拆分采购单
export const SplitPurchaseOrder = (data: any) => {
  return request({ url: '/api/PurchaseOrder/SplitPurchaseOrder', data })
}
