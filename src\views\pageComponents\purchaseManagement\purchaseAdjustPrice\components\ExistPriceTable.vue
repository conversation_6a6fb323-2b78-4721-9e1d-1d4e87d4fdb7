<template>
  <a-drawer :maskClosable="false" title="商品" width="50vw" :visible="visible" @close="handleClose">
    <a-space class="mb-12">
      <!-- <a-input placeholder="商品编码/聚水潭编码" v-model:value="queryParams.sku_id" :maxlength="200" allowClear /> -->
      <a-input-group compact class="!flex">
        <a-input v-model:value="queryParams.sku_id" placeholder="商品编码/聚水潭编码" allow-clear @blur="queryParams.sku_id = `${queryParams.sku_id || ''}`.trim()" />
        <a-button @click="showInputDlg({ label: '商品编码' })" class="!flex items-center justify-center !w-40px">
          <template #icon>
            <span class="iconfont icon-chazhao_find text-14px c-#000"></span>
          </template>
        </a-button>
      </a-input-group>
      <a-input placeholder="商品名称" v-model:value="queryParams.sku_name" :maxlength="200" allowClear />
      <a-select placeholder="商品分类" v-model:value="queryParams.category" :options="categoryOptions" allowClear />
      <a-button type="primary" @click="handleSearch">查询</a-button>
    </a-space>
    <vxe-table
      v-if="visible"
      :border="true"
      ref="productTableRef"
      size="mini"
      :row-config="{ isHover: true, keyField: 'id', height: 40 }"
      :custom-config="{ mode: 'popup' }"
      :data="data"
      :show-overflow="true"
      :show-header-overflow="true"
      :show-footer-overflow="true"
      :column-config="{ resizable: true }"
      class="tableBoxwidth"
      :checkbox-config="{ reserve: true, checkRowKeys }"
      min-height="0"
      stripe
      v-bind="$attrs"
      @checkbox-all="selectChangeEvent"
      @checkbox-change="selectChangeEvent"
    >
      <vxe-column type="checkbox" field="checkbox" width="50" fixed="left" align="center"></vxe-column>
      <slot name="column">
        <template v-for="i in tableKey" :key="i.field">
          <vxe-column v-bind="i">
            <template v-if="$slots[String(i.field)]" #default="attr">
              <slot :name="i.field" v-bind="attr" :item="i" />
            </template>
          </vxe-column>
        </template>
      </slot>
    </vxe-table>
    <div class="flex justify-end mt-10">
      <a-pagination
        v-model:current="queryParams.page"
        v-model:page-size="queryParams.pageSize"
        show-quick-jumper
        :total="queryParams.total"
        show-size-changer
        :page-size-options="['20', '50', '100', '250']"
        @change="getProductList"
        size="small"
        :showTotal="(total) => `共 ${total} 条`"
      >
        <template #buildOptionText="props">
          <span>{{ props.value }}条/页</span>
        </template>
      </a-pagination>
    </div>
    <template #footer>
      <a-space>
        <a-button type="primary" @click="handleSelectProduct">确定</a-button>
        <a-button @click="handleClose">取消</a-button>
      </a-space>
    </template>

    <a-modal v-model:open="isShowInputDlg" title="批量输入" okText="确定" @ok="closeInputDlg" @cancel="isShowInputDlg = false" style="top: 200px">
      <a-form-item
        :label="inputLabel"
        :label-col="{
          style: {
            width: '130px',
          },
        }"
      >
        <a-textarea v-model:value="inputText" :placeholder="`多个${inputLabel}，以逗号分隔或每行一个${inputLabel}`" :rows="10" />
      </a-form-item>
      <template #footer>
        <a-button style="float: left" @click="inputText = ''">清空</a-button>
        <a-button @click="isShowInputDlg = false">取消</a-button>
        <a-button type="primary" @click="closeInputDlg">确认</a-button>
      </template>
    </a-modal>
  </a-drawer>
</template>

<script setup lang="ts">
import { VxeTableInstance } from 'vxe-table'

import { GetPurchasePriceDetail } from '@/servers/Purchaseprice'

const productTableEl = useTemplateRef<VxeTableInstance>('productTableRef')
const checkRowKeys = ref<string[]>([])
const emits = defineEmits(['selectProduct'])

const visible = ref(false)

const data = ref<any[]>([])

const selectProductList = ref<any[]>([])

const queryParams = ref<any>({
  page: 1,
  pageSize: 20,
})

const categoryOptions = ref<any[]>([
  { label: '成品', value: 1 },
  { label: '半成品', value: 2 },
  { label: '原材料', value: 3 },
  { label: '包材', value: 4 },
])

const tableKey = ref([
  { title: '商品编码', field: 'sku_id', width: 100 },
  { title: '商品编码(聚水潭)', field: 'jst_sku_id', width: 180 },
  { title: '商品名称', field: 'sku_name', width: 100 },
  { title: '规格型号', field: 'type_specification', width: 100 },
  { title: '采购单位', field: 'valuation_unit', width: 100 },
  { title: '采购数量（从）', field: 'quantity_from', width: 120 },
  { title: '采购数量（至）', field: 'quantity_to', width: 120 },
  { title: '换算值', field: 'conversion_value', width: 120 },
  {
    title: '成本数量（从）',
    field: 'cost_num_from',
    width: 120,
    formatter: ({ row }) => {
      const result = row.quantity_from * row.conversion_value || 0
      if (result === 0) return ''
      return result.roundNext(5)
    },
  },
  {
    title: '成本数量（至）',
    field: 'cost_num_to',
    width: 120,
    formatter: ({ row }) => {
      const result = row.quantity_to * row.conversion_value || 0
      if (result === 0) return ''
      return result.roundNext(5)
    },
  },
  { title: '单价', field: 'str_price', width: 120 },
  { title: '生效时间', field: 'take_effect_time', width: 200 },
  { title: '失效时间', field: 'lose_efficacy_time', width: 200 },
])

// 打开
const open = (id: number) => {
  selectProductList.value = []
  queryParams.value.id = id
  checkRowKeys.value = []
  getProductList()
  visible.value = true
}
// 关闭
const handleClose = () => {
  queryParams.value = {
    page: 1,
    pageSize: 20,
  }
  visible.value = false
  selectProductList.value = []
}

// 获取商品列表
const getProductList = async (page = 1) => {
  const params: any = {}
  for (const key in queryParams.value) {
    if (queryParams.value[key]) {
      params[key] = queryParams.value[key]
    }
  }
  params.page = page
  const res = await GetPurchasePriceDetail(params)
  queryParams.value.page = page
  queryParams.value.total = res.data.total
  data.value = res.data.list
}

// 查询
const handleSearch = () => {
  getProductList()
}

// 批量输入
const isShowInputDlg = ref(false)
const inputLabel = ref(null)
const inputText = ref('')
const showInputDlg = (item) => {
  isShowInputDlg.value = true
  inputText.value = queryParams.value.sku_id
  inputLabel.value = item.label
}
const closeInputDlg = () => {
  queryParams.value.sku_id = dealStr(inputText.value)
  isShowInputDlg.value = false
}

const dealStr = (val) => {
  if (val) {
    val = val.replace(/\n/g, ',')
    val = val.replace(/，/g, ',')
    val = val.replace(/;/g, ',')
    val = val.replace(/；/g, ',')
    let arr = []
    let str = ''
    arr = val.split(',')
    arr.forEach((it: string) => {
      if (it) {
        str += `${it.trim()},`
      }
    })
    str = str.slice(0, -1)
    return str
  }
}

// 选中事件
const selectChangeEvent = () => {
  const $table = productTableEl.value
  // 当前页选中的数据
  const currentSelectedRows = $table?.getCheckboxRecords() || []
  // 其他页选中的数据
  const otherSelectedRows = $table?.getCheckboxReserveRecords() || []

  selectProductList.value = [...currentSelectedRows, ...otherSelectedRows]
}

// 确定
const handleSelectProduct = () => {
  const list = data.value.filter((i) => selectProductList.value.some((j) => j.sku_id === i.sku_id))
  emits('selectProduct', list)
  handleClose()
}

defineExpose({
  open,
})
</script>
