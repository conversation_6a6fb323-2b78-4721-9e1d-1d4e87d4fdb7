// 角色接口
import { request } from './request'
// 新增角色
export const Add = (data) => {
  return request({ url: '/api/Role/Add', data })
}
// 编辑角色
export const Update = (data) => {
  return request({ url: '/api/Role/Update', data })
}
// 停用/启用角色
export const UpdateRoleStatus = (data) => {
  return request({ url: '/api/Role/UpdateRoleStatus', data })
}
// 删除角色
export const Delete = (data) => {
  return request({ url: '/api/Role/Delete', data })
}
// 查询角色列表
export const GetList = (data) => {
  return request({ url: '/api/Role/GetList', data })
}
// 根据角色获取权限列表
export const GetListByRole = (data) => {
  return request({ url: '/api/Role/GetListByRole', data })
}
// 选择角色授权权限
export const SaveAuthRole = (data) => {
  return request({ url: '/api/Role/SaveAuthRole', data })
}
// 获取角色下拉框
export const GetRoleSelectOption = (data) => {
  return request({ url: '/api/Role/GetRoleSelectOption', data })
}
// 获取角色日志
export const GetOpLogInfos = (data) => {
  return request({ url: '/api/Role/GetOpLogInfos', data })
}
// 获取角色详情
export const Detail = (data) => {
  return request({ url: '/api/Role/GetRoleById', data }, 'GET')
}
