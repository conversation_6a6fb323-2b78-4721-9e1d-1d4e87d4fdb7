import { Enum2Map, Enum2Options } from '@/utils'

export const setFormItems = () =>
  [
    { type: 'title', span: 24, title: '基本信息' },
    { label: '应付单编号', name: 'number', type: 'input', disabled: true, placeholder: ' ', span: 9 },
    { label: '单据日期', name: 'pay_order_date', type: 'date', span: 9 },
    {
      label: '供应商',
      type: 'select',
      name: 'supplier_id',
      alias: 'supplier_name',
      isAlias: true,
      placeholder: ' ',
      options: [],
      span: 9,
      disabled: true,
    },
    {
      label: '供应商子公司',
      type: 'select',
      name: 'company_supplier_id',
      alias: 'company_supplier_name',
      isAlias: true,
      placeholder: ' ',
      options: [],
      span: 9,
      disabled: true,
    },
    {
      label: '审核状态',
      name: 'audit_status',
      type: 'text',
      textFormat: ({ value }) => {
        return Enum2Map(BillPayableOrderAuditEnum)[value]
      },
      span: 9,
    },
    {
      label: '结算状态',
      name: 'settlement_status',
      textFormat: ({ value }) => Enum2Map(BillPayableOrderSettlementEnum)[value],
      type: 'text',
      span: 9,
    },
    {
      label: '备注',
      name: 'remark',
      type: 'textarea',
      span: 18,
      props: { rows: 2 },
    },
    { type: 'title', span: 24, title: '开票明细' },
    {
      label: '发票类型',
      name: 'invoice_type',
      alias: 'invoice_type_string',
      type: 'select',
      required: false,
      options: Enum2Options(InvoiceTypeEnum),
      span: 9,
      linkage: ['invoice_date', 'invoice_number', 'invoice_subject', 'amount_invoiced', 'attachments'],
    },
    {
      label: '发票日期',
      name: 'invoice_date',
      type: 'date',
      span: 9,
      linkageFn: ({ form, item }) => {
        item.hide = form.invoice_type === 3
      },
    },
    {
      label: '发票号码',
      name: 'invoice_number',
      type: 'input',
      span: 9,
      linkageFn: ({ form, item }) => {
        item.hide = form.invoice_type === 3
      },
    },
    {
      label: '发票主体',
      name: 'invoice_subject',
      type: 'input',
      span: 9,
      linkageFn: ({ form, item }) => {
        item.hide = form.invoice_type === 3
      },
    },
    {
      label: '已开票金额',
      name: 'amount_invoiced',
      type: 'number',
      span: 9,
      linkageFn: ({ form, item }) => {
        item.hide = form.invoice_type === 3
      },
      props: {
        precision: 2,
      },
    },
    {
      label: '附件',
      name: 'attachments',
      type: 'upload',
      span: 18,
      linkageFn: ({ form, item }) => {
        item.hide = form.invoice_type === 3
      },
      module: UploadFileModuleEnum.BillPayable,
    },
    { slot: 'details', span: 24 },
    {
      label: '应付总额',
      // labelTip: '应付金额+其他费用-优惠金额+赠品应付金额',
      labelTip: '应付金额+赠品应付金额',
      name: 'inv_amount',
      placeholder: ' ',
      type: 'number',
      span: 9,
      props: { precision: 2, disabled: true },
    },
    // {
    //   label: '可抵预付金额',
    //   name: 'offset_against_prepayment',
    //   type: 'number',
    //   placeholder: ' ',
    //   span: 9,
    //   props: { precision: 2, disabled: true },
    // },
    // {
    //   label: '开模费',
    //   name: 'mould_fees',
    //   type: 'number',
    //   placeholder: ' ',
    //   span: 9,
    //   props: { precision: 2, disabled: true },
    // },
    // {
    //   label: '可抵退款金额',
    //   name: 'refundable_amount',
    //   type: 'number',
    //   placeholder: ' ',
    //   span: 9,
    //   props: { precision: 2, disabled: true },
    // },
    // {
    //   label: '折扣金额',
    //   name: 'discount_amount',
    //   type: 'number',
    //   span: 9,
    //   fill: 9,
    //   props: {
    //     precision: 2,
    //     min: 0,
    //     onChange: calcSumPrice,
    //   },
    // },
    // {
    //   label: '优化金额',
    //   name: 'optimize_amount',
    //   type: 'number',
    //   span: 9,
    //   fill: 9,
    //   props: {
    //     precision: 2,
    //     min: 0,
    //     onChange: calcSumPrice,
    //   },
    // },
    // {
    //   label: '扣款金额',
    //   name: 'deduct_amount',
    //   type: 'number',
    //   span: 9,
    //   fill: 9,
    //   props: {
    //     precision: 2,
    //     min: 0,
    //     onChange: calcSumPrice,
    //   },
    // },
    // {
    //   label: '其他费用',
    //   name: 'other_fees',
    //   type: 'number',
    //   span: 9,
    //   fill: 9,
    //   props: {
    //     precision: 2,
    //     onChange: calcSumPrice,
    //   },
    // },
    // {
    //   label: '金额备注',
    //   name: 'amount_remark',
    //   type: 'textarea',
    //   span: 9,
    //   fill: 9,
    //   props: { rows: 4 },
    // },
    // {
    //   label: '费用计入成本',
    //   name: '$ben',
    //   type: 'text',
    //   span: 24,
    //   textFormat: () => '按金额计入成本',
    // },
    // {
    //   label: '实际应付金额',
    //   labelTip: '应付总额+开模费-折扣金额-优化金额-扣款金额+其他费用',
    //   name: 'actual_amount_due',
    //   type: 'number',
    //   placeholder: ' ',
    //   span: 9,
    //   fill: 9,
    //   props: { precision: 2, disabled: true },
    // },
  ] as EasyFormItemProps[]

// 应付明细
export const setPayColumns = () => {
  return [
    { title: '序号', type: 'seq', width: 60, fixed: 'left', align: 'center' },
    { title: '进出仓单号', field: 'io_warehouse_id', width: 80 },
    { title: '单据类型', field: 'order_type', formatter: ({ cellValue }) => Enum2Map(SupplierLiquidateOrderTypeEnum)[cellValue], width: 80 },
    { title: '采购单号', field: 'purchase_order_number', slots: { default: 'link' } },
    { title: '预约进出仓单号', field: 'reserve_io_warehouse_number', width: 140 },
    { title: '进出仓日期', field: 'reserve_io_warehouse_date', width: 150 },
    { title: '采购日期', field: 'purchase_date', width: 100 },
    { title: '商品编号', field: 'k3_sku_id' },
    { title: '商品名称', field: 'sku_name', width: 180 },
    { title: '款式编码', field: 'style_code' },
    { title: '商品分类', field: 'all_category', width: 100 },
    { title: '商品标签', field: 'product_tags', formatter: 'join' },
    { title: '税率', field: 'tax_rate', width: 60, slots: { default: 'tax_rate' } },
    { title: '采购含税单价', field: 'tax_unit_price' },
    {
      title: '实际单价',
      field: 'actual_tax_unit_price',
      slots: { default: 'number' },
      params: { precision: 8 },
      // 权限控制：编辑实际单价 (135010)
      permissionField: 'edit_actual_unit_price',
      permissionCode: 135010,
    },
    { title: '采购入库数量', field: 'purchase_inbound_quantity' },
    {
      title: '应付金额',
      field: 'inv_amount',
      params: { precision: 2 },
      formatter: 'number',
    },
    {
      title: '赠品数量',
      field: 'gift_purchase_quantity',
      slots: { default: 'number' },
      params: { precision: 0, min: 0 },
    },
    {
      title: '赠品单价',
      field: 'gift_purchase_unit_price',
      slots: { default: 'number' },
      params: { precision: 2, min: 0 },
    },
    {
      title: '赠品应付金额',
      field: 'gift_amount_due',
      slots: { default: 'number' },
      params: { precision: 2 },
      titleHelp: {
        message: '赠品数量*赠品单价',
        enterable: false,
      },
    },
    {
      title: '进出仓单备注',
      field: 'remark',
    },
    {
      title: '操作',
      field: 'action',
      fixed: 'right',
      slots: { default: 'action' },
      align: 'center',
      width: 80,
    },
  ].map((f) => ({ ...f, width: f.width || 120 })) as VxeGridPropTypes.Columns
}

// 预付可抵明细
export const prepaidColumns = [
  { title: '序号', type: 'seq', width: 60, fixed: 'left', align: 'center' },
  { title: '预付单号', field: 'payment_order_number', align: 'center' },
  { title: '采购单号', field: 'purchase_order_number', align: 'center' },
  { title: '剩余可用金额', field: 'remaining_available', align: 'center' },
]

// 仅退款可抵明细
export const refundColumns = [
  { title: '序号', type: 'seq', width: 60, fixed: 'left', align: 'center' },
  { title: '退款申请单', field: 'purchase_return_application_number', align: 'center' },
  {
    title: '采购单号',
    field: 'purchase_order_numbers',
    align: 'center',
    formatter: ({ value }) => (value ?? []).join(','),
  },
  { title: '退款总金额', field: 'total_refunds', align: 'center' },
]

// 开模明细
export const mouldColumns = [
  { title: '序号', type: 'seq', width: 60, fixed: 'left', align: 'center' },
  { title: '采购单号', field: 'purchase_order_number', align: 'center' },
  { title: '商品编码', field: 'k3_sku_id', align: 'center' },
  { title: '商品名称', field: 'sku_name', align: 'center' },
  { title: '开模费', field: 'mould_fees', align: 'center' },
]
