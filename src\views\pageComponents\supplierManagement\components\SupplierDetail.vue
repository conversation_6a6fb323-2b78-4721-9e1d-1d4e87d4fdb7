<template>
  <a-drawer :maskClosable="false" v-model:open="visible" :title="isAudit ? '供应商审核' : '供应商详情'" width="80%" @close="close" :body-style="{ padding: 0 }">
    <div class="flex h-full detail-drawer c-#333" v-if="visible">
      <a-tabs tab-position="left" size="small" :active-key="activeKey" @change="tabChange">
        <a-tab-pane v-for="item in tabList" :key="item.id" :tab="item.name"></a-tab-pane>
      </a-tabs>
      <div class="flex-1 overflow-auto h-full pb-10 box-border pr-25" ref="contentRef">
        <template v-if="data">
          <div class="title" id="baseInfo">基本信息</div>
          <a-descriptions :column="3" bordered size="small" :labelStyle="{ width: px2(220) + 'px' }" :contentStyle="{ width: px2(300) + 'px' }">
            <a-descriptions-item label="供应商名称">{{ data.supplier_name }}</a-descriptions-item>
            <a-descriptions-item label="供应商编码">{{ data.supplier_id }}</a-descriptions-item>
            <a-descriptions-item label="金蝶供应商编码">{{ data.k3_supplier_number }}</a-descriptions-item>
            <a-descriptions-item label="供应商类型">{{ supplierTypeMap[data.supplier_type] }}</a-descriptions-item>
            <a-descriptions-item label="采购性质">{{ purchaseNatureMap[data.purchase_nature!] }}</a-descriptions-item>
            <a-descriptions-item label="供应商分组">{{ data.supplier_group_name }}</a-descriptions-item>
            <a-descriptions-item label="承运方式">{{ carriageModeMap[data.carriage_mode!] }}</a-descriptions-item>
            <!-- <a-descriptions-item label="公司网址/1688店铺网址">{{ data.website }}</a-descriptions-item>
            <a-descriptions-item label="1688店铺名字">{{ data.shop_name }}</a-descriptions-item> -->
            <a-descriptions-item label="申请时间">{{ data.create_at }}</a-descriptions-item>
            <a-descriptions-item label="供应商状态">{{ statusMap[data.status] }}</a-descriptions-item>
            <a-descriptions-item label="描述说明" :span="3">{{ data.describes }}</a-descriptions-item>
            <a-descriptions-item label="营业执照/资质证书等" :span="3">
              <FileModal :list="data.business_license_files" />
            </a-descriptions-item>
          </a-descriptions>

          <div class="title" id="subsidiariesInfo">子公司信息</div>
          <SimpleTable :tableKey="companyTableKeys" :data="data.supplier_company_info_list" :row-style="rowStyle" :show-overflow="false" :cell-config="{}">
            <template #buyer_names="{ row }">
              <!-- <a-button v-if="data.audit_status == 20 && isAudit" class="ml-2" type="link" size="small" @click="selectPurchaseUser(row)">修改</a-button> -->
              <div class="w-full flex justify-between items-center">
                <div>
                  <div v-for="people in row.buyer_list" :key="people.value" class="my-4px lh-16px">{{ people.label }}</div>
                </div>
                <div class="flex flex-col ml-3px" v-if="data.audit_status == 20 && isAudit">
                  <a-button class="flex-shrink-0 mb-2px" type="link" size="small" @click="selectPurchaseUser(row)">修改</a-button>
                </div>
              </div>
            </template>
          </SimpleTable>

          <div class="title" id="contactInfo">联系信息</div>
          <SimpleTable :tableKey="contactTableKeys" :data="data.supplier_contact_info_list" :row-style="rowStyle">
            <template #address="{ row }">{{ row.province }} {{ row.city }} {{ row.area }} {{ row.address }}</template>
            <template #is_default="{ row }">
              <a-checkbox v-model:checked="row.is_default" disabled />
            </template>
          </SimpleTable>

          <div class="title" id="financialInfo">财务信息</div>
          <div class="flex mb-4 text-xs">
            <div>结算方式: {{ settlementTypeMap[data.settlement_type!] }}</div>
            <div class="ml-20">发票类型: {{ invoiceTypeMap[data.invoice_type!] }}</div>
            <div class="ml-20">默认税率: {{ data.default_tax_rate }}</div>
          </div>
          <SimpleTable :tableKey="financialTableKeys" :data="data.supplier_finance_info_list" :row-style="rowStyle">
            <template #collection_account_certificate_files="{ row }">
              <FileModal :list="row.collection_account_certificate_files" />
            </template>
            <template #settlement_type>
              {{ settlementTypeMap[data.settlement_type!] }}
            </template>
            <template #payment_method="{ row }">
              {{ paymentMethodMap[row.payment_method] }}
            </template>
            <template #account_type="{ row }">
              {{ accountTypeMap[row.account_type] }}
            </template>
            <template #is_default="{ row }">
              <a-checkbox v-model:checked="row.is_default" disabled />
            </template>
          </SimpleTable>

          <div class="title" id="deliveryInfo">发货信息</div>
          <SimpleTable :tableKey="deliveryTableKeys" :data="data.supplier_shipments_info_list" :row-style="rowStyle">
            <template #shipments_address="{ row }">{{ row.province }} {{ row.city }} {{ row.area }} {{ row.shipments_address }}</template>

            <template #is_default="{ row }">
              <a-checkbox v-model:checked="row.is_default" disabled />
            </template>
          </SimpleTable>

          <div class="title" id="userInfo">用户信息</div>
          <SimpleTable :tableKey="userTableKeys" :data="data.supplier_user_info_list" :row-style="rowStyle">
            <template #status="{ row }">
              {{ row.status == 1 ? '启用' : '禁用' }}
            </template>
          </SimpleTable>

          <div class="title" id="otherInfo">其他信息</div>
          <SimpleTable :tableKey="otherTableKeys" :data="[data]">
            <template #audit_status="{ row }">
              {{ auditStatusMap[row.audit_status] }}
            </template>
          </SimpleTable>
        </template>
      </div>
    </div>

    <template #footer>
      <a-space :size="12">
        <a-button v-if="isAudit" type="primary" class="ml-2" @click="handleAudit(true)" :loading="auditLoading">通过</a-button>
        <a-button v-if="isAudit" class="ml-2" @click="handleReject()" :loading="auditLoading">拒绝</a-button>
        <a-button class="ml-2" @click="close">关闭</a-button>
      </a-space>
    </template>
  </a-drawer>

  <Purchase ref="PurchaseRef" @confirm="purchaseConfirm"></Purchase>
  <RejectModal ref="rejectModalRef" @confirm="handleRejectConfirm" />
</template>

<script lang="ts" setup>
import { message } from 'ant-design-vue'

import { px2, throttle } from '@/utils'
import SimpleTable from '@/components/SimpleTable.vue'
import { accountTypeMap, auditStatusMap, carriageModeMap, invoiceTypeMap, paymentMethodMap, purchaseNatureMap, settlementTypeMap, statusMap, supplierTypeMap } from '@/common/map'

import { SupplierInfo } from './Supplier.d'
import Purchase from './PurchaseModal.vue'

import { FinanceAudit, GetSupplierAuditProcess, GetSupplierMessage, ManagerAudit, UpdateSupplierBuyer } from '@/servers/Supplier'

const visible = ref(false)
const isAudit = ref(false)

const auditLoading = ref(false)
const activeKey = ref('baseInfo')
const scrollFlag = ref(false)
const tabList = ref([
  { id: 'baseInfo', name: '基本信息' },
  { id: 'subsidiariesInfo', name: '子公司信息' },
  { id: 'contactInfo', name: '联系信息' },
  { id: 'financialInfo', name: '财务信息' },
  { id: 'deliveryInfo', name: '发货信息' },
  { id: 'userInfo', name: '用户信息' },
  { id: 'otherInfo', name: '其他信息' },
])
const tabChange = (key) => {
  activeKey.value = key
  // 滚动到对应id
  const element = document.querySelector(`.detail-drawer #${key}`) as HTMLElement
  if (element) {
    element.scrollIntoView({ behavior: 'smooth' })
    scrollFlag.value = true
    setTimeout(() => {
      scrollFlag.value = false
    }, 1000)
  }
}

const scroll = throttle(() => {
  if (scrollFlag.value) return
  let current = null as any
  tabList.value.forEach((item) => {
    const element = document.querySelector(`.detail-drawer #${item.id}`) as HTMLElement
    if (contentRef.value && contentRef.value.scrollTop + px2(140) >= element!.offsetTop) {
      current = item
    }
  })
  if (current) activeKey.value = current.id
}, 300)
const data = ref<SupplierInfo>()
const contentRef = ref()

const loadding = ref(false)
const open = async (id, is_audit, auditStatus) => {
  data.value = undefined
  visible.value = true
  isAudit.value = auditStatus || false

  loadding.value = true
  const method = is_audit ? GetSupplierAuditProcess : GetSupplierMessage
  const res = await method({ id })
  data.value = {
    ...res.data,
    supplier_company_info_list: (res.data.supplier_company_info_list || []).map((v) => {
      const buyerNames = (v.buyer_names || '').split('、')
      return {
        ...v,
        buyer_list: (v.buyer_ids || []).reduce((acc, value, index) => {
          if (value === 0) return acc
          return [...acc, { value, label: buyerNames[index] }]
        }, []),
      }
    }),
    supplier_contact_info_list: res.data.supplier_contact_info_list?.map((v) => ({ ...v, supplier_name: v.type === 1 ? '当前供应商所有子公司可见' : v.company_supplier_name })) || [],
    supplier_finance_info_list: res.data.supplier_finance_info_list?.map((v) => ({ ...v, supplier_name: v.type === 1 ? '当前供应商所有子公司可见' : v.company_supplier_name })) || [],
    supplier_shipments_info_list: res.data.supplier_shipments_info_list?.map((v) => ({ ...v, supplier_name: v.type === 1 ? '当前供应商所有子公司可见' : v.company_supplier_name })) || [],
    supplier_user_info_list: res.data.supplier_user_info_list?.map((v) => ({ ...v })) || [],
  }
  loadding.value = false

  nextTick(() => {
    tabChange(tabList.value[0].id)
    contentRef.value?.addEventListener('scroll', scroll)
  })
}

const close = () => {
  visible.value = false

  nextTick(() => {
    contentRef.value?.removeEventListener('scroll', scroll)
  })
}

const companyTableKeys = [
  { type: 'seq', title: '序号', width: px2(50) },
  { title: '子公司供应商编号', field: 'company_supplier_id' },
  { title: '供应商编号(聚水潭)', field: 'jst_supplier_id' },
  { title: '供应商子公司名称', field: 'company_supplier_name' },
  { title: '对接采购员', field: 'buyer_names', showOverflow: false },
  { title: '供应商分类', field: 'supplier_category', showOverflow: false },
  { title: '公司网址/1688店铺网址', field: 'website' },
  { title: '1688店铺名字', field: 'shop_name' },
]

// 联系信息表格
const contactTableKeys = [
  { type: 'seq', title: '序号', width: px2(50) },
  { title: '供应商名称/子公司名称', field: 'supplier_name' },
  { title: '联系人姓名', field: 'name' },
  { title: '职务', field: 'job' },
  { title: '电话', field: 'phone_number' },
  { title: '手机', field: 'mobile_phone_number' },
  { title: '传真', field: 'fax_no' },
  { title: '邮箱', field: 'email' },
  { title: '联系地址', field: 'address' },
  { title: '是否默认', field: 'is_default' },
]

//  财务信息
const financialTableKeys = [
  { type: 'seq', title: '序号', width: px2(50) },
  { title: '供应商名称/子公司名称', field: 'supplier_name' },
  { title: '收款方式', field: 'payment_method' },
  { title: '账户名称', field: 'account_name' },
  { title: '账户类型', field: 'account_type' },
  { title: '收款卡号', field: 'collection_card_number' },
  { title: '收款银行支行', field: 'collection_bank' },
  { title: '收款账号盖章凭证', field: 'collection_account_certificate_files' },
  { title: '是否默认', field: 'is_default' },
  { title: '备注', field: 'remark' },
]

// 发货信息
const deliveryTableKeys = [
  { type: 'seq', title: '序号', width: px2(50) },
  { title: '供应商名称/子公司名称', field: 'supplier_name' },
  { title: '发货地址', field: 'shipments_address' },
  { title: '是否默认', field: 'is_default' },
]

// 用户信息
const userTableKeys = [
  { type: 'seq', title: '序号', width: px2(50) },
  { title: '姓名', field: 'real_name' },
  { title: '联系方式', field: 'phone_number' },
  { title: '账号', field: 'user_name' },
  { title: '密码', field: 'password' },
  { title: '状态', field: 'status' },
]
// 其他信息
const otherTableKeys = [
  { type: 'seq', title: '序号', width: px2(50) },
  { title: '创建人', field: 'creator_name' },
  { title: '申请日期', field: 'create_at' },
  { title: '最近修改人', field: 'modifier_name' },
  { title: '最近修改时间', field: 'modified_at' },
  { title: '最近审核人', field: 'auditor_name' },
  { title: '最近审核日期', field: 'audit_status_time' },
  { title: '审核状态', field: 'audit_status' },
  { title: '备注', field: 'remark' },
]

const emit = defineEmits(['refresh'])

const rejectModalRef = ref()
const handleReject = () => {
  remark.value = ''
  rejectModalRef.value.open()
}

const remark = ref('')
const handleRejectConfirm = (params) => {
  remark.value = params.remark
  handleAudit(false)
}

const handleAudit = (is_pass) => {
  auditLoading.value = true
  const method = data.value?.audit_status === 30 ? FinanceAudit : ManagerAudit
  const params = { audit_id: data.value?.id, is_pass, remark: remark.value }
  remark.value = ''
  method(params)
    .then(() => {
      message.success('操作成功')
      rejectModalRef.value.close()
      close()
      emit('refresh')
    })
    .finally(() => {
      auditLoading.value = false
    })
}

const rowStyle = ({ row }) => {
  if (!data.value?.id) return {}
  if ([2, 3].includes(row.data_status)) return { color: 'red' }
  if (row.data_status === 4) return { textDecoration: 'line-through' }

  return {}
}

const PurchaseRef = ref()
const purchaseConfirm = ref<any>(() => {})
const selectPurchaseUser = (row) => {
  console.log(row)
  PurchaseRef.value.open(row.buyer_list || [])
  purchaseConfirm.value = (arr) => {
    // row.buyer_name = value.label
    row.buyer_ids = arr.length ? arr.map((f) => +f.value) : [0]
    row.buyer_list = arr
    row.data_status = data.value!.supplier_id ? 3 : 1
    UpdateSupplierBuyer({
      audit_id: data.value!.id,
      supplier_company_info_list: data.value!.supplier_company_info_list,
    }).then(() => {
      message.success('更新成功')
    })
  }
}
defineExpose({ open })
</script>

<style lang="scss" scoped>
.title {
  @apply p-4 bg-#f5f7fe flex items-center text-sm font-bold my-12 rounded;
}

// :deep(.ant-tabs-content-holder) {
//   display: none;
// }

//
</style>
