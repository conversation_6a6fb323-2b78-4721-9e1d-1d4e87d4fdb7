<template>
  <a-drawer
    title="选择入库单"
    :width="width"
    :open="openFlag"
    :mask-closable="false"
    :destroyOnClose="true"
    :body-style="{ display: 'flex', padding: '16px', justifyContent: 'space-between' }"
    @close="handleClose"
  >
    <div class="h-full flex flex-col flex-1">
      <a-space class="mb-12">
        <template v-for="item in searchFormList" :key="item.name">
          <a-input v-if="item.type === 'input'" :placeholder="item.placeholder" v-model:value="queryParams[item.name]" :maxlength="200" allowClear />
          <div class="w-140" v-else-if="item.type === 'SelectSupplier'">
            <SelectSupplier
              v-model:value="queryParams[item.name]"
              v-model:label="queryParams[item.name + '_name']"
              :title="item.placeholder"
              :mode="'single'"
              :api="item.api"
              :apiParams="item.apiParams"
              :labelInValue="false"
            />
          </div>
        </template>
        <a-button type="primary" @click="tableRef.search()">查询</a-button>
      </a-space>
      <BaseTable ref="tableRef" v-bind="tableConfig" @loadData="setCheckedRows" />
    </div>
    <div
      class="flex flex-col ml-12"
      :style="{
        width: selectTableConfig.tableColumns.map((f) => f.resizeWidth || f.width || f.minWidth || 100).reduce((acc, cur) => (acc += cur), 0) + 'px',
      }"
      v-if="selectTableConfig.tableColumns?.length"
    >
      <div class="flex mb-12 h-30 items-center justify-between">
        <div class="text-14px text-#333">已选{{ selectRowsMapCount }}个单据</div>
        <a-button v-if="selectRowsMapCount" class="ml-8" type="primary" @click="handleClear">清空</a-button>
      </div>
      <BaseTable ref="selectTableRef" v-bind="selectTableConfig" />
    </div>
    <template #footer>
      <a-space>
        <a-button type="primary" @click="handleConfirm">确定</a-button>
        <a-button @click="handleClose">取消</a-button>
      </a-space>
    </template>
  </a-drawer>
</template>

<script lang="ts" setup>
import { GetSupplierLiquidateList } from '@/servers/SupplierLiquidate'
import { GetPageMRPSupplierCompanySelect, GetPageSuppliersSelect } from '@/servers/BusinessCommon'

// 包装供应商选择API，确保返回正确的数据格式
const getSuppliersSelect = async (params: any) => {
  const res = await GetPageSuppliersSelect(params)
  if (res.data?.list) {
    res.data.list = res.data.list.map((item: any) => ({
      label: item.value || item.supplier_name || item.name || item.label || '未知供应商',
      value: item.key || item.supplier_id || item.id || item.value || '',
    }))
  }
  return res
}

const emits = defineEmits(['confirm'])
const props = defineProps({
  syncConfirm: {
    type: Function,
    default: null,
  },
})

const processWidth = ref()
const width = computed(() => {
  return Math.min(processWidth.value, window.innerWidth - 180)
})
const openFlag = ref(false)

const searchFormList = ref<Record<string, any>[]>([])
const tableRef = ref()
const selectTableRef = ref()
const watcher = ref()

const baseParams = ref({})
const queryParams = ref<Record<string, any>>({})
const formFormat = (data) => {
  // 需要处理的字段
  const selectSupplierFields = ['supplier_id', 'company_supplier_id']
  const formFormatQueryParams = { ...queryParams.value, bill_payable_status: 1 }

  selectSupplierFields.forEach((field) => {
    if (formFormatQueryParams[field] && Array.isArray(formFormatQueryParams[field])) {
      // 只取 value
      formFormatQueryParams[field] = formFormatQueryParams[field].map((item) => {
        if (typeof item === 'string') {
          try {
            return JSON.parse(item).value
          } catch {
            return item
          }
        }
        // 如果是对象
        return item.value ?? item
      })
      // 如果是单选，直接取第一个
      if (formFormatQueryParams[field].length === 1) {
        formFormatQueryParams[field] = formFormatQueryParams[field][0]
      }
    }
  })
  return {
    ...baseParams.value,
    ...formFormatQueryParams,
    ...data,
  }
}

const formMap = {
  进出仓单号: { type: 'input', name: 'io_id' },
  供应商: {
    type: 'SelectSupplier',
    name: 'supplier_id',
    api: getSuppliersSelect,
  }, // 改为弹窗选择器
  供应商子公司: { type: 'SelectSupplier', name: 'company_supplier_id', api: GetPageMRPSupplierCompanySelect },
  采购单号: { type: 'input', name: 'purcharse_order_number' },
  商品编号: { type: 'input', name: 'ku_sku_id' },
}

const defaultColumns = ref([
  {
    key: 'index',
    name: '序号',
    width: 50,
    formatter: ({ rowIndex }) => {
      return 1 + rowIndex
    },
    align: 'center',
  },
  { key: 'io_warehouse_id', name: '进出仓单号', width: 100 },
  { key: 'order_type_str', name: '单据类型', width: 80 },
  { key: 'supplier_name', name: '供应商' },
  { key: 'company_supplier_name', name: '供应商子公司' },
  { key: 'reserve_io_warehouse_date', name: '进出仓日期', width: 150 },
  { key: 'purchase_order_number', name: '采购单号', width: 120 },
  { key: 'k3_sku_id', name: '商品编号' },
  { key: 'sku_name', name: '商品名称' },
  { key: 'category_str', name: '商品分类' },
  { key: 'purchase_inbound_quantity', name: '采购入库数量' },
  { key: 'inv_amount', name: '应付金额' },
])

const tableConfig = ref<Record<string, any>>({
  isCheckbox: true,
  isIndex: false,
  hideTop: true,
  autoSearch: false,
  tableColumns: [],
  formFormat,
  rowConfig: {},
  checkboxConfig: {},
  clearCheckboxFlag: false,
})

const selectTableConfig = ref<Record<string, any>>({
  hideTop: true,
  hidePagination: true,
  autoSearch: false,
  tableColumns: [],
  rowConfig: {},
})

const selectRowsMap = ref({})
const selectRowsMapCount = computed(() => Object.keys(selectRowsMap.value).length)
const setCheckedRows = () => {
  const $table = tableRef.value.tableRef
  const rows: any[] = []
  for (const item of $table.getData()) {
    if (selectRowsMap.value[item[tableKeyField.value]]) {
      rows.push(item)
    }
  }
  $table.setCheckboxRow(rows, true)
}

const tableKeyField = ref('sku_id')
const open = async ({ left, right, search, params, checkMethod, width, api, keyField, dataFormat, supplierInfo }: any, sourceRows = []) => {
  processWidth.value = width || 1050
  queryParams.value = {}
  selectRowsMap.value = {}
  baseParams.value = params || {}

  // 处理供应商信息回显
  if (supplierInfo) {
    if (supplierInfo.supplier_id) {
      queryParams.value.supplier_id = supplierInfo.supplier_id
    }
    if (supplierInfo.supplier_name) {
      // 保持原有的供应商名称回显逻辑，用于显示
      queryParams.value.supplier_name = supplierInfo.supplier_name
      // 同时设置供应商名称到对应的字段，用于 SelectSupplier 组件回显
      queryParams.value.supplier_id_name = supplierInfo.supplier_name
    }
    // 供应商子公司不回显，只回显主供应商
  }

  searchFormList.value = search.map((placeholder) => {
    const config = { ...formMap[placeholder], placeholder: formMap[placeholder]?.alias || placeholder }
    return config
  })
  if (dataFormat) {
    tableConfig.value.dataFormat = dataFormat
  }
  tableConfig.value.getList = api || GetSupplierLiquidateList
  tableRef.value && tableRef.value.clearCheckbox()
  tableKeyField.value = keyField || 'sku_id'
  tableConfig.value.rowConfig.keyField = tableKeyField.value
  selectTableConfig.value.rowConfig.keyField = tableKeyField.value
  tableConfig.value.tableColumns = left.map((key: string) => {
    const found = defaultColumns.value.find((f) => [f.name, f.key].includes(key))
    if (!found) throw new Error(`tableConfig columns ${key} not found`)
    return {
      ...found,
      name: found.name,
    }
  })
  selectTableConfig.value.tableColumns = (right || []).reduce((acc, key: string) => {
    const found = defaultColumns.value.find((f) => [f.name, f.key].includes(key))
    if (!found) throw new Error(`selectTableConfig columns ${key} not found`)
    return [
      ...acc,
      {
        ...found,
        name: found.name,
      },
    ]
  }, [])
  if (checkMethod) {
    tableConfig.value.checkboxConfig.checkMethod = checkMethod
  }
  openFlag.value = true
  selectRowsMap.value = sourceRows.reduce((acc, item: any) => {
    return { ...acc, [item[tableKeyField.value]]: item }
  }, {})

  // 立即更新右侧表格显示已选数据
  if (selectTableRef.value) {
    selectTableRef.value.tableRef?.reloadData(Object.values(selectRowsMap.value))
  }

  // console.log('selectRowsMap.value', selectRowsMap.value, sourceRows)
  await nextTick()

  // 确保右侧表格在组件渲染后显示数据
  if (selectTableRef.value && Object.keys(selectRowsMap.value).length > 0) {
    selectTableRef.value.tableRef?.reloadData(Object.values(selectRowsMap.value))
  }

  await tableRef.value?.search()
  setTimeout(() => {
    watcher.value = watch(
      () => tableRef.value.checkItemsArr,
      () => {
        const checkItems = tableRef.value.tableRef.getCheckboxRecords()
        const tableData = tableRef.value.tableData || []
        const keys = Object.keys(selectRowsMap.value)
        for (const key of keys) {
          const hasCurTable = tableData.find((f) => f[tableKeyField.value] == key)
          const hasCurSelect = checkItems.find((f) => f[tableKeyField.value] == key)

          if (hasCurTable) {
            if (!hasCurSelect) {
              delete selectRowsMap.value[key]
            }
          }
        }
        for (const item of checkItems) {
          selectRowsMap.value[item[tableKeyField.value]] = item
        }
        // 确保右侧表格数据更新
        if (selectTableRef.value) {
          selectTableRef.value.tableRef?.reloadData(Object.values(selectRowsMap.value))
        }
      },
      { immediate: true },
    )
  }, 50)
}

const handleClear = () => {
  selectRowsMap.value = {}
  tableRef.value?.clearCheckbox()
}

const handleClose = () => {
  watcher.value && watcher.value()
  openFlag.value = false
}

const handleConfirm = async () => {
  if (props.syncConfirm) {
    const flag = await props.syncConfirm(Object.values(selectRowsMap.value))
    if (flag) handleClose()
  } else {
    emits('confirm', Object.values(selectRowsMap.value))
    handleClose()
  }
}

defineExpose({
  open,
})
</script>

<style lang="scss" scoped>
//
</style>
