<template>
  <div class="flex flex-col h-full main">
    <Form ref="formRef" v-model:form="formArr" :page-type="PageTypeEnum.SupplierSubsidiariesStore" @search="search" @setting="tableRef?.showTableSetting()" />
    <BaseTable ref="tableRef" :page-type="PageTypeEnum.SupplierSubsidiariesStore" v-model:form="formArr" :get-list="QuerySupplierCompany" :isCheckbox="true">
      <template #left-btn>
        <a-button @click="changePurchase" v-if="btnPermission[41105]">批量更换采购员</a-button>
      </template>
      <template #right-btn>
        <a-button v-if="btnPermission[41103]" type="primary" @click="handleAdd">新增供应商</a-button>
      </template>

      <template #audit_status="{ row }">
        <a-tag :color="auditStatusColorMap[row.audit_status]">
          {{ auditStatusMap[row.audit_status] }}
        </a-tag>
      </template>
      <template #source_type="{ row }">
        {{ formatOptionLabel(row.source_type, sourceOption) }}
      </template>

      <template #fix_option="{ row, column }">
        <RightOperate :list="rightOperateList" :row="row" :column="column" />
      </template>
    </BaseTable>

    <Detail ref="detailRef" @refresh="search" />
    <Update ref="updateRef" @refresh="updateRefresh" />
    <a-modal v-model:open="relevanceVisible" title="选择关联价目表" width="450px" @ok="handleRelevanceOk" @cancel="handleRelevanceCancel">
      <div class="flex justify-center p-24">
        <a-select placeholder="请选择关联价目表" v-model:value="relevanceForm.purchase_price_list_id" class="w-200">
          <a-select-option v-for="item in purchasePriceOption" :key="item.key" :value="item.key">{{ item.value }}</a-select-option>
        </a-select>
      </div>
    </a-modal>
    <PriceDrawer ref="priceDrawerRef" @query="search" />
    <supplierTransferModal ref="supplierTransferModalRef" @submitPro="submitPro"></supplierTransferModal>
  </div>
</template>

<script lang="ts" setup>
import { message } from 'ant-design-vue'

import { Enum2Options, filterOption, formatOptionLabel } from '@/utils'
import { DetailTypeEnum } from '@/common/enum'
import { auditStatusColorMap, auditStatusMap } from '@/common/map'

import PriceDrawer from '@/views/pageComponents/purchaseManagement/purchaseprice/components/PriceDrawer.vue'

import supplierTransferModal from './components/supplierTransferModal.vue'

import Detail from '../components/SupplierDetail.vue'
import Update from '../components/SupplierUpdate.vue'

import { GetNotCorrelationPurchasePriceList, SupplierCompanyPriceCorrelation } from '@/servers/Purchaseprice'
import { IsCheckProcess, QuerySupplierCompany } from '@/servers/Supplier'
import { GetMRPSupplierGroupSelectByParent, GetPurchaseByDeptSelect, GetDeptSelect, GetSupplierCategorySelect } from '@/servers/BusinessCommon'

import { PageTypeEnum } from '@/enums/tableEnum'

const { btnPermission } = usePermission()
const supplierTransferModalRef = ref()
const sourceOption = Enum2Options(SupplierSourceTypeEnum)

const priceDrawerRef = ref()
const tableRef = ref()
const formRef = ref()
const search = () => tableRef.value.search()
// 关联价目表
const relevanceVisible = ref(false)
// 关联价目表单
const relevanceForm = ref({
  company_supplier_id: null,
  purchase_price_list_id: undefined,
})
// 未绑定价目表选项
const purchasePriceOption = ref<any[]>([])

// const clearForm = () => {
//   formArr.value.find((v) => v.key === 'buyer_id')!.selectArr = []
// }

const emit = defineEmits(['auditRefresh'])
const updateRefresh = () => {
  emit('auditRefresh')
}

const formArr = ref([
  { label: '供应商编码', value: '', type: 'inputNumber', key: 'supplier_id', precision: 0 },
  { label: '聚水潭编码', value: '', type: 'inputNumber', key: 'jst_supplier_id', precision: 0 },
  { label: '供应商名称', value: '', type: 'input', key: 'supplier_name' },
  {
    label: '供应商分组',
    value: [],
    type: 'cascader',
    key: 'supplier_group_id',
    selectArr: [],
    api: GetMRPSupplierGroupSelectByParent,
    formatter: (f) => {
      return {
        value: f.id,
        label: f.name,
        children: [{ label: '全部', value: f.id }, ...(f.options || [])],
      }
    },
  },
  {
    label: '结算方式',
    value: null,
    type: 'select',
    key: 'settlement_type',
    selectArr: Enum2Options(SettlementTypeEnum).filter((f) => f.value !== SettlementTypeEnum.线上付款),
  },
  {
    label: '供应商类型',
    value: null,
    type: 'select',
    key: 'supplier_type',
    selectArr: [
      { label: '线下供应商', value: 1 },
      { label: '1688线上供应商', value: 2 },
    ],
  },
  {
    label: '对接部门',
    value: null,
    type: 'select',
    key: 'dept_id',
    selectArr: [],
    showSearch: true,
    filterOption,
    onChange: (val) => getDeptMember(val),
  },
  { label: '对接采购员', value: null, type: 'select', key: 'buyer_id', selectArr: [], showSearch: true, filterOption },
  { label: '来源', value: null, type: 'select', key: 'source_type', selectArr: sourceOption, showSearch: true, filterOption },
  { label: '审核时间', value: null, type: 'range-picker', key: 'create_at', formKeys: ['start_time', 'end_time'], placeholder: ['审核开始时间', '审核结束时间'] },
  { label: '子公司编号', value: '', type: 'input', key: 'company_supplier_id' },
  {
    label: '供应商分类',
    value: [],
    type: 'select',
    key: 'supplier_categories',
    multiple: true,
    showSearch: true,
    filterOption,
    api: GetSupplierCategorySelect,
  },
])

useSearchForm(formArr)

onMounted(() => {
  getDeptMember()
  getSelectOptions()
})

const getDeptMember = async (dept_id = '') => {
  const res = await GetPurchaseByDeptSelect({ dept_id })
  formArr.value.find((v) => v.key === 'buyer_id')!.selectArr = [{ value: 0, label: '无' }, ...res.data]
}

const getSelectOptions = async () => {
  // const res1 = await GetMRPSupplierGroupSelectByParent({})
  const res2 = await GetDeptSelect({})

  formArr.value.forEach((item) => {
    if (item.key === 'dept_id') {
      item.selectArr = res2.data
    }
  })
}

const detailRef = ref()
const updateRef = ref()

const handleDetail = (row) => {
  detailRef.value.open(row.supplier_id)
}

const handleAdd = () => {
  updateRef.value.open()
}

const handleEdit = async (row) => {
  const isCheck = (await IsCheckProcess({ id: row.supplier_id })).data
  if (isCheck) return message.warning('变更审核中，请审核后再进行变更')
  updateRef.value.open(row.supplier_id, undefined, undefined, row.purchase_price_list_id)
}

// 价目表
const handlePriceList = async (row) => {
  if (!row.purchase_price_list_id) {
    relevanceForm.value = {
      company_supplier_id: null,
      purchase_price_list_id: undefined,
    }
    const res = await GetNotCorrelationPurchasePriceList()
    purchasePriceOption.value = res.data
    relevanceForm.value.company_supplier_id = row.company_supplier_id
    relevanceVisible.value = true
  } else {
    priceDrawerRef.value.open(DetailTypeEnum.VIEW, row.purchase_price_list_id, row.company_supplier_id)
  }
}

// 关联价目表确定
const handleRelevanceOk = () => {
  if (!relevanceForm.value.purchase_price_list_id) {
    message.warning('请选择关联价目表')
    return
  }
  SupplierCompanyPriceCorrelation(relevanceForm.value).then(() => {
    message.success('关联成功')
    handleRelevanceCancel()
    search()
  })
}

// 关联价目表取消
const handleRelevanceCancel = () => {
  relevanceVisible.value = false
  relevanceForm.value = {
    company_supplier_id: null,
    purchase_price_list_id: undefined,
  }
}
const submitPro = () => {
  tableRef.value.search()
}
const changePurchase = () => {
  if (tableRef.value?.checkItemsArr.length === 0) {
    message.warning('请先筛选或勾选需要更换的子公司')
    return
  }
  supplierTransferModalRef.value.open(true, tableRef.value.checkItemsArr)
}

const rightOperateList = ref([
  {
    label: '查看',
    show: 41101,
    onClick: ({ row }) => {
      handleDetail(row)
    },
  },
  {
    label: '提交变更申请',
    show: 41102,
    onClick: ({ row }) => {
      handleEdit(row)
    },
  },
  {
    label: '价目表',
    show: 41104,
    onClick: ({ row }) => {
      handlePriceList(row)
    },
  },
])
</script>

<style lang="scss" scoped>
//
</style>
