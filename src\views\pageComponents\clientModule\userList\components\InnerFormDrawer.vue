<template>
  <a-drawer
    v-model:open="formVisible"
    @afterOpenChange="formRef?.clearValidate()"
    width="40vw"
    :title="`${drawerStatus == 1 ? '新增' : '编辑'}内部用户`"
    placement="right"
    :maskClosable="false"
    :footer-style="{ textAlign: 'left' }"
  >
    <a-form ref="formRef" :model="formData">
      <a-form-item label="类型" name="type">
        <a-select id="type" :options="[{ label: '内部联系人', value: 2 }]" :value="2" class="w240" disabled />
      </a-form-item>
      <a-form-item label="账号" name="user_name" :rules="[{ required: true }, { max: 200, message: '输入内容不可超过200字符' }]">
        <a-input id="user_name" v-model:value="formData.user_name" :disabled="drawerStatus == 2" placeholder="请输入帐号" class="w240" />
      </a-form-item>
      <a-form-item v-if="drawerStatus == 1" label="密码" name="password" :rules="[{ required: true }, { max: 200, message: '输入内容不可超过200字符' }]">
        <a-input-password id="password" v-model:value="formData.password" placeholder="请输入密码" class="w240" />
      </a-form-item>
      <a-form-item
        v-if="drawerStatus == 1"
        label="确认密码"
        name="passwordStr"
        :rules="[{ required: true }, { max: 200, message: '输入内容不可超过200字符' }, { validator: checkPassRule, trigger: 'change', message: '两次输入的密码不一致' }]"
      >
        <a-input-password id="passwordStr" v-model:value="formData.passwordStr" placeholder="请输入确认密码" class="w240" />
      </a-form-item>
      <a-form-item label="用户名称" name="real_name" :rules="[{ required: true }, { max: 200, message: '输入内容不可超过200字符' }]">
        <a-input id="real_name" disabled v-model:value="formData.real_name" placeholder="请输入用户名称" class="w240" />
      </a-form-item>
      <a-form-item label="所属公司" name="sub_company_name" :rules="[{ required: true, message: '请选择所属公司' }]">
        <div style="display: flex; flex-wrap: nowrap; align-items: center">
          <a-select
            id="sub_company_name"
            disabled
            class="w350"
            @change="
              (val) => {
                formData.department_id = null
                getDepartmentTreeList(val)
              }
            "
            placeholder="选择公司"
            show-search
            :filter-option="filterOption"
            v-model:value="formData.sub_company_name"
            :options="customerOptions"
          ></a-select>
        </div>
      </a-form-item>
      <a-form-item label="所在部门" name="departmentname">
        <a-select :value="formData.departmentname" id="department_id" disabled class="w350" :options="[]" placeholder="无所在部门" />
      </a-form-item>
      <a-form-item label="岗位" name="job_title_name">
        <a-input id="job_title_name" disabled class="select w240" v-model:value="formData.job_title_name" placeholder="无岗位"></a-input>
      </a-form-item>
      <a-form-item
        label="电子邮箱"
        name="email"
        :rules="[
          { type: 'email', message: '请输入正确的电子邮箱' },
          { max: 200, message: '输入内容不可超过200字符' },
        ]"
      >
        <a-input id="jobtitlename" class="w240" v-model:value="formData.email" placeholder="请输入电子邮箱" />
      </a-form-item>
      <a-form-item label="角色" name="role_ids" :rules="[{ required: formData.status == 1, message: '请选择角色' }]">
        <!-- <a-select allow-clear v-model:value="formData.role_ids" class="w240" placeholder="选择角色" :maxTagCount="1" :options="rolesOptions"></a-select> -->
        <a-select
          id="role_ids"
          class="w240"
          v-if="rolesOptions.findIndex((e) => e.value == formData.role_ids) != -1"
          placeholder="请选择角色"
          allow-clear
          v-model:value="formData.role_ids"
          :options="rolesOptions"
        ></a-select>
        <a-select
          id="role_ids"
          class="w240"
          v-else
          placeholder="请选择角色"
          allow-clear
          @change="
            (val) => {
              formData.role_ids = val
            }
          "
          v-model:value="formData.role_names"
          :options="rolesOptions"
        ></a-select>
      </a-form-item>
      <a-form-item v-if="btnPermission?.isShowBtn21104" label="状态" name="status">
        <a-switch id="status" v-model:checked="[false, true][formData.status]" @click="startStopForm()">
          <template #checkedChildren>
            <span class="iconfont icon-zhengquede_correct"></span>
          </template>
          <template #unCheckedChildren>
            <span class="iconfont icon-guanbi_close"></span>
          </template>
        </a-switch>
      </a-form-item>
      <a-form-item v-if="drawerStatus == 2" label="来源">
        <a-select id="source_type" disabled :value="1" class="w240" :maxTagCount="1" :options="sourceOption"></a-select>
      </a-form-item>
    </a-form>
    <template #footer>
      <a-button id="userListsFormComfirm" style="margin-right: 10px" type="primary" @click="drawerStatus == 1 ? beforeIncrease() : beforeEdit()">确认</a-button>
      <a-button id="userListsFormCancel" @click="formVisible = false">取消</a-button>
    </template>
  </a-drawer>
  <!-- 确认弹窗 -->
  <a-modal :zIndex="10000" v-model:open="modalData.isShow" :title="modalData.title">
    <div class="modalContent">{{ modalData.content }}</div>
    <template #footer>
      <a-button v-if="modalData.isConfirmBtn" :danger="modalData.okType === 'danger'" type="primary" style="margin-right: 10px" @click="modalData.okFn">{{ modalData.confirmBtnText }}</a-button>
      <a-button v-if="modalData.isCancelBtn" @click="modalData.isShow = false">取消</a-button>
    </template>
  </a-modal>
</template>

<script lang="ts" setup>
import { message } from 'ant-design-vue'

import { sourceOption } from '@/common/options'
import { filterOption } from '@/utils/index'

import { GetRoleSelectOption } from '@/servers/Role'
import { GetDepartmentTreeList, Add, UpdateInner } from '@/servers/UserManager'

const emit = defineEmits(['query'])
const formVisible = ref(false)
const formData = ref<any>(null)
const drawerStatus = ref(1)
const customerOptions = ref<any[]>([])
const rolesOptions = ref<any[]>([])
const departmentOptions = ref<any[]>([])
const formRef = ref<FormInstance>()
const btnPermission = ref({
  isShowBtn21104: true,
})
const modalData = reactive({
  isShow: false,
  isConfirmBtn: true,
  isCancelBtn: true,
  confirmBtnText: '确定',
  okType: 'primary',
  title: '',
  content: '',
  okFn: () => {
    modalData.isShow = false
  },
})

const open = (status, row) => {
  // getCompanyList()
  getRoleSelectOption()
  formVisible.value = true
  drawerStatus.value = status
  if (status == 2) {
    row = JSON.parse(JSON.stringify(row))
    // 编辑
    row.role_ids = row.role_ids ? Number(row.role_ids) : null
    formData.value = row
    // GetUserInfo({ id })
    //   .then((res) => {
    //     getDepartmentTreeList(res.data.subcompanyid1)
    //     res.data.type = 2
    //     if (res.data.role_ids) {
    //       res.data.role_ids = Number(res.data.role_ids)
    //     }
    //     res.data.oldStatus = res.data.status
    //     res.data.source = res.data.source_format
    //     formData.value = res.data
    //   })
    //   .catch(() => {
    //   })
  } else {
    // 新增
    formData.value = {
      type: 2,
      user_name: null,
      password: null,
      passwordStr: null,
      real_name: null,
      subcompanyid1: null,
      role_ids: null,
      status: 1,
      source: 1,
      email: null,
      department_id: null,
      jobtitlename: null,
    }
  }
}
const startStopForm = () => {
  if (formData.value.status == 1) {
    formData.value.status = 0
  } else {
    formData.value.status = 1
  }
}
const checkPassRule = async (_rule, value) => {
  if (value !== formData.value.password) {
    return Promise.reject()
  }
  return Promise.resolve()
}
const beforeIncrease = async () => {
  try {
    await formRef.value?.validateFields()
    console.log(formData.value)
    Add(formData.value).then(() => {
      message.success('新增成功')
      formVisible.value = false
      emit('query')
    })
  } catch (errorInfo) {
    console.log('Failed:', errorInfo)
  }
}
const beforeEdit = async () => {
  try {
    await formRef.value?.validateFields()
    const fn = () => {
      const { id: user_id, real_name, customer_id, role_ids, status, department_id, job_name, email } = formData.value
      UpdateInner({ user_id, real_name, customer_id, role_ids, status, department_id, job_name, email }).then(() => {
        message.success('编辑成功')
        formVisible.value = false
        emit('query')
      })
    }
    if (formData.value.status == 0 && formData.value.oldStatus == 1) {
      modalData.isShow = true
      modalData.title = '停用用户'
      modalData.content = `停用后，该用户将无法访问系统的任何功能或数据。

确定要停用该用户的帐号吗？`
      modalData.confirmBtnText = '确定'
      modalData.isCancelBtn = true
      modalData.okType = 'danger'
      modalData.okFn = () => {
        modalData.isShow = false
        fn()
      }
    } else {
      fn()
    }
  } catch (errorInfo) {
    console.log('Failed:', errorInfo)
  }
}

const getRoleSelectOption = () => {
  rolesOptions.value = []
  GetRoleSelectOption({ scope: 1 }).then((res) => {
    res.data.forEach((e) => {
      e.label = e.role_name
      e.value = e.role_id
    })
    rolesOptions.value = res.data.filter((e) => e.status == 1)
  })
}
const getDepartmentTreeList = (val) => {
  departmentOptions.value = []
  GetDepartmentTreeList({ subcompanyid1: val }).then((res) => {
    departmentOptions.value = res.data
  })
}
// 暴露方法
defineExpose({
  open,
})
</script>

<style lang="scss" scoped>
::v-deep(.ant-form-item) {
  align-items: flex-start;

  .ant-form-item-label {
    display: flex;
    justify-content: flex-end;
    width: 110px;
    min-width: 110px;
    margin-right: 30px;

    label {
      &::after {
        display: none !important;
      }
    }
  }
}

.detailTitle {
  padding-left: 12px;
  margin-bottom: 20px;
  font-weight: bold;
  color: #000;
}

.w350 {
  width: 350px;
}

.w300 {
  width: 300px;
}

.w240 {
  width: 240px;
}

.w150 {
  width: 150px;
}

.w200 {
  width: 200px;
}

.description {
  padding-left: 20px;
  font-size: 12px;
  color: rgb(0 0 0 / 50%);
  white-space: nowrap;
}

.detailValueDescription {
  font-size: 12px;
  color: rgb(0 0 0 / 50%);
}

.detailBox {
  padding-top: 12px;

  .loadingIcon {
    font-size: 30px;
    color: #1890ff;
  }
}

.descriptionBtn {
  color: rgb(0 0 0 / 70%);
  text-decoration: underline;
  cursor: pointer;

  &:hover {
    color: #1890ff;
  }
}

.modalContent {
  font-size: 14px;
  word-break: break-word;
  white-space: pre-wrap;
}
</style>
