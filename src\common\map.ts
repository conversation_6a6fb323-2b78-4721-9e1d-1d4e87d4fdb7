export const settlementTypeMap = {
  1: '月结',
  2: '半月结',
  3: '周结',
  4: '预付',
  5: '线上付款',
}

export const carriageModeMap = {
  1: '我方承运',
  2: '他方承运',
}

export const purchaseNatureMap = {
  1: '普通采购',
  2: '委外加工',
}

export const supplierTypeMap = {
  1: '线下供应商',
  2: '1688线上供应商',
}

export const invoiceTypeMap = {
  1: '增值税专用发票',
  2: '普通发票',
}

export const accountTypeMap = {
  1: '公户',
  2: '私户',
}

export const paymentMethodMap = {
  1: '银行',
  2: '支付宝',
  3: '微信',
  4: '现金',
}

export const statusMap = {
  0: '禁用',
  1: '启用',
  2: '变更待审核',
  3: '待入驻',
}

export const auditStatusMap = {
  10: '待提审',
  20: '待审核',
  21: '待变更审核',
  30: '待财务审核',
  90: '已通过',
  95: '已拒绝',
}

export const auditStatusColorMap = {
  20: 'warning',
  21: 'warning',
  30: 'warning',
  90: 'success',
  95: 'error',
}

export const kingdeeStatusMap = {
  10: '同步中',
  90: '同步成功',
  95: '同步失败',
}

export const ApplyPeoplestatusMap = {
  10: '待确认',
  20: '已确认',
}
export const OrederStatusMap = {
  1: '进行中',
  2: '已关闭',
}
export const PurchasePriceTypeMap = {
  1: '采购',
  2: '加工',
}

export const PurchaseAdjustStatusMap = {
  1: '新增',
  2: '调价新增',
  3: '变更',
  4: '删除',
}

export const currencyMap = {
  1: '人民币',
}

export const adjustPriceReasonMap = {
  1: '定期询价',
  2: '新品询价',
  3: '临时询价',
}

export const productTypeMap = {
  1: '成品',
  2: '半成品',
  3: '原材料',
  4: '包材',
}
export const supplierTypeMap2 = {
  1: '待确认',
  2: '已确认',
}

export const accountStatusMap = {
  1: '启用',
  2: '停用',
}

export const authorizationStatusMap = {
  1: '已授权',
  2: '未授权',
}
export const adressStatusMap = {
  1: '已启用',
  2: '未启用',
}

export const AliOrderStatusMap = {
  waitbuyerpay: '等待买家付款',
  waitsellersend: '等待卖家发货',
  waitbuyerreceive: '等待买家收货',
  confirm_goods: '已收货',
  success: '交易成功',
  cancel: '交易取消',
  terminated: '父易终止',
  未枚举: '其他状态',
}
