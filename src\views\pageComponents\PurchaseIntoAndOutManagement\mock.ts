// Mock data generation for the table
export const mockData1 = [
  {
    inbound_order_number: 'INB20230901001',
    reservation_order_number: 'RES20230901001',
    srm_reservation_order_number: 'SRM20230901001',
    purchase_order_number: 'PUR20230901001',
    supplier_subsidiary: 'Subsidiary A',
    purchase_date: '2023-09-01',
    inbound_date: '2023-09-02',
    interval_time: '1天',
    modify_time: '2023-09-02 10:00:00',
    warehouse_name: 'Warehouse A',
    sub_warehouse_code: 'SWC001',
    warehouse_code: 'WC001',
    creator: 'User A',
    logistics_order_number: 'LOG20230901001',
    remark: 'Initial inbound',
  },
  {
    inbound_order_number: 'INB20230901002',
    reservation_order_number: 'RES20230901002',
    srm_reservation_order_number: 'SRM20230901002',
    purchase_order_number: 'PUR20230901002',
    supplier_subsidiary: 'Subsidiary B',
    purchase_date: '2023-09-01',
    inbound_date: '2023-09-03',
    interval_time: '2天',
    modify_time: '2023-09-03 10:00:00',
    warehouse_name: 'Warehouse B',
    sub_warehouse_code: 'SWC002',
    warehouse_code: 'WC002',
    creator: 'User B',
    logistics_order_number: 'LOG20230901002',
    remark: 'Second inbound',
  },
  // Add more entries as needed
]
