<template>
  <a-drawer :maskClosable="false" v-model:open="visible" :title="form?.supplier_id ? '供应商编辑' : '供应商准入'" width="80%" @close="close" :body-style="{ padding: 0 }">
    <div class="flex h-full update-drawer" v-if="visible">
      <a-tabs tab-position="left" size="small" :active-key="activeKey" @change="tabChange">
        <a-tab-pane v-for="item in tabList" :key="item.id" :tab="item.name"></a-tab-pane>
      </a-tabs>
      <div class="flex-1 overflow-auto h-full py-4 box-border pr-25" ref="contentRef">
        <a-form v-if="!loading">
          <div class="flex items-center">
            <div class="mr-5">
              供应商类型
              <span class="text-red">*</span>
            </div>
            <a-radio-group v-model:value="form.supplier_type">
              <a-radio :value="1">线下供应商</a-radio>
              <a-radio :value="2">1688线上供应商</a-radio>
            </a-radio-group>
          </div>
          <div class="title" id="baseInfo">基本信息</div>
          <a-descriptions :column="3" bordered size="small" :labelStyle="{ width: px2(220) + 'px' }">
            <a-descriptions-item>
              <template #label>
                供应商名称
                <span class="text-red">*</span>
              </template>
              <a-input
                v-model:value="form.supplier_name"
                class="w-180"
                placeholder="请输入供应商名称"
                :status="checkStatus('supplier_name')"
                :maxlength="200"
                @change="supplierNameChange('supplier_name')"
              />
            </a-descriptions-item>
            <a-descriptions-item label="供应商编码">{{ form.supplier_id }}</a-descriptions-item>
            <a-descriptions-item label="供应商编码(金蝶)">{{ form.k3_supplier_number }}</a-descriptions-item>
            <a-descriptions-item>
              <template #label>
                采购性质
                <span class="text-red">*</span>
              </template>
              <a-select
                :dropdownMatchSelectWidth="false"
                class="w-180"
                v-model:value="form.purchase_nature"
                placeholder="请选择采购性质"
                :status="checkStatus('purchase_nature')"
                @change="filterErrorKey('purchase_nature')"
              >
                <a-select-option :value="1">普通采购</a-select-option>
                <a-select-option :value="2">委外加工</a-select-option>
              </a-select>
            </a-descriptions-item>
            <a-descriptions-item>
              <template #label>
                供应商分组
                <span class="text-red">*</span>
              </template>
              <!-- <a-select
                show-search
                class="w-180"
                v-model:value="form.supplier_group_id"
                placeholder="请选择供应商分组"
                :options="supplierGroupOptions"
                :filter-option="supplierGroupFilter"
                :status="checkStatus('supplier_group_id')"
                @change="filterErrorKey('supplier_group_id')"
              ></a-select> -->
              <a-cascader
                class="w-180"
                v-model:value="form.supplier_group_id"
                :options="supplierGroupOptions"
                :status="checkStatus('supplier_group_id')"
                :show-search="{ filter: supplierGroupFilter }"
                placeholder="请选择供应商分组"
                @change="filterErrorKey('supplier_group_id')"
              />
            </a-descriptions-item>
            <a-descriptions-item label="供应商状态">
              {{ statusMap[form.status] }}
            </a-descriptions-item>
            <a-descriptions-item label="承运方式">
              <a-select
                class="w-180"
                :dropdownMatchSelectWidth="false"
                v-model:value="form.carriage_mode"
                placeholder="请选择承运方式"
                :status="checkStatus('carriage_mode')"
                @change="filterErrorKey('carriage_mode')"
              >
                <a-select-option :value="1">我方承担</a-select-option>
                <a-select-option :value="2">他方承担</a-select-option>
              </a-select>
            </a-descriptions-item>
            <a-descriptions-item>
              <!-- <template #label>
                公司网址/1688店铺网址
                <span class="text-red" v-if="form.supplier_type === 2">*</span>
              </template>
              <a-input class="w-180" v-model:value="form.website" placeholder="请输入链接" :maxlength="200" :status="checkStatus('website')" @change="filterErrorKey('website')" /> -->
            </a-descriptions-item>
            <a-descriptions-item>
              <!-- <template #label>
                1688店铺名字
                <span class="text-red" v-if="form.supplier_type === 2">*</span>
              </template>
              <a-input class="w-180" v-model:value="form.shop_name" placeholder="请输入店铺名字" :maxlength="200" :status="checkStatus('shop_name')" @change="filterErrorKey('shop_name')" /> -->
            </a-descriptions-item>

            <a-descriptions-item label="描述说明" :span="3">
              <a-textarea v-model:value="form.describes" placeholder="请输入描述说明" />
            </a-descriptions-item>
            <a-descriptions-item label="营业执照/资质证书等" :span="3">
              <div class="flex items-center">
                <Upload v-model:value="form.business_license_files" :module="UploadFileModuleEnum.Supplier" />
              </div>
            </a-descriptions-item>
          </a-descriptions>

          <div class="title" id="subsidiariesInfo">子公司信息</div>
          <a-button class="mb-8" type="primary" size="small" @click="addCompany">添加子公司</a-button>
          <SupplierTable :tableKey="companyTableKeys" :data="form.supplier_company_info_list">
            <template #company_supplier_name="{ row, rowIndex }">
              <a-input
                :disabled="row.is_default"
                v-model:value="row.company_supplier_name"
                placeholder="请输入"
                :status="checkStatus('company_supplier_name' + rowIndex)"
                @change="((row.data_status = form.supplier_id ? (row.id ? 3 : 2) : 1), filterErrorKey('company_supplier_name' + rowIndex))"
                :maxlength="200"
              />
            </template>

            <template #website="{ row, rowIndex }">
              <a-input
                v-model:value="row.website"
                placeholder="请输入"
                :status="checkStatus('website' + rowIndex)"
                @change="((row.data_status = form.supplier_id ? (row.id ? 3 : 2) : 1), filterErrorKey('website' + rowIndex))"
                :maxlength="200"
              />
            </template>

            <template #shop_name="{ row, rowIndex }">
              <a-input
                v-model:value="row.shop_name"
                placeholder="请输入"
                :status="checkStatus('shop_name' + rowIndex)"
                @change="((row.data_status = form.supplier_id ? (row.id ? 3 : 2) : 1), filterErrorKey('shop_name' + rowIndex))"
                :maxlength="200"
              />
            </template>

            <template #buyer_names="{ row }">
              <!-- :disabled="!row.is_view" -->
              <a-button type="link" size="small" v-if="!row.buyer_list?.length" @click="selectPurchaseUser(row)">选择对接采购员</a-button>
              <div class="w-full flex justify-between items-center" v-else>
                <div>
                  <div v-for="people in row.buyer_list" :key="people.value" class="my-4px lh-16px">{{ people.label }}</div>
                </div>
                <div class="flex flex-col ml-3px">
                  <a-button class="flex-shrink-0 mb-2px" type="link" size="small" @click="selectPurchaseUser(row)">修改</a-button>
                  <a-button class="flex-shrink-0" type="link" danger size="small" @click="deletePurchaseUser(row)">删除</a-button>
                </div>
              </div>
            </template>

            <template #supplier_category="{ row, rowIndex }">
              <a-select
                class="w-full"
                v-model:value="row.supplier_category"
                placeholder="请选择供应商分类"
                :options="supplierCategoryList"
                :status="checkStatus('supplier_category' + rowIndex)"
                @change="((row.data_status = form.supplier_id ? (row.id ? 3 : 2) : 1), filterErrorKey('supplier_category' + rowIndex))"
              ></a-select>
            </template>
            <template #operate="{ row }">
              <a-button v-if="!row.id" :disabled="row.is_default || !row.is_view" type="link" size="small" danger @click="deleteCompany(row)">删除</a-button>
            </template>
          </SupplierTable>

          <div class="title" id="contactInfo">联系信息</div>
          <a-button class="mb-8" type="primary" size="small" @click="addContact">添加联系人</a-button>
          <SupplierTable :tableKey="contactTableKeys" :data="form.supplier_contact_info_list">
            <template #supplier_name="{ row, rowIndex }">
              <a-select
                :dropdownMatchSelectWidth="false"
                class="w-full"
                v-model:value="row.supplier_name"
                :status="checkStatus('connection_supplier_name' + rowIndex)"
                placeholder="请选择"
                :options="companyList"
                allowClear
                @change="((row.data_status = row.id ? 3 : 2), filterErrorKey('connection_supplier_name' + rowIndex))"
              ></a-select>
            </template>
            <template #name="{ row, rowIndex }">
              <a-input
                v-model:value="row.name"
                placeholder="请输入"
                :status="checkStatus('connection_name' + rowIndex)"
                :maxlength="200"
                @change="((row.data_status = form.supplier_id ? (row.id ? 3 : 2) : 1), filterErrorKey('connection_name' + rowIndex))"
              />
            </template>
            <template #mobile_phone_number="{ row, rowIndex }">
              <a-input
                v-model:value="row.mobile_phone_number"
                placeholder="请输入"
                :status="checkStatus('connection_mobile_phone_number' + rowIndex)"
                :maxlength="200"
                @change="((row.data_status = form.supplier_id ? (row.id ? 3 : 2) : 1), filterErrorKey('connection_mobile_phone_number' + rowIndex))"
              />
            </template>

            <template #address="{ row }">
              <div class="flex items-center w-full">
                <div v-if="row.province || row.city || row.area || row.address" class="flex-1 w-0 overflow-hidden text-ellipsis whitespace-nowrap">
                  {{ row.province }} {{ row.city }} {{ row.area }} {{ row.address }}
                </div>
                <a-button type="link" size="small" @click="selectDeliveryAddress(row, '联系地址')">{{ row.address ? '修改' : '选择' }}</a-button>
              </div>
            </template>
            <template #is_default="{ row, rowIndex }">
              <a-checkbox v-model:checked="row.is_default" @change="defaultChange(form.supplier_contact_info_list, rowIndex)" />
            </template>
            <template #operate="{ row }">
              <a-button :disabled="!row.is_view" type="link" size="small" danger @click="deleteContact(row)">删除</a-button>
            </template>
          </SupplierTable>
          <div class="title" id="financialInfo">财务信息</div>
          <a-space :size="12">
            <div>
              <span class="mr-6">结算方式</span>
              <a-select
                class="w-130"
                :dropdownMatchSelectWidth="false"
                v-model:value="form.settlement_type"
                placeholder="请选择结算方式"
                :allowClear="true"
                @change="form.is_update_finance_info = true"
              >
                <a-select-option v-for="i in settleAccountOption" :key="i.value" :value="i.value">{{ i.label }}</a-select-option>
              </a-select>
            </div>

            <div>
              <span class="mr-6">发票类型</span>
              <a-select :dropdownMatchSelectWidth="false" v-model:value="form.invoice_type" placeholder="请选择发票类型" :allowClear="true" @change="form.is_update_finance_info = true">
                <a-select-option :value="1">增值税专用发票</a-select-option>
                <a-select-option :value="2">普通发票</a-select-option>
              </a-select>
            </div>

            <a-flex align="center">
              <span class="mr-6">默认税率</span>
              <a-select
                class="w-130"
                :dropdownMatchSelectWidth="false"
                v-model:value="form.default_tax_rate_id"
                @change="form.is_update_finance_info = true"
                placeholder="请选择"
                allow-clear
                :options="taxRateList"
              ></a-select>
            </a-flex>
          </a-space>
          <div class="mt-8">
            <a-button class="mb-8" type="primary" size="small" @click="addAccount">添加账户</a-button>
          </div>
          <SupplierTable :tableKey="financialTableKeys" :data="form.supplier_finance_info_list" @change="form.is_update_finance_info = true">
            <template #supplier_name="{ row }">
              <a-select
                :dropdownMatchSelectWidth="false"
                class="w-full"
                v-model:value="row.supplier_name"
                placeholder="请选择"
                :options="companyList"
                allowClear
                @change="
                  () => {
                    row.data_status = row.id ? 3 : 2
                    form.is_update_finance_info = true
                  }
                "
                :disabled="!row.is_view"
              ></a-select>
            </template>
            <template #account_type="{ row }">
              <a-select
                :dropdownMatchSelectWidth="false"
                class="w-full"
                v-model:value="row.account_type"
                placeholder="请选择"
                :options="[
                  { label: '对公', value: 1 },
                  { label: '对私', value: 2 },
                ]"
                @change="
                  () => {
                    row.data_status = row.id ? 3 : 2
                    form.is_update_finance_info = true
                  }
                "
                :disabled="!row.is_view"
              />
            </template>
            <template #payment_method="{ row }">
              <a-select
                :dropdownMatchSelectWidth="false"
                class="w-full"
                v-model:value="row.payment_method"
                placeholder="请选择"
                :options="[
                  { label: '银行', value: 1 },
                  { label: '支付宝', value: 2 },
                  { label: '微信', value: 3 },
                  { label: '现金', value: 4 },
                ]"
                @change="
                  () => {
                    row.data_status = row.id ? 3 : 2
                    form.is_update_finance_info = true
                  }
                "
                :disabled="!row.is_view"
              />
            </template>
            <template #collection_account_certificate_file_ids="{ row }">
              <Upload
                v-model:value="row.collection_account_certificate_files"
                :module="UploadFileModuleEnum.Supplier"
                @change="
                  () => {
                    row.data_status = row.id ? 3 : 2
                    form.is_update_finance_info = true
                  }
                "
              />
            </template>
            <template #is_default="{ row, rowIndex }">
              <a-checkbox
                :disabled="!row.is_view"
                v-model:checked="row.is_default"
                @change="
                  () => {
                    defaultChange(form.supplier_finance_info_list, rowIndex)
                    form.is_update_finance_info = true
                  }
                "
              />
            </template>
            <template #operate="{ row }">
              <a-button :disabled="!row.is_view" type="link" size="small" danger @click="deleteAccount(row)">删除</a-button>
            </template>
          </SupplierTable>

          <div class="title" id="deliveryInfo">发货信息</div>
          <a-button class="mb-8" type="primary" size="small" @click="addDelivery">添加发货地址</a-button>
          <SupplierTable :tableKey="deliveryTableKeys" :data="form.supplier_shipments_info_list">
            <template #supplier_name="{ row, rowIndex }">
              <a-select
                :dropdownMatchSelectWidth="false"
                class="w-full"
                v-model:value="row.supplier_name"
                placeholder="请选择"
                :options="companyList"
                allowClear
                :status="checkStatus('shipments_supplier_name' + rowIndex)"
                @change="((row.data_status = row.id ? 3 : 2), filterErrorKey('shipments_supplier_name' + rowIndex))"
                :disabled="!row.is_view"
              ></a-select>
            </template>
            <template #shipments_address="{ row }">
              <div class="flex items-center w-full">
                <div v-if="row.province || row.city || row.area || row.shipments_address" class="flex-1 w-0 overflow-hidden text-ellipsis whitespace-nowrap">
                  {{ row.province }} {{ row.city }} {{ row.area }} {{ row.shipments_address }}
                </div>
                <a-button :disabled="!row.is_view" type="link" size="small" @click="selectDeliveryAddress(row, '发货地址')">{{ row.shipments_address ? '修改' : '选择' }}</a-button>
              </div>
            </template>
            <template #is_default="{ row, rowIndex }">
              <a-checkbox :disabled="!row.is_view" v-model:checked="row.is_default" @change="defaultChange(form.supplier_shipments_info_list, rowIndex)" />
            </template>
            <template #operate="{ row }">
              <a-button type="link" size="small" danger @click="deleteDelivery(row)">删除</a-button>
            </template>
          </SupplierTable>
          <div class="title" id="userInfo">用户信息</div>
          <a-button class="mb-8" type="primary" size="small" @click="addUser">添加用户</a-button>
          <SupplierTable :tableKey="userTableKeys" :data="form.supplier_user_info_list" :isView="true" :errorKeyList="errorKeyList">
            <template #user_name="{ row, rowIndex }">
              <a-input
                v-model:value="row.user_name"
                placeholder="请输入用户名"
                :disabled="!!row.id"
                :maxlength="200"
                :status="checkStatus('user_name' + rowIndex)"
                @change="filterErrorKey('user_name' + rowIndex)"
              />
            </template>
            <template #status="{ row }">
              {{ statusMap[row.status] }}
            </template>
            <template #operate="{ row }">
              <a-button type="link" size="small" @click="copyUser(row)">复制</a-button>
              <a-button type="link" size="small" danger @click="deleteUser(row)">删除</a-button>
            </template>
          </SupplierTable>

          <div class="title" id="otherInfo">其他信息</div>
          <SimpleTable :tableKey="otherTableKeys" :data="[form]">
            <template #audit_status="{ row }">
              {{ auditStatusMap[row.audit_status] }}
            </template>
          </SimpleTable>
        </a-form>
        <div v-else class="h-40"></div>
      </div>
    </div>

    <template #footer>
      <a-space :size="12">
        <a-button type="primary" @click="submit(true)" :loading="submitLoading">提交审核</a-button>
        <a-button v-if="!closeTemporary" type="primary" @click="submit(false)" :loading="submitLoading">保存暂不提交</a-button>
        <a-button @click="close">取消</a-button>
      </a-space>
    </template>
  </a-drawer>

  <Purchase ref="PurchaseRef" @confirm="purchaseConfirm"></Purchase>
  <Delivery ref="DeliveryRef" @confirm="deliveryCb"></Delivery>
</template>

<script lang="ts" setup>
import { message, Modal } from 'ant-design-vue'

import { px2, throttle, debounce } from '@/utils'
import { settleAccountOption } from '@/common/options'
import { auditStatusMap, statusMap } from '@/common/map'

import { ContactMessage, DeliveryMessage, FinancialMessage, SubCompanyMessage, SupplierInfo, UserMessage } from './Supplier.d'
import Delivery from './DeliveryModal.vue'
import Purchase from './PurchaseModal.vue'
import SupplierTable from './SupplierTable.vue'

import { GetMRPSupplierGroupSelectByParent, GetTaxRateSelect, GetSupplierCategorySelect } from '@/servers/BusinessCommon'
import { AddSupplierAuditProcess, GetSupplierAuditProcess, GetSupplierMessage, UpdateSupplierAuditProcess, GetSupplierAuditList } from '@/servers/Supplier'

const visible = ref(false)
const saveSupplier = ref('')
const activeKey = ref('baseInfo')
const scrollFlag = ref(false)
const tabList = ref([
  { id: 'baseInfo', name: '基本信息' },
  { id: 'subsidiariesInfo', name: '子公司信息' },
  { id: 'contactInfo', name: '联系信息' },
  { id: 'financialInfo', name: '财务信息' },
  { id: 'deliveryInfo', name: '发货信息' },
  { id: 'userInfo', name: '用户信息' },
  { id: 'otherInfo', name: '其他信息' },
])

const isRequired = computed(() => form.value.supplier_type === 1)
// 滚动到对应id
const tabChange = (key) => {
  activeKey.value = key
  const element = document.querySelector(`.update-drawer #${key}`)
  if (element) {
    element.scrollIntoView({ behavior: 'smooth' })
    scrollFlag.value = true
    setTimeout(() => {
      scrollFlag.value = false
    }, 1000)
  }
}

const errorKeyList = ref<string[]>([])

const scroll = throttle(() => {
  if (scrollFlag.value) return
  let current = null as any
  tabList.value.forEach((item) => {
    const element = document.querySelector(`.update-drawer #${item.id}`) as HTMLElement
    if (contentRef.value && contentRef.value!.scrollTop + px2(140) >= element!.offsetTop) {
      current = item
    }
  })
  if (current) activeKey.value = current.id
}, 300)

const initForm = () => ({
  supplier_name: '',
  supplier_type: 1,
  k3_supplier_id: null,
  k3_supplier_number: null,
  purchase_nature: undefined,
  supplier_group_id: [],
  carriage_mode: undefined,
  describes: '',
  business_license_file_ids: [],
  status: 0,
  remark: null,
  settlement_type: undefined,
  invoice_type: undefined,
  default_tax_rate_id: undefined,
  is_update_finance_info: false,
  supplier_company_info_list: [
    {
      company_supplier_id: null,
      company_supplier_name: null,
      supplier_id: null,
      k3_supplier_id: null,
      jst_supplier_id: null,
      buyer_oa_id: null,
      buyer_ids: [],
      data_status: 1,
      is_default: true,
      is_view: true,
      website: null,
      shop_name: null,
      supplier_category: null,
    },
  ],
  supplier_contact_info_list: [
    {
      id: 0,
      type: 0,
      supplier_name: null,
      supplier_id: null,
      name: '',
      job: '',
      phone_number: '',
      mobile_phone_number: '',
      fax_no: '',
      email: '',
      province: null,
      city: null,
      area: null,
      address: '',
      is_default: true,
      data_status: 1,
      is_view: true,
    },
  ],
  supplier_finance_info_list: [],
  supplier_shipments_info_list: [
    {
      id: null,
      type: 0,
      supplier_name: null,
      supplier_id: 0,
      province: '',
      city: '',
      area: '',
      shipments_address: '',
      is_default: true,
      data_status: 1,
      is_view: true,
    },
  ],
  supplier_user_info_list: [],
  is_review: false,
})

type OmitSupplierProperty = 'supplier_group_id' | 'purchase_nature' | 'carriage_mode' | 'settlement_type' | 'invoice_type' | 'default_tax_rate_id'
const form = ref<
  Omit<SupplierInfo, OmitSupplierProperty> & {
    supplier_group_id: string[]
    purchase_nature: number | undefined
    carriage_mode: number | undefined
    settlement_type: number | undefined
    invoice_type: number | undefined
    default_tax_rate_id: number | undefined
  }
>(initForm())
provide('form', form)
const contentRef = ref()
const auditId = ref(null)
const loading = ref(false)
const submitLoading = ref(false)
const closeTemporary = ref(false)
const listId = ref(null) // 价目表id
const open = async (id, is_audit, newCloseTemporary, purchase_price_list_id) => {
  listId.value = purchase_price_list_id
  saveSupplier.value = ''
  errorKeyList.value = []
  haveSupplierName.value = false
  closeTemporary.value = newCloseTemporary || false
  visible.value = true
  deleteData.value = {
    supplier_company_info_list: [],
    supplier_contact_info_list: [],
    supplier_finance_info_list: [],
    supplier_shipments_info_list: [],
    supplier_user_info_list: [],
  }
  if (id) {
    loading.value = true
    const method = is_audit ? GetSupplierAuditProcess : GetSupplierMessage
    if (is_audit) auditId.value = id
    console.log(auditId.value, 'auditId')

    const res = await method({ id })
    form.value = {
      ...res.data,
      default_tax_rate_id: res.data.default_tax_rate_id ? `${res.data.default_tax_rate_id}` : null,
      // is_update_finance_info: false,
      supplier_company_info_list:
        res.data.supplier_company_info_list?.map((v) => {
          const buyerNames = (v.buyer_names || '').split('、')
          return {
            ...v,
            id: v.company_supplier_id || null,
            buyer_list: (v.buyer_ids || []).reduce((acc, value, index) => {
              if (value === 0) return acc
              return [...acc, { value, label: buyerNames[index] }]
            }, []),
            data_status: res.data.id ? v.data_status : 1,
          }
        }) || [],
      supplier_contact_info_list:
        res.data.supplier_contact_info_list?.map((v) => ({
          ...v,
          supplier_name: v.type === 1 ? '当前供应商所有子公司可见' : v.company_supplier_name,
          data_status: res.data.id ? (v.data_status === 4 ? 1 : v.data_status) : 1,
        })) || [],
      supplier_finance_info_list:
        res.data.supplier_finance_info_list?.map((v) => ({
          ...v,
          supplier_name: v.type === 1 ? '当前供应商所有子公司可见' : v.company_supplier_name,
          data_status: res.data.id ? v.data_status : 1,
        })) || [],
      supplier_shipments_info_list:
        res.data.supplier_shipments_info_list?.map((v) => ({
          ...v,
          supplier_name: v.type === 1 ? '当前供应商所有子公司可见' : v.company_supplier_name,
          data_status: res.data.id ? v.data_status : 1,
        })) || [],
      supplier_user_info_list:
        res.data.supplier_user_info_list?.map((v) => ({
          ...v,
          data_status: res.data.id ? v.data_status : 1,
        })) || [],
    }
    console.log('res', res.data)
    saveSupplier.value = form.value.supplier_name || ''
    loading.value = false
  } else {
    auditId.value = null
    form.value = initForm()
  }

  nextTick(() => {
    tabChange(tabList.value[0].id)
    contentRef.value?.scrollTo({ top: 0, behavior: 'smooth' })
    contentRef.value?.addEventListener('scroll', scroll)
  })

  form.value.supplier_group_id = await getMRPSupplierGroupSelectByParent(`${form.value.supplier_group_id || ''}`)
  getTaxRateSelect()
  getSupplierCategorySelect()
}

const supplierGroupOptions = ref([])
// const supplierGroupFilter = (input, option) => {
//   return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
// }

const supplierGroupFilter = (inputValue, path) => {
  return path.some((option) => option.label.toLowerCase().indexOf(inputValue.toLowerCase()) > -1)
}
const getMRPSupplierGroupSelectByParent = async (id) => {
  let result: string[] = []
  const { data } = await GetMRPSupplierGroupSelectByParent({})
  supplierGroupOptions.value = data.map((v) => {
    const found = v.options.find((f) => f.value === id) || id === v.id
    if (found) {
      result = [v.id, found.value || v.id]
    }
    return {
      label: v.name,
      value: v.id,
      children: [{ value: v.id, label: '全部' }, ...(v.options || [])],
    }
  })
  return Promise.resolve(result)
}

const taxRateList = ref([])
const getTaxRateSelect = () => {
  GetTaxRateSelect({}).then((res) => {
    taxRateList.value = res.data
  })
}

const supplierCategoryList = ref([])
const getSupplierCategorySelect = () => {
  GetSupplierCategorySelect({}).then((res) => {
    supplierCategoryList.value = res.data
  })
}

const companyList = computed(() => [
  { label: '当前供应商所有子公司可见', value: '当前供应商所有子公司可见' },
  ...form.value.supplier_company_info_list.map((item) => ({ label: item.company_supplier_name, value: item.company_supplier_name })),
])

const close = () => {
  visible.value = false
  closeTemporary.value = false

  nextTick(() => {
    contentRef.value?.removeEventListener('scroll', scroll)
  })
}

const defaultChange = (list, rowIndex) => {
  list.forEach((item, index) => {
    if (index !== rowIndex) item.is_default = false
  })
}

const haveSupplierName = ref(false)
const supplierNameChange = (key) => {
  haveSupplierName.value = false
  filterErrorKey(key)
  const company = form.value.supplier_company_info_list.find((v) => v.is_default)
  if (company) company.company_supplier_name = form.value.supplier_name || ''
  if (form.value.supplier_contact_info_list.length > 0) {
    const item = form.value.supplier_contact_info_list[0]
    item.supplier_name = form.value.supplier_name || ''
  }
  if (form.value.supplier_shipments_info_list.length > 0) {
    const item = form.value.supplier_shipments_info_list[0]
    item.supplier_name = form.value.supplier_name || ''
  }
  checkHaveSupplierName()
}

const checkHaveSupplierName = debounce(() => {
  GetSupplierAuditList({ supplier_name: form.value.supplier_name }).then((res) => {
    const audit = res.data.list.find((i) => i.supplier_name === form.value.supplier_name)
    if (audit) {
      haveSupplierName.value = true
      message.error('当前供应商已存在，若需要新增对应的子公司，请联系有权限的进行操作')
    }
  })
}, 1000)

const filterErrorKey = (key) => {
  errorKeyList.value = errorKeyList.value.filter((v) => v !== key)
}
const companyTableKeys = ref([
  { type: 'seq', title: '序号', width: px2(80) },
  { title: '子公司供应商编号', field: 'company_supplier_id' },
  { title: '供应商编号(聚水潭)', field: 'jst_supplier_id' },
  {
    title: '供应商子公司名称',
    field: 'company_supplier_name',
    dataType: 'input',
    required: isRequired,
  },
  { title: '对接采购员', field: 'buyer_names', width: px2(300) },
  { title: '供应商分类', field: 'supplier_category', width: px2(150), required: true },
  { title: '公司网址/1688店铺网址', field: 'website' },
  { title: '1688店铺名字', field: 'shop_name' },
  { title: '操作', field: 'operate', width: px2(80) },
])
const addCompany = () => {
  form.value!.supplier_company_info_list.push({
    company_supplier_id: null,
    company_supplier_name: null,
    supplier_id: null,
    k3_supplier_id: null,
    jst_supplier_id: null,
    buyer_oa_id: null,
    buyer_ids: null,
    data_status: form.value.supplier_id ? 2 : 1,
    is_view: true,
    website: null,
    shop_name: null,
  })
}
const deleteCompany = (row) => {
  Modal.confirm({
    title: '确定要删除吗？',
    content: '删除后将无法恢复',
    onOk: () => {
      form.value.supplier_company_info_list = form.value.supplier_company_info_list.filter((item) => item !== row)
      if (row.id) deleteData.value.supplier_company_info_list.push({ ...row, data_status: 4 })
    },
  })
}
const PurchaseRef = ref()
const purchaseConfirm = ref<any>(() => {})
const selectPurchaseUser = (row) => {
  console.log(row)
  PurchaseRef.value.open(row.buyer_list || [])
  purchaseConfirm.value = (data) => {
    console.log('purchaseConfirm data', data)
    row.buyer_ids = data.map((f) => +f.value)
    row.buyer_list = data
    // row.buyer_name = data.label
    row.data_status = form.value.supplier_id ? (row.id ? 3 : 2) : 1
  }
}
const deletePurchaseUser = (row) => {
  row.buyer_list = []
  row.buyer_ids = []
}

// 联系信息表格
const contactTableKeys = ref([
  { type: 'seq', title: '序号', width: px2(80) },
  { title: '供应商名称/子公司名称', field: 'supplier_name', required: isRequired },
  {
    title: '联系人姓名',
    field: 'name',
    required: isRequired,
  },
  {
    title: '职务',
    field: 'job',
    dataType: 'contact',
  },
  {
    title: '电话',
    field: 'phone_number',
    dataType: 'contact',
  },
  {
    title: '手机',
    field: 'mobile_phone_number',
    required: isRequired,
  },
  {
    title: '传真',
    field: 'fax_no',
    dataType: 'contact',
  },
  {
    title: '邮箱',
    field: 'email',
    dataType: 'contact',
  },
  {
    title: '联系地址',
    field: 'address',
    dataType: 'input',
  },
  { title: '设为默认', field: 'is_default' },
  { title: '操作', field: 'operate' },
])
const addContact = () => {
  form.value!.supplier_contact_info_list.push({
    id: 0,
    type: 0,
    supplier_name: null,
    supplier_id: null,
    name: '',
    job: '',
    phone_number: '',
    mobile_phone_number: '',
    fax_no: '',
    email: '',
    address: '',
    province: null,
    city: null,
    area: null,
    is_default: false,
    data_status: form.value.supplier_id ? 2 : 1,
    is_view: true,
  })
}
const deleteContact = (row) => {
  Modal.confirm({
    title: '确定要删除吗？',
    content: '删除后将无法恢复',
    onOk: () => {
      form.value!.supplier_contact_info_list = form.value!.supplier_contact_info_list.filter((item) => item !== row)
      if (row.id) deleteData.value.supplier_contact_info_list.push({ ...row, data_status: 4 })
    },
  })
}

//  财务信息
const financialTableKeys = [
  { type: 'seq', title: '序号', width: px2(50) },
  { title: '供应商名称/子公司名称', field: 'supplier_name' },
  {
    title: '收款方式',
    field: 'payment_method',
    dataType: 'select',
    options: [
      { label: '银行', value: 1 },
      { label: '支付宝', value: 2 },
      { label: '微信', value: 3 },
      { label: '现金', value: 4 },
    ],
  },
  {
    title: '账户名称',
    field: 'account_name',
    dataType: 'input',
  },
  {
    title: '账户类型',
    field: 'account_type',
    dataType: 'select',
    options: [
      { label: '公户', value: 1 },
      { label: '私户', value: 2 },
    ],
  },
  {
    title: '收款卡号',
    field: 'collection_card_number',
    dataType: 'number',
  },
  {
    title: '收款银行支行',
    field: 'collection_bank',
    dataType: 'input',
  },
  {
    title: '收款账号盖章凭证',
    field: 'collection_account_certificate_file_ids',
    required: true,
  },
  {
    title: '备注',
    field: 'remark',
    dataType: 'input',
  },
  {
    title: '设为默认',
    field: 'is_default',
    width: px2(80),
  },
  { title: '操作', field: 'operate', width: px2(80) },
]
const addAccount = () => {
  form.value!.supplier_finance_info_list.push({
    id: null,
    type: 0,
    supplier_name: null,
    supplier_id: null,
    account_type: null,
    account_name: '',
    collection_card_number: '',
    collection_bank: '',
    collection_account_certificate_file_ids: [],
    remark: '',
    is_default: false,
    data_status: form.value.supplier_id ? 2 : 1,
    is_view: true,
  })

  form.value.is_update_finance_info = true
}
const deleteAccount = (row) => {
  Modal.confirm({
    title: '确定要删除吗？',
    content: '删除后将无法恢复',
    onOk: () => {
      form.value!.supplier_finance_info_list = form.value!.supplier_finance_info_list.filter((item) => item !== row)
      if (row.id) {
        deleteData.value.supplier_finance_info_list.push({ ...row, data_status: 4 })
        form.value.is_update_finance_info = true
      }
    },
  })
}

// 发货信息
const deliveryTableKeys = ref([
  { type: 'seq', title: '序号', width: px2(80) },
  { title: '供应商名称/子公司名称', field: 'supplier_name', required: isRequired },
  { title: '发货地址', field: 'shipments_address', required: isRequired },
  { title: '设为默认', field: 'is_default' },
  { title: '操作', field: 'operate' },
])
const addDelivery = () => {
  form.value!.supplier_shipments_info_list.push({
    id: null,
    type: 0,
    supplier_name: null,
    supplier_id: null,
    shipments_address: '',
    is_default: false,
    province: null,
    city: null,
    area: null,
    data_status: 2,
    is_view: true,
  })
}

const DeliveryRef = ref()
const deliveryCb = ref((data) => console.log(data))

const selectDeliveryAddress = (row, title) => {
  DeliveryRef.value.open(row, title)
  deliveryCb.value = (data) => {
    row.province = data.province
    row.city = data.city
    row.area = data.area
    row.shipments_address = data.address
    row.address = data.address
    row.data_status = form.value.supplier_id ? (row.id ? 3 : 2) : 1
  }
}

const deleteDelivery = (row) => {
  Modal.confirm({
    title: '确定要删除吗？',
    content: '删除后将无法恢复',
    onOk: () => {
      form.value!.supplier_shipments_info_list = form.value!.supplier_shipments_info_list.filter((item) => item !== row)
      if (row.id) deleteData.value.supplier_shipments_info_list.push({ ...row, data_status: 4 })
    },
  })
}

// 用户信息
const userTableKeys = [
  { type: 'seq', title: '序号', width: px2(80) },
  {
    title: '姓名',
    field: 'real_name',
    dataType: 'input',
    required: true,
  },
  {
    title: '联系方式',
    field: 'phone_number',
    dataType: 'input',
    // required: true,
  },
  {
    title: '账号',
    field: 'user_name',
    dataType: 'input',
    required: true,
  },
  {
    title: '密码',
    field: 'password',
    dataType: 'input',
    required: true,
  },
  { title: '状态', field: 'status' },
  { title: '操作', field: 'operate' },
]
const addUser = () => {
  form.value!.supplier_user_info_list.push({
    id: null,
    supplier_id: null,
    supplier_name: null,
    real_name: '',
    user_name: '',
    password: '',
    phone_number: '',
    status: 0,
    data_status: form.value.supplier_id ? 2 : 1,
  })
}
const copyUser = (row) => {
  form.value!.supplier_user_info_list.push({
    id: null,
    supplier_id: null,
    supplier_name: row.supplier_name,
    real_name: row.real_name,
    user_name: row.user_name,
    password: row.password,
    phone_number: row.phone_number,
    status: 0,
    data_status: form.value.supplier_id ? 2 : 1,
  })
}
const deleteUser = (row) => {
  Modal.confirm({
    title: '确定要删除吗？',
    content: '删除后将无法恢复',
    onOk: () => {
      form.value!.supplier_user_info_list = form.value!.supplier_user_info_list.filter((item) => item !== row)
      if (row.id) deleteData.value.supplier_user_info_list.push({ ...row, data_status: 4 })
    },
  })
}

// 其他信息
const otherTableKeys = [
  { type: 'seq', title: '序号', width: px2(50) },
  { title: '创建人', field: 'creator_name' },
  { title: '申请日期', field: 'create_at' },
  { title: '最近修改人', field: 'modifier_name' },
  { title: '最近修改时间', field: 'modified_at' },
  { title: '最近审核人', field: 'auditor_name' },
  { title: '最近审核日期', field: 'audit_status_time' },
  { title: '审核状态', field: 'audit_status' },
  { title: '备注', field: 'remark' },
]

const validate = () => {
  errorKeyList.value = []
  let err = ''
  const {
    supplier_type,
    supplier_name,
    purchase_nature,
    supplier_group_id,
    // website,
    // shop_name,

    supplier_company_info_list,
    supplier_contact_info_list,
    supplier_finance_info_list,
    supplier_shipments_info_list,
    supplier_user_info_list,
  } = form.value

  if (!supplier_type) {
    err = '供应商类型不能为空'
  } else if (!supplier_name) {
    err = '供应商名称不能为空'
    errorKeyList.value.push('supplier_name')
  } else if (haveSupplierName.value) {
    err = '当前供应商已存在，若需要新增对应的子公司，请联系有权限的进行操作'
    errorKeyList.value.push('supplier_name')
  } else if (!purchase_nature) {
    err = '采购性质不能为空'
    errorKeyList.value.push('purchase_nature')
  } else if (!supplier_group_id?.length) {
    err = '供应商分组不能为空'
    errorKeyList.value.push('supplier_group_id')
    // } else if (supplier_type === 2 && !website) {
    //   err = '公司网址不能为空'
    //   errorKeyList.value.push('website')
    // } else if (supplier_type === 2 && !shop_name) {
    //   err = '店铺名称不能为空'
    //   errorKeyList.value.push('shop_name')
  }

  if (err) {
    message.error(err)
    nextTick(() => {
      scrollToError()
    })
    return false
  }
  // console.log(supplier_company_info_list)

  supplier_company_info_list.some((item, index) => {
    if (!item.company_supplier_name && isRequired.value) {
      err = `子公司列表第${index + 1}行 子公司名称不能为空`
      errorKeyList.value.push(`company_supplier_name${index}`)
      return true
    }
    if (item.website === null && form.value.supplier_type === 2) {
      err = `子公司列表第${index + 1}行公司网址/1688店铺网址不能为空`
      errorKeyList.value.push(`website${index}`)
      return true
    }
    if (item.shop_name === null && form.value.supplier_type === 2) {
      err = `子公司列表第${index + 1}行1688店铺名字不能为空`
      errorKeyList.value.push(`shop_name${index}`)
      return true
    }
    if (!item.supplier_category) {
      err = `子公司列表第${index + 1}行 供应商分类不能为空`
      errorKeyList.value.push(`supplier_category${index}`)
      return true
    }
    return false
  })

  supplier_finance_info_list.some((item, index) => {
    if (!item.collection_account_certificate_files?.length && item.account_type === 1) {
      err = `财务信息列表第${index + 1}行 收款账号盖章凭证不能为空`
      errorKeyList.value.push(`collection_account_certificate_files${index}`)
      return true
    }
    return false
  })

  if ([...new Set(supplier_company_info_list.map((v) => v.company_supplier_name))]?.length !== supplier_company_info_list.length) {
    err = '子公司列表中存在重复的子公司名称'
  }

  if (err) {
    message.error(err)
    nextTick(() => {
      scrollToError()
    })
    return false
  }
  supplier_contact_info_list.some((item, index) => {
    if (!item.supplier_name && isRequired.value) {
      err = `联系人列表第${index + 1}行 供应商名称不能为空`
      errorKeyList.value.push(`connection_supplier_name${index}`)
      return true
    }

    if (!['当前供应商所有子公司可见', supplier_name, ...supplier_company_info_list.map((v) => v.company_supplier_name)].includes(item.supplier_name)) {
      err = `联系人列表第${index + 1}行 供应商名称或供应商子公司名称不存在`
      return true
    }

    if (!item.name && isRequired.value) {
      err = `联系人列表第${index + 1}行 联系人姓名不能为空`
      errorKeyList.value.push(`connection_name${index}`)
      console.log(errorKeyList)

      return true
    }

    if (!item.mobile_phone_number && isRequired.value) {
      err = `联系人列表第${index + 1}行 联系人手机不能为空`
      errorKeyList.value.push(`connection_mobile_phone_number${index}`)
      return true
    }

    if (item.mobile_phone_number && !/^1[3-9]\d{9}$/.test(item.mobile_phone_number)) {
      err = `联系人列表第${index + 1}行 联系人手机格式不正确`
      item.mobile_phone_number = ''
      errorKeyList.value.push(`connection_mobile_phone_number${index}`)
      return true
    }

    // 校验邮箱格式
    if (item.email && !/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(item.email)) {
      err = `联系人列表第${index + 1}行 邮箱格式不正确`
      return true
    }
    return false
  })

  if (supplier_company_info_list.length === 0 && isRequired.value) {
    err = '子公司列表不能为空'
  }

  if (err) {
    message.error(err)
    nextTick(() => {
      scrollToError()
    })
    return false
  }
  supplier_finance_info_list.some((item, index) => {
    if (item.supplier_name?.length) {
      if (!['当前供应商所有子公司可见', supplier_name, ...supplier_company_info_list.map((v) => v.company_supplier_name)].includes(item.supplier_name)) {
        err = `财务信息列表第${index + 1}行 供应商名称或供应商子公司名称不存在`
        return true
      }
    }
    return false
  })

  if (err) {
    message.error(err)
    return false
  }
  supplier_shipments_info_list.some((item, index) => {
    if (!item.supplier_name && isRequired.value) {
      err = `发货信息列表第${index + 1}行 供应商名称不能为空`
      errorKeyList.value.push(`shipments_supplier_name${index}`)
      return true
    }

    if (!['当前供应商所有子公司可见', supplier_name, ...supplier_company_info_list.map((v) => v.company_supplier_name)].includes(item.supplier_name)) {
      err = `发货信息列表第${index + 1}行 供应商名称或供应商子公司名称不存在`
      return true
    }

    if (!item.shipments_address && isRequired.value) {
      err = `发货信息列表第${index + 1}行 发货地址不能为空`
      return true
    }
    return false
  })

  // 校验用户信息
  supplier_user_info_list.some((item, index) => {
    if (!item.real_name) {
      err = `用户信息列表第${index + 1}行 姓名不能为空`
      errorKeyList.value.push(`real_name${index}`)
      return true
    }
    // if (!item.phone_number) {
    //   err = `用户信息列表第${index + 1}行 联系方式不能为空`
    //   errorKeyList.value.push(`phone_number${index}`)
    //   return true
    // }
    if (!item.user_name) {
      err = `用户信息列表第${index + 1}行 账号不能为空`
      errorKeyList.value.push(`user_name${index}`)
      return true
    }
    if (!item.password) {
      err = `用户信息列表第${index + 1}行 密码不能为空`
      errorKeyList.value.push(`password${index}`)
      return true
    }
    return false
  })

  if (err) {
    message.error(err)
    nextTick(() => {
      scrollToError()
    })
    return false
  }

  return true
}

const submit = async (is_review) => {
  if (submitLoading.value) return
  if (is_review && !validate()) return

  const method = auditId.value ? UpdateSupplierAuditProcess : AddSupplierAuditProcess
  const data = submitData()
  data.supplier_finance_info_list = data.supplier_finance_info_list.filter((item) => {
    return item.supplier_name || item.account_name || item.account_type || item.collection_card_number || item.collection_bank || item.remark
  })

  const flag = data.supplier_company_info_list.some((item) => {
    return !item.buyer_ids || (item.buyer_ids.length === 1 && item.buyer_ids[0] === 0)
  })
  console.log('data', data)
  console.log(flag)

  if (flag) {
    Modal.confirm({
      title: '确认',
      icon: () => {},
      content: '供应商子公司对接采购员为空，供应商关联单据所有人可见，确定提交吗？',
      onOk() {
        submitLoading.value = true
        method({
          ...data,
          is_review,
        })
          .then(() => {
            message.success('操作成功')
            close()
            emit('refresh')
            submitLoading.value = false
          })
          .catch(() => {
            submitLoading.value = false
          })
      },
      onCancel() {},
    })
  } else {
    submitLoading.value = true
    method({
      ...data,
      is_review,
    })
      .then(() => {
        message.success('操作成功')
        close()
        emit('refresh')
        submitLoading.value = false
      })
      .catch(() => {
        submitLoading.value = false
      })
  }
}

const submitData = () => {
  console.log(form.value, 'form.value')

  const newData = JSON.parse(JSON.stringify(form.value))
  return {
    ...(newData.supplier_id || newData.audit_id || newData.id
      ? {
          ...newData,
        }
      : {}),
    is_update_finance_info: newData.is_update_finance_info,
    type: newData.supplier_id ? 2 : 1,
    supplier_name: newData.supplier_name,
    supplier_type: newData.supplier_type,
    purchase_nature: newData.purchase_nature || null,
    supplier_group_id: newData.supplier_group_id?.[1] || null,
    // website: newData.website,
    // shop_name: newData.shop_name,
    carriage_mode: newData.carriage_mode || null,
    describes: newData.describes,
    business_license_file_ids: newData.business_license_files?.map((v) => v.id) || [],
    settlement_type: newData.settlement_type || null,
    invoice_type: newData.invoice_type || null,
    default_tax_rate_id: newData.default_tax_rate_id ?? null,
    supplier_company_info_list: newData.supplier_company_info_list
      .map((v, index) => {
        const val = {
          ...(v.company_supplier_id ? { ...v } : {}),
          company_supplier_name: v.company_supplier_name || null,
          // 因为数据权限空数组很难写, 这里用零...
          buyer_ids: v.buyer_ids?.length ? v.buyer_ids : [0],
          data_status: index == 0 && saveSupplier.value != v.company_supplier_name && saveSupplier.value ? 3 : v.data_status,
          is_default: v.is_default || false,
          website: v.website || null,
          shop_name: v.shop_name || null,
          supplier_category: v.supplier_category || null,
        }
        return val
      })
      .concat(deleteData.value.supplier_company_info_list)
      .map((item) => {
        return {
          ...item,
          purchase_price_list_id: listId.value || undefined,
        }
      }),
    supplier_contact_info_list: newData.supplier_contact_info_list
      .map((v) => {
        const val = {
          ...(v.id ? { ...v } : {}),
          type: v.supplier_name === '当前供应商所有子公司可见' ? 1 : 2,
          supplier_name: v.supplier_name === '当前供应商所有子公司可见' ? newData.supplier_name : v.supplier_name,
          company_supplier_name: v.supplier_name || null,
          name: v.name || null,
          job: v.job || null,
          phone_number: v.phone_number || null,
          mobile_phone_number: v.mobile_phone_number || null,
          fax_no: v.fax_no || null,
          email: v.email || null,
          address: v.address || null,
          is_default: v.is_default || false,
          data_status: newData.supplier_id ? v.data_status : 1,
          province: v.province || null,
          city: v.city || null,
          area: v.area || null,
        }
        return val
      })
      .concat(deleteData.value.supplier_contact_info_list),
    supplier_finance_info_list: newData.supplier_finance_info_list
      .map((v) => {
        const val = {
          ...(v.id ? { ...v } : {}),
          type: v.supplier_name === '当前供应商所有子公司可见' ? 1 : 2,
          supplier_name: v.supplier_name === '当前供应商所有子公司可见' ? newData.supplier_name : v.supplier_name,
          company_supplier_name: v.supplier_name || null,
          account_type: v.account_type || null,
          account_name: v.account_name || null,
          collection_card_number: v.collection_card_number || null,
          collection_bank: v.collection_bank || null,
          collection_account_certificate_file_ids: v.collection_account_certificate_files?.map((v) => v.id) || [],
          remark: v.remark || null,
          is_default: v.is_default || false,
          data_status: newData.supplier_id ? v.data_status : 1,
          payment_method: v.payment_method || null,
        }
        return val
      })
      .concat(deleteData.value.supplier_finance_info_list),
    supplier_shipments_info_list: newData.supplier_shipments_info_list
      .map((v) => {
        const val = {
          ...(v.id ? { ...v } : {}),
          type: v.supplier_name === '当前供应商所有子公司可见' ? 1 : 2,
          supplier_name: v.supplier_name === '当前供应商所有子公司可见' ? newData.supplier_name : v.supplier_name,
          company_supplier_name: v.supplier_name || null,
          province: v.province || null,
          city: v.city || null,
          area: v.area || null,
          shipments_address: v.shipments_address || null,
          is_default: v.is_default || false,
          data_status: newData.supplier_id ? v.data_status : 1,
        }
        return val
      })
      .concat(deleteData.value.supplier_shipments_info_list),
    supplier_user_info_list: newData.supplier_user_info_list
      .map((v) => {
        const val = {
          ...(v.id ? { ...v } : {}),
          supplier_name: v.supplier_name || null,
          real_name: v.real_name || null,
          user_name: v.user_name || null,
          password: v.password || null,
          phone_number: v.phone_number || null,
          status: v.status || 0,
          data_status: newData.supplier_id ? v.data_status : 1,
        }
        return val
      })
      .concat(deleteData.value.supplier_user_info_list),
  }
}

const deleteData = ref<{
  supplier_company_info_list: SubCompanyMessage[]
  supplier_contact_info_list: ContactMessage[]
  supplier_finance_info_list: FinancialMessage[]
  supplier_shipments_info_list: DeliveryMessage[]
  supplier_user_info_list: UserMessage[]
}>({
  supplier_company_info_list: [],
  supplier_contact_info_list: [],
  supplier_finance_info_list: [],
  supplier_shipments_info_list: [],
  supplier_user_info_list: [],
})

const checkStatus = (key: string) => (errorKeyList.value.includes(key) ? 'error' : '')

const scrollToError = () => {
  // const errorKey = errorKeyList.value[0]
  if (!errorKeyList.value.length) return
  const errorElement = document.querySelector(`[status="error"]`)
  if (errorElement) {
    errorElement?.scrollIntoView({ behavior: 'smooth', block: 'center' })
    return
  }
  const errorSelectElement = document.querySelector('.ant-select-status-error')
  errorSelectElement?.scrollIntoView({ behavior: 'smooth', block: 'center' })
}

const emit = defineEmits(['refresh'])
defineExpose({ open })
</script>

<style lang="scss" scoped>
.title {
  @apply p-4 bg-#f5f7fe flex items-center text-sm font-bold my-12 rounded;
}
</style>
