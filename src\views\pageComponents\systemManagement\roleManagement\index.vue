<template>
  <div class="main">
    <Form ref="formRef" v-model:form="formArr" @search="search" @setting="tableRef?.showTableSetting()" :page-type="PageTypeEnum.ROLE_MANAGE" />
    <!-- 表格 -->
    <BaseTable ref="tableRef" v-model:form="formArr" :page-type="PageTypeEnum.ROLE_MANAGE" :get-list="GetList">
      <template #right-btn>
        <a-button id="roleManagementIncrease" type="primary" @click="tapManipulate('add')" v-if="btnPermission[31001]">新增角色</a-button>
      </template>
      <template #scope="{ row }">
        <span>{{ row.scope == '1' ? '内部联系人' : '外部联系人' }}</span>
      </template>

      <template #status="{ row }">
        <a-switch v-model:checked="[false, true][row.status]" @click="tapSwitch($event, row)" :disabled="!btnPermission[31005] || row.is_def">
          <template #checkedChildren>
            <span class="iconfont icon-zhengquede_correct"></span>
          </template>
          <template #unCheckedChildren>
            <span class="iconfont icon-guanbi_close"></span>
          </template>
        </a-switch>
      </template>
      <template #create_at="{ row }">
        <span>{{ row.create_at ? row.create_at.slice(0, 16) : '' }}</span>
      </template>
      <template #update_at="{ row }">
        <span>{{ row.update_at ? row.update_at.slice(0, 16) : '' }}</span>
      </template>

      <template #fix_option="{ row, column }">
        <RightOperate :list="rightOperateList" :row="row" :column="column" />
      </template>
    </BaseTable>

    <a-drawer
      v-model:open="isAddRole"
      @afterOpenChange="formRef.clearValidate()"
      width="26vw"
      :title="roleModuleType == 'add' ? '新建角色' : '编辑角色'"
      placement="right"
      :maskClosable="false"
      :footer-style="{ textAlign: 'left' }"
    >
      <a-form ref="formRef" :model="addRoleData">
        <a-form-item
          label="角色名称"
          name="role_name"
          :rules="[
            { required: true },
            {
              validator: (_rule, value) => validateStr(_rule, value, 50),
              message: '输入内容不可超过50字符',
            },
          ]"
        >
          <a-input id="role_name" v-model:value="addRoleData.role_name" placeholder="请输入角色名称" />
        </a-form-item>

        <!-- <a-form-item label="角色编码" name="role_code" v-if="roleModuleType == 'add'">
          <a-input id="role_code" v-model:value="addRoleData.role_code" placeholder="请输入角色编码" />
        </a-form-item> -->

        <a-form-item label="适用范围" name="scope">
          <a-radio-group v-model:value="addRoleData.scope">
            <a-radio :value="1">内部联系人</a-radio>
            <a-radio :value="2">外部联系人</a-radio>
          </a-radio-group>
        </a-form-item>

        <a-form-item v-if="roleModuleType == 'add'" label="状态" name="status">
          <a-switch v-model:checked="[false, true][addRoleData.status]" @click="startStopForm()" :disabled="addRoleData.is_def">
            <template #checkedChildren>
              <span class="iconfont icon-zhengquede_correct"></span>
            </template>
            <template #unCheckedChildren>
              <span class="iconfont icon-guanbi_close"></span>
            </template>
          </a-switch>
        </a-form-item>
      </a-form>
      <template #footer>
        <a-button style="margin-right: 10px" type="primary" @click="tapAddRoleSubmit">确认</a-button>
        <a-button @click="isAddRole = false">取消</a-button>
      </template>
    </a-drawer>
    <!-- 查看 -->
    <detail-drawer ref="detailDrawerRef" />
    <a-modal :zIndex="10000" v-model:open="visibleData.isShow" :title="visibleData.title">
      <div class="modalContent">{{ visibleData.content }}</div>
      <template #footer>
        <a-button id="roleManagementFormComfirm" v-if="visibleData.isConfirmBtn" style="margin-right: 10px" type="primary" @click="visibleData.okFn" :danger="visibleData.okType === 'danger'">
          {{ visibleData.confirmBtnText }}
        </a-button>
        <a-button id="roleManagementFormCancel" v-if="visibleData.isCancelBtn" @click="visibleData.isShow = false">取消</a-button>
      </template>
    </a-modal>
    <!-- 权限 -->
    <EditLimits ref="editLimitsRef" @query="search"></EditLimits>
  </div>
</template>
<script lang="ts" setup>
import { message } from 'ant-design-vue'
import { onMounted } from 'vue'

import { validateStr } from '@/utils/index'
import Form from '@/components/Form.vue'

import DetailDrawer from './components/DetailDrawer.vue'
import EditLimits from './components/PermissionDrawer.vue'

import { Add, Delete, GetList, Update, UpdateRoleStatus } from '@/servers/Role'

import { PageTypeEnum } from '@/enums/tableEnum'

const isAddRole = ref(false)
const editLimitsRef = ref()
// const showFilter = ref(false)

const { btnPermission } = usePermission()

const roleModuleType = ref('add')
// 查看
const detailDrawerRef = ref()
const oldStatus = ref(null)
const visibleData = reactive({
  isShow: false,
  isConfirmBtn: true,
  isCancelBtn: true,
  confirmBtnText: '确定',
  title: '',
  okType: 'primary',
  content: '',
  okFn: () => {
    visibleData.isShow = false
  },
})
const formArr: any = ref([
  {
    label: '适用范围',
    value: null,
    type: 'select',
    selectArr: [
      { label: '内部联系人', value: 1 },
      { label: '外部联系人', value: 2 },
    ],
    key: 'scope',
  },
  {
    label: '状态',
    value: null,
    type: 'select',
    selectArr: [
      { label: '启用', value: 1 },
      { label: '禁用', value: 0 },
    ],
    key: 'status',
  },
  {
    label: '创建时间',
    value: null,
    type: 'range-picker',
    selectArr: [],
    key: 'create_at',
    formKeys: ['start_time', 'end_time'],
    placeholder: ['创建开始时间', '创建结束时间'],
  },
  {
    label: '修改时间',
    value: null,
    type: 'range-picker',
    selectArr: [],
    key: 'update_at',
    formKeys: ['update_start_time', 'update_end_time'],
    placeholder: ['修改开始时间', '修改结束时间'],
  },
])
const addRoleData = reactive({
  id: null,
  role_name: '',
  role_code: '',
  scope: 1,
  status: 1,
  is_def: false,
})

const rightOperateList = ref([
  {
    label: '查看',
    onClick: ({ row }) => {
      detail(row)
    },
  },
  {
    label: '权限',
    show: 31003,
    onClick: ({ row }) => {
      tapManipulate('permissions', row)
    },
  },
  {
    label: '编辑',
    show: 31002,
    onClick: ({ row }) => {
      tapManipulate('compiler', row)
    },
  },
  {
    label: '删除',
    show: ({ row }) => btnPermission.value[31004] && !row.is_def,
    onClick: ({ row }) => {
      tapManipulate('removes', row)
    },
  },
])

onMounted(() => {
  search()
  getEnum()
})

const tapAddRoleSubmit = async () => {
  try {
    await formRef.value.validateFields()
    switch (roleModuleType.value) {
      case 'add':
        addRole()
        break
      case 'compiler':
        upRoleDate()
        break
      default:
        break
    }
  } catch (errorInfo) {
    console.log('Failed:', errorInfo)
  }
}
const tapManipulate = (type: string, row: any = '') => {
  switch (type) {
    case 'add':
      isAddRole.value = true
      roleModuleType.value = 'add'
      addRoleData.role_name = ''
      addRoleData.role_code = ''
      addRoleData.scope = 1
      addRoleData.status = 1
      addRoleData.is_def = false
      break
    case 'compiler':
      oldStatus.value = row.status
      addRoleData.is_def = row.is_def
      addRoleData.id = row.id
      addRoleData.role_name = row.role_name
      addRoleData.scope = row.scope
      addRoleData.status = row.status
      isAddRole.value = true
      roleModuleType.value = 'compiler'
      break
    case 'permissions':
      openEditLimits(row)
      break
    case 'removes':
      visibleData.isShow = true
      visibleData.title = '删除角色'
      visibleData.content = `即将删除该角色，删除后：
  - 现有系统权限配置可能会受到影响，请确保已经调整了相关权限设置。

请在执行此操作前确认：
  - 已检查并调整系统中与该角色相关的权限配置。
  - 已通知受影响的用户，且已重新分配必要的角色或权限。

此操作不可恢复，确定要删除该角色吗？`
      visibleData.confirmBtnText = '确定'
      visibleData.isCancelBtn = true
      visibleData.okType = 'danger'
      visibleData.okFn = () => {
        deleteRole(row.id)
      }
      break
    default:
      break
  }
}
const tapSwitch = (e, row) => {
  if (!row.status) {
    updateRoleStatus({ id: row.id, status: e ? 1 : 0 })
  } else {
    visibleData.isShow = true
    visibleData.title = '停用角色'
    visibleData.content = `即将停用该角色，停用后：
  - 所有使用此角色的用户将失去与该角色相关的权限。
  - 受影响的用户将无法执行与此角色权限相关的操作，直到他们被分配新的角色。

确定要停用该角色吗？`
    visibleData.confirmBtnText = '确定'
    visibleData.isCancelBtn = true
    visibleData.okType = 'danger'
    visibleData.okFn = () => {
      updateRoleStatus({ id: row.id, status: e ? 1 : 0 })
    }
  }
}

const startStopForm = () => {
  if (addRoleData.status == 1) {
    addRoleData.status = 0
  } else {
    addRoleData.status = 1
  }
}

// 详情
const detail = (item) => {
  detailDrawerRef.value?.open(item.id, btnPermission[31007])
}

const tableRef = ref()
const formRef = ref()
const search = () => tableRef.value.search()
// const showTableSetting = () => tableRef.value.showTableSetting()
// const tapScreeningShow = () => formRef.value.openScreening()

// 角色启用停用
const updateRoleStatus = (obj) => {
  UpdateRoleStatus(obj).then(() => {
    tableRef.value.search()
    visibleData.isShow = false
  })
}
// 获取所有枚举选项
const getEnum = () => {
  // GetEnum().then(res => {
  //   enumData.value = res.data;
  //   formArr.value.map(item => {
  //     if (item.key == 'status') {
  //       item.selectArr = enumData.value.common.status;
  //     }
  //     if (item.key == 'scope') {
  //       item.selectArr = enumData.value.role.scope;
  //     }
  //   });
  // });
}
// 新增角色
const addRole = () => {
  const obj = JSON.parse(JSON.stringify(addRoleData))
  Add(obj).then(() => {
    message.success('新增成功')
    isAddRole.value = false
    search()
  })
}
// 编辑角色
const upRoleDate = () => {
  const fn = () => {
    const obj = JSON.parse(JSON.stringify(addRoleData))
    Update(obj).then(() => {
      message.success('修改成功')
      isAddRole.value = false
      search()
    })
  }
  if (addRoleData.status == 0 && oldStatus.value == 1) {
    // 停用
    visibleData.isShow = true
    visibleData.title = '停用角色'
    visibleData.content = `即将停用该角色，停用后：
  - 所有使用此角色的用户将失去与该角色相关的权限。
  - 受影响的用户将无法执行与此角色权限相关的操作，直到他们被分配新的角色。

确定要停用该角色吗？`
    visibleData.confirmBtnText = '确定'
    visibleData.isCancelBtn = true
    visibleData.okType = 'danger'
    visibleData.okFn = () => {
      visibleData.isShow = false
      fn()
    }
  } else {
    fn()
  }
}
// 删除角色
const deleteRole = (id) => {
  Delete({ id })
    .then(() => {
      visibleData.isShow = false
      message.success('删除成功')
      search()
    })
    .catch(() => {
      visibleData.isShow = false
    })
}
// 打开权限弹窗
const openEditLimits = (item) => {
  editLimitsRef.value.opendrawer(item)
}
</script>
<style lang="scss" scoped>
.main {
  display: flex;
  flex-direction: column;
  height: 100%;

  .btnBox {
    display: flex;
    align-items: center;
  }
}

::v-deep(.ant-form-item) {
  align-items: flex-start;

  .ant-form-item-label {
    display: flex;
    justify-content: flex-end;
    width: 100px;
    min-width: 100px;
    margin-right: 30px;

    label {
      &::after {
        display: none !important;
      }
    }
  }
}

.modalContent {
  font-size: 14px;
  word-break: break-word;
  white-space: pre-wrap;
}

.vxe-icon {
  width: 16px;
  height: 16px;
}
</style>
