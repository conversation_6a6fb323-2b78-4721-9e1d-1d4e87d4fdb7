import { Radio, RadioGroupProps, RadioChangeEvent } from 'ant-design-vue'

export default defineComponent({
  name: 'EasyFormRadio',
  props: {
    innerProps: {
      require: true,
      type: Object as PropType<RadioGroupProps>,
    },
    item: {
      require: true,
      type: Object,
      default: () => ({}),
    },
    form: {
      type: Object as PropType<Record<string, any>>,
      default: () => ({}),
    },
  },
  setup(props) {
    const renderVN = () => {
      if (!props.innerProps) return null

      return h(Radio.Group as any, {
        options: (props.item.options || []).filter((f) => {
          return (typeof f === 'object' && f.hide !== true) || typeof f === 'string' || typeof f === 'number'
        }),
        ...props.innerProps,
        onChange: (e: RadioChangeEvent) => {
          props?.innerProps?.onChange && props?.innerProps?.onChange(e.target.value)
        },
      })
    }
    return {
      renderVN,
    }
  },
  render() {
    return this.renderVN()
  },
})
