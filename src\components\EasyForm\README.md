# 使用方法

```
<template>
  <EasyForm ref="formRef" :formItems="formItems" v-model:form="formData" />
</template>

<script lang="ts" setup>
import dayjs from 'dayjs'

import { Enum2Options } from '@/utils'
import { areaCity } from '@/utils/address'

import { List } from '@/servers/WarehouseApi'

const formRef = ref()
const formData = ref<Record<string, unknown>>({})
const formItems = ref<EasyFormItemProps[]>([
  // input
  {
    type: 'input',
    label: '仓库名称',
    name: 'warehouse_name',
    span: 7,
  },
  // text
  {
    type: 'text',
    label: '1688采购账号',
    name: 'ali_purchase_account_list_id',
    alias: 'ali_purchase_account_list_name',
    span: 7,
    textFormat: ({ item, value }) => {
      return `${item.alias}: ${value}`
    },
  },
  // select
  {
    type: 'select',
    label: '仓库ID',
    required: true,
    name: 'warehouse_id',
    api: () => List({}),
    span: 7,
  },
  // 搜索选择
  {
    type: 'select',
    label: '物流公司',
    name: 'logistics_company_id',
    alias: 'logistics_company',
    api: ({ keyword }) => List({ logistics_company_name: keyword, PageSize: 20, Page: 1 }),
    optionFormatter: (item) => ({
      value: item.id,
      label: item.company_name,
    }),
    props: {
      showSearch: true,
    },
  },
  // radio
  {
    type: 'radio',
    label: '操作类型',
    required: true,
    name: 'operation_type',
    options: Enum2Options(RecommendOrderOperationTypeEnum),
    span: 13,
    // hide: true,
  },
  // date
  {
    type: 'date',
    label: '预计到货时间',
    required: true,
    name: 'scheduled_arrival_time',
    // showTime: true,
    showTime: { defaultValue: dayjs().set('hour', 23).set('minute', 59).set('second', 59) },
    props: {
      disabledDate: (current) => {
        return current && current < dayjs().startOf('day')
      },
    },
  },
  // 联动
  {
    label: '收货地址',
    name: 'province',
    span: 6,
    type: 'select',
    required: true,
    options: areaCity.map((f) => ({ ...f, value: f.name, label: f.name })),
    linkage: ['city', 'area'],
  },
  {
    label: '',
    name: 'city',
    span: 6,
    type: 'select',
    required: true,
    options: [],
    linkage: 'area',
    linkageFn: ({ option, item }) => {
      item.options = (option?.children || []).map((f) => ({ ...f, value: f.name, label: f.name }))
    },
  },
  {
    label: '',
    name: 'area',
    span: 6,
    type: 'select',
    required: true,
    options: [],
    linkageFn: ({ option, item, name }) => {
      if (name === 'city') {
        item.options = (option?.children || []).map((f) => ({ ...f, value: f.name, label: f.name }))
      }
    },
  },
])
</script>

```
