<template>
  <a-modal v-model:open="visible" :footer="null" :title="form.is_pass ? '审核通过' : '审核拒绝'">
    <a-textarea v-model:value="form.audit_opinion" :rows="4" placeholder="请输入审核意见" />
    <a-flex justify="center" class="mt-16">
      <a-space :size="16">
        <a-button type="primary" @click="handleOk" :loading="loading">确定{{ form.is_pass ? '通过' : '拒绝' }}</a-button>
        <a-button @click="handleCancel">取消</a-button>
      </a-space>
    </a-flex>
  </a-modal>
</template>

<script lang="ts" setup>
import { message } from 'ant-design-vue'

const visible = ref(false)
const loading = ref(false)

const emits = defineEmits(['audit'])

const form = ref({
  audit_opinion: '',
  is_pass: false,
})
// 确定
const handleOk = async () => {
  // 只有在审核拒绝时才要求必填审核意见
  if (!form.value.is_pass && !form.value.audit_opinion) {
    message.warning('请输入审核意见')
    return
  }
  if (form.value.audit_opinion.length > 500) {
    message.warning('输入内容不能超过500个字')
    return
  }
  await emits('audit', form.value)
  handleCancel()
}
// loading
const setLoading = (val: boolean) => {
  loading.value = val
}
// 取消
const handleCancel = () => {
  visible.value = false
  form.value = {
    audit_opinion: '',
    is_pass: false,
  }
}

// 打开
const open = (auditStatus: boolean) => {
  form.value = {
    audit_opinion: '',
    is_pass: auditStatus,
  }
  visible.value = true
}

defineExpose({
  open,
  close: handleCancel,
  setLoading,
})
</script>
<style scoped lang="scss"></style>
