import path from 'path'

import viteCompression from 'vite-plugin-compression'
import vue from '@vitejs/plugin-vue'
import UnoCSS from 'unocss/vite'
import autoImport from 'unplugin-auto-import/vite'
import { AntDesignVueResolver } from 'unplugin-vue-components/resolvers'
import Components from 'unplugin-vue-components/vite'
import { defineConfig, loadEnv } from 'vite'
import vueJsx from '@vitejs/plugin-vue-jsx'
import { VitePWA } from 'vite-plugin-pwa'

// https://vite.dev/config/
export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd())
  const { VITE_APP_BASE_API, VITE_APP_ENV, VITE_APP_VERSION } = env
  return {
    base: VITE_APP_ENV === 'production' ? './' : '/',
    plugins: [
      vue(),
      vueJsx(),
      UnoCSS(),
      autoImport({
        imports: [
          'vue',
          'vue-router',
          {
            from: 'vxe-table/types/all',
            imports: [
              'VxeFormPropTypes',
              'VxeFormEvents',
              'VxeGridPropTypes',
              'VxeGridListeners',
              'VxeButtonEvents',
              'VxeTableDefines',
              'VxePulldownInstance',
              'VxeGridProps',
              'VxeTablePropTypes',
              'VxeColumnPropTypes',
              'SizeType',
            ],
            type: true,
          },
          {
            from: 'ant-design-vue',
            imports: ['FormInstance', 'ModalProps', 'SelectProps'],
            type: true,
          },
        ],
        eslintrc: {
          enabled: true, // <-- this
          globalsPropValue: true,
        },
        dirs: ['src/hook', 'src/types'],
        dts: 'src/auto-imports.d.ts',
        vueTemplate: true,
      }),
      Components({
        dts: 'src/components.d.ts',
        resolvers: [
          AntDesignVueResolver({ importStyle: false }),
          (name) => {
            const names = ['EasyForm']
            if (names.includes(name)) {
              return path.resolve(__dirname, `src/components/${name}/index.tsx`).replaceAll('\\', '/')
            }
            const businessNames = ['SelectSupplier', 'OperationLog']
            if (businessNames.includes(name)) {
              return path.resolve(__dirname, `src/components/business/${name}.tsx`).replaceAll('\\', '/')
            }
          },
        ],
      }),
      VitePWA({
        workbox: {
          navigateFallbackDenylist: [/^\/api/, /^\/file/, /^index.html$/],
          cleanupOutdatedCaches: false,
        },
        registerType: 'autoUpdate',
        includeAssets: ['favicon.ico', 'robots.txt'],
        devOptions: {
          enabled: true,
          type: 'module',
        },
        manifest: {
          name: 'SRM',
          short_name: 'SRM',
          start_url: '/',
          display: 'standalone',
          background_color: '#ffffff',
          theme_color: '#1890FF',
          icons: [
            {
              src: '/pwa-192x192.png',
              sizes: '192x192',
              type: 'image/png',
            },
            {
              src: '/pwa-512x512.png',
              sizes: '512x512',
              type: 'image/png',
            },
          ],
        },
      }),
      viteCompression({
        algorithm: 'gzip',
        deleteOriginFile: false, // 不删除源文件
      }),
    ],
    css: {
      // postcss: {
      //   plugins: [postCssPxToRem({ rootValue: 16, propList: ['*'] })],
      // },
      preprocessorOptions: {
        scss: {
          additionalData: `
            @use "@/assets/style/mixin.scss" as *;
          `,
        },
      },
    },
    resolve: {
      alias: {
        '@': path.resolve(__dirname, 'src'),
        '@img': path.resolve(__dirname, 'src/assets/image'),
      },
    },
    server: {
      port: 8899,
      host: true,
      open: true,
      proxy: {
        '/api': {
          target: VITE_APP_BASE_API,
          changeOrigin: true,
          rewrite: (p) => p.replace(/\/api/, '/'),
        },
      },
    },
    build: {
      minify: 'terser',
      terserOptions: {
        compress: {
          // drop_console: true,
          drop_debugger: true,
          pure_funcs: [
            'console.debug',
            // 'console.error',
            'console.log',
            // 'console.info',
            'console.warn',
            'console.dir',
            'console.dirxml',
            'console.table',
            'console.trace',
            'console.group',
            'console.groupCollapsed',
            'console.groupEnd',
            'console.clear',
            'console.count',
            'console.countReset',
            'console.assert',
            'console.profile',
            'console.profileEnd',
            'console.time',
            'console.timeLog',
            'console.timeEnd',
            'console.timeStamp',
          ],
        },
      },
      chunkSizeWarningLimit: 1500,
      rollupOptions: {
        output: {
          manualChunks: {
            'vue-core': ['vue', 'vue-router'],
            'ant-design-vue': ['ant-design-vue'],
            charts: ['@antv/g2'],
            utils: ['dayjs', 'axios'],
            common: ['@/components/BaseTable.vue', '@/components/Form.vue'],
          },
          chunkFileNames: `js/[name]-[hash]-${VITE_APP_VERSION}.js`,
          entryFileNames: `js/[name]-[hash]-${VITE_APP_VERSION}.js`,
          assetFileNames: `[ext]/[name]-[hash]-${VITE_APP_VERSION}.[ext]`,
        },
      },
    },
  }
})
