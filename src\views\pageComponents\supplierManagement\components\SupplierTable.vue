<template>
  <div>
    <vxe-table
      :loading="tableLoading"
      :border="true"
      ref="tableRef"
      size="mini"
      :row-config="{ keyField: keyField || '_X_ROW_KEY', isHover: true }"
      :custom-config="{ mode: 'popup' }"
      :data="data"
      :show-header-overflow="true"
      :show-footer-overflow="true"
      :column-config="{ resizable: true }"
      :virtual-x-config="{ enabled: true, gt: 0 }"
      :virtual-y-config="{ enabled: true, gt: 0 }"
      class="tableBoxwidth"
      :checkbox-config="{ reserve: true }"
      @checkbox-all="selectChangeEvent"
      @checkbox-change="selectChangeEvent"
      min-height="0"
      stripe
      v-bind="$attrs"
    >
      <slot name="column">
        <slot name="append"></slot>
        <template v-for="i in tableKey" :key="i.field">
          <vxe-column v-bind="i">
            <template v-if="$slots[i.field + '-header']" #header="attr">
              <slot :name="i.field + '-header'" v-bind="attr" :item="i" />
            </template>
            <template v-else-if="i.required" #header="attr">
              <span class="text-red">*</span>
              {{ attr.column.title }}
            </template>

            <template v-if="$slots[i.field]" #default="attr">
              <slot :name="i.field" v-bind="attr" :item="i" />
            </template>
            <template v-else-if="i.dataType" #default="{ row, rowIndex }">
              <a-input
                v-if="i.dataType === 'input'"
                v-model:value="row[i.field]"
                placeholder="请输入"
                @change="row.data_status = form.supplier_id ? (row.id ? 3 : 2) : 1"
                :maxlength="200"
                :disabled="!row.is_view && !isView"
                :status="i.required && !row[i.field] && errorKeyList.find((f) => f == i.field + rowIndex) ? 'error' : ''"
              />
              <a-input v-else-if="i.dataType === 'contact'" v-model:value="row[i.field]" placeholder="请输入" @change="row.data_status = form.supplier_id ? (row.id ? 3 : 2) : 1" :maxlength="200" />
              <a-input-number
                v-else-if="i.dataType === 'number'"
                class="!w-full"
                type="number"
                v-model:value="row[i.field]"
                placeholder="请输入"
                :controls="false"
                @input="(val) => inputChange(val, row, i.field)"
                @change="row.data_status = form.supplier_id ? (row.id ? 3 : 2) : 1"
                :maxlength="200"
                :disabled="!row.is_view && !isView"
              ></a-input-number>
              <a-select
                class="w-full"
                :dropdownMatchSelectWidth="false"
                v-else-if="i.dataType === 'select'"
                v-model:value="row[i.field]"
                :options="i.options"
                placeholder="请选择"
                @change="row.data_status = form.supplier_id ? (row.id ? 3 : 2) : 1"
                :disabled="!row.is_view && !isView"
              />
            </template>
          </vxe-column>
        </template>
      </slot>
    </vxe-table>
    <input ref="focusInput" class="absolute top-0 left-0 w-0 h-0 opacity-0 pointer-events-none" />
  </div>
</template>

<script setup lang="ts">
// import { px2 } from '@/utils'
import { VxeColumnProps, VxeTableInstance } from 'vxe-table'

const form = inject('form') as any

const tableLoading = ref(false)
const tableRef = ref<VxeTableInstance>()

const data = defineModel<any[]>('data', { required: true })

const props = defineProps({
  tableKey: {
    type: Array as PropType<VxeColumnProps[] | any[]>,
  },
  checkCb: {
    type: Function,
  },
  keyField: {
    type: String,
    default: '_X_ROW_KEY',
  },
  isView: {
    type: Boolean,
    default: false,
  },
  errorKeyList: {
    type: Array as PropType<string[]>,
    default: () => [],
  },
})

onMounted(() => {
  window.addEventListener('resize', updateWindowWidth)
})
const windowScreenWidth = ref(window.innerWidth)
const updateWindowWidth = () => {
  windowScreenWidth.value = window.innerWidth
}

onBeforeUnmount(() => {
  window.removeEventListener('resize', updateWindowWidth)
})

const checkItemsArr = ref([] as any)
const selectChangeEvent = () => {
  const $table = tableRef.value
  // 当前页选中的数据
  const currentSelectedRows = $table?.getCheckboxRecords() || []
  // 其他页选中的数据
  const otherSelectedRows = $table?.getCheckboxReserveRecords() || []
  checkItemsArr.value = [...currentSelectedRows, ...otherSelectedRows]
  if (props.checkCb) props.checkCb(checkItemsArr.value)
}

const inputChange = (value, row, field) => {
  nextTick(() => {
    row[field] = value.match(/^[0-9]*\.([0-9]{0,2})|^[0-9]*/)[0]
  })
}

defineExpose({ checkItemsArr, tableRef })
</script>
<style lang="scss" scoped>
.tableBox {
  position: relative;
  flex: 1;
  border-bottom: 1px solid #ddd;

  .toolbarBtn {
    position: absolute;
    right: 0;
    bottom: 100%;
    padding: 0;
    padding-bottom: 0.6em;
    margin-block: -5px;
  }

  .box {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;

    // overflow-y: scroll;
    .editbtn {
      color: #1890ff;
      cursor: pointer;
    }

    .movesea {
      margin-left: 20px;
      color: #1890ff;
      cursor: pointer;
    }
  }
}

.paginationBox {
  display: flex;
  align-items: center;
  margin-top: 0.83rem;

  .totalBox {
    display: flex;
    align-items: flex-end;
    margin-left: 20px;
    color: #000;

    .text {
      margin-right: 8px;
      font-size: 14px;
    }

    .total {
      font-size: 16px;
      color: #1890ff;
    }
  }
}
</style>
