<template>
  <a-button type="link" v-if="btn" class="pl-10! pr-10!" :class="disabled ? 'text-gray-500 w-auto' : 'color cursor-pointer w-auto'" @click="handleOpen">
    {{ text }}
    <CloudUploadOutlined />
  </a-button>

  <a-modal :width="600" v-model:open="open" @ok="handleConfirm" title="上传文件" :bodyStyle="{ padding: 0 }" :maskClosable="false" :closable="false">
    <div class="p-4">
      <a-upload-dragger
        :fileList="fileList"
        name="file"
        :multiple="multiple"
        :before-upload="beforeUpload"
        @drop="handleDrop"
        @mouseenter="enter"
        @mouseleave="leave"
        @remove="handleRemove"
        @download="downloadFile"
        :show-upload-list="{ showDownloadIcon: true, showRemoveIcon: true }"
        :accept="accept"
        @preview="previewFile"
      >
        <div class="h-240 center flex-col">
          <p class="ant-upload-drag-icon">
            <inbox-outlined></inbox-outlined>
          </p>
          <p class="ant-upload-text">点击、拖拽文件或将文件粘贴到这里上传</p>
          <p class="ant-upload-hint">{{ `支持扩展名：${accept}` }}</p>
          <slot name="tip"></slot>
        </div>
      </a-upload-dragger>
    </div>
    <a-image
      :width="200"
      :style="{ display: 'none' }"
      :preview="{
        visible: imgVisible,
        onVisibleChange: setImgVisible,
      }"
      :src="preview"
    />
  </a-modal>
</template>

<script lang="ts" setup>
import { CloudUploadOutlined, InboxOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { PropType, ref } from 'vue'

import { FileUpload, GetFileUrl, GetDownloadUrl } from '@/servers/Common'

export interface UploadItem {
  id: string
  name: string
}

const props = defineProps({
  btn: {
    default: true,
    type: Boolean,
  },
  multiple: {
    default: true,
    type: Boolean,
  },
  defaultFileList: {
    default: [] as any,
    type: Array,
  },
  value: {
    default: [] as any,
    type: Array,
  },
  defaultText: {
    default: '点击上传附件',
    type: String,
  },
  maxSize: {
    default: 100 * 1024 * 1024,
    type: Number,
  },
  accept: {
    default: '.jpg,.png,.pdf,.docx,.xlsx,.xls,.csv',
    type: String,
  },
  disabled: {
    default: false,
    type: Boolean,
  },
  module: {
    default: UploadFileModuleEnum.Common,
    type: Number as PropType<UploadFileModuleEnum>,
  },
})

const text = computed(() => (props.value?.length ? `已选择${props.value.length}个文件` : props.defaultText))

const emit = defineEmits(['update:value', 'change'])
const open = ref(false)
const handleOpen = () => {
  if (props.disabled) return
  open.value = true
  const data = JSON.parse(JSON.stringify(props.value))
  fileList.value = data.map((v: any) => ({
    ...v,
    name: v.org_name || v.name,
    status: 'done',
    url: v.url || 'test',
  }))
  console.log(fileList.value)
}
const handleConfirm = () => {
  open.value = false
  const list = fileList.value.filter((v) => v.status === 'done')
  const val = fileList.value.length ? list : []

  emit('update:value', val)
  emit('change', val)
}

type fileItem = {
  id: string
  uid?: string
  name: string
  status: string
  url: string
  oa_file_id?: string
}

const fileList = ref([] as fileItem[])

function handleDrop(e: DragEvent) {
  console.log(e)
}

function handleRemove(file: fileItem) {
  fileList.value = fileList.value.filter((v) => v.id !== file.id)
}

const beforeUpload = (file) => {
  // 判断名称是否重复，如重复则提示
  if (fileList.value.some((v) => v.name === file.name && v.status === 'done')) {
    message.warning(`文件 ${file.name} 已存在`)
    return false
  }

  if (file.size > props.maxSize) {
    message.warning(`文件 ${file.name} 大小超过限制，最大支持 ${props.maxSize / 1024 / 1024}MB`)
    return false
  }

  file.status = 'pending'
  if (fileList.value.some((v) => v.name === file.name && v.status === 'error')) {
    fileList.value = fileList.value.filter((v) => v.name !== file.name)
  }
  fileList.value.push(file)

  upload()
  return false
}

const upload = () => {
  fileList.value.forEach((file: any) => {
    if (file.status !== 'pending') return
    file.status = 'uploading'
    const formData = new FormData()
    formData.append('file', file)
    formData.append('module', props.module)
    FileUpload(formData)
      .then((res) => {
        message.success(`上传文件 ${file.name} 成功`)
        fileList.value = fileList.value.map((item) => {
          if (item.uid === file.uid) {
            return {
              ...item,
              id: res.data.id,
              name: res.data.org_name,
              url: res.data.url,
              status: 'done',
            }
          }
          return item
        })
        console.log(fileList.value)
      })
      .catch(() => {
        message.error(`上传文件 ${file.name} 失败`)
        fileList.value = fileList.value.map((item) => (item.uid === file.uid ? { ...item, status: 'error' } : item))
      })
  })
}

const preview = ref('')
const imgVisible = ref(false)
const setImgVisible = (value) => {
  imgVisible.value = value
}
const previewFile = async (file) => {
  if (file.url === 'test') {
    // const res = await GetFileUrl({ fileId: file.id })
    let res: any = {}
    if (/(mp4|mp3|avi|mov|wmv|flv|webm|mkv|m4v|3gp|rmvb|wav|aac|ogg|flac|wma|m4a)$/i.test(file.name)) {
      res = await GetDownloadUrl({ fileId: file.id })
    } else {
      res = await GetFileUrl({ fileId: file.id })
    }
    file.url = res.data
  }

  if (file.url.endsWith('.png') || file.url.endsWith('.jpg') || file.url.endsWith('.jpeg')) {
    preview.value = file.url
    nextTick(() => {
      setImgVisible(true)
    })
  } else window.open(file.url, '_blank')
}

const downloadFile = async (file) => {
  if (file.url === 'test') {
    const res = await GetFileUrl({ fileId: file.id })
    file.url = res.data
  }
  window.open(file.url, '_blank')
}

const enter = () => {
  // 监听 键盘粘贴快捷键
  document.onpaste = (e) => {
    const clipboardData: any = e.clipboardData
    const items = clipboardData.items
    if (!items) return
    for (let i = 0; i < items.length; i++) {
      const item = items[i]
      // 获取剪切板的文件
      if (item.kind === 'file') {
        const blob = item.getAsFile()
        const file = new File([blob], blob.name, { type: blob.type }) as any
        file.status = 'pending'
        beforeUpload(file)
      }
    }
  }
}
const leave = () => {
  // 清除监听
  document.onpaste = null
}
</script>

<style lang="scss" scoped>
// 修复上传列表容器高度问题
:deep(.ant-upload-list-item-container) {
  height: auto !important;
  min-height: 22px !important;
}

:deep(.ant-motion-collapse) {
  height: auto !important;
}

:deep(.ant-upload-list-item-container.ant-motion-collapse) {
  height: 32px !important;
  min-height: 32px !important;
}

:deep(.ant-upload-list .ant-upload-list-item .ant-upload-list-item-container) {
  height: 32px !important;
  min-height: 32px !important;
}

:deep(.ant-upload-list-item-container[style*='height: 0px']) {
  height: 32px !important;
  min-height: 32px !important;
}

:global(.ant-upload-list-item-container) {
  height: 32px !important;
  min-height: 32px !important;
}
</style>
