<template>
  <a-drawer :footer="logVisble ? undefined : false" v-model:open="detailVisible" width="45vw" title="查看角色" placement="right" :maskClosable="false" :footer-style="{ textAlign: 'left' }">
    <div class="detailBox">
      <LoadingOutlined v-show="detailloading" class="loadingIcon" />
      <a-form v-if="!detailloading && target">
        <div class="drawer-title">角色</div>
        <a-form-item label="角色名称">
          <span class="detailValue">{{ target.role_name }}</span>
        </a-form-item>
        <a-form-item label="适用范围">
          <span class="detailValue">{{ target.scope == 1 ? '内部' : '外部' }}联系人</span>
        </a-form-item>
        <a-form-item label="状态">
          <span class="detailValue">{{ target.status == 0 ? '停用' : '启用' }}</span>
        </a-form-item>
        <div class="drawer-title">其他信息</div>
        <div style="display: flex">
          <a-form-item label="创建时间">
            <div class="detailValue w200">{{ target.create_at }}</div>
          </a-form-item>
          <a-form-item label="创建人">
            <a-tooltip :overlayStyle="{ maxWidth: 'none' }">
              <template #title>
                <div>用户名称：{{ target.creator_name }}</div>
                <!-- <div>所属{{ target.scope == 1 ? '公司' : '客户' }}：{{ target.create_user_company ? target.create_user_company : '--' }}</div> -->
                <div>所在部门：{{ target.created_department_name ? target.created_department_name : '--' }}</div>
                <div>
                  岗
                  <span style="visibility: hidden">占位</span>
                  位：{{ target.created_job_name ? target.created_job_name : '--' }}
                </div>
              </template>
              <div style="display: flex; align-items: center">
                <span class="detailValue">{{ target.creator_name ? target.creator_name : '--' }}</span>
                <span v-if="target.created_department_name || target.created_job_name" class="detailValueDescription">
                  （
                  <span v-if="target.created_job_name">{{ target.created_job_name }}&nbsp;|&nbsp;</span>
                  <span v-if="target.created_department_name">
                    {{ target.created_department_name.length > 16 ? target.created_department_name.slice(0, 16) + '...' : target.created_department_name }}
                  </span>
                  ）
                </span>
              </div>
            </a-tooltip>
          </a-form-item>
        </div>
        <div style="display: flex">
          <a-form-item label="最后修改时间">
            <div class="detailValue w200">{{ target.update_at }}</div>
          </a-form-item>
          <a-form-item label="最后修改人">
            <a-tooltip :overlayStyle="{ maxWidth: 'none' }">
              <template #title>
                <div>用户名称：{{ target.modifier_name }}</div>
                <!-- <div>所属{{ target.update_user_scope == 1 ? '公司' : '客户' }}：{{ target.update_user_company ? target.update_user_company : '--' }}</div> -->
                <div>所在部门：{{ target.modified_department_name ? target.modified_department_name : '--' }}</div>
                <div>
                  岗
                  <span style="visibility: hidden">占位</span>
                  位：{{ target.modified_job_name ? target.modified_job_name : '--' }}
                </div>
              </template>
              <div style="display: flex; align-items: center">
                <span class="detailValue">{{ target.modifier_name ? target.modifier_name : '--' }}</span>
                <span v-if="target.modified_department_name || target.modified_job_name" class="detailValueDescription">
                  （
                  <span v-if="target.modified_job_name">{{ target.modified_job_name }}&nbsp;|&nbsp;</span>
                  <span v-if="target.modified_department_name">
                    {{ target.modified_department_name.length > 16 ? target.modified_department_name.slice(0, 16) + '...' : target.modified_department_name }}
                  </span>
                  ）
                </span>
              </div>
            </a-tooltip>
          </a-form-item>
        </div>
      </a-form>
    </div>
    <template #footer>
      <a-button id="roleManagementLog" @click="log">日志</a-button>
    </template>
  </a-drawer>
  <!-- 日志 -->
  <log-drawer ref="logDrawerRef" />
</template>

<script lang="ts" setup>
import { LoadingOutlined } from '@ant-design/icons-vue'

import LogDrawer from './LogDrawer.vue'

import { Detail } from '@/servers/Role'

const logDrawerRef = ref()
const detailVisible = ref(false)
const detailloading = ref(false)
const logVisble = ref(false)

const target = ref<any>(null)
const open = (id, boolean) => {
  target.value = null
  detailloading.value = true
  detailVisible.value = true
  logVisble.value = boolean
  Detail({ id })
    .then((res) => {
      target.value = res.data
      detailloading.value = false
    })
    .catch(() => {
      detailloading.value = false
    })
}
const log = () => {
  logDrawerRef.value?.open(target.value)
}
// 暴露方法
defineExpose({
  open,
})
</script>

<style lang="scss" scoped>
::v-deep(.ant-form-item) {
  align-items: flex-start;

  .ant-form-item-label {
    display: flex;
    justify-content: flex-end;
    width: 110px;
    min-width: 110px;
    margin-right: 30px;

    label {
      &::after {
        display: none !important;
      }
    }
  }
}

.detailTitle {
  padding-left: 12px;
  margin-bottom: 20px;
  font-weight: bold;
  color: #000;
}

.w350 {
  width: 350px;
}

.w250 {
  width: 250px;
}

.w150 {
  width: 150px;
}

.w200 {
  width: 200px;
}

.description {
  padding-left: 20px;
  font-size: 12px;
  color: #00000080;
  white-space: nowrap;
}

.detailValueDescription {
  font-size: 12px;
  color: #00000080;
}

.detailBox {
  padding-top: 12px;

  .loadingIcon {
    font-size: 30px;
    color: #1890ff;
  }
}
</style>
