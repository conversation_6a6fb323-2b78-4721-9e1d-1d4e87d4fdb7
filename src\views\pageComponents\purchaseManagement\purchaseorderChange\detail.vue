<template>
  <div class="flex flex-col h-full main">
    <EasyForm v-if="type" :mode="mode" ref="formRef" :formItems="formItems" v-model:form="formData">
      <!-- 明细信息 -->
      <template #details>
        <div class="relative">
          <a-button class="absolute top-10 right-0 z-10" :icon="h(SettingOutlined)" @click="handleSetting" type="text">设置</a-button>
          <a-tabs v-model:activeKey="activeTabKey" class="mb-20 custom-tabs" @change="handleActiveChange">
            <a-tab-pane key="purchase" tab="采购明细">
              <div>
                <a-button class="mr-10 mb-20" @click="handleAddPro" v-if="type === 'new' && !isSpecialEditKeys?.length" type="primary">添加商品</a-button>
              </div>
              <div class="mb-20">
                <BaseTable v-bind="tableConfig" :data="tableData">
                  <template #number="{ row, column, rowIndex }" v-if="['new', 'edit'].includes(type)">
                    <a-input-number
                      class="order-input-number w-full"
                      v-model:value="row[column.field]"
                      placeholder="请输入"
                      :controls="false"
                      :precision="column.params?.precision ?? 0"
                      :min="0"
                      :step="0.01 * Math.pow(10, -(column.params?.precision ?? 0))"
                      :disabled="
                        (['gift_purchase_quantity', 'process_fee', 'mould_fees', 'discount_amount'].includes(column.field) && isSRS) ||
                        isSpecialEditKeys.includes(column.field) ||
                        (calculatePriceIndex && calculatePriceIndex?.[0] !== `${rowIndex}`)
                      "
                      @change="changePrice(column.field, row, rowIndex)"
                    >
                      <template #addonAfter v-if="calculatePriceIndex?.[0] === `${rowIndex}` && calculatePriceIndex?.[1] === `${column.field}`">
                        <LoadingOutlined class="link text-14px" />
                      </template>
                    </a-input-number>
                  </template>
                  <template #upPice="{ row, column }" v-if="['new', 'edit'].includes(type)">
                    <div class="upPice">
                      <div>{{ row[column.field] }}</div>
                      <a-button @click="setHistory(row)" type="link">查看历史</a-button>
                    </div>
                  </template>

                  <template #remark="{ row, column }" v-if="['new', 'edit'].includes(type)">
                    <a-input v-model:value="row[column.field as any]" placeholder="请输入" :maxlength="2000" />
                  </template>

                  <template #predict_delivery_date="{ row, column }" v-if="['new', 'edit'].includes(type)">
                    <a-date-picker :valueFormat="'YYYY-MM-DD'" format="YYYY-MM-DD" v-model:value="row[column.field]" :disabled="isSpecialEditKeys.includes(column.field)" style="width: 100%" />
                  </template>

                  <template #tax_rate_id="{ row, column, rowIndex }">
                    <div v-if="!['new', 'edit'].includes(type)">
                      {{ (taxRateOption || []).find((f) => f.value === row['tax_rate_id'])?.label }}
                    </div>
                    <a-select v-else v-model:value="row[column.field]" :options="taxRateOption" style="width: 100px" @change="changePrice(column.field, row, rowIndex)"></a-select>
                  </template>

                  <template #operation="{ row, rowIndex }">
                    <a-button v-if="type === 'new' && !row.has_take_quantity && !isSpecialEditKeys?.length" class="ml mr" @click="deleteItem(row, rowIndex)">删除</a-button>
                    <a-button v-if="$route.params.id" @click="openChildrenList(row)">拍单子表</a-button>
                  </template>
                </BaseTable>
              </div>
            </a-tab-pane>

            <a-tab-pane key="inbound" tab="入库明细">
              <vxe-grid ref="bookingGridRef" v-bind="bookingGridOptions"></vxe-grid>
            </a-tab-pane>

            <a-tab-pane key="payment" tab="付款明细" v-if="formData.type !== 4">
              <a-table :columns="paymentColumns.filter((v) => v.is_show)" :data-source="paymentTableData" :pagination="false" size="small" bordered :scroll="{ x: 1750, y: 700 }">
                <template #bodyCell="{ column, record, index }">
                  <template v-if="column.key === 'key'">{{ index + 1 }}</template>
                  <template v-if="column.key === 'prepayment_amount'">
                    <span>{{ Number(record[column.dataIndex as string]).roundNext(2) }}</span>
                  </template>
                  <template v-if="column.key === 'deduct_amount'">
                    <span>{{ Number(record[column.dataIndex as string]).roundNext(2) }}</span>
                  </template>
                  <template v-if="column.key === 'the_actual_amount'">
                    <span>{{ Number(record[column.dataIndex as string]).roundNext(2) }}</span>
                  </template>
                  <template v-if="column.key === 'expected_payment_date'">
                    <span>{{ record[column.dataIndex as string] ? dayjs(record[column.dataIndex as string]).format('YYYY-MM-DD') : '-' }}</span>
                  </template>
                </template>
              </a-table>
            </a-tab-pane>

            <a-tab-pane key="refund" tab="退库明细">
              <a-table :columns="refundColumns.filter((v) => v.is_show)" :data-source="refundTableData" :pagination="false" size="small" bordered :scroll="{ x: 1750, y: 700 }">
                <template #bodyCell="{ column, record, index }">
                  <template v-if="column.key === 'key'">{{ index + 1 }}</template>
                  <template v-if="column.key === 'image_url'">
                    <EasyImage :src="record[column.dataIndex as string]"></EasyImage>
                  </template>
                  <template v-if="column.key === 'return_amount'">
                    <span>{{ Number(record[column.dataIndex as string]).roundNext(2) }}</span>
                  </template>
                  <template v-if="column.key === 'refund_warehouse_status'">
                    <a-tag :color="record[column.dataIndex as string] === 1 ? 'green' : 'blue'">
                      {{ record[column.dataIndex as string] === 1 ? '已退库' : '退库中' }}
                    </a-tag>
                  </template>
                </template>
              </a-table>
            </a-tab-pane>
          </a-tabs>
        </div>
      </template>
    </EasyForm>
    <div class="mb-50">&nbsp;</div>
    <div class="submit-row" v-if="initFlag">
      <!-- 新增 & 编辑 & 批量创建 -->
      <template v-if="['new', 'edit'].includes(type)">
        <a-button class="ml mr" :disabled="!!calculatePriceIndex" :loading="submitLoading" type="primary" @click="handleSubmit(true)">提交审核</a-button>
        <a-button class="ml mr" :disabled="!!calculatePriceIndex" :loading="submitLoading" @click="handleSubmit(false)">保存暂不提交</a-button>
        <a-button class="ml mr" v-if="formData.id && [10, 95].includes(formData.change_audit_status)" @click="reviewType()">审核记录</a-button>
      </template>
      <!-- 查看 -->
      <template v-if="type == 'detail'">
        <a-button class="ml mr" @click="closePage()">关闭</a-button>
        <a-button class="ml mr" @click="reviewType()">审核记录</a-button>
        <a-button class="ml mr" @click="openLog">日志</a-button>
      </template>
      <!-- 审核 -->
      <template v-if="type == 'review'">
        <a-button class="ml mr" @click="reviewType(1)">审核通过</a-button>
        <a-button class="ml mr" @click="reviewType(2)">审核拒绝</a-button>
        <a-button class="ml mr" @click="reviewType()">审核记录</a-button>
      </template>
    </div>
    <SelectProduct ref="selectProductRef" @confirm="onAddProduct" />
    <historyModal ref="historyModalRef"></historyModal>
    <SelectSupplier ref="selectSupplierRef" hide />
    <childrenListDlg ref="ChildrenListDlgRef" />
    <reviewTimer ref="reviewTimerRef"></reviewTimer>
    <review ref="reviewRef" @ClickReview="ClickReview"></review>
    <OperationLog ref="operationLogRef"></OperationLog>
    <AntTableSetting ref="antTableSettingRef" v-model:visible="visible" @save="handleSaveTableSetting" v-if="showSetting" v-model:columns="tableSettingKeys" @reset="handleReSet" />
  </div>
</template>
<script setup lang="ts">
import { message, Button } from 'ant-design-vue'
import dayjs from 'dayjs'
import { SettingOutlined } from '@ant-design/icons-vue'
import type { VxeComponentSizeType } from 'vxe-table'

import { generateUUID } from '@/utils'

import { setFormItems, setColumns, setInnerColumns, setBookingColumns, setPaymentColumns, setRefundColumns } from './detail.data'

import historyModal from '../components/purchaseorderManagementComponents/historyModal.vue'
import childrenListDlg from '../components/purchaseorderManagementComponents/childrenListDlg.vue'
import reviewTimer from '../components/purchaseorderManagementComponents/reviewTimer.vue'
import review from '../components/review.vue'
import AntTableSetting from '../purchaseOrderContent/antTableSetting.vue'

import { GetPurchaseDetailBookingList } from '@/servers/ReservationApi'
import {
  GetPurchaseOrderProductInfo,
  AddPurchaseOrderChange,
  GetPurchaseOrderDetail,
  GetPurchaseOrderDetailList,
  GetPurchaseOrderPaymentOrderList,
  GetPurchaseOrderReturnApplyOrderList,
} from '@/servers/PurchaseManage'
import { GetTaxRateSelect, GetPageMRPSupplierCompanySelect } from '@/servers/BusinessCommon'
import { GetSystemConfigValue } from '@/servers/System'
import { GetPurchaseOrderChange, UpdatePurchaseOrderChange, Audit } from '@/servers/PurchaseOrderchange'
import { GetTableConfig, SetTableConfig } from '@/servers/Common'

const route = useRoute()
const visible = ref(false)
const showSetting = ref(false)
const tableSettingKeys = ref()
const type = ref()
const formRef = ref()
const { closePage } = usePage({ initMethod: () => initMethod(), formRef })

const formData = ref<Record<string, any>>({})
const formItems = ref<EasyFormItemProps[]>([])
formItems.value = setFormItems(
  formData,
  formRef,
  {
    type: (val) => onTypeChange(val),
    company_supplier_id: () => {
      tableData.value.forEach((item, index) => {
        InputPrice('purchase_quantity', item, index)
      })
    },
    company_supplier_id_ck: ({ item }) => {
      if (item.disabled) return
      selectSupplierRef.value.open(
        {
          key: 'company_supplier_id',
          mode: 'single',
          labelInValue: false,
          api: GetPageMRPSupplierCompanySelect,
          apiParams: {
            type: formData.value.type === 2 ? 2 : 1,
            is_contains_srs: true,
          },
          callback: (data) => {
            onSupplierIdChange(data, true)
          },
        },
        item.options,
      )
    },
    settlementTypeChange: (val) => {
      // 当结算方式改变时，动态更新预付比例字段的required状态
      const isPrepayment = val === 4
      formRef.value?.changeItem('prepayment_ratio', {
        required: isPrepayment,
      })

      // 重新验证预付比例字段
      nextTick(() => {
        formRef.value?.validate(['prepayment_ratio'])
      })
    },
  },
  route.params.id,
)

const onTypeChange = (val) => {
  for (const name of ['consignee', 'phone_number', 'shipments_address', 'province', 'city', 'area', 'warehourse_id']) {
    formRef.value.changeItem(name, { disabled: name === 'warehourse_id' ? false : val == 2 })
  }
}

const selectSupplierRef = ref()

const tableData = ref<any>([])
const columns = ref<Record<string, any>[]>([])
const innerColumns = ref<Record<string, any>[]>([
  ...setInnerColumns,
  {
    title: '操作',
    key: 'operation',
    align: 'center',
    fixed: 'right',
    render: ({ row, rowIndex }) => {
      return type.value === 'new' && h(Button, { style: { height: '24px', padding: '0 12px' }, onClick: () => deleteChildItem(row, rowIndex) }, { default: () => '删除' })
    },
  },
])
const tableConfig = computed(() => {
  return {
    stripe: false,
    hideTop: true,
    hidePagination: true,
    autoSearch: false,
    tableColumns: columns.value,
    boxCls: ['relative!', 'mt-10', 'mb-20'],
    maxHeight: 600,
    minHeight: 106,
    height: null,
    rowConfig: {
      keyField: 'k3_sku_id',
      isHover: true,
      height: 55,
    },
    keyField: 'k3_sku_id',
    expandData: ({ row }) => row.purchaseOrderApplyDetails,
    expandColumns: innerColumns.value,
    expandCellMinWidth: 150,
  }
})

const bookingTableData = ref<any>([])
const bookingColumns = ref<any>(setBookingColumns)
const bookingGridRef = ref()
const bookingGridOptions = computed(() => ({
  maxHeight: 600,
  minHeight: 100,
  border: true,
  size: 'mini' as VxeComponentSizeType,
  columnConfig: { resizable: true },
  virtualXConfig: {
    enabled: true,
    gt: 20,
  },
  virtualYConfig: {
    enabled: true,
    gt: 20,
  },
  rowConfig: {
    height: 34,
    isHover: true,
  },
  columns: bookingColumns.value,
  data: bookingTableData.value,
  spanMethod: ({ row, column }) => {
    const bookingMergeFields = [
      'key',
      'image_url',
      'sku_name',
      'k3_sku_id',
      'jst_sku_id',
      'type_specification',
      'valuation_unit',
      'booking_number',
      'warehouse_name',
      'audit_status',
      'purchase_scheduled_quantity',
      'gift_scheduled_quantity',
      'scheduled_quantity',
      'box_num',
      'create_at',
      'logistics_company',
      'tracking_number',
      'scheduled_arrival_time',
    ]
    if (bookingMergeFields.includes(column.field)) {
      if (row.index === 0) {
        return { rowspan: row.count, colspan: 1 }
      }
      return { rowspan: 0, colspan: 0 }
    }
    return { rowspan: 1, colspan: 1 }
  },
}))

// 新增tabs相关变量
const activeTabKey = ref('purchase')

// 付款明细相关
const paymentTableData = ref<any>([])
const paymentColumns = ref(setPaymentColumns)

// 退款明细相关
const refundTableData = ref<any>([])
const refundColumns = ref(setRefundColumns)

// 选择商品
const selectProductRef = ref()

const operationLogRef = ref()
const handleSaveTableSetting = (tableKeys) => {
  const data = tableKeys.map((item) => {
    return {
      freeze: item.fixed == 'left' ? 1 : item.fixed == 'right' ? 2 : 0,
      name: item.name,
      width: item.width,
      sort_type: '',
      sort_index: 0,
      key: item.dataIndex || item.key,
      is_sort: false,
      index: item.index,
      is_show: item.is_show,
    }
  })
  tableSettingKeys.value = tableKeys
  if (activeTabKey.value === 'purchase') {
    columns.value = tableKeys
    saveSetting({ maps: data, page_type: 1701 })
    return
  }
  if (activeTabKey.value === 'inbound') {
    bookingColumns.value = tableKeys
    saveSetting({ maps: data, page_type: 1702 })
    return
  }
  if (activeTabKey.value === 'payment') {
    paymentColumns.value = tableKeys
    saveSetting({ maps: data, page_type: 1703 })
    return
  }
  if (activeTabKey.value === 'refund') {
    refundColumns.value = tableKeys
    saveSetting({ maps: data, page_type: 1704 })
  }
}
const handleReSet = () => {
  const obj: any = {
    data: null,
    page_type: null,
  }
  if (activeTabKey.value === 'purchase') {
    columns.value = setColumns()
    tableSettingKeys.value = columns.value
    obj.data = columns.value
    obj.page_type = 1701
  }
  if (activeTabKey.value === 'inbound') {
    bookingColumns.value = setBookingColumns
    tableSettingKeys.value = bookingColumns.value.map((f) => ({ ...f, key: f.field, name: f.title }))
    obj.data = tableSettingKeys.value
    obj.page_type = 1702
  }
  if (activeTabKey.value === 'payment') {
    paymentColumns.value = setPaymentColumns
    tableSettingKeys.value = paymentColumns.value
    obj.data = paymentColumns.value
    obj.page_type = 1703
  }
  if (activeTabKey.value === 'refund') {
    refundColumns.value = setRefundColumns
    tableSettingKeys.value = refundColumns.value
    obj.data = refundColumns.value
    obj.page_type = 1704
  }
  const maps = obj.data.map((item) => {
    return {
      freeze: item.fixed == 'left' ? 1 : item.fixed == 'right' ? 2 : 0,
      name: item.name || item.title,
      width: item.width,
      sort_type: '',
      sort_index: 0,
      key: item.dataIndex || item.key,
      is_sort: false,
      index: item.index,
      is_show: item.is_show,
    }
  })
  saveSetting({ maps, page_type: obj.page_type })
}
const saveSetting = async (obj: any) => {
  await SetTableConfig(obj)
  message.success('保存成功')
}
const handleAddPro = () => {
  if (!formData.value.company_supplier_id) {
    message.info('请先选择供应商子公司')
    return
  }
  selectProductRef.value.open({
    search: ['商品编号/聚水潭编号', '商品名称', '供应商名称'],
    left: ['商品分类', '商品主图', '商品编码', '商品名称', '规格型号', '款式编码', '材质', '换算值', '换算公式', '采购单位', '标准装箱数', '聚水潭编号', '默认供应商'],
  })
}
const onAddProduct = async (arr) => {
  const paramList: any[] = arr.map((item) => {
    return {
      k3_sku_id: (item as any).sku_id,
      jst_sku_id: (item as any).jst_sku_id,
      supplier_compeny_id: formData.value.company_supplier_id,
    }
  })

  const res = await GetPurchaseOrderProductInfo({ paramList, process_type: formData.value.process_type })
  if (!res.success) {
    return message.info(res.message)
  }
  const tableDataIdArr = tableData.value.map((v: any) => {
    return v.k3_sku_id
  })
  res.data
    .filter((f) => !tableDataIdArr.includes(f.k3_sku_id))
    .forEach(async (item) => {
      for (const key of ['other_fees', 'mould_fees', 'discount_amount', 'purchase_quantity', 'gift_purchase_quantity', 'ordernum', 'process_fee', 'material_price', 'system_unit_price', 'tax']) {
        item[key] = item[key] || 0
      }
      const params = {
        ...item,
        predict_delivery_date: item.predict_delivery_date ? dayjs(item.predict_delivery_date, 'YYYY-MM-DD') : '',
        purchaseOrderApplyDetails: [],
        tax_rate_id: taxRateOption.value[0].value,
        tax_rate: taxRateOption.value[0].tax_rate,
        // reverse_price: false,
        key: generateUUID(),
        purcharse_order_id: formData.value.purcharse_order_id,
      }
      tableData.value.push(params)
      await nextTick()
      InputPrice('conversion_value', params, tableData.value.length - 1)
    })
}

const onSupplierIdChange = async (data, isChange = false) => {
  let _data = data
  if (data instanceof Array) {
    _data = data[0]
  }
  await formRef.value.changeValue('company_supplier_id', _data.value, isChange)
  formRef.value.changeItem('company_supplier_id', {
    options: [_data],
  })
}

// 查看历史
const historyModalRef = ref()
const setHistory = (item) => {
  if (formData.value.company_supplier_id > 0) {
    historyModalRef.value.setModel(true, item, formData.value.company_supplier_id, {
      process_type: formData.value.process_type,
    })
  } else {
    message.info('请先选择供应商子公司')
  }
}

// 防抖
const timer = ref()
const debounce = (func, delay, dataIndex, item, index) => {
  if (timer.value) clearTimeout(timer.value) // 清除这个定时器
  timer.value = setTimeout(() => {
    func(dataIndex, item, index)
  }, delay)
}

const calculatePriceIndex = ref()
const calculatePriceItem = ref()
const calculatePriceItemWatcher = ref()
const InputPrice = async (dataIndex, item, index) => {
  if (['purchase_quantity', 'gift_purchase_quantity', 'conversion_value'].includes(dataIndex) && item.purchase_quantity > 0) {
    const params = {
      paramList: [
        {
          k3_sku_id: (item as any).k3_sku_id,
          jst_sku_id: (item as any).jst_sku_id,
          supplier_compeny_id: formData.value.company_supplier_id,
          sum_quantity: item.purchase_quantity,
        },
      ],
      process_type: formData.value.process_type,
    }
    calculatePriceIndex.value = [`${index}`, `${dataIndex}`]
    const res = await GetPurchaseOrderProductInfo(params)
    if (res.success) {
      Object.assign(item, {
        material_price: res.data[0].material_price,
        process_fee: res.data[0].process_fee,
        system_unit_price: res.data[0].system_unit_price,
      })
    }
  }
  console.log('item', item, item[dataIndex])
  const itemState = {
    ...item,
    [dataIndex]: item[dataIndex],
  }
  for (const key in itemState) {
    if (Object.values(decimalKeys.value).flat().includes(key)) {
      itemState[key] = Number.isNaN(Number(itemState[key])) ? 0 : Number(itemState[key])
    }
  }
  calculatePriceItem.value = {
    ...itemState,
    dataIndex,
    index,
  }
}

const changePrice = (dataIndex, item, index) => {
  // 处理 conversion_value 字段，如果输入为0则自动赋值为1
  if (dataIndex === 'conversion_value' && item[dataIndex] === 0) {
    item[dataIndex] = 1
  }
  debounce(InputPrice, 300, dataIndex, item, index)
}

const deleteChildItem = (item, index) => {
  if (item.quantity > 0) {
    message.info('当前存在采购数量不可以删除')
    return
  }
  // 逻辑1：变更单删除商品需要该商品已执行数量为0时才可以删除
  if (item.total_purchase_scheduled_quantity > 0) {
    message.info('当前存在已执行数量不可以删除')
    return
  }
  tableData.value.forEach((titem) => {
    if (titem.parentkey == item.key) {
      titem.purchaseOrderApplyDetails.splice(index, 1)
    }
  })

  // 如果申请单全部删除，同步删除该商品
  const tableDataState: any = []
  tableData.value.forEach((titem) => {
    if (titem.purchaseOrderApplyDetails.length > 0) {
      tableDataState.push(titem)
    }
  })
  tableData.value = tableDataState
}

const handleSetting = async () => {
  showSetting.value = true
  await nextTick()
  visible.value = true
}
const handleActiveChange = () => {
  switch (activeTabKey.value) {
    case 'purchase':
      tableSettingKeys.value = columns.value
      break
    case 'inbound':
      tableSettingKeys.value = bookingColumns.value.map((f) => ({ ...f, key: f.field, name: f.title }))
      break
    case 'refund':
      tableSettingKeys.value = refundColumns.value.map((f) => ({ ...f, name: f.title }))
      break
    case 'payment':
      tableSettingKeys.value = paymentColumns.value.map((f) => ({ ...f, name: f.title }))
      break
    default:
      tableSettingKeys.value = columns.value
      break
  }
}

// 逻辑1：变更单删除商品需要该商品已执行数量为0时才可以删除
// 逻辑2：采购单编辑不收限制
const deleteItem = (item, index) => {
  // 逻辑1：变更单删除商品需要该商品已执行数量为0时才可以删除
  if (item.total_purchase_scheduled_quantity > 0) {
    message.info('当前存在已执行数量不可以删除')
    return
  }
  tableData.value.splice(index, 1)
}
// 获取税率下拉列表
const taxRateOption = ref<Record<string, any>[]>([])
const getTaxRateList = async () => {
  const res = await GetTaxRateSelect({})
  if (res.success) {
    taxRateOption.value = res.data
      .map((v) => {
        return {
          label: v.label,
          value: Number(v.value),
          tax_rate: v.tax_rate,
        }
      })
      .sort((a, b) => a.tax_rate - b.tax_rate)
  }
}

// 拍单子表
const ChildrenListDlgRef = ref()
const openChildrenList = (row) => {
  const bol = type.value == 'new'
  ChildrenListDlgRef.value.open(row, bol)
}

const allowOverApply = ref(false)
const mode = computed(() => {
  return ['detail', 'review'].includes(type.value) ? 'detail' : 'add'
}) as any

// 线下、委外类型的采购单状态=已完成，允许申请变更，可变更字段：
// 成本材料单价、成本加工费、税率、商品总金额、开模费、其他费用、优惠金额
const isSpecialEditKeys = ref<string[]>([])
const initFlag = ref(false)
const isSRS = ref(false)
const initMethod = async () => {
  const systemConfigRes = await GetSystemConfigValue({ type: 1, key: 'allowOverApply' })
  if (systemConfigRes.success) {
    allowOverApply.value = systemConfigRes.data?.switch_case || false
  }
  type.value = /^查看/.test(route.name as string) ? 'detail' : /^审核/.test(route.name as string) ? 'review' : /^编辑/.test(route.name as string) ? 'edit' : 'new'
  console.log('type.value ', type.value)

  getTaxRateList()
  formData.value = {
    supplier_type: SupplierStatusEnum.待确认,
    order_status: PurchaseOrderStatusEnum.进行中,
    change_audit_status: PurchaseOrderAuditStatusEnum.待提审,
    audit_status: PurchaseOrderAuditStatusEnum.待提审,
    received_status: PurchaseOrderReceivedStatusEnum.未领料,
    id: route.params.id,
    purchase_time: dayjs().format('YYYY-MM-DD'), // 设置默认采购时间为当前日期
  }

  const RSRMode = (data) => {
    if (data.supplier_source_type === SupplierSourceTypeEnum.SRS) {
      isSRS.value = true
      formRef.value?.changeItem('company_supplier_id', {
        disabled: true,
      })
      formRef.value?.changeItem('type', {
        disabled: true,
      })
    }
  }
  // 存在ID时
  if (formData.value.id) {
    const api = ['detail', 'edit', 'review'].includes(type.value) ? GetPurchaseOrderChange : GetPurchaseOrderDetail
    const res = await api({
      id: formData.value.id,
      is_check: type.value === 'detail',
      is_view: ['detail'].includes(type.value),
    })
    if (res.data.order_status === PurchaseOrderStatusEnum.已完成 && [PurchaseTypeEnum.委外加工单, PurchaseTypeEnum.线下采购订单].includes(res.data.type)) {
      console.log('特殊处理')
      isSpecialEditKeys.value = ['conversion_value', 'packing_qty', 'purchase_quantity', 'gift_purchase_quantity', 'predict_delivery_date']
    }

    formData.value.purcharse_order_id = res.data.id
    if (['detail', 'review'].includes(type.value)) {
      // 查看的 & 审核的
      await formRef.value.changeValue({
        type: res.data.type,
      })
      formData.value = {
        ...formData.value,
        ...res.data,
        purcharse_order_id: res.data.id,
        id: route.params.id,
      }
      // 预付比例为null时显示为0
      if (formData.value.prepayment_ratio == null) {
        formData.value.prepayment_ratio = 0
      }
      tableData.value = res.data.purchaseOrderDetails.map((f) => {
        return {
          ...formatPriceDecimal(f),
        }
      })
    } else {
      // 编辑 & 从采购单新增
      const { supplier_name, company_supplier_id, is_change_account, is_allow_change_type, ..._data } = res.data

      formRef.value.changeItem('warehourse_id', { type: 'select' })
      formRef.value.changeItem('type', { disabled: !is_allow_change_type, required: true })
      formRef.value.changeItem('ali_purchase_account_list_id', { disabled: !is_change_account })

      RSRMode(_data)

      await formRef.value.changeValue({
        type: res.data.type,
        ..._data,
        warehourse_id: `${res.data.warehourse_id}`,
        buyer_id: `${res.data.buyer_id}`,
        order_status: res.data.order_status || PurchaseOrderStatusEnum.进行中,
        change_audit_status: res.data.change_audit_status || PurchaseOrderAuditStatusEnum.待提审,
        supplier_type: res.data.supplier_type || SupplierStatusEnum.待确认,
        tags: (res.data.tags || []).join(','),
        supplier_name: res.data.supplier_name,
      })

      // 预付比例为null时显示为0
      if (formData.value.prepayment_ratio == null) {
        formData.value.prepayment_ratio = 0
      }

      if (company_supplier_id) {
        await onSupplierIdChange({ label: supplier_name, value: company_supplier_id })
      }
      for (const name of ['type', 'consignee', 'phone_number', 'shipments_address', 'province', 'city', 'area', 'warehourse_id']) {
        formRef.value.changeItem(name, { isDetail: false })
      }

      onTypeChange(res.data.type)

      if (isSpecialEditKeys.value?.length) {
        for (const i of formItems.value) {
          formRef.value.changeItem(i.name, { disabled: true })
        }
      }

      if (type.value === 'new') {
        const res = await GetPurchaseOrderDetailList({ id: formData.value.purcharse_order_id })
        if (res.success) {
          tableData.value = res.data || []
        }
      } else {
        tableData.value = res.data.purchaseOrderDetails
      }
    }
    tableData.value.forEach((item, index) => {
      let count = 0
      item.purchaseOrderApplyDetails.forEach((applyItem) => {
        count += applyItem.await_purchase_quantity
        applyItem.key = index + 1
      })
      item.ordernum = count
      item.parentkey = index + 1
      // 兼容旧数据 没材料单价 & 加工费 塞到系材料单价
      if (!item.material_price && !item.process_fee && item.unit_price) {
        item.material_price = item.unit_price
      }
      // 兼容旧数据 税率tax_rate_id === 0
      if (item.tax_rate_id === 0) {
        item.tax_rate_id = taxRateOption.value.find((f) => f.tax_rate == item.tax_rate)?.value
      }
    })

    const bookingRes = await GetPurchaseDetailBookingList({ id: formData.value.purcharse_order_id })
    if (bookingRes.success) {
      bookingTableData.value = bookingRes.data.reduce((acc, cur) => {
        const items = cur.purchaseinRelationDos?.length
          ? cur.purchaseinRelationDos.map((f, index) => ({
              ...cur,
              ...f,
              purchaseinRelationDos: undefined,
              count: cur.purchaseinRelationDos.length,
              index,
            }))
          : [{ ...cur, count: 1, index: 0 }]
        return [...acc, ...items]
      }, [])
    }
    // 获取付款明细数据
    const paymentRes = await GetPurchaseOrderPaymentOrderList({ id: formData.value.purcharse_order_id })
    if (paymentRes.success) {
      paymentTableData.value = paymentRes.data
    }

    // 获取退款明细数据
    const refundRes = await GetPurchaseOrderReturnApplyOrderList({ purchase_order_id: formData.value.purcharse_order_id })
    if (refundRes.success) {
      refundTableData.value = refundRes.data
    }

    // 根据回显的结算方式设置预付比例字段的必填状态
    if (formData.value.settlement_type === 4) {
      formRef.value?.changeItem('prepayment_ratio', {
        required: true,
      })
    }
    const promiseArr: Promise<any>[] = [GetTableConfig({ page_type: 1701 }), GetTableConfig({ page_type: 1702 }), GetTableConfig({ page_type: 1703 }), GetTableConfig({ page_type: 1704 })]
    const tableColumns = await Promise.all(promiseArr)
    columns.value = formatColumn(tableColumns[0].data?.maps, setColumns())
    tableSettingKeys.value = columns.value
    bookingColumns.value = formatColumn(tableColumns[1].data?.maps, setBookingColumns)
    paymentColumns.value = formatColumn(tableColumns[2].data?.maps, setPaymentColumns)
    refundColumns.value = formatColumn(tableColumns[3].data?.maps, setRefundColumns)
    initFlag.value = true
  }
}
const formatColumn = (data: any[], old: any[]) => {
  if (!data) {
    return old
  }
  const arr: any[] = []
  old.forEach((oldItem) => {
    const obj = data.find((f) => f.key == oldItem.key || f.key == oldItem.field)
    if (obj) {
      arr.push({
        ...oldItem,
        fixed: obj.freeze == 1 ? 'left' : obj.freeze == 2 ? 'right' : null,
        width: obj.width,
        is_show: obj.is_show,
        visible: obj.is_show,
      })
    } else {
      arr.push(oldItem)
    }
  })
  return arr
}

// 格式化金额后小数点（去除科学计数法）
const decimalKeys = ref({
  2: ['tax', 'optimize_sum_price', 'tax_sum_price', 'total_purchase_amount'],
  8: ['unit_price', 'tax_unit_price', 'purchase_tax_price', 'k3_reference_price', 'k3_reference_acreage', 'sum_price'],
  5: ['cost_num'],
})
const formatPriceDecimal = (data) => {
  const result = {}
  for (const name of Object.keys(data)) {
    let precision = 0
    if (decimalKeys.value[8].includes(name)) {
      precision = 8
    } else if (decimalKeys.value[2].includes(name)) {
      precision = 2
    } else if (decimalKeys.value[5].includes(name)) {
      precision = 5
    }
    result[name] = precision ? (+data[name]).roundNext(precision) : data[name]
  }
  return result
}
onActivated(() => {
  calculatePriceItemWatcher.value = watchEffect(() => {
    if (!calculatePriceItem.value) return
    const {
      purchase_quantity, // 采购数量
      gift_purchase_quantity, // 赠品数量
      conversion_value, // 换算值
      purchaseOrderApplyDetails, // 采购子订单
      ordernum, // 订单数量
      cost_unit, // 成本单位
      valuation_unit, // 计量单位
      process_fee,
      material_price,
      tax_rate_id,
      // is_contain_tax, // 是否含税价目表
      system_unit_price,
      other_fees,
      mould_fees,
      discount_amount,
      index,
      dataIndex,
      sum_price,
      k3_sku_id,
      reservation_number,
      take_quantity,
      prepaid_amount,
    } = calculatePriceItem.value

    console.log('index', calculatePriceItem.value)

    const result: Record<string, any> = {
      tax_rate: taxRateOption.value.find((f) => f.value == tax_rate_id)?.tax_rate,
    }
    if (purchaseOrderApplyDetails.length && !allowOverApply.value) {
      result.purchase_quantity = Math.min(ordernum, +(purchase_quantity || 0))
    } else {
      result.purchase_quantity = purchase_quantity
    }
    const arr = bookingTableData.value.filter((f) => f.k3_sku_id == k3_sku_id)
    const purchase_number = arr.reduce((total, cur) => total + Number((cur.audit_status !== 200 && cur.purchase_scheduled_quantity) || 0), 0)
    if (result.purchase_quantity < purchase_number) {
      result.purchase_quantity = purchase_number
    }
    const gift_number = arr.reduce((total, cur) => total + Number(cur.gift_scheduled_quantity || 0), 0)

    result.gift_purchase_quantity = gift_purchase_quantity < purchase_number ? gift_number : gift_purchase_quantity

    // 计算成本数量
    result.cost_num = Math.max(0, result.purchase_quantity * conversion_value)
    if (!result.cost_num) {
      calculatePriceItem.value = null
      return
    }
    // 计算合计单价 & 商品总金额
    if (dataIndex == 'sum_price') {
      result.unit_price = sum_price / result.cost_num
      result.material_price = result.unit_price
      result.process_fee = 0
      result.sum_price = sum_price
    } else {
      result.unit_price = (process_fee || 0) + (material_price || 0)

      // 商品总金额 = 成本合计单价 * 成本数量
      result.sum_price = result.unit_price * result.cost_num
    }
    // 计算采购总数
    result.total_purchase_quantity = result.purchase_quantity + result.gift_purchase_quantity

    // 数量在变更时不能小于已执行数量
    if (reservation_number > result.total_purchase_quantity) {
      result.total_purchase_quantity = reservation_number
    }
    if (take_quantity > result.total_purchase_quantity) {
      message.info('采购数量不能小于推单数量')
      result.total_purchase_quantity = take_quantity
    }

    // 修改换算值时换算公式同步修改
    result.conversion_formula = `${conversion_value}${cost_unit}=1${valuation_unit}`
    // 数量改变要按顺序同步到采购子订单
    let quantity = result.total_purchase_quantity
    result.purchaseOrderApplyDetails = purchaseOrderApplyDetails.map((item, index) => {
      // 超采打开时分摊到最后一个
      if (allowOverApply.value && index === purchaseOrderApplyDetails.length - 1) {
        item.quantity = quantity
      } else {
        item.quantity = Math.min(item.await_purchase_quantity, quantity)
      }
      quantity -= item.quantity
      return item
    })
    // 含税单价
    result.tax_unit_price = result.unit_price + (result.tax_rate === 0 ? 0 : (result.unit_price * result.tax_rate) / 100)

    // 税额
    result.tax = result.tax_rate === 0 ? 0 : result.sum_price * (result.tax_rate * 0.01)
    // if (result.tax_unit_price) {
    //   result.tax = result.tax_unit_price - result.unit_price
    // } else {
    //   result.tax = 0
    // }

    // 商品优化总金额 = (成本含税单价 - 系统单价) * 成本数量
    result.optimize_sum_price = (result.tax_unit_price - system_unit_price) * result.cost_num

    // 商品含税总金额 = 商品总金额 + 税额
    result.tax_sum_price = result.sum_price + result.tax

    // 采购含税单价 = 含税总金额 / 采购总数量（四舍五入到8位小数）
    result.purchase_tax_price = result.total_purchase_quantity > 0 ? result.tax_sum_price / result.total_purchase_quantity : 0

    // 当价目表为含税价目表:   优化金额=含税单价*数量 - 系统单价*数量
    // 当价目表为不含税价目表: 优化金额=单价*数量 - 系统单价*数量
    // result.optimize_sum_price = (result[is_contain_tax ? 'tax_unit_price' : 'unit_price'] - system_unit_price) * result.cost_num
    result.total_purchase_amount = result.tax_sum_price + Number(mould_fees ?? 0) + Number(other_fees ?? 0) - Number(discount_amount ?? 0)

    // debugger

    // 验证采购总金额是否小于预付金额
    if (prepaid_amount && result.total_purchase_amount < prepaid_amount && paymentTableData.value.length) {
      message.warning('采购总金额小于已预付金额')
    }

    tableData.value[index] = {
      ...tableData.value[index],
      ...formatPriceDecimal(result),
    }
    console.log('result', result)

    calculatePriceItem.value = null
    calculatePriceIndex.value = null
  })
})
onDeactivated(() => {
  calculatePriceItemWatcher.value()
  calculatePriceItem.value = null
  calculatePriceIndex.value = null
})

// 审核记录
const reviewTimerRef = ref()
const reviewRef = ref()
const reviewType = (val?: number) => {
  if (val && [1, 2].includes(val)) {
    reviewTypeMode(val)
  } else {
    reviewTimerRef.value.setModel(true, formData.value.id, 5)
  }
}
const reviewTypeMode = (type) => {
  const useData = JSON.parse(localStorage.getItem('userData') as any)
  const permissions_infos = useData.permissions_infos.find((item) => item.id == 80000)
  const permissions_infos_child = permissions_infos.children.find((item) => item.id == 85000)
  const btnList = {}
  permissions_infos_child.btnList.forEach((item) => {
    btnList[item.id] = true
  })
  if (
    (formData.value.change_audit_status == PurchaseOrderAuditStatusEnum['待一级审核'] && btnList[85003]) ||
    (formData.value.change_audit_status == PurchaseOrderAuditStatusEnum['待二级审核'] && btnList[85004]) ||
    (formData.value.change_audit_status == PurchaseOrderAuditStatusEnum['待三级审核'] && btnList[85005]) ||
    (formData.value.change_audit_status == PurchaseOrderAuditStatusEnum['待四级审核'] && btnList[85006])
  ) {
    if (type == 1) {
      reviewRef.value.setReview(true, '审核通过', '确认通过', true)
    } else {
      reviewRef.value.setReview(true, '审核拒绝', '确认拒绝', false)
    }
  } else {
    message.error('您没有该状态的审核权限')
  }
}
const ClickReview = async (val, is_pass) => {
  const params = {
    is_pass,
    audit_opinion: val,
    id: formData.value.id,
  }
  const res = await Audit(params)
  console.log('res', res)

  if (res.success) {
    message.success('操作成功')
    closePage()
  } else {
    message.error(res.message)
  }
}

// const getData = async () => {}
const submitLoading = ref(false)
const handleSubmit = async (is_pass) => {
  if (tableData.value.length == 0) {
    message.info('请选择采购商品')
    return
  }
  let check = true
  tableData.value.forEach((item, index) => {
    if ((check && parseFloat(item.total_purchase_quantity.toString()) > 0) == false) {
      check = false
    }
    if (item.prepaid_amount && item.prepaid_amount > item.total_purchase_amount && paymentTableData.value.length) {
      const msg = `采购明细 第${index + 1}行 采购总金额不得低于预付金额`
      message.info(msg)
      throw msg
    }
  })

  if (!check) {
    message.info('采购数量不能为0')
    return
  }
  const formState = await formRef.value.validate()
  console.log('formState', formState)
  const params = {
    id: route.params.id || undefined,
    ...formState,
  }

  // if (params) return
  // tags 字段转数组
  if (params.tags && typeof params.tags === 'string') {
    params.tags = params.tags.split(',').filter(Boolean)
  }
  const api = type.value === 'new' ? AddPurchaseOrderChange : UpdatePurchaseOrderChange
  if (['new', 'edit'].includes(type.value)) {
    params.is_pass = is_pass
    params.purchaseOrderDetails = tableData.value
    params.purchase_order_id = formData.value.purcharse_order_id
    // 添加采购时间和子供应商ID
    params.purchase_time = formData.value.purchase_time
    params.company_supplier_id = parseInt(formData.value.company_supplier_id) || formData.value.company_supplier_id

    // 为UpdatePurchaseOrderChange添加额外字段
    if (type.value === 'edit') {
      params.settlement_type = parseInt(formState.settlement_type || formData.value.settlement_type) || formData.value.settlement_type
      params.prepayment_ratio = parseInt(formState.prepayment_ratio || formData.value.prepayment_ratio) || 0
      params.process_type = parseInt(formState.process_type || formData.value.process_type) || formData.value.process_type
      params.product_type = parseInt(formState.product_type || formData.value.product_type) || formData.value.product_type
      params.remark = formState.remark || formData.value.remark
    }
  }

  console.log('params', params)
  // if (params) return
  const res = await api(params, { loading: submitLoading })
  if (res.success == true) {
    message.success('提交成功')
    closePage()
  } else {
    message.error(res.message)
  }
}
const openLog = () => {
  operationLogRef.value.open({
    params: {
      id: formData.value.id,
      pageType: OpLogPageTypeEnum.采购单变更,
    },
  })
}
</script>

<style scoped>
.custom-tabs :deep(.ant-tabs-tab) {
  font-size: 14px;
  font-weight: bold;
}

.custom-tabs :deep(.ant-tabs-tab-btn) {
  font-size: 14px;
  font-weight: bold;
}
</style>
